import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { LlmService } from '../../../ai/llm/services/llm.service';
import { LangChainLlmService } from '../../../ai/llm/services/langchain-llm.service';
import { ExecuteAgentDto } from '../dto/agent.dto';
import { JwtUser } from '../../../../types/jwt-user.type';
import { ai_agents, ai_models, ai_keys, AiAgentResponseFormat } from '@prisma/client';

type FullAgent = ai_agents & { ai_models: ai_models; ai_keys: ai_keys };

@Injectable()
export class AgentExecutorService {
  private readonly logger = new Logger(AgentExecutorService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly llmService: LlmService,
    private readonly langChainLlmService: LangChainLlmService,
  ) {}

  /**
   * 執行指定的 Agent
   */
  async execute(agentId: string, executeDto: ExecuteAgentDto) {
    const agent = await this.findAgentById(agentId);

    const response = await this.llmService.execute(
      [{ role: 'system', content: agent.system_prompt || '' }, ...executeDto.messages],
      {
        providerType: agent.provider_type,
        model: agent.ai_models.model_name,
        keyId: agent.key_id,
        temperature: executeDto.temperature || agent.temperature || 0.7,
        maxTokens: agent.max_tokens || 2048,
        responseFormat:
          agent.response_format === AiAgentResponseFormat.JSON ? 'json_object' : 'text',
        systemPrompt: agent.system_prompt || undefined,
      },
    );

    return response;
  }

  /**
   * 測試 Agent
   */
  async testAgent(agentId: string, message: string, prompt?: string, temperature?: number) {
    const agent = await this.findAgentById(agentId);
    const executeDto: ExecuteAgentDto = {
      messages: [{ role: 'user', content: message }],
      temperature: temperature ?? agent.temperature ?? undefined,
    };
    return this.execute(agentId, executeDto);
  }

  /**
   * 簡化的 Agent 執行（基本版本）
   */
  async runAgent(userInput: string, user: JwtUser, agentConfigId?: string, sessionId?: string): Promise<string> {
    try {
      // 基本驗證
      this.validateInput(userInput, user.tenant_id);

      // 如果有 sessionId，可以在未來用於會話管理
      if (sessionId) {
        this.logger.debug(`Agent execution with session: ${sessionId}`);
      }

      // 如果有指定 Agent，使用指定的；否則創建預設回應
      if (agentConfigId) {
        const agent = await this.findAgentById(agentConfigId);

        // 驗證租戶權限
        if (agent.tenant_id !== user.tenant_id) {
          throw new BadRequestException('無權限使用此 Agent');
        }

        const executeDto: ExecuteAgentDto = {
          messages: [{ role: 'user', content: userInput }],
        };

        const response = await this.execute(agentConfigId, executeDto);
        return response.content || `Agent response for: ${userInput}`;
      }

      // 如果沒有指定 Agent，嘗試使用租戶的預設 Agent
      const defaultAgent = await this.findDefaultAgentForTenant(user.tenant_id || '');

      if (defaultAgent) {
        const executeDto: ExecuteAgentDto = {
          messages: [{ role: 'user', content: userInput }],
        };

        const response = await this.execute(defaultAgent.id, executeDto);
        return response.content || this.generateFallbackResponse(userInput);
      }

      // 如果沒有可用的 Agent，返回友好的回應
      return this.generateFallbackResponse(userInput);
    } catch (error) {
      this.logger.error(`Agent execution failed: ${error.message}`, error.stack);
      throw new BadRequestException(`Agent 執行失敗: ${error.message}`);
    }
  }

  /**
   * 私有方法：查找 Agent
   */
  private async findAgentById(id: string): Promise<FullAgent> {
    const agent = await this.prisma.ai_agents.findUnique({
      where: { id },
      include: {
        ai_models: true,
        ai_keys: true,
      },
    });

    if (!agent) {
      throw new BadRequestException(`Agent with ID ${id} not found`);
    }

    return agent;
  }

  /**
   * 私有方法：驗證輸入
   */
  private validateInput(userInput: string, tenantId: string | null | undefined): void {
    if (!tenantId) {
      throw new BadRequestException('用戶資訊中缺少租戶 ID');
    }
    if (!userInput) {
      throw new BadRequestException('用戶輸入不能為空');
    }
    if (userInput.length > 10000) {
      throw new BadRequestException('用戶輸入過長，最大允許 10000 個字符');
    }
  }

  /**
   * 私有方法：查找租戶的預設 Agent
   */
  private async findDefaultAgentForTenant(tenantId: string): Promise<FullAgent | null> {
    try {
      const defaultAgent = await this.prisma.ai_agents.findFirst({
        where: {
          tenant_id: tenantId,
          is_enabled: true,
          // 可以添加額外條件來確定「預設」Agent，例如：
          // 1. 查找名稱包含 'default' 的 Agent
          // 2. 查找最早創建的 Agent
          // 3. 查找特定標記為預設的 Agent
        },
        include: {
          ai_models: true,
          ai_keys: true,
        },
        orderBy: {
          created_at: 'asc', // 選擇最早創建的 Agent 作為預設
        },
      });

      return defaultAgent;
    } catch (error) {
      this.logger.error(`Failed to find default agent for tenant ${tenantId}:`, error);
      return null;
    }
  }

  /**
   * 私有方法：生成友好的後備回應
   */
  private generateFallbackResponse(userInput: string): string {
    // 根據輸入內容的特性生成不同的回應
    const lowerInput = userInput.toLowerCase();

    if (lowerInput.includes('hello') || lowerInput.includes('hi') || lowerInput.includes('你好')) {
      return '您好！我是 HorizAI 助手。目前系統正在配置中，請稍後再試或聯繫管理員設定您的專屬 Agent。';
    }

    if (lowerInput.includes('help') || lowerInput.includes('幫助') || lowerInput.includes('協助')) {
      return '我想為您提供幫助！但目前沒有可用的 Agent 配置。請聯繫您的系統管理員來設定 AI 助手功能。';
    }

    if (lowerInput.includes('?') || lowerInput.includes('？')) {
      return '我理解您有疑問需要解答。為了提供最佳服務，請先確保您的帳戶已配置適當的 AI Agent，或聯繫管理員協助設定。';
    }

    // 預設回應
    return `收到您的訊息：「${userInput.length > 50 ? userInput.substring(0, 50) + '...' : userInput}」。目前沒有可用的 Agent 處理此請求，請聯繫管理員配置 AI 助手功能。`;
  }
}
