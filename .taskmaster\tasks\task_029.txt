# Task ID: 29
# Title: 更新 AgentRunnerService 支援自定義 Agent 執行
# Status: pending
# Dependencies: 19, 27
# Priority: high
# Description: 重構 AgentRunnerService 以支援從資料庫載入自定義 Agent 配置，並根據執行類型選擇適當的執行引擎（LangChain 或 LangGraph）。
# Details:
擴展現有的 AgentRunnerService:
1. **Agent 配置載入**: 
   - loadAgentConfig(botId) - 從資料庫載入完整 Agent 配置
   - 包含模型設定、API 金鑰、系統提示詞、可用工具列表
2. **動態工具註冊**: 
   - registerToolsForAgent(agent) - 根據 Agent 的工具配置動態註冊 LangChain Tools
   - 工具實例化和權限檢查
3. **執行引擎選擇**: 
   - 檢查 execution_type 欄位
   - SINGLE_CALL: 使用現有 LangChain AgentExecutor
   - GRAPH: 載入對應的 LangGraph 實例 (為未來準備)
4. **租戶隔離**: 確保 Agent 執行時所有操作都在正確的租戶範圍內
5. **錯誤處理和日誌**: 增強日誌記錄，包含 Agent 配置和工具使用情況

# Test Strategy:
單元測試 Agent 配置載入邏輯。測試工具動態註冊功能。驗證不同執行類型的路由邏輯。集成測試完整的 Agent 執行流程。確認租戶隔離和權限控制。
