import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import {
  CreateTenantUserDto,
  UpdateTenantUserDto,
  ToggleUserStatusDto,
  HandleEmployeeLeavingDto,
  UpdateDataTransferStatusDto,
} from './dto/tenant-user.dto';
import { ITenantUser, ITenantUserProfile } from '../../../types/models/user.model';
import * as bcrypt from 'bcryptjs';
import { SystemLogService } from '../../../common/services/system-log.service';
import { MailService } from '../../core/mail/mail.service';
import { Prisma, tenant_users } from '@prisma/client';

@Injectable()
export class TenantUsersService {
  private readonly logger = new Logger(TenantUsersService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly systemLogService: SystemLogService,
    private readonly mailService: MailService,
  ) {}

  /**
   * 讀取租戶的所有使用者
   * @param tenant_id 租戶 ID (可選)
   * @returns 使用者列表
   */
  async findAll(tenant_id?: string): Promise<ITenantUserProfile[]> {
    const where: Prisma.tenant_usersWhereInput = {};
    if (tenant_id) {
      where.tenant_id = tenant_id;
    }
    const users = await this.prisma.tenant_users.findMany({ where });
    return users.map((user) => this.toProfile(user));
  }

  /**
   * 根據 ID 查詢使用者
   * @param id 使用者 ID
   * @param tenant_id 租戶 ID
   * @returns 使用者資料
   */
  async findOne(id: string, tenant_id?: string): Promise<ITenantUserProfile> {
    const user = await this.prisma.tenant_users.findUnique({ where: { id } });

    if (!user || (tenant_id && user.tenant_id !== tenant_id)) {
      throw new NotFoundException(`Tenant user with ID ${id} not found`);
    }
    return this.toProfile(user);
  }

  /**
   * 建立新的租戶使用者
   * @param createTenantUserDto 建立使用者資料
   * @returns 建立的使用者資料
   */
  async create(createTenantUserDto: CreateTenantUserDto): Promise<ITenantUserProfile> {
    this.logger.debug(`建立租戶使用者: ${createTenantUserDto.email}`);

    // 檢查 email 是否已存在
    const existingUser = await this.prisma.tenant_users.findUnique({
      where: { email: createTenantUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('此電子郵件地址已被使用');
    }

    const { email, password, tenant_id, invited_by, ...rest } = createTenantUserDto;

    // 如果沒有提供密碼，生成一個隨機密碼
    const finalPassword = password || this.generateTempPassword();
    const hashedPassword = await bcrypt.hash(finalPassword, 10);

    try {
      const newUser = await this.prisma.tenant_users.create({
        data: {
          email,
          password: hashedPassword,
          tenant_id: tenant_id,
          invited_by,
          ...rest,
        },
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      this.logger.log(`成功建立租戶使用者: ${newUser.email}`);
      return this.toProfile(newUser);
    } catch (error) {
      this.logger.error(`建立租戶使用者失敗: ${error.message}`);
      throw new BadRequestException(`建立使用者失敗: ${error.message}`);
    }
  }

  /**
   * 更新租戶使用者
   * @param id 使用者 ID
   * @param updateTenantUserDto 更新資料
   * @returns 更新後的使用者資料
   */
  async update(id: string, updateTenantUserDto: UpdateTenantUserDto): Promise<ITenantUserProfile> {
    const data: Prisma.tenant_usersUpdateInput = { ...updateTenantUserDto };
    // Note: password field is not included in UpdateTenantUserDto for security reasons
    const user = await this.prisma.tenant_users.update({ where: { id }, data });
    return this.toProfile(user);
  }

  /**
   * 刪除租戶使用者
   * @param id 使用者 ID
   * @returns 刪除的使用者資料
   */
  async remove(id: string): Promise<ITenantUserProfile> {
    const user = await this.prisma.tenant_users.delete({ where: { id } });
    return this.toProfile(user);
  }

  /**
   * 切換使用者狀態
   * @param id 使用者 ID
   * @param toggleUserStatusDto 狀態切換資料
   * @returns 更新後的使用者資料
   */
  async toggleStatus(
    id: string,
    toggleUserStatusDto: ToggleUserStatusDto,
  ): Promise<ITenantUserProfile> {
    const user = await this.prisma.tenant_users.update({
      where: { id },
      data: { status: toggleUserStatusDto.status },
    });
    return this.toProfile(user);
  }

  /**
   * 更新使用者狀態
   * @param id 使用者 ID
   * @param toggleUserStatusDto 狀態切換資料
   * @returns 更新後的使用者資料
   */
  async updateStatus(
    id: string,
    toggleUserStatusDto: ToggleUserStatusDto,
  ): Promise<ITenantUserProfile> {
    return this.toggleStatus(id, toggleUserStatusDto);
  }

  /**
   * 處理員工離職
   * @param id 使用者 ID
   * @param handleEmployeeLeavingDto 離職處理資料
   * @param currentUser 當前操作使用者
   * @returns 更新後的使用者資料
   */
  async handleEmployeeLeaving(
    id: string,
    handleEmployeeLeavingDto: HandleEmployeeLeavingDto,
    currentUser: any,
  ): Promise<ITenantUserProfile> {
    this.logger.debug(`處理員工離職: ${id}`);

    const user = await this.findOne(id);
    const { reason, transferToUserId } = handleEmployeeLeavingDto;

    // 如果指定了資料轉移目標使用者，檢查該使用者是否存在且屬於同一租戶
    if (transferToUserId) {
      const targetUser = await this.findOne(transferToUserId, user.tenant_id);
      if (!targetUser) {
        throw new NotFoundException('資料轉移目標使用者不存在或不屬於同一租戶');
      }
    }

    try {
      const updatedUser = await this.prisma.tenant_users.update({
        where: { id },
        data: {
          status: 'LEFT_COMPANY',
          left_company_at: new Date(),
          left_company_reason: reason,
          data_transfer_status: transferToUserId ? 'pending' : 'completed',
        },
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // 記錄系統日誌
      await this.systemLogService.logAudit({
        message: `員工離職處理: ${user.email}`,
        user_id: currentUser.id,
        tenant_id: user.tenant_id,
        target_resource: 'TenantUser',
        target_resource_id: id,
        action: 'EMPLOYEE_LEAVING',
        status: 'success',
        details: {
          reason,
          transfer_to_user_id: transferToUserId,
        },
      });

      this.logger.log(`成功處理員工離職: ${updatedUser.email}`);
      return this.toProfile(updatedUser);
    } catch (error) {
      this.logger.error(`處理員工離職失敗: ${error.message}`);
      throw new BadRequestException(`處理員工離職失敗: ${error.message}`);
    }
  }

  /**
   * 更新資料轉移狀態
   * @param id 使用者 ID
   * @param status 轉移狀態
   * @param note 備註
   * @returns 更新後的使用者資料
   */
  async updateDataTransferStatus(
    id: string,
    status: 'pending' | 'in_progress' | 'completed' | 'failed',
    note?: string,
  ): Promise<ITenantUserProfile> {
    this.logger.debug(`更新資料轉移狀態: ${id} -> ${status}`);

    try {
      const updatedUser = await this.prisma.tenant_users.update({
        where: { id },
        data: {
          data_transfer_status: status,
          data_transfer_note: note,
        },
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      this.logger.log(`成功更新資料轉移狀態: ${updatedUser.email} -> ${status}`);
      return this.toProfile(updatedUser);
    } catch (error) {
      this.logger.error(`更新資料轉移狀態失敗: ${error.message}`);
      throw new BadRequestException(`更新資料轉移狀態失敗: ${error.message}`);
    }
  }

  /**
   * 獲取租戶內角色列表
   * @returns 租戶角色列表
   */
  async getTenantRoles(): Promise<
    Array<{ role: string; displayName: string; description: string }>
  > {
    return [
      {
        role: 'admin',
        displayName: '租戶管理員',
        description: '負責管理租戶內的所有資源和使用者',
      },
      {
        role: 'manager',
        displayName: '租戶經理',
        description: '負責管理租戶內的日常運營事務',
      },
      {
        role: 'user',
        displayName: '租戶使用者',
        description: '一般使用者，可以使用租戶內的基本功能',
      },
    ];
  }

  /**
   * 重置使用者密碼
   * @param id 使用者 ID
   * @returns 臨時密碼
   */
  async resetPassword(id: string): Promise<{ temporaryPassword: string }> {
    this.logger.debug(`重置使用者密碼: ${id}`);

    try {
      // 確認使用者存在
      await this.findOne(id);

      // 生成臨時密碼
      const temporaryPassword = this.generateTempPassword();
      const hashedPassword = await bcrypt.hash(temporaryPassword, 12);

      await this.prisma.tenant_users.update({
        where: { id },
        data: {
          password: hashedPassword,
          password_last_changed_at: new Date(),
        },
      });

      this.logger.log(`成功重置使用者密碼: ${id}`);

      // TODO: 發送密碼重置通知郵件

      return { temporaryPassword };
    } catch (error) {
      this.logger.error(`重置密碼失敗: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`重置密碼失敗: ${error.message}`);
    }
  }

  /**
   * 讀取可新增到租戶的使用者列表（暫時保留向後相容）
   * @returns 可用的使用者列表
   */
  async findAvailableUsers(): Promise<ITenantUserProfile[]> {
    this.logger.debug('查詢可新增到租戶的使用者');

    try {
      // 查詢狀態為 ACTIVE 或 INACTIVE 的租戶使用者
      const users = await this.prisma.tenant_users.findMany({
        where: {
          OR: [{ status: 'ACTIVE' }, { status: 'INACTIVE' }],
        },
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { created_at: 'desc' },
      });

      return users.map((user) => this.toProfile(user));
    } catch (error) {
      this.logger.error(`查詢可用使用者失敗: ${error.message}`);
      throw new BadRequestException(`查詢可用使用者失敗: ${error.message}`);
    }
  }

  /**
   * 啟動資料轉移流程
   * @param fromUserId 離職使用者 ID
   * @param toUserId 目標使用者 ID
   * @description 依據 PRD，將離職員工的資產（Project、Album、Photo、Workspace、AiBot 等）所有權轉移給新接手人，並處理無法轉移的資產（封存/待指派），同時移除關聯（WorkspaceMember、UserRoleMapping、RefreshToken 等），產生稽核紀錄與通知。
   */
  private async initiateDataTransfer(fromUserId: string, toUserId: string): Promise<void> {
    this.logger.debug(`啟動資料轉移: ${fromUserId} -> ${toUserId}`);
    await this.updateDataTransferStatus(fromUserId, 'in_progress', '開始資料轉移流程');
    const transferredDataTypes: string[] = [];
    try {
      // 1. 轉移 Project 擁有權
      const updatedProjects = await this.prisma.projects.updateMany({
        where: { user_id: fromUserId },
        data: { user_id: toUserId },
      });
      if (updatedProjects.count > 0) transferredDataTypes.push('projects');

      // 2. 轉移 Album 擁有權
      const updatedAlbums = await this.prisma.albums.updateMany({
        where: { user_id: fromUserId },
        data: { user_id: toUserId },
      });
      if (updatedAlbums.count > 0) transferredDataTypes.push('albums');

      // 3. 轉移 Photo 擁有權
      const updatedPhotos = await this.prisma.photos.updateMany({
        where: { user_id: fromUserId },
        data: { user_id: toUserId },
      });
      if (updatedPhotos.count > 0) transferredDataTypes.push('photos');

      // 4. 轉移 Workspace 擁有權
      const updatedWorkspaces = await this.prisma.workspaces.updateMany({
        where: { owner_id: fromUserId },
        data: { owner_id: toUserId },
      });
      if (updatedWorkspaces.count > 0) transferredDataTypes.push('workspaces');

      // 5. 轉移 AiAgent 建立者
      const updatedAiAgents = await this.prisma.ai_agents.updateMany({
        where: { created_by: fromUserId },
        data: { created_by: toUserId },
      });
      if (updatedAiAgents.count > 0) transferredDataTypes.push('ai_agents');

      // 6. 移除 WorkspaceMember
      await this.prisma.workspace_members.deleteMany({
        where: { tenant_user_id: fromUserId },
      });
      transferredDataTypes.push('workspace_members');

      // 7. 撤銷原始使用者的 refresh tokens
      await this.prisma.refresh_tokens.updateMany({
        where: { tenant_user_id: fromUserId },
        data: { is_valid: false },
      });
      transferredDataTypes.push('refresh_tokens');

      // 8. 移除 TenantUserRoleMapping
      await this.prisma.tenant_user_roles.deleteMany({
        where: { tenant_user_id: fromUserId },
      });
      transferredDataTypes.push('tenant_user_role_mappings');

      // 9. 處理 Setting（移除個人設定、更新共享設定維護者）
      await this.prisma.settings.deleteMany({
        where: { created_by: fromUserId },
      });
      await this.prisma.settings.updateMany({
        where: { updated_by: fromUserId },
        data: { updated_by: toUserId },
      });
      transferredDataTypes.push('settings');

      // 10. 處理 TenantInvite（撤銷未接受邀請）
      await this.prisma.tenant_invitations.deleteMany({
        where: { accepted_by_id: fromUserId },
      });
      transferredDataTypes.push('tenant_invites');

      // 11. 處理 LineGroupVerification（更新管理員/聯絡人）
      await this.prisma.line_group_verifications.updateMany({
        where: { verified_by_user_id: fromUserId },
        data: { verified_by_user_id: toUserId },
      });
      transferredDataTypes.push('line_group_verifications');

      // 12. 封存無法轉移的資產（如有）
      // 實務上可根據業務規則補充封存邏輯

      // 13. 寫入稽核日誌
      await this.systemLogService.logAudit({
        message: `員工資料轉移完成: ${fromUserId} -> ${toUserId}`,
        user_id: toUserId,
        tenant_id: undefined,
        target_resource: 'TenantUser',
        target_resource_id: fromUserId,
        action: 'data_transfer',
        status: 'success',
        details: { transferredDataTypes },
      });

      // 14. 通知新接手人與租戶管理員（可擴充 mailService）
      // ...

      await this.updateDataTransferStatus(fromUserId, 'completed', '資料轉移完成');
      this.logger.log(`資料轉移完成: ${fromUserId} -> ${toUserId}`);
    } catch (error) {
      this.logger.error(`資料轉移失敗: ${error.message}`);
      await this.updateDataTransferStatus(fromUserId, 'failed', `資料轉移失敗: ${error.message}`);
      throw error;
    }
  }

  /**
   * 生成臨時密碼
   */
  private generateTempPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let result = '';
    for (let i = 0; i < 12; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 轉換為 Profile 格式（移除敏感資訊）
   */
  private toProfile(user: tenant_users): ITenantUserProfile {
    const { password, ...profile } = user;
    return profile as any;
  }
}
