import { Injectable } from '@nestjs/common';
import { SettingsService } from '../../settings/settings.service';
import { SettingCategory } from '../../settings/constants/setting-categories.enum';
import { AuthService } from '../../../core/auth/auth.service';
import { UpdateLineLoginSettingsDto } from '../dto/update-line-login-settings.dto';
import { UpdateChannelIdDto } from '../dto/update-channel-id.dto';
import { UpdateChannelSecretDto } from '../dto/update-channel-secret.dto';
import { UpdateEnableDto } from '../dto/update-enable.dto';

@Injectable()
export class LineLoginConfigService {
  constructor(
    private readonly settingsService: SettingsService,
    private readonly authService: AuthService,
  ) {}

  /** 取得完整的 LINE Login 設定 */
  async getSettings() {
    return this.settingsService.getLineSettings();
  }

  /** 更新完整的 LINE Login 設定 */
  async updateSettings(dto: UpdateLineLoginSettingsDto, userId: string) {
    return this.settingsService.updateCategorySettings({
      category: SettingCategory.LINE,
      data: dto,
      user_id: userId,
    });
  }

  /** 檢查 LINE Login 設定狀態 */
  async getStatus() {
    const settings = await this.settingsService.getCategorySettings(SettingCategory.LINE);
    return {
      channelId: settings.channelId,
      isChannelSecretSet: !!settings.channelSecret,
      enabled: settings.enabled,
    };
  }

  /** 更新 Channel ID */
  async updateChannelId(dto: UpdateChannelIdDto, userId: string) {
    return this.settingsService.updateSetting({
      category: SettingCategory.LINE,
      key: 'channelId',
      value: dto.channel_id,
      user_id: userId,
    });
  }

  /** 更新 Channel Secret */
  async updateChannelSecret(dto: UpdateChannelSecretDto, userId: string) {
    return this.settingsService.updateSetting({
      category: SettingCategory.LINE,
      key: 'channelSecret',
      value: dto.channel_secret,
      user_id: userId,
    });
  }

  /** 更新是否啟用 LINE Login */
  async updateEnable(dto: UpdateEnableDto, userId: string) {
    return this.settingsService.updateSetting({
      category: SettingCategory.LINE,
      key: 'enabled',
      value: dto.enabled,
      user_id: userId,
    });
  }

  /** 取得 LINE OAuth 認證網址 */
  async getAuthUrl(userId: string) {
    // TODO: 實現 LINE OAuth 認證網址生成邏輯
    throw new Error('getLineAuthUrl method not implemented in AuthService');
  }

  /** 解除 LINE 綁定 */
  async disconnectLine(userId: string) {
    // TODO: 實現 LINE 綁定解除邏輯
    throw new Error('disconnectLine method not implemented in AuthService');
  }
}
