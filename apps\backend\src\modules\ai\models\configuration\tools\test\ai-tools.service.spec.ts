import { Test, TestingModule } from '@nestjs/testing';
import { AiToolsService } from '../ai-tools.service';
import { PrismaService } from '../../../../../core/prisma/prisma.service';
import { CreateAiToolDto, UpdateAiToolDto, AiToolQueryDto } from '../dto/ai-tools.dto';
import { AiToolScope } from '@prisma/client';
import { NotFoundException, ConflictException } from '@nestjs/common';

// Mock PrismaService
const mockPrismaService = {
  ai_tools: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  ai_bot_tools: {
    count: jest.fn(),
  },
};

describe('AiToolsService', () => {
  let service: AiToolsService;
  let prismaService: PrismaService;

  const mockAiTool = {
    id: 'tool-123',
    key: 'test_tool',
    name: 'Test Tool',
    description: 'Test tool description',
    input_schema: {
      type: 'object',
      properties: {
        input: { type: 'string' },
      },
      required: ['input'],
    },
    scope: AiToolScope.SYSTEM,
    is_enabled: true,
    tenant_id: null,
    workspace_id: null,
    created_at: new Date(),
    updated_at: new Date(),
    created_by: 'user-123',
    updated_by: null,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiToolsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<AiToolsService>(AiToolsService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of AI tools', async () => {
      mockPrismaService.ai_tools.findMany.mockResolvedValue([mockAiTool]);
      const query: AiToolQueryDto = {};

      const result = await service.findAll(query);

      expect(result).toEqual([mockAiTool]);
      expect(mockPrismaService.ai_tools.findMany).toHaveBeenCalledWith({
        where: {},
        orderBy: [{ scope: 'asc' }, { updated_at: 'desc' }],
      });
    });

    it('should return empty array when no tools exist', async () => {
      mockPrismaService.ai_tools.findMany.mockResolvedValue([]);
      const query: AiToolQueryDto = {};

      const result = await service.findAll(query);

      expect(result).toEqual([]);
      expect(mockPrismaService.ai_tools.findMany).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single AI tool', async () => {
      mockPrismaService.ai_tools.findUnique.mockResolvedValue(mockAiTool);

      const result = await service.findOne('tool-123');

      expect(result).toEqual(mockAiTool);
      expect(mockPrismaService.ai_tools.findUnique).toHaveBeenCalledWith({
        where: { id: 'tool-123' },
      });
    });

    it('should throw NotFoundException when tool not found', async () => {
      mockPrismaService.ai_tools.findUnique.mockResolvedValue(null);

      await expect(service.findOne('non-existent')).rejects.toThrow(NotFoundException);
      expect(mockPrismaService.ai_tools.findUnique).toHaveBeenCalledWith({
        where: { id: 'non-existent' },
      });
    });
  });

  describe('create', () => {
    const createDto: CreateAiToolDto = {
      key: 'test_tool',
      name: 'Test Tool',
      description: 'Test tool description',
      inputSchema: {
        type: 'object',
        properties: {
          input: { type: 'string' },
        },
        required: ['input'],
      },
      scope: AiToolScope.SYSTEM,
      isEnabled: true,
    };

    it('should create a new AI tool', async () => {
      mockPrismaService.ai_tools.findUnique.mockResolvedValue(null);
      mockPrismaService.ai_tools.create.mockResolvedValue(mockAiTool);

      const result = await service.create(createDto, 'user-123');

      expect(result).toEqual(mockAiTool);
      expect(mockPrismaService.ai_tools.findUnique).toHaveBeenCalledWith({
        where: { key: createDto.key },
      });
      expect(mockPrismaService.ai_tools.create).toHaveBeenCalled();
    });

    it('should throw ConflictException when tool key already exists', async () => {
      mockPrismaService.ai_tools.findUnique.mockResolvedValue(mockAiTool);

      await expect(service.create(createDto, 'user-123')).rejects.toThrow(ConflictException);
      expect(mockPrismaService.ai_tools.findUnique).toHaveBeenCalledWith({
        where: { key: createDto.key },
      });
      expect(mockPrismaService.ai_tools.create).not.toHaveBeenCalled();
    });
  });

  describe('update', () => {
    const updateDto: UpdateAiToolDto = {
      name: 'Updated Tool Name',
      description: 'Updated description',
      isEnabled: false,
    };

    it('should throw NotFoundException when tool not found', async () => {
      mockPrismaService.ai_tools.findUnique.mockResolvedValue(null);

      await expect(service.update('non-existent', updateDto)).rejects.toThrow(NotFoundException);
      expect(mockPrismaService.ai_tools.findUnique).toHaveBeenCalledWith({
        where: { id: 'non-existent' },
      });
      expect(mockPrismaService.ai_tools.update).not.toHaveBeenCalled();
    });

    it('should update an AI tool successfully', async () => {
      mockPrismaService.ai_tools.findUnique.mockResolvedValue(mockAiTool);
      const updatedTool = { ...mockAiTool, ...updateDto };
      mockPrismaService.ai_tools.update.mockResolvedValue(updatedTool);

      const result = await service.update('tool-123', updateDto);

      expect(result).toEqual(updatedTool);
      expect(mockPrismaService.ai_tools.findUnique).toHaveBeenCalledWith({
        where: { id: 'tool-123' },
      });
      expect(mockPrismaService.ai_tools.update).toHaveBeenCalled();
    });
  });

  describe('remove', () => {
    it('should remove an AI tool', async () => {
      mockPrismaService.ai_tools.findUnique.mockResolvedValue(mockAiTool);
      mockPrismaService.ai_bot_tools.count.mockResolvedValue(0);
      mockPrismaService.ai_tools.delete.mockResolvedValue(mockAiTool);

      await service.remove('tool-123');

      expect(mockPrismaService.ai_tools.findUnique).toHaveBeenCalledWith({
        where: { id: 'tool-123' },
      });
      expect(mockPrismaService.ai_bot_tools.count).toHaveBeenCalledWith({
        where: { ai_tool_id: 'tool-123' },
      });
      expect(mockPrismaService.ai_tools.delete).toHaveBeenCalledWith({
        where: { id: 'tool-123' },
      });
    });

    it('should throw NotFoundException when tool not found', async () => {
      mockPrismaService.ai_tools.findUnique.mockResolvedValue(null);

      await expect(service.remove('non-existent')).rejects.toThrow(NotFoundException);
      expect(mockPrismaService.ai_tools.findUnique).toHaveBeenCalledWith({
        where: { id: 'non-existent' },
      });
      expect(mockPrismaService.ai_tools.delete).not.toHaveBeenCalled();
    });
  });
});
