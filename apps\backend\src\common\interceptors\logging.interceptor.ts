import { Injectable, NestInterceptor, ExecutionContext, CallHandler, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { Request, Response } from 'express';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SystemLogService } from '../services/system-log.service';
import { AuditLogService } from '../services/audit-log.service';
import { JwtUser } from '../../types/jwt-user.type';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  constructor(
    private readonly systemLogService: SystemLogService,
    private readonly auditLogService: AuditLogService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const method = request.method;
    const url = request.url;
    const startTime = Date.now();

    // 提取用戶信息
    const user = request.user as JwtUser | undefined;
    const userId = user?.id;
    const tenantId = user?.tenant_id;

    // 提取請求上下文
    const requestContext = this.extractRequestContext(request);

    // 為所有請求記錄基本日誌，但只為修改操作記錄詳細稽核
    const shouldAudit = this.shouldAuditRequest(method, url);

    return next.handle().pipe(
      tap({
        next: async (data) => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          const statusCode = response.statusCode;

          // 基本日誌記錄（所有請求）- 發送事件
          this.eventEmitter.emit('audit.system.log', {
            message: `${method} ${url} - 成功 (${duration}ms)`,
            user_id: userId,
            tenant_id: tenantId || undefined,
            ip: requestContext.ip,
            status: 'SUCCESS',
            path: url,
            method,
            action: 'API_REQUEST',
            details: {
              userAgent: requestContext.userAgent,
              executionTime: duration,
              statusCode,
              ...(shouldAudit && {
                requestBody: this.sanitizeRequestBody(request.body),
                responseSize: this.getResponseSize(data),
              }),
            },
          });

          // 詳細稽核記錄（僅修改操作）- 發送事件
          if (shouldAudit) {
            this.eventEmitter.emit('audit.detailed.log', {
              logData: {
                action: this.mapMethodToAction(method, url),
                message: this.generateAuditMessage(method, url, true, duration),
                target_resource: this.extractResourceType(url),
                target_resource_id: this.extractResourceId(url, request.params),
                status: 'SUCCESS',
                details: {
                  request: {
                    method,
                    url,
                    body: this.sanitizeRequestBody(request.body),
                    query: request.query,
                    params: request.params,
                    headers: this.sanitizeHeaders(request.headers),
                  },
                  response: {
                    statusCode,
                    size: this.getResponseSize(data),
                    executionTime: duration,
                  },
                  metadata: {
                    timestamp: new Date().toISOString(),
                    sessionId: this.extractSessionId(request),
                    requestId: this.extractRequestId(request),
                  },
                },
              },
              context: {
                user,
                ip: requestContext.ip,
                userAgent: requestContext.userAgent,
                path: url,
                method,
              },
            });
          }
        },
      }),
      catchError(async (error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        const statusCode = error.status || 500;

        // 基本錯誤日誌記錄 - 發送事件
        this.eventEmitter.emit('audit.system.log', {
          message: `${method} ${url} - 失敗 (${duration}ms): ${error.message || '未知錯誤'}`,
          user_id: userId,
          tenant_id: tenantId || undefined,
          ip: requestContext.ip,
          status: 'ERROR',
          path: url,
          method,
          action: 'API_REQUEST',
          error_message: error.message,
          details: {
            userAgent: requestContext.userAgent,
            requestBody: this.sanitizeRequestBody(request.body),
            responseStatusCode: statusCode,
            executionTime: duration,
            errorStack: error.stack,
          },
        });

        // 詳細錯誤稽核記錄 - 發送事件
        if (shouldAudit) {
          this.eventEmitter.emit('audit.detailed.log', {
            logData: {
              action: this.mapMethodToAction(method, url),
              message: this.generateAuditMessage(method, url, false, duration, error),
              target_resource: this.extractResourceType(url),
              target_resource_id: this.extractResourceId(url, request.params),
              status: 'FAILURE',
              error_message: error.message,
              details: {
                request: {
                  method,
                  url,
                  body: this.sanitizeRequestBody(request.body),
                  query: request.query,
                  params: request.params,
                  headers: this.sanitizeHeaders(request.headers),
                },
                error: {
                  name: error.name,
                  message: error.message,
                  statusCode,
                  stack: this.sanitizeErrorStack(error.stack),
                },
                response: {
                  statusCode,
                  executionTime: duration,
                },
                metadata: {
                  timestamp: new Date().toISOString(),
                  sessionId: this.extractSessionId(request),
                  requestId: this.extractRequestId(request),
                },
              },
            },
            context: {
              user,
              ip: requestContext.ip,
              userAgent: requestContext.userAgent,
              path: url,
              method,
            },
          });
        }

        return throwError(() => error);
      }),
    );
  }

  /**
   * 提取請求上下文信息
   */
  private extractRequestContext(request: Request) {
    return {
      ip: this.getClientIp(request),
      userAgent: request.get('User-Agent') || 'Unknown',
      referer: request.get('Referer'),
      origin: request.get('Origin'),
      contentType: request.get('Content-Type'),
      contentLength: request.get('Content-Length'),
    };
  }

  /**
   * 判斷是否需要進行詳細稽核
   */
  private shouldAuditRequest(method: string, url: string): boolean {
    // 修改操作需要詳細稽核
    if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
      return true;
    }

    // 敏感讀取操作也需要稽核
    const sensitivePatterns = [
      '/auth/',
      '/admin/',
      '/users/',
      '/settings/',
      '/permissions/',
      '/audit/',
      '/export',
      '/download',
    ];

    return sensitivePatterns.some((pattern) => url.includes(pattern));
  }

  /**
   * 將HTTP方法和URL映射為稽核操作
   */
  private mapMethodToAction(method: string, url: string): string {
    const resource = this.extractResourceType(url);

    switch (method) {
      case 'POST':
        return `${resource}_CREATE`;
      case 'PUT':
      case 'PATCH':
        return `${resource}_UPDATE`;
      case 'DELETE':
        return `${resource}_DELETE`;
      case 'GET':
        return `${resource}_READ`;
      default:
        return `${resource}_${method}`;
    }
  }

  /**
   * 生成稽核消息
   */
  private generateAuditMessage(
    method: string,
    url: string,
    success: boolean,
    duration: number,
    error?: any,
  ): string {
    const operation = success ? '成功' : '失敗';
    const resource = this.extractResourceType(url);

    let message = `${method} ${resource} ${operation} (${duration}ms)`;

    if (!success && error) {
      message += ` - ${error.message}`;
    }

    return message;
  }

  /**
   * 從URL中提取資源類型
   */
  private extractResourceType(url: string): string {
    // 移除查詢參數
    const cleanUrl = url.split('?')[0];

    // 提取資源路徑
    const pathSegments = cleanUrl.split('/').filter((segment) => segment);

    if (pathSegments.length === 0) {
      return 'ROOT';
    }

    // 返回第一個路徑段作為資源類型
    return pathSegments[0].toUpperCase();
  }

  /**
   * 從URL和參數中提取資源ID
   */
  private extractResourceId(url: string, params: any): string | undefined {
    // 首先檢查路由參數中的常見ID字段
    const commonIdFields = ['id', 'userId', 'tenantId', 'itemId'];

    for (const field of commonIdFields) {
      if (params[field]) {
        return params[field];
      }
    }

    // 如果沒有找到，嘗試從URL路徑中提取最後一個數字或UUID
    const pathSegments = url.split('/').filter((segment) => segment && !segment.includes('?'));

    for (let i = pathSegments.length - 1; i >= 0; i--) {
      const segment = pathSegments[i];

      // 檢查是否為UUID格式
      if (
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(segment)
      ) {
        return segment;
      }

      // 檢查是否為數字ID
      if (/^\d+$/.test(segment)) {
        return segment;
      }
    }

    return undefined;
  }

  /**
   * 清理請求內容，移除敏感資訊
   */
  private sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sanitized = { ...body };
    const sensitiveFields = [
      'password',
      'secret',
      'token',
      'key',
      'auth',
      'credential',
      'apiKey',
      'accessToken',
      'refreshToken',
      'privateKey',
      'clientSecret',
      'sessionId',
      'cookieValue',
    ];

    const sanitizeRecursive = (obj: any): any => {
      if (!obj || typeof obj !== 'object') {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map((item) => sanitizeRecursive(item));
      }

      const result = { ...obj };

      for (const field of sensitiveFields) {
        if (field in result) {
          result[field] = '***已隱藏***';
        }
      }

      // 遞歸處理嵌套對象
      for (const [key, value] of Object.entries(result)) {
        if (typeof value === 'object' && value !== null) {
          result[key] = sanitizeRecursive(value);
        }
      }

      return result;
    };

    return sanitizeRecursive(sanitized);
  }

  /**
   * 清理請求頭，移除敏感信息
   */
  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    const sensitiveHeaders = [
      'authorization',
      'cookie',
      'x-api-key',
      'x-auth-token',
      'x-access-token',
      'x-session-id',
      'proxy-authorization',
    ];

    for (const header of sensitiveHeaders) {
      if (header in sanitized) {
        sanitized[header] = '***已隱藏***';
      }
    }

    return sanitized;
  }

  /**
   * 清理錯誤堆疊信息
   */
  private sanitizeErrorStack(stack?: string): string | undefined {
    if (!stack) {
      return undefined;
    }

    // 移除可能包含敏感信息的絕對路徑
    return stack.replace(/\/[^:\s]+/g, '/***');
  }

  /**
   * 獲取客戶端IP地址
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string) ||
      (request.headers['x-real-ip'] as string) ||
      request.ip ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      'unknown'
    );
  }

  /**
   * 獲取響應大小
   */
  private getResponseSize(data: any): number | undefined {
    if (!data) {
      return 0;
    }

    try {
      return JSON.stringify(data).length;
    } catch {
      return undefined;
    }
  }

  /**
   * 提取會話ID
   */
  private extractSessionId(request: Request): string | undefined {
    return (request.headers['x-session-id'] as string) || request.cookies?.sessionId || undefined;
  }

  /**
   * 提取請求ID
   */
  private extractRequestId(request: Request): string | undefined {
    return (
      (request.headers['x-request-id'] as string) ||
      (request.headers['x-correlation-id'] as string) ||
      undefined
    );
  }
}
