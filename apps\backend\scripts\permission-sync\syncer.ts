import { PrismaClient } from "@prisma/client";
import { PermissionDefinition, SyncResult } from "./types";
import { PERMISSION_CATEGORY_LABELS } from "../../src/common/constants/i18n.constants";
import { randomUUID } from "crypto";

/**
 * 權限同步器
 * 負責將掃描結果同步到資料庫
 */
export class PermissionSyncer {
  private prisma: PrismaClient;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
  }

  /**
   * 同步權限到資料庫
   */
  async sync(
    permissions: PermissionDefinition[],
    options: {
      dryRun?: boolean;
      force?: boolean;
    } = {}
  ): Promise<SyncResult> {
    const { dryRun = false, force = false } = options;
    const originalPrisma = this.prisma;

    // 非乾跑模式：使用事務包裹同步流程，以在發生錯誤時回滾
    if (!dryRun) {
      // 初始化結果統計
      const result: SyncResult = {
        created: 0,
        updated: 0,
        deprecated: 0,
        errors: 0,
        details: {
          createdPermissions: [],
          updatedPermissions: [],
          deprecatedPermissions: [],
          errorMessages: [],
        },
      };
      try {
        await originalPrisma.$transaction(async (tx) => {
          // 臨時切換到事務 Prisma 客戶端 (強制轉型以包含 PrismaClient 方法)
          this.prisma = tx as unknown as PrismaClient;
          // 1. 同步權限分類
          await this.syncPermissionCategories(permissions, false);
          // 2. 同步權限定義
          await this.syncPermissions(permissions, result, {
            dryRun: false,
            force,
          });
          // 3. 標記廢棄權限
          await this.deprecateUnusedPermissions(permissions, result, false);
        });
      } catch (error: any) {
        const errorMsg = `事務同步失敗並已回滾: ${error.message}`;
        console.error(`❌ ${errorMsg}`);
        result.errors++;
        result.details.errorMessages.push(errorMsg);
      } finally {
        // 還原 Prisma 客戶端
        this.prisma = originalPrisma;
      }
      return result;
    }

    await this.prisma.system_logs.create({
      data: {
        id: randomUUID(),
        level: "AUDIT",
        message: `permission-sync start`,
        action: "permission-sync",
        details: { dryRun, force, count: permissions.length },
      },
    });

    console.log(`\n📥 ${dryRun ? "預覽" : "執行"}權限同步...`);
    console.log(`  總共發現 ${permissions.length} 個權限定義`);

    if (force) {
      console.log("  ⚠️  強制覆蓋模式已啟用");
    }

    const result: SyncResult = {
      created: 0,
      updated: 0,
      deprecated: 0,
      errors: 0,
      details: {
        createdPermissions: [],
        updatedPermissions: [],
        deprecatedPermissions: [],
        errorMessages: [],
      },
    };

    try {
      // 1. 同步權限分類
      await this.syncPermissionCategories(permissions, dryRun);

      // 2. 同步權限
      await this.syncPermissions(permissions, result, { dryRun, force });

      // 3. 標記廢棄權限
      await this.deprecateUnusedPermissions(permissions, result, dryRun);
    } catch (error) {
      const errorMsg = `同步過程發生錯誤: ${error.message}`;
      console.error(`❌ ${errorMsg}`);
      result.errors++;
      result.details.errorMessages.push(errorMsg);
    }

    console.log(`\n✅ ${dryRun ? "預覽" : "同步"}完成！`);
    console.log(
      `  📊 統計: 新增 ${result.created} 個, 更新 ${result.updated} 個, 標記廢棄 ${result.deprecated} 個, 錯誤 ${result.errors} 個`
    );

    await this.prisma.system_logs.create({
      data: {
        id: randomUUID(),
        level: "AUDIT",
        message: `permission-sync finished`,
        action: "permission-sync",
        details: result as any,
      },
    });

    return result;
  }

  /**
   * 同步權限分類到資料庫
   */
  private async syncPermissionCategories(
    permissions: PermissionDefinition[],
    dryRun = false
  ): Promise<void> {
    console.log("  📝 同步權限分類...");

    // 收集所有分類
    const categories = new Set<string>();
    permissions.forEach((perm) => {
      if (perm.category) {
        categories.add(perm.category);
      }
    });

    const categoryDisplayNames = PERMISSION_CATEGORY_LABELS;
    let created = 0;
    let updated = 0;

    for (const categoryId of categories) {
      const displayName = categoryDisplayNames[categoryId] || categoryId;

      try {
        if (dryRun) {
          const existing = await this.prisma.permission_categories.findUnique({
            where: { name: categoryId },
          });

          if (!existing) {
            created++;
            console.log(
              `    [預覽] 會新增分類: ${categoryId} (${displayName})`
            );
          } else if (existing.description !== `${displayName} 相關權限`) {
            updated++;
            console.log(
              `    [預覽] 會更新分類: ${categoryId} 描述 ${existing.description} -> ${displayName} 相關權限`
            );
          }
        } else {
          const category = await this.prisma.permission_categories.upsert({
            where: { name: categoryId },
            update: {
              description: `${displayName} 相關權限`,
              updated_at: new Date(),
            },
            create: {
              id: randomUUID(),
              name: categoryId,
              description: `${displayName} 相關權限`,
              is_active: true,
              updated_at: new Date(),
            },
          });

          if (category.created_at.getTime() === category.updated_at.getTime()) {
            created++;
            console.log(`    ✅ 新增分類: ${categoryId} (${displayName})`);
          } else {
            updated++;
            console.log(`    📝 更新分類: ${categoryId} (${displayName})`);
          }
        }
      } catch (error) {
        console.error(
          `    ❌ 處理分類 ${categoryId} 時發生錯誤: ${error.message}`
        );
      }
    }

    // 標記未使用的分類為廢棄
    const unusedCategories = await this.prisma.permission_categories.findMany({
      where: {
        name: {
          notIn: Array.from(categories),
        },
        is_active: true,
      },
    });

    for (const unusedCategory of unusedCategories) {
      if (dryRun) {
        console.log(`    [預覽] 會標記分類為廢棄: ${unusedCategory.name}`);
      } else {
        await this.prisma.permission_categories.update({
          where: { id: unusedCategory.id },
          data: {
            is_active: false,
            updated_at: new Date(),
          },
        });
        console.log(`    🗑️  標記分類為廢棄: ${unusedCategory.name}`);
      }
    }

    if (!dryRun) {
      console.log(
        `    分類同步完成: 新增 ${created} 個, 更新 ${updated} 個, 標記廢棄 ${unusedCategories.length} 個`
      );
    } else {
      console.log(
        `    [預覽] 分類變更: 會新增 ${created} 個, 會更新 ${updated} 個, 會標記廢棄 ${unusedCategories.length} 個`
      );
    }
  }

  /**
   * 同步權限
   */
  private async syncPermissions(
    permissions: PermissionDefinition[],
    result: SyncResult,
    options: { dryRun: boolean; force: boolean }
  ): Promise<void> {
    console.log("  🔄 同步權限定義...");

    for (const permission of permissions) {
      try {
        await this.syncSinglePermission(permission, result, options);
      } catch (error) {
        const errorMsg = `同步權限 ${permission.action}:${permission.subject} 失敗: ${error.message}`;
        console.error(`    ❌ ${errorMsg}`);
        result.errors++;
        result.details.errorMessages.push(errorMsg);
      }
    }
  }

  /**
   * 同步單一權限
   */
  private async syncSinglePermission(
    permission: PermissionDefinition,
    result: SyncResult,
    options: { dryRun: boolean; force: boolean }
  ): Promise<void> {
    const { dryRun, force } = options;
    const permissionKey = `${permission.action}:${permission.subject}`;

    // 檢查權限是否已存在
    const existingPermission = await this.prisma.permissions.findFirst({
      where: {
        action: permission.action,
        subject: permission.subject,
      },
    });

    if (dryRun) {
      // 在乾跑模式下，也需要檢查是否真的需要更新
      if (existingPermission) {
        // 查找分類ID以進行比對
        const category = await this.prisma.permission_categories.findUnique({
          where: { name: permission.category || "other" },
        });

        const needsUpdate =
          force ||
          existingPermission.description !==
            (permission.description ||
              `${permission.action} ${permission.subject}`) ||
          existingPermission.scope !== (permission.scope || "GLOBAL") ||
          // existingPermission.name !== permission.name ||
          // existingPermission.zone !== permission.zone ||
          (category && existingPermission.category_id !== category.id);

        if (needsUpdate) {
          result.updated++;
          result.details.updatedPermissions.push(permissionKey);
          console.log(`    [預覽] 會更新權限: ${permissionKey}`);
        }
        // 如果不需要更新，就不做任何事情
      } else {
        result.created++;
        result.details.createdPermissions.push(permissionKey);
        console.log(`    [預覽] 會新增權限: ${permissionKey}`);
      }
      return;
    }

    // 實際執行模式 - 尋找對應的分類
    const category = await this.prisma.permission_categories.findUnique({
      where: { name: permission.category || "other" },
    });

    if (!category) {
      throw new Error(`找不到權限分類: ${permission.category}`);
    }

    if (existingPermission) {
      // 檢查是否需要更新
      const needsUpdate =
        force ||
        existingPermission.description !==
          (permission.description ||
            `${permission.action} ${permission.subject}`) ||
        existingPermission.scope !== (permission.scope || "GLOBAL") ||
        // existingPermission.name !== permission.name ||
        // existingPermission.zone !== permission.zone ||
        existingPermission.category_id !== category.id;

      if (needsUpdate) {
        await this.prisma.permissions.update({
          where: { id: existingPermission.id },
          data: {
            description:
              permission.description ||
              `${permission.action} ${permission.subject}`,
            scope: permission.scope || "GLOBAL",
            // name: permission.name,
            // zone: permission.zone,
            category_id: category.id,
            is_system_defined: permission.isSystemDefined ?? true,
          },
        });

        result.updated++;
        result.details.updatedPermissions.push(permissionKey);
        console.log(`    📝 更新權限: ${permissionKey}`);
      }
    } else {
      // 新增權限
      await this.prisma.permissions.create({
        data: {
          id: randomUUID(),
          action: permission.action,
          subject: permission.subject,
          // name: permission.name,
          description:
            permission.description ||
            `${permission.action} ${permission.subject}`,
          // zone: permission.zone,
          scope: permission.scope || "GLOBAL",
          category_id: category.id,
          is_system_defined: permission.isSystemDefined ?? true,
          updated_at: new Date(),
        },
      });

      result.created++;
      result.details.createdPermissions.push(permissionKey);
      console.log(`    ✅ 新增權限: ${permissionKey}`);
    }
  }

  /**
   * 標記未使用的權限為廢棄
   */
  private async deprecateUnusedPermissions(
    scannedPermissions: PermissionDefinition[],
    result: SyncResult,
    dryRun = false
  ): Promise<void> {
    console.log("  🗑️  檢查廢棄權限...");

    // 建立掃描權限的 Set 以快速查詢
    const scannedKeys = new Set(
      scannedPermissions.map((p) => `${p.action}:${p.subject}`)
    );

    // 找出資料庫中存在但掃描結果中沒有的權限
    const allDbPermissions = await this.prisma.permissions.findMany({
      where: {
        is_system_defined: true, // 只檢查系統定義的權限
        deprecated: false, // 只檢查尚未廢棄的權限
      },
    });

    for (const dbPermission of allDbPermissions) {
      const dbKey = `${dbPermission.action}:${dbPermission.subject}`;

      if (!scannedKeys.has(dbKey)) {
        // 這個權限在程式碼中已不存在，標記為廢棄
        if (dryRun) {
          result.deprecated++;
          result.details.deprecatedPermissions.push(dbKey);
          console.log(`    [預覽] 會標記權限為廢棄: ${dbKey}`);
        } else {
          await this.prisma.permissions.update({
            where: { id: dbPermission.id },
            data: {
              description: `已廢棄 - ${dbPermission.description}`,
              deprecated: true,
              updated_at: new Date(),
            },
          });

          result.deprecated++;
          result.details.deprecatedPermissions.push(dbKey);
          console.log(`    🗑️  標記權限為廢棄: ${dbKey}`);
        }
      }
    }

    if (result.deprecated > 0) {
      console.log(`    發現 ${result.deprecated} 個廢棄權限`);
    } else {
      console.log(`    沒有發現廢棄權限`);
    }
  }

  /**
   * 關閉資料庫連接
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
