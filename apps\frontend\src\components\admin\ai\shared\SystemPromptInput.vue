<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Maximize, Sparkles, Loader2 } from 'lucide-vue-next'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Label } from '@/components/ui/label'
import { httpService } from '@/services/http.service'
import { AiBotProviderType } from '@/types/models/ai.model'

const props = defineProps({
  modelValue: {
    type: String as () => string | null | undefined,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  rows: {
    type: Number,
    default: 5,
  },
  placeholder: {
    type: String,
    default: '例如：你是一位專業的 AI 客戶服務助理。你的目標是協助使用者解決關於我們產品的問題。請以友善且樂於助人的語氣回答。避免使用過於技術性的術語。'
  },
  notificationService: {
    type: Object,
    required: true,
  },
  scene: {
    type: String,
    default: ''
  },
  providerType: {
    type: String,
    default: ''
  },
  apiKey: {
    type: String,
    default: undefined
  },
  apiUrl: {
    type: String,
    default: undefined
  },
  botId: { 
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'blur-save'])

const internalSystemPrompt = computed({
  get: () => props.modelValue || '',
  set: (value) => emit('update:modelValue', value),
})

const handleBlur = () => {
  emit('blur-save')
}

const isMaximizeDialogVisible = ref(false)
const openMaximizeDialog = () => {
  isMaximizeDialogVisible.value = true
}

const isOptimizationPopoverOpen = ref(false)
const optimizationRequirementText = ref('')
const isProcessingOptimization = ref(false)

// 設定較長的超時時間
const OPTIMIZE_TIMEOUT = 60000 // 60 秒

const handleOptimizePrompt = async () => {
  if (!optimizationRequirementText.value.trim()) {
    props.notificationService.flash.warning('請輸入優化需求')
    return
  }
  
  if (!props.providerType) {
    props.notificationService.flash.error('請先選擇 AI 金鑰，才能使用提示詞優化功能')
    return
  }

  isProcessingOptimization.value = true
  try {
    let providerPayloadValue: string;
    switch (props.providerType.toUpperCase()) {
      case AiBotProviderType.OPENAI:
        providerPayloadValue = 'openai';
        break;
      case AiBotProviderType.CLAUDE:
        providerPayloadValue = 'claude';
        break;
      case AiBotProviderType.OPENAI_COMPATIBLE:
        providerPayloadValue = 'openai-compatible';
        break;
      case AiBotProviderType.GEMINI: 
        providerPayloadValue = 'gemini';
        break;
      default:
        props.notificationService.flash.error(`優化功能不支援的提供者類型: ${props.providerType}`);
        isProcessingOptimization.value = false;
        return;
    }

    const payload: Record<string, any> = {
      // 對於新建機器人，使用特殊標識或留空
      id: props.botId || 'new-bot-temp',
      provider: providerPayloadValue,
      apiKey: props.apiKey,
      prompt: props.modelValue || '', // 要優化的原始提示詞
      scene: props.scene,
      requirement: optimizationRequirementText.value // 優化需求
    }
    if (props.apiUrl) payload.apiUrl = props.apiUrl

    const response = await httpService.post('/admin/ai/bots/optimize-prompt', payload, {
      timeout: OPTIMIZE_TIMEOUT
    })
    
    if (response.data?.success && response.data?.response?.content) {
      const optimized = response.data.response.content
      emit('update:modelValue', optimized)
      props.notificationService.flash.success('提示詞已優化，已更新到系統提示詞欄位。')
      isOptimizationPopoverOpen.value = false
      optimizationRequirementText.value = ''
    } else {
      throw new Error('優化失敗，未收到有效的優化後提示詞。')
    }

  } catch (error: any) {
    console.error('優化提示詞失敗:', error);
    const errorMsg = error.response?.data?.message || error.message || '提示詞優化失敗';
    if (error.code === 'ECONNABORTED') {
      props.notificationService.flash.error('優化提示詞超時，請稍後再試。');
    } else {
      props.notificationService.flash.error(errorMsg);
    }
  } finally {
    isProcessingOptimization.value = false
  }
}

const handleEnterKeyOptimize = () => {
  if (!isProcessingOptimization.value && optimizationRequirementText.value.trim()) {
    handleOptimizePrompt();
  }
}

</script>

<template>
  <div class="relative">
    <div class="relative">
      <Textarea
        v-model="internalSystemPrompt"
        :rows="props.rows"
        :placeholder="props.placeholder"
        :disabled="props.disabled"
        class="w-full pr-12 resize-none"
        @blur="handleBlur"
      />
      <TooltipProvider :delay-duration="300">
        <div class="absolute top-2 right-3 z-10">
          <Popover v-model:open="isOptimizationPopoverOpen">
            <PopoverTrigger as-child>
              <button
                type="button"
                :disabled="false"
                class="p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:outline-none disabled:opacity-50"
                style="line-height:0"
                tabindex="0"
              >
                <Sparkles class="h-4 w-4 text-primary" />
              </button>
            </PopoverTrigger>
            <PopoverContent side="right" align="start" class="w-96 bg-slate-900/90 backdrop-blur-lg border border-purple-500/60 shadow-2xl shadow-purple-600/30 text-slate-200 rounded-xl p-6">
              <div class="grid gap-5">
                <div class="space-y-1.5 text-center">
                  <h4 class="font-semibold leading-none text-lg text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-500 tracking-wide">AI 提示詞增強</h4>
                  <p class="text-xs text-slate-400">
                    輸入您的優化指令，讓 AI 助手為您精煉系統提示詞。
                  </p>
                </div>
                <div class="grid gap-3">
                  
                  <Textarea
                    id="optimizationRequirement"
                    v-model="optimizationRequirementText"
                    placeholder="例如：使其更具同理心並能主動提供相關建議..."
                    :rows="4"
                    class="col-span-3 resize-none bg-slate-800/70 border-purple-600/50 placeholder-slate-500 focus:ring-purple-500 focus:border-purple-500 text-slate-100 rounded-lg text-sm p-3 shadow-inner focus:shadow-outline-purple transition-all duration-300"
                    @keydown.enter.prevent="handleEnterKeyOptimize"
                  />
                </div>
                <Button 
                  @click="handleOptimizePrompt" 
                  :disabled="isProcessingOptimization || !optimizationRequirementText.trim()"
                  class="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white font-semibold py-2.5 px-4 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 disabled:opacity-60 disabled:transform-none disabled:shadow-none focus:outline-none focus:ring-2 focus:ring-pink-400 focus:ring-offset-2 focus:ring-offset-slate-900"
                >
                  <Loader2 v-if="isProcessingOptimization" class="mr-2 h-5 w-5 animate-spin" />
                  <Sparkles v-else class="mr-2 h-5 w-5 text-yellow-300 group-hover:animate-pulse" />
                  {{ isProcessingOptimization ? '優化中...' : '優化提示詞' }}
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
        <div class="absolute bottom-2 right-3 z-10">
          <Tooltip>
            <TooltipTrigger as-child>
              <button
                type="button"
                @click="openMaximizeDialog"
                :disabled="false"
                class="p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:outline-none disabled:opacity-50"
                style="line-height:0"
                tabindex="0"
              >
                <Maximize class="h-4 w-4" />
              </button>
            </TooltipTrigger>
            <TooltipContent>
              <p>放大編輯</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </TooltipProvider>
    </div>

    <Dialog v-model:open="isMaximizeDialogVisible">
      <DialogContent class="sm:max-w-[60vw] min-h-[70vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>編輯系統提示詞</DialogTitle>
          <DialogDescription>
            在此處編輯您的 AI 助手系統提示詞。詳細的提示詞有助於 AI 更精準地理解您的需求。
          </DialogDescription>
        </DialogHeader>
        <div class="flex-grow py-4 flex flex-col">
          <Textarea
            v-model="internalSystemPrompt"
            class="w-full flex-grow resize-none text-base"
            :placeholder="props.placeholder"
            :disabled="props.disabled"
            @blur="handleBlur"
          />
        </div>
        <DialogFooter>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template> 