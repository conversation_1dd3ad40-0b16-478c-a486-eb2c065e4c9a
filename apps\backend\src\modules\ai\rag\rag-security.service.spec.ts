import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, ForbiddenException } from '@nestjs/common';
import { RAGSecurityService } from './rag-security.service';
import { PrismaService } from '../../../prisma/prisma.service';

describe('RAGSecurityService', () => {
  let service: RAGSecurityService;
  let prismaService: jest.Mocked<PrismaService>;

  const mockPrismaService = {
    system_logs: {
      create: jest.fn(),
    },
    vector_documents: {
      findFirst: jest.fn(),
    },
    workspaces: {
      findFirst: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RAGSecurityService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<RAGSecurityService>(RAGSecurityService);
    prismaService = module.get(PrismaService);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateDocumentAccess', () => {
    const validTenantId = 'tenant-123';
    const validWorkspaceId = 'workspace-456';
    const validDocumentId = 'doc-789';

    beforeEach(() => {
      mockPrismaService.vector_documents.findFirst.mockResolvedValue({
        id: validDocumentId,
        tenant_id: validTenantId,
        workspace_id: validWorkspaceId,
        content: 'Test document',
        metadata: {},
        embedding: [],
        created_at: new Date(),
        updated_at: new Date(),
      });
    });

    it('should validate access for correct tenant and workspace', async () => {
      await expect(
        service.validateDocumentAccess(validDocumentId, validTenantId, validWorkspaceId),
      ).resolves.not.toThrow();

      expect(mockPrismaService.vector_documents.findFirst).toHaveBeenCalledWith({
        where: {
          id: validDocumentId,
          tenant_id: validTenantId,
          workspace_id: validWorkspaceId,
        },
        select: { id: true },
      });
    });

    it('should validate access for correct tenant without workspace', async () => {
      await expect(
        service.validateDocumentAccess(validDocumentId, validTenantId),
      ).resolves.not.toThrow();

      expect(mockPrismaService.vector_documents.findFirst).toHaveBeenCalledWith({
        where: {
          id: validDocumentId,
          tenant_id: validTenantId,
        },
        select: { id: true },
      });
    });

    it('should throw ForbiddenException for wrong tenant', async () => {
      mockPrismaService.vector_documents.findFirst.mockResolvedValue(null);

      await expect(
        service.validateDocumentAccess(validDocumentId, 'wrong-tenant', validWorkspaceId),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for wrong workspace', async () => {
      mockPrismaService.vector_documents.findFirst.mockResolvedValue(null);

      await expect(
        service.validateDocumentAccess(validDocumentId, validTenantId, 'wrong-workspace'),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for non-existent document', async () => {
      mockPrismaService.vector_documents.findFirst.mockResolvedValue(null);

      await expect(
        service.validateDocumentAccess('non-existent-doc', validTenantId, validWorkspaceId),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('validateFileUpload', () => {
    const validTenantId = 'tenant-123';
    const validWorkspaceId = 'workspace-456';

    beforeEach(() => {
      mockPrismaService.workspaces.findFirst.mockResolvedValue({
        id: validWorkspaceId,
        tenant_id: validTenantId,
        name: 'Test Workspace',
        created_at: new Date(),
        updated_at: new Date(),
      });
    });

    it('should validate file upload for correct tenant and workspace', async () => {
      const fileName = 'test-document.pdf';
      const filePath = '/uploads/test-document.pdf';

      await expect(
        service.validateFileUpload(fileName, filePath, validTenantId, validWorkspaceId),
      ).resolves.not.toThrow();
    });

    it('should throw BadRequestException for path traversal attempt', async () => {
      const maliciousFileName = '../../../etc/passwd';
      const filePath = '/uploads/test.pdf';

      await expect(
        service.validateFileUpload(maliciousFileName, filePath, validTenantId, validWorkspaceId),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for file path with ../', async () => {
      const fileName = 'test.pdf';
      const maliciousPath = '/uploads/../config/secret.txt';

      await expect(
        service.validateFileUpload(fileName, maliciousPath, validTenantId, validWorkspaceId),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw ForbiddenException for invalid workspace', async () => {
      mockPrismaService.workspaces.findFirst.mockResolvedValue(null);

      await expect(
        service.validateFileUpload(
          'test.pdf',
          '/uploads/test.pdf',
          validTenantId,
          'invalid-workspace',
        ),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for workspace belonging to different tenant', async () => {
      mockPrismaService.workspaces.findFirst.mockResolvedValue(null);

      await expect(
        service.validateFileUpload(
          'test.pdf',
          '/uploads/test.pdf',
          'wrong-tenant',
          validWorkspaceId,
        ),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('validateSearchQuery', () => {
    const validTenantId = 'tenant-123';

    it('should accept normal search queries', () => {
      const validQueries = [
        'What is artificial intelligence?',
        'How to implement REST APIs',
        'Database design best practices',
        'Machine learning algorithms',
      ];

      validQueries.forEach((query) => {
        expect(() => service.validateSearchQuery(query, validTenantId)).not.toThrow();
      });
    });

    it('should throw BadRequestException for SQL injection attempts', () => {
      const maliciousQueries = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        'UNION SELECT * FROM sensitive_table',
        "admin'--",
      ];

      maliciousQueries.forEach((query) => {
        expect(() => service.validateSearchQuery(query, validTenantId)).toThrow(
          BadRequestException,
        );
      });
    });

    it('should throw BadRequestException for XSS attempts', () => {
      const xssQueries = [
        '<script>alert("xss")</script>',
        'javascript:alert(1)',
        '<img src=x onerror=alert(1)>',
        'onload=alert(1)',
      ];

      xssQueries.forEach((query) => {
        expect(() => service.validateSearchQuery(query, validTenantId)).toThrow(
          BadRequestException,
        );
      });
    });

    it('should throw BadRequestException for empty queries', () => {
      expect(() => service.validateSearchQuery('', validTenantId)).toThrow(BadRequestException);
      expect(() => service.validateSearchQuery('   ', validTenantId)).toThrow(BadRequestException);
    });

    it('should throw BadRequestException for overly long queries', () => {
      const longQuery = 'a'.repeat(1001); // Exceeds 1000 character limit
      expect(() => service.validateSearchQuery(longQuery, validTenantId)).toThrow(
        BadRequestException,
      );
    });
  });

  describe('validateBulkDocumentAccess', () => {
    const validTenantId = 'tenant-123';
    const validWorkspaceId = 'workspace-456';
    const documentIds = ['doc-1', 'doc-2', 'doc-3'];

    beforeEach(() => {
      mockPrismaService.vector_documents.findFirst.mockImplementation(({ where }) => {
        // Simulate that all documents belong to the valid tenant
        if (where.tenant_id === validTenantId) {
          return Promise.resolve({
            id: where.id,
            tenant_id: validTenantId,
            workspace_id: validWorkspaceId,
          });
        }
        return Promise.resolve(null);
      });
    });

    it('should validate access for all documents belonging to tenant', async () => {
      await expect(
        service.validateBulkDocumentAccess(documentIds, validTenantId, validWorkspaceId),
      ).resolves.not.toThrow();

      expect(mockPrismaService.vector_documents.findFirst).toHaveBeenCalledTimes(3);
    });

    it('should throw ForbiddenException if any document fails validation', async () => {
      mockPrismaService.vector_documents.findFirst.mockImplementation(({ where }) => {
        // First document fails validation
        if (where.id === 'doc-1') {
          return Promise.resolve(null);
        }
        return Promise.resolve({
          id: where.id,
          tenant_id: validTenantId,
          workspace_id: validWorkspaceId,
        });
      });

      await expect(
        service.validateBulkDocumentAccess(documentIds, validTenantId, validWorkspaceId),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('logSecurityEvent', () => {
    it('should log security events to system_logs', async () => {
      const eventType = 'suspicious_query';
      const tenantId = 'tenant-123';
      const metadata = { query: 'malicious query', ip: '***********' };

      mockPrismaService.system_logs.create.mockResolvedValue({
        id: 'log-123',
        event_type: eventType,
        context: JSON.stringify(metadata),
        created_at: new Date(),
      });

      await service.logSecurityEvent(eventType, tenantId, metadata);

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: {
          event_type: eventType,
          context: JSON.stringify({
            tenant_id: tenantId,
            ...metadata,
          }),
          level: 'warning',
          created_at: expect.any(Date),
        },
      });
    });

    it('should handle logging errors gracefully', async () => {
      mockPrismaService.system_logs.create.mockRejectedValue(new Error('Database error'));

      // Should not throw even if logging fails
      await expect(
        service.logSecurityEvent('access_violation', 'tenant-123', {}),
      ).resolves.not.toThrow();
    });
  });

  describe('private helper methods', () => {
    describe('containsPathTraversal', () => {
      it('should detect path traversal patterns', () => {
        const pathTraversalInputs = [
          '../config',
          '..\\config',
          '/etc/../passwd',
          'file\\..\\secret.txt',
        ];

        pathTraversalInputs.forEach((input) => {
          // We need to test the private method indirectly through validateFileUpload
          expect(() =>
            service.validateFileUpload(input, '/uploads/test.pdf', 'tenant-123', 'workspace-456'),
          ).toThrow(BadRequestException);
        });
      });

      it('should allow normal file paths', () => {
        const normalInputs = ['document.pdf', 'folder/document.txt', 'reports/annual-report.docx'];

        normalInputs.forEach((input) => {
          expect(() =>
            service.validateFileUpload(input, '/uploads/test.pdf', 'tenant-123', 'workspace-456'),
          ).not.toThrow();
        });
      });
    });

    describe('containsSuspiciousPatterns', () => {
      it('should detect suspicious patterns in queries', () => {
        const suspiciousInputs = ['DROP TABLE', '<script>', 'javascript:', 'SELECT * FROM'];

        suspiciousInputs.forEach((input) => {
          expect(() => service.validateSearchQuery(input, 'tenant-123')).toThrow(
            BadRequestException,
          );
        });
      });
    });
  });
});
