name: Permission Sync Check

on:
  pull_request:
    paths:
      - 'apps/backend/**'
      - 'packages/permissions/**'

jobs:
  check-permissions:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: pnpm/action-setup@v2
        with:
          version: 7
      - run: pnpm install
      - run: pnpm db:sync-perms --dry-run
      - run: |
          summary=apps/backend/reports/permission-sync-summary.json
          if [ -f "$summary" ]; then
            created=$(jq '.stats.sync.created' $summary)
            updated=$(jq '.stats.sync.updated' $summary)
            deprecated=$(jq '.stats.sync.deprecated' $summary)
            if [ "$created" != "0" ] || [ "$updated" != "0" ] || [ "$deprecated" != "0" ]; then
              echo "Permissions not in sync" && exit 1
            fi
          fi
