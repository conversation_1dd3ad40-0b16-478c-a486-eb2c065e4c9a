import { Modu<PERSON> } from '@nestjs/common';
import { SystemLogService } from '@/common/services/system-log.service';
import { AuditLogService } from '@/common/services/audit-log.service';
import { LogArchivingService } from '@/common/services/log-archiving.service';
import { AuditInterceptor } from '@/common/interceptors/audit.interceptor';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { EncryptionModule } from '@/modules/core/encryption/encryption.module';
import { StorageModule } from '@/modules/core/storage/storage.module';
import { SettingsModule } from '@/modules/admin/settings/settings.module';

@Module({
  imports: [PrismaModule, EncryptionModule, StorageModule, SettingsModule],
  providers: [SystemLogService, AuditLogService, LogArchivingService, AuditInterceptor],
  exports: [SystemLogService, AuditLogService, LogArchivingService, AuditInterceptor],
})
export class CommonModule {}
