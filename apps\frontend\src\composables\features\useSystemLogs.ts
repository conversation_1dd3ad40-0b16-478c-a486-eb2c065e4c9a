import { ref, reactive, computed, onMounted } from 'vue';
import { systemLogService } from '@/services/admin';
import type {
  SystemLog,
  SystemLogQuery,
  SystemLogStats,
  AvailableLogAction,
  SystemLogReportDto,
  PaginatedSystemLogs,
} from '@/types/models/system-log.model';
import { useToast } from '@/components/ui/toast/use-toast';
import { downloadFile } from '@/utils/download.util';

export function useSystemLogs() {
  const { toast } = useToast();

  const logs = ref<SystemLog[]>([]);
  const stats = ref<SystemLogStats | null>(null);
  const availableActions = ref<AvailableLogAction[]>([]);
  
  const pagination = reactive({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
  });

  const filters = reactive<SystemLogQuery>({
    search: '',
    user_id: '',
    target_resource: '',
    action_types: [],
    start_date: '',
    end_date: '',
  });

  const isLoading = ref(false);
  const isExporting = ref(false);

  /**
   * 獲取系統日誌列表
   */
  const fetchLogs = async () => {
    try {
      isLoading.value = true;
      
      const query: SystemLogQuery = {
        page: pagination.page,
        limit: pagination.limit,
        ...filters,
      };

      const response = await systemLogService.getLogs(query);
      
      logs.value = response.items || [];
      pagination.total = response.total || 0;
      pagination.totalPages = response.totalPages || 0;
    } catch (error) {
      console.error('Failed to fetch logs:', error);
      toast({
        title: '錯誤',
        description: '獲取系統日誌失敗',
        variant: 'destructive',
      });
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 獲取統計數據
   */
  const fetchStats = async () => {
    try {
      const response = await systemLogService.getStats();
      stats.value = response;
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  /**
   * 獲取可用操作類型
   */
  const fetchAvailableActions = async () => {
    try {
      const response = await systemLogService.getAvailableActions();
      availableActions.value = response || [];
    } catch (error) {
      console.error('Failed to fetch available actions:', error);
    }
  };

  /**
   * 生成報告
   */
  const generateReport = async (format: 'json' | 'csv' | 'xlsx') => {
    try {
      isExporting.value = true;

      const reportDto: SystemLogReportDto = {
        format,
        query: { ...filters },
      };

      const blob = await systemLogService.generateReport(reportDto);
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `system-logs-report-${timestamp}.${format}`;
      
      downloadFile(blob, filename);

      toast({
        title: '成功',
        description: `${format.toUpperCase()} 報告已下載`,
      });
    } catch (error) {
      console.error('Failed to generate report:', error);
      toast({
        title: '錯誤',
        description: '生成報告失敗',
        variant: 'destructive',
      });
    } finally {
      isExporting.value = false;
    }
  };

  /**
   * 重置篩選條件
   */
  const resetFilters = () => {
    Object.assign(filters, {
      search: '',
      user_id: '',
      target_resource: '',
      action_types: [],
      start_date: '',
      end_date: '',
    });
    pagination.page = 1;
    fetchLogs();
  };

  /**
   * 切換頁面
   */
  const changePage = (page: number) => {
    pagination.page = page;
    fetchLogs();
  };

  /**
   * 切換每頁數量
   */
  const changeLimit = (limit: number) => {
    pagination.limit = limit;
    pagination.page = 1;
    fetchLogs();
  };

  /**
   * 初始化數據
   */
  onMounted(async () => {
    await Promise.all([
      fetchLogs(),
      fetchStats(),
      fetchAvailableActions(),
    ]);
  });

  return {
    // 數據
    logs,
    stats,
    availableActions,
    pagination,
    filters,
    
    // 狀態
    isLoading,
    isExporting,
    
    // 方法
    fetchLogs,
    generateReport,
    resetFilters,
    changePage,
    changeLimit,
  };
} 