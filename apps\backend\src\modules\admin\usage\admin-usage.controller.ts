import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions } from '@horizai/permissions';
import { UsageStatisticsService } from '../../core/usage-tracking/services/usage-statistics.service';
import { TenantQuotaService } from '../../core/usage-tracking/services/tenant-quota.service';
import { AiUsageQueryDto, DetailedAiUsageStatisticsDto } from '../../core/usage-tracking/dto/usage.dto';
import { CurrentUser } from '../../core/auth/decorators/current-user.decorator';
import { JwtUser } from '../../../types/jwt-user.type';

@ApiTags('admin/ai/usage')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, PoliciesGuard)
@Controller('admin/ai/usage')
export class AdminUsageController {
  constructor(
    private readonly usageStatisticsService: UsageStatisticsService,
    private readonly tenantQuotaService: TenantQuotaService,
  ) {}

  @Get('statistics')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'AiUsageLog'))
  @ApiOperation({ summary: '系統管理員讀取 AI 使用統計' })
  @ApiResponse({ 
    status: 200, 
    description: '成功讀取 AI 使用統計',
    type: DetailedAiUsageStatisticsDto 
  })
  async getStatistics(@Query() query: AiUsageQueryDto): Promise<DetailedAiUsageStatisticsDto> {
    return this.usageStatisticsService.getDetailedStatistics(query);
  }

  @Get('credits/:tenantId')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'AiUsageLog'))
  @ApiOperation({ summary: '系統管理員讀取特定租戶信用點數餘額' })
  @ApiResponse({ 
    status: 200, 
    description: '成功讀取特定租戶信用點數餘額' 
  })
  async getTenantCredits(@Param('tenantId') tenantId: string) {
    return this.tenantQuotaService.getCurrentCredits(tenantId);
  }
} 