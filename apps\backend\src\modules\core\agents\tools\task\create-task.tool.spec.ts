import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { CreateTaskTool } from './create-task.tool';
import { TasksService } from '../../../workspace/tasks/tasks.service';
import { CreateTaskDto } from '../../../workspace/tasks/dto/create-task.dto';

describe('CreateTaskTool', () => {
  let tool: CreateTaskTool;
  let tasksService: jest.Mocked<TasksService>;

  const mockTasksService = {
    create: jest.fn(),
  };

  const mockTenantId = 'test-tenant-id';
  const mockUserId = 'test-user-id';
  const mockAbility = {
    can: jest.fn().mockReturnValue(true),
  } as any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: TasksService,
          useValue: mockTasksService,
        },
      ],
    }).compile();

    tasksService = module.get<TasksService>(TasksService) as jest.Mocked<TasksService>;
    tool = new CreateTaskTool(tasksService, mockTenantId, mockUserId, mockAbility);

    // 清除所有 mock 調用記錄
    jest.clearAllMocks();
  });

  describe('Tool Properties', () => {
    it('should have correct name', () => {
      expect(tool.name).toBe('CreateTaskTool');
    });

    it('should have comprehensive description', () => {
      expect(tool.description).toContain('建立新任務的工具');
      expect(tool.description).toContain('自然語言描述');
      expect(tool.description).toContain('JSON 格式');
    });
  });

  describe('JSON Input Parsing', () => {
    it('should parse valid JSON input correctly', async () => {
      const jsonInput = JSON.stringify({
        title: '修復登入錯誤',
        description: '用戶回報登入時出現 500 錯誤',
        projectId: 'test-project-id',
        priority: 'high',
      });

      const mockCreatedTask = {
        id: 'task-id',
        title: '修復登入錯誤',
        description: '用戶回報登入時出現 500 錯誤',
        project: { name: '測試專案' },
        status: 'todo',
        priority: 'high',
      };

      tasksService.create.mockResolvedValue(mockCreatedTask as any);

      const result = await tool._call(jsonInput);

      expect(tasksService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '修復登入錯誤',
          description: '用戶回報登入時出現 500 錯誤',
          projectId: 'test-project-id',
          priority: 'high',
          status: 'todo',
        }),
        mockUserId,
        mockTenantId,
      );

      expect(result).toContain('✅ 任務建立成功！');
      expect(result).toContain('修復登入錯誤');
    });

    it('should handle invalid JSON input gracefully', async () => {
      const invalidJson = '{ invalid json }';

      // 應該嘗試解析為自然語言
      const result = await tool._call(invalidJson);

      // 因為缺少 projectId，應該返回錯誤
      expect(result).toContain('❌ 錯誤：專案 ID 是必要的');
    });
  });

  describe('Natural Language Parsing', () => {
    beforeEach(() => {
      const mockCreatedTask = {
        id: 'task-id',
        title: '修復登入功能',
        project: { name: '測試專案' },
        status: 'todo',
        priority: 'medium',
      };
      tasksService.create.mockResolvedValue(mockCreatedTask as any);
    });

    it('should parse task title from natural language', async () => {
      const naturalInput = JSON.stringify({
        title: '修復登入功能',
        projectId: 'test-project-id',
      });

      await tool._call(naturalInput);

      expect(tasksService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '修復登入功能',
        }),
        mockUserId,
        mockTenantId,
      );
    });

    it('should extract priority from natural language', async () => {
      const naturalInput = JSON.stringify({
        title: '修復任務',
        projectId: 'test-project-id',
        priority: 'high',
      });

      await tool._call(naturalInput);

      expect(tasksService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          priority: 'high',
        }),
        mockUserId,
        mockTenantId,
      );
    });

    it('should extract estimated hours from natural language', async () => {
      const naturalInput = JSON.stringify({
        title: '測試任務',
        projectId: 'test-project-id',
        estimatedHours: 8,
      });

      await tool._call(naturalInput);

      expect(tasksService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          estimatedHours: 8,
        }),
        mockUserId,
        mockTenantId,
      );
    });

    it('should extract project information from natural language', async () => {
      const naturalInput = JSON.stringify({
        title: '測試任務',
        projectId: 'test-project-id',
        description: '[專案: ABC]',
      });

      await tool._call(naturalInput);

      expect(tasksService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          title: expect.stringContaining('測試任務'),
          description: expect.stringContaining('[專案: ABC]'),
        }),
        mockUserId,
        mockTenantId,
      );
    });

    it('should extract assignee information from natural language', async () => {
      const naturalInput = JSON.stringify({
        title: '測試任務',
        projectId: 'test-project-id',
        description: '[指派: 張三]',
      });

      await tool._call(naturalInput);

      expect(tasksService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          description: expect.stringContaining('[指派: 張三]'),
        }),
        mockUserId,
        mockTenantId,
      );
    });
  });

  describe('Validation', () => {
    it('should return error when title is missing', async () => {
      const input = JSON.stringify({
        description: '只有描述沒有標題',
        // 缺少 title
      });

      const result = await tool._call(input);

      expect(result).toContain('❌ 錯誤：任務標題是必要的');
      expect(tasksService.create).not.toHaveBeenCalled();
    });

    it('should return error when projectId is missing', async () => {
      const input = JSON.stringify({
        title: '有標題但沒有專案 ID',
        // 缺少 projectId
      });

      const result = await tool._call(input);

      expect(result).toContain('❌ 錯誤：專案 ID 是必要的');
      expect(tasksService.create).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle TasksService errors gracefully', async () => {
      const validInput = JSON.stringify({
        title: '測試任務',
        projectId: 'test-project-id',
      });

      const serviceError = new Error('Database connection failed');
      tasksService.create.mockRejectedValue(serviceError);

      const result = await tool._call(validInput);

      expect(result).toContain('❌ 建立任務時發生錯誤：Database connection failed');
    });

    it('should handle validation errors from TasksService', async () => {
      const validInput = JSON.stringify({
        title: '測試任務',
        projectId: 'invalid-project-id',
      });

      const validationError = new Error('專案不存在');
      tasksService.create.mockRejectedValue(validationError);

      const result = await tool._call(validInput);

      expect(result).toContain('❌ 建立任務時發生錯誤：專案不存在');
    });
  });

  describe('Response Formatting', () => {
    it('should format success response correctly', async () => {
      const input = JSON.stringify({
        title: '測試任務',
        projectId: 'test-project-id',
        description: '這是測試描述',
        priority: 'high',
        estimatedHours: 5,
      });

      const mockCreatedTask = {
        id: 'created-task-id',
        title: '測試任務',
        description: '這是測試描述',
        project: { name: '測試專案' },
        status: 'todo',
        priority: 'high',
        estimatedHours: 5,
        dueDate: '2024-12-31T00:00:00.000Z',
      };

      tasksService.create.mockResolvedValue(mockCreatedTask as any);

      const result = await tool._call(input);

      expect(result).toContain('✅ 任務建立成功！');
      expect(result).toContain('- 任務 ID: created-task-id');
      expect(result).toContain('- 標題: 測試任務');
      expect(result).toContain('- 描述: 這是測試描述');
      expect(result).toContain('- 專案: 測試專案');
      expect(result).toContain('- 狀態: 待處理');
      expect(result).toContain('- 優先級: 高');
      expect(result).toContain('- 預估工時: 5 小時');
      expect(result).toContain('- 截止日期: 2024/12/31');
    });

    it('should handle minimal response data', async () => {
      const input = JSON.stringify({
        title: '簡單任務',
        projectId: 'test-project-id',
      });

      const mockCreatedTask = {
        id: 'simple-task-id',
        title: '簡單任務',
        status: 'todo',
        priority: 'medium',
      };

      tasksService.create.mockResolvedValue(mockCreatedTask as any);

      const result = await tool._call(input);

      expect(result).toContain('✅ 任務建立成功！');
      expect(result).toContain('- 任務 ID: simple-task-id');
      expect(result).toContain('- 標題: 簡單任務');
      expect(result).toContain('- 專案: 未知專案'); // 當沒有 project.name 時的處理
    });
  });

  describe('Date Parsing', () => {
    it('should parse ISO date format', async () => {
      const naturalInput = JSON.stringify({
        title: '測試任務',
        projectId: 'test-project-id',
        dueDate: '2024-12-31T00:00:00.000Z',
      });

      await tool._call(naturalInput);

      expect(tasksService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          dueDate: expect.stringContaining('2024-12-31'),
        }),
        mockUserId,
        mockTenantId,
      );
    });

    it('should handle invalid date formats gracefully', async () => {
      const naturalInput = JSON.stringify({
        title: '測試任務',
        projectId: 'test-project-id',
        dueDate: '無效日期',
      });

      // 應該不會因為無效日期而失敗，但會建立任務
      const result = await tool._call(naturalInput);

      // 驗證任務被建立了
      expect(tasksService.create).toHaveBeenCalled();
      expect(result).toContain('✅ 任務建立成功！');
    });
  });
});
