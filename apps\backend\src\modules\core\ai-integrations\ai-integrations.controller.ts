import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  UseGuards,
  Logger,
  BadRequestException,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { AiIntegrationsService } from './ai-integrations.service';
import {
  ProjectAnalysisDto,
  PhotoAnalysisDto,
  WorkflowOptimizationDto,
} from './dto/ai-integrations.dto';
import { JwtAuthGuard } from '../auth/guards/auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import {
  ProjectAnalysisRequest,
  PhotoAnalysisRequest,
  WorkflowOptimizationRequest,
} from './types/business.types';

// 定義 User 類型
interface User {
  id: string;
  email: string;
  name?: string;
  tenant_id?: string;
}

@ApiTags('admin/ai-integrations')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('admin/ai/integrations')
export class AiIntegrationsController {
  private readonly logger = new Logger(AiIntegrationsController.name);

  constructor(private readonly aiIntegrationsService: AiIntegrationsService) {}

  @Post('analyze-project')
  @HttpCode(200)
  @ApiOperation({ summary: '分析項目狀態、風險和性能' })
  async analyzeProject(@Body() dto: ProjectAnalysisDto, @CurrentUser() user: User) {
    this.logger.log(`用戶 ${user.id} 請求分析項目 ${dto.projectId}, 分析類型: ${dto.analysisType}`);

    try {
      // 驗證請求參數
      if (!dto.projectId || !dto.tenantId) {
        throw new BadRequestException('項目 ID 和租戶 ID 為必填項');
      }

      // 驗證分析類型
      const validAnalysisTypes = ['status', 'risk', 'performance', 'optimization'];
      if (!validAnalysisTypes.includes(dto.analysisType)) {
        throw new BadRequestException(
          `無效的分析類型。支援的類型：${validAnalysisTypes.join(', ')}`,
        );
      }
      const request: ProjectAnalysisRequest = {
        project_id: dto.projectId,
        tenant_id: dto.tenantId,
        analysis_type: dto.analysisType,
        include_sub_projects: dto.includeSubProjects,
        include_tasks: dto.includeTasks,
        include_progress: dto.includeProgress,
        confidence: dto.confidence,
      };

      const result = await this.aiIntegrationsService.analyzeProject(request);

      this.logger.log(`項目 ${dto.projectId} 分析完成，信心度: ${result.confidence}`);

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`項目分析失敗: ${error.message}`, error.stack);

      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: '項目分析失敗',
          error: error.message,
          timestamp: new Date().toISOString(),
          details: JSON.stringify(dto, null, 2),
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('analyze-photo')
  @HttpCode(200)
  @ApiOperation({ summary: '分析單張照片，提取工程相關資訊' })
  async analyzePhoto(@Body() dto: PhotoAnalysisDto, @CurrentUser() user: User) {
    this.logger.log(
      `用戶 ${user.id} 請求分析項目 ${dto.projectId} 的照片，分析類型: ${dto.analysisType}`,
    );

    try {
      // 驗證請求參數
      if (!dto.photoUrl || !dto.projectId || !dto.tenantId) {
        throw new BadRequestException('照片 URL、項目 ID 和租戶 ID 為必填項');
      }

      // 驗證分析類型
      const validAnalysisTypes = ['progress', 'quality', 'safety', 'equipment'];
      if (!validAnalysisTypes.includes(dto.analysisType)) {
        throw new BadRequestException(
          `無效的分析類型。支援的類型：${validAnalysisTypes.join(', ')}`,
        );
      }

      // 驗證照片 URL 格式
      try {
        new URL(dto.photoUrl);
      } catch {
        throw new BadRequestException('無效的照片 URL 格式');
      }

      // 轉換 DTO 為服務請求格式
      const request: PhotoAnalysisRequest = {
        photo_url: dto.photoUrl,
        project_id: dto.projectId,
        tenant_id: dto.tenantId,
        analysis_type: dto.analysisType,
        context: dto.context,
        confidence: dto.confidence,
      };

      const result = await this.aiIntegrationsService.analyzePhoto(request);

      this.logger.log(`項目 ${dto.projectId} 照片分析完成，信心度: ${result.confidence}`);

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`照片分析失敗: ${error.message}`, error.stack);

      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: '照片分析失敗',
          error: error.message,
          timestamp: new Date().toISOString(),
          details: JSON.stringify(dto, null, 2),
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('optimize-workflow')
  @HttpCode(200)
  @ApiOperation({ summary: '分析和優化工作流程' })
  async optimizeWorkflow(@Body() dto: WorkflowOptimizationDto, @CurrentUser() user: User) {
    this.logger.log(`用戶 ${user.id} 請求優化租戶 ${dto.tenantId} 的工作流程`);

    try {
      // 驗證請求參數
      if (!dto.tenantId || !dto.startDate || !dto.endDate) {
        throw new BadRequestException('租戶 ID、開始日期和結束日期為必填項');
      }

      // 驗證時間範圍
      const startDate = new Date(dto.startDate);
      const endDate = new Date(dto.endDate);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new BadRequestException('無效的日期格式');
      }

      if (startDate >= endDate) {
        throw new BadRequestException('開始日期必須早於結束日期');
      }

      // 檢查指標是否有效
      if (dto.includeMetrics) {
        const validMetrics = [
          'task_completion_rate',
          'time_spent',
          'cost_analysis',
          'resource_allocation',
        ];
        for (const metric of dto.includeMetrics) {
          if (!validMetrics.includes(metric)) {
            throw new BadRequestException(`無效的指標: ${metric}`);
          }
        }
      }

      const request: WorkflowOptimizationRequest = {
        tenant_id: dto.tenantId,
        project_id: dto.projectId,
        time_range: {
          start_date: startDate,
          end_date: endDate,
        },
        include_metrics: dto.includeMetrics || [],
        confidence: dto.confidence,
      };

      const result = await this.aiIntegrationsService.optimizeWorkflow(request);

      this.logger.log(`租戶 ${dto.tenantId} 工作流程優化完成，信心度: ${result.confidence}`);

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`工作流程優化失敗: ${error.message}`, error.stack);

      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: '工作流程優化失敗',
          error: error.message,
          timestamp: new Date().toISOString(),
          details: JSON.stringify(dto, null, 2),
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
