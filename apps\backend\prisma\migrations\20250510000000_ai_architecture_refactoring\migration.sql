-- 建立新枚舉類型
CREATE TYPE "AiBotProviderType" AS ENUM ('O<PERSON><PERSON><PERSON><PERSON>', 'CLAUDE', 'GEMINI', 'O<PERSON><PERSON><PERSON>I_COMPATIBLE');
CREATE TYPE "AiBotResponseFormat" AS ENUM ('TEXT', 'JSON_OBJECT');

-- 建立新表
CREATE TABLE "ai_feature_configs" (
  "id" TEXT NOT NULL,
  "feature_key" TEXT NOT NULL,
  "is_enabled" BOOLEAN NOT NULL DEFAULT false,
  "bot_id" TEXT,
  "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "ai_feature_configs_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "ai_global_settings" (
  "id" TEXT NOT NULL,
  "is_ai_globally_enabled" BOOLEAN NOT NULL DEFAULT true,
  "global_monthly_quota_tokens" BIGINT,
  "global_monthly_quota_calls" INTEGER,
  "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "ai_global_settings_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "ai_usage_logs" (
  "id" TEXT NOT NULL,
  "user_id" TEXT,
  "tenant_id" TEXT,
  "bot_id" TEXT NOT NULL,
  "feature_key" TEXT,
  "api_key_id" TEXT NOT NULL,
  "provider" TEXT NOT NULL,
  "model_name" TEXT NOT NULL,
  "input_tokens" INTEGER NOT NULL,
  "output_tokens" INTEGER NOT NULL,
  "call_count" INTEGER NOT NULL DEFAULT 1,
  "estimated_cost" DECIMAL(65,30) NOT NULL,
  "request_timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "response_timestamp" TIMESTAMP(3),
  "is_success" BOOLEAN NOT NULL,
  "error_message" TEXT,
  "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

  CONSTRAINT "ai_usage_logs_pkey" PRIMARY KEY ("id")
);

-- 修改 AiModel 表
ALTER TABLE "ai_models" DROP COLUMN "enabled";
ALTER TABLE "ai_models" DROP COLUMN "displayName";
ALTER TABLE "ai_models" DROP COLUMN "modelName";
ALTER TABLE "ai_models" DROP COLUMN "createdAt";
ALTER TABLE "ai_models" DROP COLUMN "updatedAt";

ALTER TABLE "ai_models" ADD COLUMN "model_name" TEXT NOT NULL;
ALTER TABLE "ai_models" ADD COLUMN "display_name" TEXT NOT NULL;
ALTER TABLE "ai_models" ADD COLUMN "is_enabled" BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "ai_models" ADD COLUMN "input_price_per_1k_tokens" DECIMAL(65,30) NOT NULL DEFAULT 0;
ALTER TABLE "ai_models" ADD COLUMN "output_price_per_1k_tokens" DECIMAL(65,30) NOT NULL DEFAULT 0;
ALTER TABLE "ai_models" ADD COLUMN "currency" TEXT NOT NULL DEFAULT 'USD';
ALTER TABLE "ai_models" ADD COLUMN "price_last_updated_at" TIMESTAMP(3) NOT NULL;
ALTER TABLE "ai_models" ADD COLUMN "context_window_tokens" INTEGER;
ALTER TABLE "ai_models" ADD COLUMN "notes" TEXT;
ALTER TABLE "ai_models" ADD COLUMN "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "ai_models" ADD COLUMN "updated_at" TIMESTAMP(3) NOT NULL;

-- 修改 AiKey 表
ALTER TABLE "ai_keys" DROP COLUMN "description";
ALTER TABLE "ai_keys" DROP COLUMN "apiKey";
ALTER TABLE "ai_keys" DROP COLUMN "apiUrl";
ALTER TABLE "ai_keys" DROP COLUMN "models";
ALTER TABLE "ai_keys" DROP COLUMN "lastTest";
ALTER TABLE "ai_keys" DROP COLUMN "isEnabled";
ALTER TABLE "ai_keys" DROP COLUMN "createdAt";
ALTER TABLE "ai_keys" DROP COLUMN "updatedAt";

ALTER TABLE "ai_keys" ADD COLUMN "api_key" TEXT NOT NULL;
ALTER TABLE "ai_keys" ADD COLUMN "api_url" TEXT;
ALTER TABLE "ai_keys" ADD COLUMN "is_enabled" BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "ai_keys" ADD COLUMN "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "ai_keys" ADD COLUMN "updated_at" TIMESTAMP(3) NOT NULL;
ALTER TABLE "ai_keys" ADD COLUMN "last_test" TIMESTAMP(3);

-- 備份並清空現有的 AiBot 表，因為結構變化較大
CREATE TABLE "ai_bots_backup" AS SELECT * FROM "ai_bots";

-- 刪除外鍵約束
ALTER TABLE "ai_features" DROP CONSTRAINT IF EXISTS "ai_features_botId_fkey";

-- 清空 ai_bots 表
TRUNCATE TABLE "ai_bots" CASCADE;

-- 修改 AiBot 表
ALTER TABLE "ai_bots" DROP COLUMN "model";
ALTER TABLE "ai_bots" DROP COLUMN "prompt";
ALTER TABLE "ai_bots" DROP COLUMN "systemPrompt";
ALTER TABLE "ai_bots" DROP COLUMN "apiKey";
ALTER TABLE "ai_bots" DROP COLUMN "apiUrl";
ALTER TABLE "ai_bots" DROP COLUMN "provider";
ALTER TABLE "ai_bots" DROP COLUMN "isEnabled";
ALTER TABLE "ai_bots" DROP COLUMN "isTemplate";
ALTER TABLE "ai_bots" DROP COLUMN "createdAt";
ALTER TABLE "ai_bots" DROP COLUMN "updatedAt";
ALTER TABLE "ai_bots" DROP COLUMN "createdBy";
ALTER TABLE "ai_bots" DROP COLUMN "updatedBy";
ALTER TABLE "ai_bots" DROP COLUMN "tenantId";
ALTER TABLE "ai_bots" DROP COLUMN "responseFormat";

ALTER TABLE "ai_bots" ADD COLUMN "provider_type" "AiBotProviderType" NOT NULL;
ALTER TABLE "ai_bots" ADD COLUMN "model_id" TEXT NOT NULL;
ALTER TABLE "ai_bots" ADD COLUMN "key_id" TEXT NOT NULL;
ALTER TABLE "ai_bots" ADD COLUMN "provider_config_override" JSONB;
ALTER TABLE "ai_bots" ADD COLUMN "system_prompt" TEXT;
ALTER TABLE "ai_bots" ADD COLUMN "max_tokens" INTEGER;
ALTER TABLE "ai_bots" ADD COLUMN "response_format" "AiBotResponseFormat" NOT NULL DEFAULT 'TEXT';
ALTER TABLE "ai_bots" ADD COLUMN "is_enabled" BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "ai_bots" ADD COLUMN "is_template" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "ai_bots" ADD COLUMN "tenant_id" TEXT;
ALTER TABLE "ai_bots" ADD COLUMN "workspace_id" TEXT;
ALTER TABLE "ai_bots" ADD COLUMN "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "ai_bots" ADD COLUMN "updated_at" TIMESTAMP(3) NOT NULL;
ALTER TABLE "ai_bots" ADD COLUMN "created_by" TEXT NOT NULL;
ALTER TABLE "ai_bots" ADD COLUMN "updated_by" TEXT;

-- 建立外鍵約束
ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "ai_models"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_key_id_fkey" FOREIGN KEY ("key_id") REFERENCES "ai_keys"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- 建立索引
CREATE UNIQUE INDEX "ai_feature_configs_feature_key_key" ON "ai_feature_configs"("feature_key");
CREATE INDEX "ai_bots_scope_tenant_id_workspace_id_idx" ON "ai_bots"("scope", "tenant_id", "workspace_id");
CREATE INDEX "ai_usage_logs_user_id_idx" ON "ai_usage_logs"("user_id");
CREATE INDEX "ai_usage_logs_tenant_id_idx" ON "ai_usage_logs"("tenant_id");
CREATE INDEX "ai_usage_logs_bot_id_idx" ON "ai_usage_logs"("bot_id");
CREATE INDEX "ai_usage_logs_feature_key_idx" ON "ai_usage_logs"("feature_key");
CREATE INDEX "ai_usage_logs_api_key_id_idx" ON "ai_usage_logs"("api_key_id");
CREATE INDEX "ai_usage_logs_request_timestamp_idx" ON "ai_usage_logs"("request_timestamp");

-- 建立 feature_configs 表的外鍵
ALTER TABLE "ai_feature_configs" ADD CONSTRAINT "ai_feature_configs_bot_id_fkey" FOREIGN KEY ("bot_id") REFERENCES "ai_bots"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- 建立 usage_logs 表的外鍵
ALTER TABLE "ai_usage_logs" ADD CONSTRAINT "ai_usage_logs_bot_id_fkey" FOREIGN KEY ("bot_id") REFERENCES "ai_bots"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "ai_usage_logs" ADD CONSTRAINT "ai_usage_logs_api_key_id_fkey" FOREIGN KEY ("api_key_id") REFERENCES "ai_keys"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- 處理 AIFeature 表（轉換為 AiFeatureConfig）
-- 我們會在代碼中處理資料遷移
-- DROP TABLE "ai_features"; 