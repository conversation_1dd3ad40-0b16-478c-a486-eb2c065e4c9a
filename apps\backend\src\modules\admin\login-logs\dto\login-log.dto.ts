import { ApiProperty } from '@nestjs/swagger';

export class LoginLogDto {
  @ApiProperty({ description: '登入紀錄 ID' })
  id: string;

  @ApiProperty({ description: '使用者 ID', required: false })
  user_id: string | null;

  @ApiProperty({ description: 'IP 位址', required: false })
  ip_address: string | null;

  @ApiProperty({ description: 'User Agent', required: false })
  user_agent: string | null;

  @ApiProperty({ description: '登入時間' })
  login_at: Date;

  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '失敗原因', required: false })
  fail_reason?: string | null;

  @ApiProperty({ description: '使用者資訊', required: false })
  user?: {
    id: string;
    email: string;
    name?: string | null;
  } | null;
}
