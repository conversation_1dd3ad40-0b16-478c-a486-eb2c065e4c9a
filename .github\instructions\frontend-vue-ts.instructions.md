---
applyTo: "apps/frontend/src/**/*.{ts,vue}"
---

# 前端開發指南 (Vue 3 & TypeScript)

本指南適用於 `apps/frontend/src/` 目錄下的所有 Vue 3 和 TypeScript 程式碼。

**核心原則**:

- 遵循 [通用編碼標準](./general-coding.instructions.md)。
- 以使用者體驗為中心，注重效能和可訪問性。

## Vue 3

- **`<script setup>`**: 新元件一律使用 `<script setup>` 語法。
- **Composition API**:
  - 邏輯應按功能組織在 `composables/` 目錄下，或在元件內部按功能分組。
  - 優先使用 `ref` 和 `reactive` 進行響應式狀態管理。
  - `computed` 用於衍生狀態。
  - `watch` 和 `watchEffect` 用於監聽變化並執行副作用，謹慎使用以避免效能問題。
- **元件 (Components)**:
  - 元件命名使用 `PascalCase` (例如 `UserProfileCard.vue`)。
  - Props:
    - 使用 `defineProps` 明確定義型別。
    - 必要時提供 `default` 值。
    - 複雜型別應從 `types/` 或 `models/` 匯入。
  - Emits:
    - 使用 `defineEmits` 明確定義事件及其參數型別。
    - 事件命名使用 `kebab-case` (例如 `update:modelValue`)。
  - Slots: 善用插槽提高元件的靈活性和可複用性。
  - UI 元件: 優先使用 `apps/frontend/src/components/ui/` 中的 Shadcn/UI 元件 (基於 `components.json`)。自訂元件應保持風格一致。
- **路由 (Router)**:
  - 路由設定位於 `apps/frontend/src/router/index.ts`。
  - 使用命名路由。
  - 路由守衛 (Navigation Guards) 用於權限控制和頁面載入邏輯，參考 `apps/frontend/src/router/guards.ts` (如果存在) 或現有模式。
- **狀態管理 (Pinia)**:
  - Store 檔案應放置於 `apps/frontend/src/stores/`，每個 store 一個檔案 (例如 `user.store.ts`)。
  - State 應透過 `actions` 修改。
  - `getters` 用於計算衍生狀態。
  - Actions 可以是非同步的，用於處理 API 呼叫等。

## TypeScript

- 型別定義應放置於 `apps/frontend/src/types/` (通用型別) 或 `apps/frontend/src/models/` (資料模型)。
- 與後端共用的型別，優先從 `packages/@auth/shared/types.ts` 或其他共用套件匯入。
- 善用 TypeScript 的 Utility Types (例如 `Partial`, `Required`, `Pick`, `Omit`)。

## API 呼叫

- API 服務邏輯應封裝在 `apps/frontend/src/services/` 目錄下 (例如 `userService.ts`)。
- 使用 `axios` 或 `fetch` (根據專案配置) 進行 HTTP 請求。
- 統一處理 API 錯誤 (例如，顯示通知、記錄錯誤)。
- 考慮實作請求攔截器和回應攔截器來處理通用邏輯 (例如，加入 token、處理全域錯誤)。

## Tailwind CSS

- 優先使用 Tailwind CSS 的 utility classes。
- 避免在 `<style scoped>` 中撰寫大量自訂 CSS，除非是無法用 utility classes 實現的複雜樣式。
- 參考 `tailwind.config.js` 中的主題設定 (顏色、字型、間距等)。
- 保持 class 的順序一致性，可考慮使用 Prettier Plugin for Tailwind CSS。

## 專案結構參考

- `assets/`: 靜態資源 (圖片、字型等)。
- `components/`: 通用 Vue 元件。
  - `ui/`: Shadcn/UI 或類似的基礎 UI 元件。
- `composables/`: 可組合的 Vue 函式。
- `config/`: 專案設定 (例如 API 基本路徑)。
- `constants/`: 應用程式常數。
- `lib/`: 第三方函式庫的封裝或設定。
- `models/`: 資料模型定義 (通常是介面或類別)。
- `router/`: Vue Router 設定。
- `services/`: API 呼叫服務。
- `stores/`: Pinia 狀態管理。
- `types/`: TypeScript 型別定義。
- `utils/`: 通用工具函式。
- `views/`: 頁面級 Vue 元件。
