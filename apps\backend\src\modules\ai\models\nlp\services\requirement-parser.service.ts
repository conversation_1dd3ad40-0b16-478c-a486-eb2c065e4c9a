import { Injectable, Logger } from '@nestjs/common';
import { NLPProcessorService } from './nlp-processor.service';
import {
  RequirementDocument,
  ParsedRequirement,
  RequirementParsingResult,
  RequirementParsingOptions,
  RequirementType,
  Priority,
  RequirementCategory,
  RequirementConstraint,
  AcceptanceCriterion,
  ConstraintType,
  AcceptanceCriterionType,
  EntityLabel,
  ParsingError,
  ParsingWarning,
  ParsingStatistics
} from '../types/requirement-parsing.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * 需求解析服務
 * 負責將自然語言文本解析為結構化的需求信息
 */
@Injectable()
export class RequirementParserService {
  private readonly logger = new Logger(RequirementParserService.name);

  constructor(private readonly nlpProcessor: NLPProcessorService) {}

  /**
   * 解析需求文檔
   */
  async parseRequirements(
    document: RequirementDocument,
    options: RequirementParsingOptions = {}
  ): Promise<RequirementParsingResult> {
    const startTime = Date.now();
    const errors: ParsingError[] = [];
    const warnings: ParsingWarning[] = [];

    try {
      this.logger.log(`Parsing requirements from document: ${document.title}`);

      // 使用 NLP 處理器分析文本
      const nlpResult = await this.nlpProcessor.processText(document.content, options);

      // 提取需求
      const requirements = this.extractRequirements(document, nlpResult, options);

      // 後處理和驗證
      const validatedRequirements = this.validateAndEnhanceRequirements(requirements, warnings);

      // 計算統計信息
      const statistics: ParsingStatistics = {
        totalSentences: nlpResult.sentences.length,
        totalTokens: nlpResult.tokens.length,
        requirementsFound: validatedRequirements.length,
        averageConfidence: this.calculateAverageConfidence(validatedRequirements),
        processingTime: Date.now() - startTime,
        entitiesExtracted: nlpResult.entities.length
      };

      this.logger.log(`Parsing completed: ${validatedRequirements.length} requirements found`);

      return {
        requirements: validatedRequirements,
        statistics,
        errors,
        warnings
      };

    } catch (error) {
      this.logger.error('Error parsing requirements:', error);
      errors.push({
        type: 'PARSING_ERROR',
        message: `Failed to parse requirements: ${error.message}`,
        severity: 'error'
      });

      return {
        requirements: [],
        statistics: {
          totalSentences: 0,
          totalTokens: 0,
          requirementsFound: 0,
          averageConfidence: 0,
          processingTime: Date.now() - startTime,
          entitiesExtracted: 0
        },
        errors,
        warnings
      };
    }
  }

  /**
   * 從 NLP 結果中提取需求
   */
  private extractRequirements(
    document: RequirementDocument,
    nlpResult: any,
    options: RequirementParsingOptions
  ): ParsedRequirement[] {
    const requirements: ParsedRequirement[] = [];

    // 分析每個句子以識別潛在的需求
    nlpResult.sentences.forEach((sentence: any, index: number) => {
      const requirement = this.analyzeSentenceForRequirement(
        sentence,
        nlpResult,
        document,
        index
      );

      if (requirement && requirement.confidence >= (options.confidenceThreshold || 0.5)) {
        requirements.push(requirement);
      }
    });

    // 限制結果數量
    const maxRequirements = options.maxRequirements || 100;
    return requirements
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, maxRequirements);
  }

  /**
   * 分析句子以識別需求
   */
  private analyzeSentenceForRequirement(
    sentence: any,
    nlpResult: any,
    document: RequirementDocument,
    index: number
  ): ParsedRequirement | null {
    const text = sentence.text.trim();
    
    // 跳過太短的句子
    if (text.length < 10) return null;

    // 檢查是否包含需求關鍵詞
    const requirementIndicators = this.identifyRequirementIndicators(text);
    if (requirementIndicators.length === 0) return null;

    // 確定需求類型
    const type = this.determineRequirementType(text, sentence.tokens);

    // 確定優先級
    const priority = this.determinePriority(text);

    // 確定類別
    const category = this.determineCategory(text, type);

    // 提取相關實體
    const entities = nlpResult.entities.filter((entity: any) =>
      entity.start >= sentence.start && entity.end <= sentence.end
    );

    // 提取約束條件
    const constraints = this.extractConstraints(text, entities);

    // 提取驗收標準
    const acceptanceCriteria = this.extractAcceptanceCriteria(text);

    // 生成標題
    const title = this.generateRequirementTitle(text, entities);

    // 計算信心度
    const confidence = this.calculateConfidence(text, entities, requirementIndicators);

    return {
      id: uuidv4(),
      type,
      title,
      description: text,
      priority,
      category,
      entities,
      dependencies: [], // 可以在後續處理中填充
      constraints,
      acceptanceCriteria,
      confidence,
      sourceText: text,
      extractedAt: new Date()
    };
  }

  /**
   * 識別需求指示詞
   */
  private identifyRequirementIndicators(text: string): string[] {
    const indicators: string[] = [];
    
    // 功能性需求指示詞
    const functionalIndicators = [
      '系統必須', '系統應該', '系統能夠', '用戶可以', '用戶需要', '用戶應該',
      '應用程式必須', '應用程式應該', '軟體必須', '軟體應該',
      'system must', 'system should', 'user can', 'user should', 'application must',
      '需要', '必須', '應該', '能夠', '可以', '支持', '提供', '實現', '執行'
    ];

    // 非功能性需求指示詞
    const nonFunctionalIndicators = [
      '性能', '安全', '可用性', '可靠性', '可擴展性', '可維護性',
      'performance', 'security', 'usability', 'reliability', 'scalability', 'maintainability',
      '響應時間', '吞吐量', '並發', '加密', '認證', '授權'
    ];

    // 約束指示詞
    const constraintIndicators = [
      '限制', '約束', '不得', '禁止', '最大', '最小', '不超過', '至少',
      'constraint', 'limitation', 'must not', 'cannot', 'maximum', 'minimum', 'at least'
    ];

    functionalIndicators.forEach(indicator => {
      if (text.toLowerCase().includes(indicator.toLowerCase())) {
        indicators.push(indicator);
      }
    });

    nonFunctionalIndicators.forEach(indicator => {
      if (text.toLowerCase().includes(indicator.toLowerCase())) {
        indicators.push(indicator);
      }
    });

    constraintIndicators.forEach(indicator => {
      if (text.toLowerCase().includes(indicator.toLowerCase())) {
        indicators.push(indicator);
      }
    });

    return indicators;
  }

  /**
   * 確定需求類型
   */
  private determineRequirementType(text: string, tokens: any[]): RequirementType {
    const lowerText = text.toLowerCase();

    // 安全需求
    if (/\b(安全|加密|認證|授權|權限|密碼|登入|登出|security|authentication|authorization|encryption)\b/i.test(text)) {
      return RequirementType.SECURITY;
    }

    // 性能需求
    if (/\b(性能|效能|響應時間|吞吐量|延遲|速度|performance|response time|throughput|latency)\b/i.test(text)) {
      return RequirementType.PERFORMANCE;
    }

    // 可用性需求
    if (/\b(可用性|易用性|用戶體驗|界面|交互|usability|user experience|interface|interaction)\b/i.test(text)) {
      return RequirementType.USABILITY;
    }

    // 數據需求
    if (/\b(數據|資料|數據庫|存儲|備份|data|database|storage|backup)\b/i.test(text)) {
      return RequirementType.DATA;
    }

    // 接口需求
    if (/\b(API|接口|界面|集成|整合|interface|integration)\b/i.test(text)) {
      return RequirementType.INTERFACE;
    }

    // 技術需求
    if (/\b(技術|架構|平台|框架|library|framework|architecture|platform)\b/i.test(text)) {
      return RequirementType.TECHNICAL;
    }

    // 業務需求
    if (/\b(業務|商業|流程|規則|政策|business|process|rule|policy)\b/i.test(text)) {
      return RequirementType.BUSINESS;
    }

    // 約束需求
    if (/\b(限制|約束|不得|禁止|constraint|limitation|restriction)\b/i.test(text)) {
      return RequirementType.CONSTRAINT;
    }

    // 默認為功能性需求
    return RequirementType.FUNCTIONAL;
  }

  /**
   * 確定優先級
   */
  private determinePriority(text: string): Priority {
    const lowerText = text.toLowerCase();

    if (/\b(關鍵|重要|緊急|必須|critical|essential|urgent|must)\b/i.test(text)) {
      return Priority.CRITICAL;
    }

    if (/\b(高|高優先級|重要|high|important)\b/i.test(text)) {
      return Priority.HIGH;
    }

    if (/\b(低|低優先級|可選|low|optional)\b/i.test(text)) {
      return Priority.LOW;
    }

    if (/\b(可選|非必須|nice to have|optional)\b/i.test(text)) {
      return Priority.OPTIONAL;
    }

    return Priority.MEDIUM;
  }

  /**
   * 確定類別
   */
  private determineCategory(text: string, type: RequirementType): RequirementCategory {
    const lowerText = text.toLowerCase();

    // 用戶故事模式
    if (/\b(作為|as a|用戶|使用者|user)\b/i.test(text) && /\b(我想|我需要|i want|i need)\b/i.test(text)) {
      return RequirementCategory.USER_STORY;
    }

    // Epic 模式
    if (/\b(epic|大功能|主要功能)\b/i.test(text)) {
      return RequirementCategory.EPIC;
    }

    // Bug 模式
    if (/\b(bug|錯誤|問題|修復|fix|error|issue)\b/i.test(text)) {
      return RequirementCategory.BUG;
    }

    // 改進模式
    if (/\b(改進|優化|enhancement|improvement|optimize)\b/i.test(text)) {
      return RequirementCategory.IMPROVEMENT;
    }

    // 研究模式
    if (/\b(研究|調查|分析|research|investigation|analysis)\b/i.test(text)) {
      return RequirementCategory.RESEARCH;
    }

    // 任務模式
    if (/\b(任務|task|工作|job)\b/i.test(text)) {
      return RequirementCategory.TASK;
    }

    return RequirementCategory.FEATURE;
  }

  /**
   * 提取約束條件
   */
  private extractConstraints(text: string, entities: any[]): RequirementConstraint[] {
    const constraints: RequirementConstraint[] = [];

    // 時間約束
    const timeConstraints = text.match(/\b(\d+)\s*(秒|分鐘|小時|天|週|月|seconds?|minutes?|hours?|days?|weeks?|months?)\b/gi);
    timeConstraints?.forEach(match => {
      constraints.push({
        type: ConstraintType.TIME,
        description: `時間限制: ${match}`,
        value: match
      });
    });

    // 性能約束
    const performanceConstraints = text.match(/\b響應時間\s*[<≤]\s*(\d+)\s*(ms|秒|seconds?)\b/gi);
    performanceConstraints?.forEach(match => {
      constraints.push({
        type: ConstraintType.PERFORMANCE,
        description: `性能約束: ${match}`,
        value: match
      });
    });

    // 資源約束
    const resourceConstraints = text.match(/\b(記憶體|內存|CPU|磁碟|storage)\s*[<≤]\s*(\d+)\s*(MB|GB|TB|%)\b/gi);
    resourceConstraints?.forEach(match => {
      constraints.push({
        type: ConstraintType.RESOURCE,
        description: `資源約束: ${match}`,
        value: match
      });
    });

    return constraints;
  }

  /**
   * 提取驗收標準
   */
  private extractAcceptanceCriteria(text: string): AcceptanceCriterion[] {
    const criteria: AcceptanceCriterion[] = [];

    // Given-When-Then 模式
    const givenWhenThen = text.match(/\b(given|當|假設)\s+(.+?)\s+(when|然後|如果)\s+(.+?)\s+(then|那麼|應該)\s+(.+)/gi);
    givenWhenThen?.forEach((match, index) => {
      criteria.push({
        id: uuidv4(),
        description: match,
        type: AcceptanceCriterionType.GIVEN_WHEN_THEN,
        testable: true
      });
    });

    // 條件模式
    const conditions = text.match(/\b(如果|當|條件|if|when|condition)\s+(.+)/gi);
    conditions?.forEach((match, index) => {
      if (!givenWhenThen?.includes(match)) {
        criteria.push({
          id: uuidv4(),
          description: match,
          type: AcceptanceCriterionType.CONDITION,
          testable: true
        });
      }
    });

    return criteria;
  }

  /**
   * 生成需求標題
   */
  private generateRequirementTitle(text: string, entities: any[]): string {
    // 提取動作和對象
    const actions = entities.filter((e: any) => e.label === EntityLabel.ACTION).map((e: any) => e.text);
    const objects = entities.filter((e: any) => e.label === EntityLabel.OBJECT).map((e: any) => e.text);

    if (actions.length > 0 && objects.length > 0) {
      return `${actions[0]}${objects[0]}`;
    }

    // 如果沒有找到實體，使用句子的前幾個詞
    const words = text.split(/\s+/).slice(0, 8);
    let title = words.join(' ');
    
    if (title.length > 50) {
      title = title.substring(0, 47) + '...';
    }

    return title;
  }

  /**
   * 計算信心度
   */
  private calculateConfidence(text: string, entities: any[], indicators: string[]): number {
    let confidence = 0.3; // 基礎信心度

    // 根據指示詞數量增加信心度
    confidence += Math.min(indicators.length * 0.2, 0.4);

    // 根據實體數量增加信心度
    confidence += Math.min(entities.length * 0.1, 0.2);

    // 根據句子長度調整（太短或太長都降低信心度）
    const wordCount = text.split(/\s+/).length;
    if (wordCount >= 5 && wordCount <= 30) {
      confidence += 0.1;
    } else if (wordCount < 5 || wordCount > 50) {
      confidence -= 0.1;
    }

    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * 驗證和增強需求
   */
  private validateAndEnhanceRequirements(
    requirements: ParsedRequirement[],
    warnings: ParsingWarning[]
  ): ParsedRequirement[] {
    return requirements.map(requirement => {
      // 檢查是否有重複的需求
      const duplicates = requirements.filter(r => 
        r !== requirement && 
        this.calculateTextSimilarity(r.description, requirement.description) > 0.8
      );

      if (duplicates.length > 0) {
        warnings.push({
          type: 'DUPLICATE_REQUIREMENT',
          message: `可能存在重複需求: "${requirement.title}"`,
          suggestion: '請檢查並合併重複的需求'
        });
      }

      // 檢查是否缺少驗收標準
      if (requirement.acceptanceCriteria.length === 0) {
        warnings.push({
          type: 'MISSING_ACCEPTANCE_CRITERIA',
          message: `需求 "${requirement.title}" 缺少驗收標準`,
          suggestion: '建議為此需求添加明確的驗收標準'
        });
      }

      return requirement;
    });
  }

  /**
   * 計算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  /**
   * 計算平均信心度
   */
  private calculateAverageConfidence(requirements: ParsedRequirement[]): number {
    if (requirements.length === 0) return 0;
    
    const total = requirements.reduce((sum, req) => sum + req.confidence, 0);
    return total / requirements.length;
  }
} 