import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { PrismaService } from '../src/core/prisma/prisma.service';
import { ToolRegistryService } from '../src/modules/agent/tools/core/tool-registry.service';
import { ToolExecutorService } from '../src/modules/agent/tools/core/tool-executor.service';
import { ToolRegistrationService } from '../src/modules/agent/tools/tool-registration.service';
import { FileReaderToolImplementation } from '../src/modules/agent/tools/implementations/file-reader.tool';
import { WebSearchToolImplementation } from '../src/modules/agent/tools/implementations/web-search.tool';
import { CodeGeneratorToolImplementation } from '../src/modules/agent/tools/implementations/code-generator.tool';
import { AppModule } from '../src/app.module';
import { ToolExecutionContext } from '../src/modules/agent/tools/core/tool-registry.interface';
import * as fs from 'fs/promises';
import * as path from 'path';

describe('Tool Execution Framework (e2e)', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let toolRegistry: ToolRegistryService;
  let toolExecutor: ToolExecutorService;
  let toolRegistration: ToolRegistrationService;
  
  let testTenantId: string;
  let testUserId: string;
  let testBotId: string;
  let testToolIds: string[] = [];

  const mockContext: ToolExecutionContext = {
    tenantId: '',
    userId: '',
    workspaceId: undefined,
    ability: {} as any,
    user: {} as any,
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prismaService = moduleFixture.get<PrismaService>(PrismaService);
    toolRegistry = moduleFixture.get<ToolRegistryService>(ToolRegistryService);
    toolExecutor = moduleFixture.get<ToolExecutorService>(ToolExecutorService);
    toolRegistration = moduleFixture.get<ToolRegistrationService>(ToolRegistrationService);

    await app.init();

    // 建立測試資料
    await setupTestData();
  });

  afterAll(async () => {
    // 清理測試資料
    await cleanupTestData();
    await app.close();
  });

  async function setupTestData() {
    // 建立測試租戶
    const tenant = await prismaService.tenants.create({
      data: {
        name: 'Test Tenant for Tools',
        domain: 'test-tools.com',
        status: 'active',
        admin_email: '<EMAIL>',
        admin_name: 'Test Admin',
        billing_cycle: 'monthly',
        company_size: '10-50',
        industry: '測試',
        max_users: 5,
        max_projects: 10,
        max_storage: 10,
        payment_status: 'paid',
      },
    });
    testTenantId = tenant.id;

    // 建立測試用戶
    const user = await prismaService.tenant_users.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        name: 'Test Tools User',
        tenant_id: testTenantId,
        role: 'TENANT_ADMIN',
        status: 'ACTIVE',
        department: '技術部',
        title: '測試工程師',
      },
    });
    testUserId = user.id;

    // 建立測試 AI 工具
    const fileReaderTool = await prismaService.ai_tools.create({
      data: {
        key: 'file_reader',
        name: '檔案讀取工具',
        description: '讀取指定路徑的檔案內容',
        input_schema: {
          type: 'object',
          properties: {
            filePath: { type: 'string', description: '檔案路徑' },
          },
          required: ['filePath'],
        },
        scope: 'SYSTEM',
        is_enabled: true,
        created_by: testUserId,
      },
    });
    testToolIds.push(fileReaderTool.id);

    const webSearchTool = await prismaService.ai_tools.create({
      data: {
        key: 'web_search',
        name: '網路搜尋工具',
        description: '搜尋網路資訊',
        input_schema: {
          type: 'object',
          properties: {
            query: { type: 'string', description: '搜尋關鍵字' },
          },
          required: ['query'],
        },
        scope: 'SYSTEM',
        is_enabled: true,
        created_by: testUserId,
      },
    });
    testToolIds.push(webSearchTool.id);

    // 建立測試 Bot
    const bot = await prismaService.ai_bots.create({
      data: {
        name: 'Test Bot with Tools',
        description: '測試工具執行的 Bot',
        system_prompt: '你是一個測試助手',
        tenant_id: testTenantId,
        execution_type: 'tool',
        is_enabled: true,
        created_by: testUserId,
      },
    });
    testBotId = bot.id;

    // 關聯 Bot 與工具
    await prismaService.ai_bot_tools.createMany({
      data: [
        {
          ai_bot_id: testBotId,
          ai_tool_id: fileReaderTool.id,
          is_enabled: true,
          config: {},
        },
        {
          ai_bot_id: testBotId,
          ai_tool_id: webSearchTool.id,
          is_enabled: true,
          config: {},
        },
      ],
    });

    // 更新 mock context
    mockContext.tenantId = testTenantId;
    mockContext.userId = testUserId;
    mockContext.user = user as any;
  }

  async function cleanupTestData() {
    // 清理順序很重要，要按照外鍵依賴關係
    await prismaService.ai_bot_tools.deleteMany({
      where: { ai_bot_id: testBotId },
    });
    
    await prismaService.ai_bots.deleteMany({
      where: { id: testBotId },
    });
    
    await prismaService.ai_tools.deleteMany({
      where: { id: { in: testToolIds } },
    });
    
    await prismaService.tenant_users.deleteMany({
      where: { id: testUserId },
    });
    
    await prismaService.tenants.deleteMany({
      where: { id: testTenantId },
    });
  }

  describe('Tool Registration', () => {
    it('should register tools on application bootstrap', async () => {
      // 檢查工具是否已註冊
      expect(toolRegistry.hasImplementation('file_reader')).toBe(true);
      expect(toolRegistry.hasImplementation('web_search')).toBe(true);
      expect(toolRegistry.hasImplementation('code_generator')).toBe(true);
    });

    it('should list all registered tools', () => {
      const tools = toolRegistry.listTools();
      expect(tools).toContain('file_reader');
      expect(tools).toContain('web_search');
      expect(tools).toContain('code_generator');
    });
  });

  describe('Tool Execution', () => {
    it('should load bot tools successfully', async () => {
      const tools = await toolExecutor.loadBotTools(testBotId, mockContext);
      
      expect(tools).toHaveLength(2);
      expect(tools.map(t => t.name)).toContain('file_reader');
      expect(tools.map(t => t.name)).toContain('web_search');
    });

    it('should validate tool access correctly', async () => {
      const isFileReaderValid = await toolExecutor.validateToolAccess('file_reader', mockContext);
      const isInvalidToolValid = await toolExecutor.validateToolAccess('non_existent_tool', mockContext);
      
      expect(isFileReaderValid).toBe(true);
      expect(isInvalidToolValid).toBe(false);
    });

    it('should get available tools for context', async () => {
      const availableTools = await toolExecutor.getAvailableTools(mockContext);
      
      expect(availableTools.length).toBeGreaterThan(0);
      expect(availableTools.some(t => t.key === 'file_reader')).toBe(true);
      expect(availableTools.some(t => t.key === 'web_search')).toBe(true);
      expect(availableTools.some(t => t.key === 'code_generator')).toBe(true);
    });
  });

  describe('File Reader Tool', () => {
    let testFilePath: string;

    beforeAll(async () => {
      // 建立測試檔案
      testFilePath = path.join(process.cwd(), 'test-file.txt');
      await fs.writeFile(testFilePath, 'This is a test file content for tool testing.');
    });

    afterAll(async () => {
      // 清理測試檔案
      try {
        await fs.unlink(testFilePath);
      } catch (error) {
        // 檔案可能已被刪除
      }
    });

    it('should read file content successfully', async () => {
      const toolConfig = {
        id: 'test-config',
        key: 'file_reader',
        name: '檔案讀取工具',
        description: '測試檔案讀取',
        inputSchema: {},
        isEnabled: true,
        config: {},
      };

      const tool = await toolExecutor.loadTool(toolConfig, mockContext);
      expect(tool).toBeDefined();

      // 執行工具
      const result = await tool.call({ filePath: 'test-file.txt' });
      expect(result).toContain('This is a test file content');
    });

    it('should handle non-existent file gracefully', async () => {
      const toolConfig = {
        id: 'test-config',
        key: 'file_reader',
        name: '檔案讀取工具',
        description: '測試檔案讀取',
        inputSchema: {},
        isEnabled: true,
        config: {},
      };

      const tool = await toolExecutor.loadTool(toolConfig, mockContext);
      
      // 嘗試讀取不存在的檔案
      const result = await tool.call({ filePath: 'non-existent-file.txt' });
      expect(result).toContain('讀取檔案失敗');
    });
  });

  describe('Code Generator Tool', () => {
    it('should generate TypeScript code successfully', async () => {
      const toolConfig = {
        id: 'test-config',
        key: 'code_generator',
        name: '程式碼生成工具',
        description: '測試程式碼生成',
        inputSchema: {},
        isEnabled: true,
        config: {},
      };

      const tool = await toolExecutor.loadTool(toolConfig, mockContext);
      expect(tool).toBeDefined();

      // 執行工具
      const result = await tool.call({
        language: 'typescript',
        description: '建立一個簡單的 Hello World 函數',
        includeComments: true,
      });

      expect(result).toContain('typescript');
      expect(result).toContain('Hello World');
      expect(result).toContain('export');
    });

    it('should generate Python code with tests', async () => {
      const toolConfig = {
        id: 'test-config',
        key: 'code_generator',
        name: '程式碼生成工具',
        description: '測試程式碼生成',
        inputSchema: {},
        isEnabled: true,
        config: {},
      };

      const tool = await toolExecutor.loadTool(toolConfig, mockContext);
      
      // 執行工具
      const result = await tool.call({
        language: 'python',
        description: '建立一個計算器類別',
        includeTests: true,
      });

      expect(result).toContain('python');
      expect(result).toContain('class');
      expect(result).toContain('unittest'); // 應該包含測試程式碼
    });
  });

  describe('Web Search Tool', () => {
    it('should handle search requests gracefully', async () => {
      const toolConfig = {
        id: 'test-config',
        key: 'web_search',
        name: '網路搜尋工具',
        description: '測試網路搜尋',
        inputSchema: {},
        isEnabled: true,
        config: {},
      };

      const tool = await toolExecutor.loadTool(toolConfig, mockContext);
      expect(tool).toBeDefined();

      // 執行工具（可能會失敗，因為網路連線問題，所以我們只檢查基本功能）
      const result = await tool.call({
        query: 'test search',
        maxResults: 3,
      });

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      // 結果應該包含搜尋相關的資訊或錯誤訊息
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle disabled tools', async () => {
      const disabledConfig = {
        id: 'test-config',
        key: 'file_reader',
        name: '檔案讀取工具',
        description: '測試檔案讀取',
        inputSchema: {},
        isEnabled: false, // 停用工具
        config: {},
      };

      await expect(toolExecutor.loadTool(disabledConfig, mockContext))
        .rejects.toThrow('工具已被停用');
    });

    it('should handle non-existent tool implementations', async () => {
      const invalidConfig = {
        id: 'test-config',
        key: 'non_existent_tool',
        name: '不存在的工具',
        description: '測試不存在的工具',
        inputSchema: {},
        isEnabled: true,
        config: {},
      };

      await expect(toolExecutor.loadTool(invalidConfig, mockContext))
        .rejects.toThrow('找不到工具實作');
    });
  });
}); 