/**
 * 新架構工具統一導出
 *
 * 此檔案導出所有採用新架構的工具factory函數和類別
 */

// File Reader Tools
export {
  createFileReaderTool,
  FileReaderToolsFactory,
  type FileReaderConfig,
  type FileReadInput,
} from './file-reader-tools.factory';

// Web Search Tools
export {
  createWebSearchTool,
  WebSearchToolsFactory,
  type WebSearchConfig,
  type WebSearchInput,
  type WebSearchResult,
} from './web-search-tools.factory';

// Notification Tools
export {
  createSendMessageTool,
  createNotificationTool,
  createGetNotificationsTool,
  createMarkNotificationReadTool,
  NotificationToolsFactory,
} from '../notification';

// Project Info Tools
export {
  createProjectInfoTool,
  ProjectInfoToolsFactory,
  type ProjectInfoConfig,
  type ProjectInfoInput,
} from '../project/project-info-tools.factory';

// Create Task Tools
export {
  createCreateTaskTool,
  CreateTaskToolsFactory,
  type CreateTaskConfig,
  type CreateTaskInput,
} from '../task/create-task-tools.factory';

// Progress Update Tools
export {
  createProgressUpdateTool,
  ProgressUpdateToolsFactory,
  type ProgressUpdateConfig,
  type ProgressUpdateInput,
} from '../progress/progress-update-tools.factory';

// Knowledge Base Tools
export {
  createKnowledgeBaseSearchTool,
  KnowledgeBaseToolsFactory,
  type KnowledgeBaseConfig,
  type KnowledgeBaseInput,
  KnowledgeBaseErrorType,
} from '../knowledge/knowledge-base-tools.factory';

// Import for type usage
import { FileReaderToolsFactory } from './file-reader-tools.factory';
import { WebSearchToolsFactory } from './web-search-tools.factory';
import { NotificationToolsFactory } from '../notification';
import { ProjectInfoToolsFactory } from '../project/project-info-tools.factory';
import { CreateTaskToolsFactory } from '../task/create-task-tools.factory';
import { ProgressUpdateToolsFactory } from '../progress/progress-update-tools.factory';
import { KnowledgeBaseToolsFactory } from '../knowledge/knowledge-base-tools.factory';

// 統一的工具創建函數型別
export type ToolFactory =
  | typeof FileReaderToolsFactory
  | typeof WebSearchToolsFactory
  | typeof NotificationToolsFactory
  | typeof ProjectInfoToolsFactory
  | typeof CreateTaskToolsFactory
  | typeof ProgressUpdateToolsFactory
  | typeof KnowledgeBaseToolsFactory;

// 工具名稱列舉
export enum ModernToolName {
  FILE_READER = 'file_reader',
  WEB_SEARCH = 'web_search',
  SEND_MESSAGE = 'send_message',
  CREATE_NOTIFICATION = 'create_notification',
  GET_NOTIFICATIONS = 'get_notifications',
  MARK_NOTIFICATION_READ = 'mark_notification_read',
  PROJECT_INFO = 'project_info',
  CREATE_TASK = 'create_task',
  PROGRESS_UPDATE = 'progress_update',
  KNOWLEDGE_BASE = 'knowledge_base_search',
}

// 現代工具註冊表
export const MODERN_TOOL_REGISTRY = {
  [ModernToolName.FILE_READER]: FileReaderToolsFactory,
  [ModernToolName.WEB_SEARCH]: WebSearchToolsFactory,
  [ModernToolName.PROJECT_INFO]: ProjectInfoToolsFactory,
  [ModernToolName.CREATE_TASK]: CreateTaskToolsFactory,
  [ModernToolName.PROGRESS_UPDATE]: ProgressUpdateToolsFactory,
  [ModernToolName.KNOWLEDGE_BASE]: KnowledgeBaseToolsFactory,
  // Notification tools are handled separately in NotificationToolsFactory
} as const;
