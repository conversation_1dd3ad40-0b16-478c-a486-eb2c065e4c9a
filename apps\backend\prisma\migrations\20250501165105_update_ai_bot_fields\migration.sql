-- AlterTable
ALTER TABLE "ai_bots" 
  ADD COLUMN IF NOT EXISTS "responseFormat" TEXT NOT NULL DEFAULT 'text',
  ADD COLUMN IF NOT EXISTS "scope" TEXT NOT NULL DEFAULT 'WORKSPACE',
  ADD COLUMN IF NOT EXISTS "isTemplate" BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN IF NOT EXISTS "isEnabled" BOOLEAN NOT NULL DEFAULT true,
  ADD COLUMN IF NOT EXISTS "updatedBy" UUID REFERENCES "users"("id");

-- AddConstraint
ALTER TABLE "ai_bots"
  ADD CONSTRAINT "ai_bots_response_format_check" 
  CHECK ("responseFormat" IN ('text', 'json_object', 'json_schema'));

ALTER TABLE "ai_bots"
  ADD CONSTRAINT "ai_bots_scope_check" 
  CHECK ("scope" IN ('ADMIN', 'WORKSPACE', 'BACKEND')); 