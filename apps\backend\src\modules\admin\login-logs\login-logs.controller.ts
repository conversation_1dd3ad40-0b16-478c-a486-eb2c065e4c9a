import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { LoginLogsService } from './login-logs.service';
import { LoginLogDto } from './dto/login-log.dto';

@ApiTags('admin/login-logs')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('admin/login-logs')
export class LoginLogsController {
  constructor(private readonly loginLogsService: LoginLogsService) {}

  @Get()
  @ApiOperation({ summary: '查詢登入紀錄' })
  @ApiResponse({ status: 200, type: [LoginLogDto] })
  async findMany(
    @Query('userId') userId?: string,
    @Query('success') success?: string,
    @Query('start') start?: string,
    @Query('end') end?: string,
  ): Promise<LoginLogDto[]> {
    const where: any = {};
    if (userId) where.userId = userId;
    if (success !== undefined) where.success = success === 'true';
    if (start || end) {
      where.login_at = {};
      if (start) where.login_at.gte = new Date(start);
      if (end) where.login_at.lte = new Date(end);
    }
    const logs = await this.loginLogsService.findMany(where);
    return logs.map((log) => ({
      id: log.id,
      user_id: log.user_id,
      ip_address: log.ip_address,
      user_agent: log.user_agent,
      login_at: log.login_at,
      success: log.success,
      fail_reason: log.fail_reason,
    }));
  }
}
