import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsUrl } from 'class-validator';

export class UpdateLineSettingsDto {
  @ApiProperty({ description: 'Line Channel ID' })
  @IsString()
  channelId: string;

  @ApiProperty({ description: 'Line Channel Secret' })
  @IsString()
  channelSecret: string;

  @ApiProperty({ description: 'Line Login Callback URL' })
  @IsUrl()
  loginCallbackUrl: string;

  @ApiProperty({ description: '是否啟用 Line 登入' })
  @IsBoolean()
  enableLineLogin: boolean;

  @ApiProperty({ description: '是否啟用 Line Notify' })
  @IsBoolean()
  enableLineNotify: boolean;
}
