# 敏感數據加密指南

## 簡介

本文檔描述了 HorizAI 系統中用於保護敏感數據（如 API 金鑰、密碼等）的加密機制。系統使用 AES-256-CBC 標準對敏感數據進行加密，確保即使數據庫被洩露，敏感信息也不會被直接讀取。

## 加密實現

系統提供了一個全局的 `EncryptionService` 來處理所有加密和解密操作，確保整個應用程序中一致的加密機制。

### 關鍵技術細節

- **加密算法**: AES-256-CBC
- **密鑰管理**: 使用環境變數 `ENCRYPTION_KEY` 存儲主加密密鑰
- **加密格式**: `[iv(hex)]:[加密數據(hex)]`，其中 IV 為隨機生成的初始化向量
- **密鑰格式**: 32 字節的十六進制字符串（64 個字符）

## 使用方法

### 前提條件

1. 確保環境變數 `ENCRYPTION_KEY` 已正確設置。使用以下命令可以生成一個新的加密密鑰：

```bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

2. 將生成的密鑰添加到 `.env` 文件中：

```
ENCRYPTION_KEY=生成的32字節十六進制字符串
```

### 在服務中使用加密

在需要加密敏感數據的服務中，注入 `EncryptionService` 並使用其方法：

```typescript
import { Injectable } from '@nestjs/common';
import { EncryptionService } from '@/modules/core/encryption/encryption.service';

@Injectable()
export class YourService {
  constructor(private readonly encryptionService: EncryptionService) {}

  async storeApiKey(apiKey: string): Promise<void> {
    // 加密 API 金鑰
    const encryptedApiKey = this.encryptionService.encrypt(apiKey);
    
    // 將加密後的金鑰存儲到數據庫
    await this.repository.save({ apiKey: encryptedApiKey });
  }

  async getApiKey(): Promise<string> {
    // 從數據庫讀取加密的 API 金鑰
    const { apiKey: encryptedApiKey } = await this.repository.findOne();
    
    // 解密 API 金鑰
    return this.encryptionService.decrypt(encryptedApiKey);
  }
}
```

### 主要方法

`EncryptionService` 提供以下主要方法：

- **encrypt(plain: string): string** - 加密明文數據
- **decrypt(cipherText: string): string** - 解密密文數據
- **isEncrypted(text: string): boolean** - 檢查字符串是否已經是加密格式

## 數據遷移

為了加密現有數據庫中的敏感信息，系統提供了一個遷移腳本 `encrypt-api-keys.ts`。此腳本會：

1. 加密 `ai_bots` 表中未加密的 API 金鑰
2. 加密 `settings` 表中的敏感設定
3. 處理 JSON 格式的 AI 設定中的 API 金鑰

執行遷移腳本的方法：

```bash
npm run script encrypt-api-keys
```

## 敏感數據類型

系統定義了以下類型的敏感數據需要加密：

- **AI API 金鑰**:
  - OpenAI API 金鑰
  - Claude API 金鑰
  - Bot 專用 API 金鑰

- **第三方服務密鑰**:
  - LINE 頻道密鑰和令牌
  - SMTP 密碼
  - SendGrid API 金鑰
  - S3 存儲訪問金鑰

## 安全考量

- 主加密密鑰（`ENCRYPTION_KEY`）是系統安全的核心，應妥善保管
- 避免將此密鑰存儲在代碼庫中或公開場合
- 定期更換加密密鑰，並重新加密敏感數據
- 生產環境和開發環境應使用不同的加密密鑰
- 對加密/解密操作進行系統日誌記錄

## 故障排除

如果遇到加密或解密錯誤，請檢查：

1. `ENCRYPTION_KEY` 環境變數是否正確設置
2. 是否嘗試解密未加密的數據
3. 是否嘗試重複加密已加密的數據
4. 加密密鑰是否在不同環境之間發生變化（造成無法解密）

如需更多幫助，請參考日誌記錄或聯繫系統管理員。 