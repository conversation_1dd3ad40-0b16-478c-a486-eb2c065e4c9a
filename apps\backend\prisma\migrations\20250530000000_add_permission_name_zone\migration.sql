-- AddPermissionNameAndZone
-- 為 Permission 表添加中文名稱和區域分類欄位

-- 添加新欄位
ALTER TABLE "permissions" ADD COLUMN "name" TEXT;

ALTER TABLE "permissions" ADD COLUMN "zone" TEXT;

-- 為現有權限設置預設區域
UPDATE "permissions" SET "zone" = 'admin' WHERE "zone" IS NULL;

-- 為現有權限生成預設中文名稱（基於 action 和 subject）
UPDATE "permissions"
SET
    "name" = CASE
        WHEN "action" = 'manage' THEN '管理' || CASE
            WHEN "subject" = 'User' THEN '用戶'
            WHEN "subject" = 'Tenant' THEN '租戶'
            WHEN "subject" = 'Workspace' THEN '工作區'
            WHEN "subject" = 'Project' THEN '專案'
            WHEN "subject" = 'Role' THEN '角色'
            WHEN "subject" = 'Permission' THEN '權限'
            ELSE "subject"
        END
        WHEN "action" = 'create' THEN '建立' || CASE
            WHEN "subject" = 'User' THEN '用戶'
            WHEN "subject" = 'Tenant' THEN '租戶'
            WHEN "subject" = 'Workspace' THEN '工作區'
            WHEN "subject" = 'Project' THEN '專案'
            WHEN "subject" = 'Role' THEN '角色'
            WHEN "subject" = 'Permission' THEN '權限'
            ELSE "subject"
        END
        WHEN "action" = 'read' THEN '查看' || CASE
            WHEN "subject" = 'User' THEN '用戶'
            WHEN "subject" = 'Tenant' THEN '租戶'
            WHEN "subject" = 'Workspace' THEN '工作區'
            WHEN "subject" = 'Project' THEN '專案'
            WHEN "subject" = 'Role' THEN '角色'
            WHEN "subject" = 'Permission' THEN '權限'
            ELSE "subject"
        END
        WHEN "action" = 'update' THEN '修改' || CASE
            WHEN "subject" = 'User' THEN '用戶'
            WHEN "subject" = 'Tenant' THEN '租戶'
            WHEN "subject" = 'Workspace' THEN '工作區'
            WHEN "subject" = 'Project' THEN '專案'
            WHEN "subject" = 'Role' THEN '角色'
            WHEN "subject" = 'Permission' THEN '權限'
            ELSE "subject"
        END
        WHEN "action" = 'delete' THEN '刪除' || CASE
            WHEN "subject" = 'User' THEN '用戶'
            WHEN "subject" = 'Tenant' THEN '租戶'
            WHEN "subject" = 'Workspace' THEN '工作區'
            WHEN "subject" = 'Project' THEN '專案'
            WHEN "subject" = 'Role' THEN '角色'
            WHEN "subject" = 'Permission' THEN '權限'
            ELSE "subject"
        END
        WHEN "action" = 'access' THEN '存取' || CASE
            WHEN "subject" = 'AdminPanel' THEN '管理後台'
            WHEN "subject" = 'Dashboard' THEN '儀表板'
            ELSE "subject"
        END
        ELSE "action" || ':' || "subject"
    END
WHERE
    "name" IS NULL;