<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HorizAI - Workspace (Shadcn Layout)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 142.1 76.2% 36.3%; /* Green 600 */
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96.1%;
            --secondary-foreground: 222.2 47.4% 11.2%;
            --muted: 210 40% 96.1%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 142.1 76.2% 95%; /* Light Green */
            --accent-foreground: 142.1 76.2% 28.3%; /* Dark Green */
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 142.1 76.2% 36.3%;
            /* Green 600 */
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: hsl(var(--secondary));
            color: hsl(var(--foreground));
        }

        .sidebar {
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar[data-collapsed="true"] {
            width: 4rem;
            /* 64px */
        }

        /* Navigation buttons in collapsed state */
        .sidebar[data-collapsed="true"] nav a {
            justify-content: center;
            padding: 0.75rem 0.5rem;
            height: 3rem;
            border-radius: 0.5rem;
            margin-bottom: 0.125rem;
        }

        .sidebar[data-collapsed="true"] nav a .mr-3 {
            margin-right: 0;
        }

        .sidebar[data-collapsed="false"] {
            width: 220px;
        }

        .sidebar .nav-link-text {
            opacity: 1;
            transition: opacity 0.2s ease-in-out;
        }

        .sidebar[data-collapsed="true"] .nav-link-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
            white-space: nowrap;
            pointer-events: none;
        }

        /* Tooltip styles using shadcn/ui approach */
        .tooltip-trigger {
            position: relative;
        }

        .tooltip-content {
            position: absolute;
            left: calc(100% + 0.75rem);
            top: 50%;
            transform: translateY(-50%);
            background-color: hsl(var(--popover));
            color: hsl(var(--popover-foreground));
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius);
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.15s ease-out;
            pointer-events: none;
            z-index: 50;
            line-height: 1.4;
            border: 1px solid hsl(var(--border));
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            display: flex;
            align-items: center;
        }

        /* Sidebar tooltip styles - positioned differently for sidebar buttons */
        .sidebar-tooltip-trigger {
            position: relative;
        }

        .sidebar-tooltip-content {
            position: absolute;
            right: calc(100% + 0.75rem);
            top: 50%;
            transform: translateY(-50%);
            background-color: hsl(var(--popover));
            color: hsl(var(--popover-foreground));
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius);
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.15s ease-out;
            pointer-events: none;
            z-index: 50;
            line-height: 1.4;
            border: 1px solid hsl(var(--border));
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        /* Show tooltip on hover */
        .sidebar-tooltip-trigger:hover .sidebar-tooltip-content {
            opacity: 1;
            visibility: visible;
        }

        /* Multi-line tooltip for user avatar */
        .user-avatar-container .tooltip-content {
            white-space: pre-line;
            min-width: 140px;
        }

        /* Show tooltip on hover when sidebar is collapsed */
        .sidebar[data-collapsed="true"] .tooltip-trigger:hover .tooltip-content {
            opacity: 1;
            visibility: visible;
        }

        /* Hide tooltips when sidebar is expanded */
        .sidebar[data-collapsed="false"] .tooltip-content {
            display: none;
        }

        /* Red dot indicator for collapsed sidebar buttons */
        .collapsed-indicator {
            position: absolute;
            top: calc(50% + 4px);
            left: calc(0.75rem + 16px);
            width: 12px;
            height: 12px;
            background-color: #ef4444; /* red-500 */
            border-radius: 50%;
            border: 2px solid white;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease-in-out;
            z-index: 10;
        }

        /* Adjust position for collapsed sidebar - center the buttons and position dot relative to icon */
        .sidebar[data-collapsed="true"] .collapsed-indicator {
            top: calc(50% + 4px);
            left: calc(50% + 4px);
        }

        /* Show indicator when sidebar is collapsed and has notifications */
        .sidebar[data-collapsed="true"] .has-notifications .collapsed-indicator {
            opacity: 1;
            visibility: visible;
        }

        /* Hide indicator when sidebar is expanded */
        .sidebar[data-collapsed="false"] .collapsed-indicator {
            opacity: 0;
            visibility: hidden;
        }

        /* User avatar and settings styling for collapsed sidebar */
        .sidebar[data-collapsed="true"] .user-avatar-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0.75rem 0.5rem;
            margin: 0;
            width: 100%;
            position: relative;
            border-radius: 0.5rem;
            height: 3rem;
        }

        .sidebar[data-collapsed="true"] .user-avatar-container:hover {
            background-color: hsl(142.1 76.2% 95%);
        }

        .sidebar[data-collapsed="true"] .user-avatar-container img {
            width: 2rem;
            height: 2rem;
            margin: 0;
            flex-shrink: 0;
            border-radius: 50%;
        }

        .sidebar[data-collapsed="true"] .user-avatar-info {
            display: none;
        }

        .sidebar[data-collapsed="true"] .user-avatar-container img {
            display: block !important;
        }

        /* Project list styling for collapsed sidebar */
        .sidebar[data-collapsed="true"] #my-projects-list {
            display: flex;
            flex-direction: column;
            gap: 0.125rem;
            padding: 0;
        }

        .sidebar[data-collapsed="true"] #my-projects-list a {
            justify-content: center;
            padding: 0.5rem;
            margin: 0;
            position: relative;
            height: 2.5rem;
            border-radius: 0.375rem;
        }

        .sidebar[data-collapsed="true"] #my-projects-list a .w-2 {
            margin-right: 0;
            width: 1rem;
            height: 1rem;
        }

        .sidebar[data-collapsed="true"] #my-projects-list a .flex-1,
        .sidebar[data-collapsed="true"] #my-projects-list a .ml-2 {
            display: none;
        }

        /* Project section header for collapsed sidebar */
        .sidebar[data-collapsed="true"] .mt-6.mb-4 h3 {
            opacity: 0;
            width: 0;
            overflow: hidden;
            white-space: nowrap;
            pointer-events: none;
        }

        .sidebar[data-collapsed="true"] .mt-6.mb-4 {
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
        }

        /* Navigation area styling for collapsed sidebar */
        .sidebar[data-collapsed="true"] nav {
            padding: 1rem 0.5rem;
        }

        /* Bottom area styling for collapsed sidebar */
        .sidebar[data-collapsed="true"] .sidebar-bottom-container {
            padding: 1rem 0.5rem;
        }

        /* Bottom container styling for collapsed sidebar */
        .sidebar[data-collapsed="true"] .flex.items-center.px-3.py-2.mt-3.gap-2 {
            flex-direction: column;
            gap: 0.25rem;
            padding: 0;
            margin-top: 0.75rem;
        }

        .sidebar[data-collapsed="true"] .flex.items-center.px-3.py-2.mt-3.gap-2 .flex-1 {
            width: 100%;
            order: 1;
            padding: 0.5rem;
        }

        .sidebar[data-collapsed="true"] .flex.items-center.px-3.py-2.mt-3.gap-2 .flex-shrink-0 {
            width: 100%;
            justify-content: center;
            order: 2;
            padding: 0.5rem;
            display: flex;
            align-items: center;
        }

        /* Quick actions styling for collapsed sidebar */
        .sidebar[data-collapsed="true"] .mb-3 h4 {
            opacity: 0;
            width: 0;
            overflow: hidden;
            white-space: nowrap;
            pointer-events: none;
        }

        .sidebar[data-collapsed="true"] #quick-ai-chat {
            justify-content: center;
            padding: 0.75rem 0.5rem;
            height: 3rem;
            border-radius: 0.5rem;
        }

        .sidebar[data-collapsed="true"] #quick-ai-chat .flex-1,
        .sidebar[data-collapsed="true"] #quick-ai-chat .ml-2 {
            display: none;
        }

        .sidebar[data-collapsed="true"] #quick-ai-chat .mr-3 {
            margin-right: 0;
        }

        /* User menu styling for collapsed sidebar */
        .sidebar[data-collapsed="true"] #user-menu-toggle .ml-3,
        .sidebar[data-collapsed="true"] #user-menu-toggle .h-4 {
            display: none;
        }

        /* Ensure user avatar is visible in collapsed state */
        .sidebar[data-collapsed="true"] .user-avatar-container {
            display: flex !important;
        }

        .sidebar[data-collapsed="true"] #user-menu-toggle {
            justify-content: center;
            padding: 0.75rem;
        }

        .sidebar[data-collapsed="true"] #user-menu {
            left: calc(100% + 0.5rem);
            bottom: 0;
        }

        /* Button group styling for collapsed sidebar */
        .sidebar[data-collapsed="true"] .grid.grid-cols-2.gap-2.mb-3 {
            grid-template-columns: 1fr;
            gap: 0.25rem;
            margin-bottom: 0.75rem;
        }

        .sidebar[data-collapsed="true"] .grid.grid-cols-2.gap-2.mb-3 button {
            justify-content: center;
            padding: 0.75rem 0.5rem;
            flex-direction: row;
            align-items: center;
            height: 3rem;
            border-radius: 0.5rem;
        }

        .sidebar[data-collapsed="true"] .grid.grid-cols-2.gap-2.mb-3 button .mb-2 {
            margin-bottom: 0;
            margin-right: 0;
        }

        .sidebar[data-collapsed="true"] .grid.grid-cols-2.gap-2.mb-3 button .nav-link-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
            white-space: nowrap;
            pointer-events: none;
        }

        .sidebar[data-collapsed="true"] .grid.grid-cols-2.gap-2.mb-3 button .absolute {
            position: absolute;
            top: 0.25rem;
            right: 0.25rem;
        }



        /* Logo styling for collapsed/expanded states */
        .logo-container {
            transition: all 0.2s ease-in-out;
        }

        .sidebar[data-collapsed="true"] .logo-full {
            display: none;
        }

        .sidebar[data-collapsed="true"] .logo-icon {
            display: block !important;
        }

        .sidebar[data-collapsed="false"] .logo-full {
            display: block;
        }

        .sidebar[data-collapsed="false"] .logo-icon {
            display: none;
        }

        /* Ensure logo container is always visible and centered */
        .sidebar[data-collapsed="true"] .logo-container {
            opacity: 1;
            width: auto;
            overflow: visible;
            white-space: nowrap;
            pointer-events: auto;
            justify-content: center;
        }

        /* Make sure the logo container parent maintains h-16 in collapsed state */
        .sidebar[data-collapsed="true"] .h-16 {
            height: 4rem; /* 64px - keeping consistent with main header */
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem; /* Add padding to match main content header */
        }

        /* Chat sidebar styles */
        .chat-sidebar {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            width: 0;
            overflow: hidden;
        }

        .chat-sidebar.show {
            width: 320px;
        }

        .chat-sidebar.hidden {
            width: 0;
        }

        /* Pinned chat sidebar styles */
        .chat-sidebar.pinned {
            position: relative !important;
            transform: none !important;
            z-index: auto !important;
            box-shadow: none !important;
            background-color: white !important;
            backdrop-filter: none !important;
        }

        .chat-sidebar.floating {
            position: relative;
            top: 0;
            right: 0;
            height: 100vh;
            z-index: 10;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05), -2px 0 8px rgba(0, 0, 0, 0.04);
            background-color: rgba(248, 250, 252, 0.98);
            backdrop-filter: blur(8px);
        }

        /* Todo sidebar styles */
        .todo-sidebar {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            width: 0;
            overflow: hidden;
        }

        .todo-sidebar.show {
            width: 320px;
        }

        .todo-sidebar.hidden {
            width: 0;
        }

        /* Pinned todo sidebar styles */
        .todo-sidebar.pinned {
            position: relative !important;
            transform: none !important;
            z-index: auto !important;
            box-shadow: none !important;
            background-color: white !important;
            backdrop-filter: none !important;
        }

        .todo-sidebar.floating {
            position: relative;
            top: 0;
            right: 0;
            height: 100vh;
            z-index: 10;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05), -2px 0 8px rgba(0, 0, 0, 0.04);
            background-color: rgba(248, 250, 252, 0.98);
            backdrop-filter: blur(8px);
        }

        /* Backdrop styles */
        .backdrop {
            transition: opacity 0.3s ease-in-out;
            opacity: 0;
            visibility: hidden;
            z-index: 30;
        }

        .backdrop.show {
            opacity: 1;
            visibility: visible;
        }

        /* Pin button styles */
        .pin-btn {
            transition: all 0.2s ease-in-out;
        }

        .pin-btn.pinned {
            color: hsl(var(--primary));
            background-color: hsl(var(--accent));
        }

        /* Chat badge animation */
        .chat-badge {
            animation: badge-pulse 2s infinite;
            background-color: #fecaca !important; /* 淺紅色背景 */
            color: #991b1b !important; /* 深紅色文字 */
        }

        /* Todo badge animation */
        .todo-badge {
            animation: badge-pulse-orange 2s infinite;
            background-color: #fed7aa !important; /* 淺橙色背景 */
            color: #9a3412 !important; /* 深橙色文字 */
        }

        @keyframes badge-pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(254, 202, 202, 0.8);
            }
            50% {
                transform: scale(1.1);
                box-shadow: 0 0 0 4px rgba(254, 202, 202, 0);
            }
        }

        @keyframes badge-pulse-orange {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(254, 215, 170, 0.8);
            }
            50% {
                transform: scale(1.1);
                box-shadow: 0 0 0 4px rgba(254, 215, 170, 0);
            }
        }

        /* Notification badge styles */
        .notification-badge {
            animation: bounce 1s infinite;
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0,-3px,0);
            }
            70% {
                transform: translate3d(0,-2px,0);
            }
            90% {
                transform: translate3d(0,-1px,0);
            }
        }

        /* Project list styles */
        #my-projects-list a {
            transition: all 0.2s ease-in-out;
            border-left: 3px solid transparent;
        }

        #my-projects-list a:hover {
            border-left-color: hsl(var(--primary));
            transform: translateX(2px);
        }

        #my-projects-list a.active {
            background-color: hsl(var(--accent));
            color: hsl(var(--accent-foreground));
            border-left-color: hsl(var(--primary));
        }

        #my-projects-list .w-2 {
            transition: all 0.2s ease-in-out;
        }

        /* Project status animations */
        .project-status-green {
            animation: pulse-green 2s infinite;
        }

        .project-status-red {
            animation: pulse-red 2s infinite;
        }

        @keyframes pulse-green {
            0%, 100% {
                opacity: 1;
                box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
            }
            50% {
                opacity: 0.8;
                box-shadow: 0 0 0 4px rgba(34, 197, 94, 0);
            }
        }

        @keyframes pulse-red {
            0%, 100% {
                opacity: 1;
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
            }
            50% {
                opacity: 0.8;
                box-shadow: 0 0 0 4px rgba(239, 68, 68, 0);
            }
        }

        /* Quick actions and bottom area styling */
        #quick-ai-chat {
            position: relative;
            overflow: hidden;
        }

        #quick-ai-chat::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease-in-out;
        }

        #quick-ai-chat:hover::before {
            left: 100%;
        }

        /* Enhanced button styling for chat and todo */
        .grid.grid-cols-2 button {
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .grid.grid-cols-2 button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.4s ease-in-out;
        }

        .grid.grid-cols-2 button:hover::before {
            left: 100%;
        }

        /* Improved badge animations */
        .chat-badge, .todo-badge {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* Enhanced status indicator for collapsed sidebar */
        .sidebar[data-collapsed="true"] .collapsed-indicator {
            background: radial-gradient(circle, #ef4444 40%, #dc2626 100%);
            border: 2px solid white;
            box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
        }

        /* Floating AI Chat Input Styles */
        .floating-ai-chat {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .floating-ai-toggle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .floating-ai-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
        }

        .floating-ai-toggle.active {
            transform: rotate(45deg);
        }

        .floating-ai-input-container {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 380px;
            max-width: calc(100vw - 40px);
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px) scale(0.9);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #E5E7EB;
        }

        .floating-ai-input-container.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
        }

        .floating-ai-pulse {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 20px;
            height: 20px;
            background: #EF4444;
            border: 2px solid white;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
    </style>
</head>

<body class="bg-secondary text-foreground">

    <div id="main-layout" class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar bg-white border-r border-border flex flex-col" data-collapsed="false">
            <div class="flex items-center justify-center h-16 px-4 border-b border-border">
                <a href="#" class="flex items-center justify-center font-bold logo-container">
                    <div class="h-8 w-32 bg-no-repeat bg-contain logo-full" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;128&quot; height=&quot;32&quot; viewBox=&quot;0 0 128 32&quot; fill=&quot;none&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cpath d=&quot;M16 4L28 24H4L16 4Z&quot; fill=&quot;currentColor&quot;/%3E%3Ctext x=&quot;40&quot; y=&quot;24&quot; font-family=&quot;SF Pro Display&quot; font-size=&quot;24&quot; font-weight=&quot;600&quot; fill=&quot;currentColor&quot;%3EHorizAI%3C/text%3E%3C/svg%3E')"></div>
                    <div class="h-8 w-8 bg-no-repeat bg-contain logo-icon hidden" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;32&quot; height=&quot;32&quot; viewBox=&quot;0 0 32 32&quot; fill=&quot;none&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cpath d=&quot;M16 4L28 24H4L16 4Z&quot; fill=&quot;currentColor&quot;/%3E%3C/svg%3E')"></div>
                </a>
            </div>
            <nav class="flex-1 px-2 py-4 space-y-1">
                <a href="#"
                    class="tooltip-trigger flex items-center px-3 py-2 text-sm font-medium rounded-md bg-green-100 text-green-800 relative">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span class="nav-link-text">儀表板</span>
                    <div class="tooltip-content">儀表板</div>
                </a>
                <a href="#"
                    class="tooltip-trigger flex items-center px-3 py-2 text-sm font-medium rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700 relative">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                    </svg>
                    <span class="nav-link-text">專案管理</span>
                    <div class="tooltip-content">專案管理</div>
                </a>
                <a href="#"
                    class="tooltip-trigger flex items-center px-3 py-2 text-sm font-medium rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700 relative">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                    <span class="nav-link-text">任務管理</span>
                    <div class="tooltip-content">任務管理</div>
                </a>
                <a href="#"
                    class="tooltip-trigger flex items-center px-3 py-2 text-sm font-medium rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700 relative">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    <span class="nav-link-text">派工管理</span>
                    <div class="tooltip-content">派工管理</div>
                </a>
                <a href="#"
                    class="tooltip-trigger flex items-center px-3 py-2 text-sm font-medium rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700 relative">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.653-.125-1.274-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.653.125-1.274.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <span class="nav-link-text">客戶管理</span>
                    <div class="tooltip-content">客戶管理</div>
                </a>
                
                <!-- 我負責的專案列表 -->
                <div class="mt-6 mb-4">
                    <div class="tooltip-trigger px-3 py-2 relative">
                        <h3 class="nav-link-text text-xs font-semibold text-muted-foreground uppercase tracking-wider">負責專案</h3>
                        <div class="tooltip-content">負責專案</div>
                    </div>
                    <div class="space-y-1 mt-2" id="my-projects-list">
                        <!-- 台北信義豪宅 -->
                        <a href="#" class="tooltip-trigger group flex items-center px-3 py-1.5 text-sm rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700 relative">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-3 flex-shrink-0 group-hover:bg-green-600 project-status-green"></div>
                            <div class="flex-1 min-w-0">
                                <span class="nav-link-text truncate">台北信義豪宅</span>
                            </div>
                            <div class="tooltip-content">
                                台北信義豪宅
                                <span class="block text-xs text-green-600 mt-1">進行中 • 75% 完成</span>
                            </div>
                        </a>
                        
                        <!-- 新北三重商辦 -->
                        <a href="#" class="tooltip-trigger group flex items-center px-3 py-1.5 text-sm rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700 relative">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mr-3 flex-shrink-0 group-hover:bg-blue-600"></div>
                            <div class="flex-1 min-w-0">
                                <span class="nav-link-text truncate">新北三重商辦</span>
                            </div>
                            <div class="tooltip-content">
                                新北三重商辦
                                <span class="block text-xs text-blue-600 mt-1">進行中 • 100% 完成</span>
                            </div>
                        </a>
                        
                        <!-- 台中西區餐廳 -->
                        <a href="#" class="tooltip-trigger group flex items-center px-3 py-1.5 text-sm rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700 relative">
                            <div class="w-2 h-2 bg-red-500 rounded-full mr-3 flex-shrink-0 group-hover:bg-red-600 project-status-red"></div>
                            <div class="flex-1 min-w-0">
                                <span class="nav-link-text truncate">台中西區餐廳</span>
                            </div>
                            <div class="tooltip-content">
                                台中西區餐廳
                                <span class="block text-xs text-red-600 mt-1">延遲 • 10% 完成</span>
                            </div>
                        </a>
                        
                        <!-- 桃園辦公室 -->
                        <a href="#" class="tooltip-trigger group flex items-center px-3 py-1.5 text-sm rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700 relative">
                            <div class="w-2 h-2 bg-yellow-500 rounded-full mr-3 flex-shrink-0 group-hover:bg-yellow-600"></div>
                            <div class="flex-1 min-w-0">
                                <span class="nav-link-text truncate">桃園辦公室</span>
                            </div>
                            <div class="tooltip-content">
                                桃園辦公室
                                <span class="block text-xs text-yellow-600 mt-1">進行中 • 90% 完成</span>
                            </div>
                        </a>
                        
                        <!-- 高雄住宅 -->
                        <a href="#" class="tooltip-trigger group flex items-center px-3 py-1.5 text-sm rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700 relative">
                            <div class="w-2 h-2 bg-purple-500 rounded-full mr-3 flex-shrink-0 group-hover:bg-purple-600"></div>
                            <div class="flex-1 min-w-0">
                                <span class="nav-link-text truncate">高雄住宅</span>
                            </div>
                            <div class="tooltip-content">
                                高雄住宅
                                <span class="block text-xs text-purple-600 mt-1">進行中 • 60% 完成</span>
                            </div>
                        </a>
                    </div>
                </div>
            </nav>
            <div class="mt-auto px-2 py-4 border-t border-border sidebar-bottom-container">
                <!-- 快速操作 -->
                <div class="mt-auto mb-4">
                    <div class="tooltip-trigger px-3 py-2 relative">
                        <h4 class="nav-link-text text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">快速操作</h4>
                    </div>
                    
                    <button id="quick-ai-chat"
                        class="tooltip-trigger flex items-center px-3 py-2 text-sm font-medium rounded-md text-muted-foreground hover:bg-blue-50 hover:text-blue-700 relative w-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                        <div class="flex-1 text-left">
                            <span class="nav-link-text">AI助理</span>
                            <p class="nav-link-text text-xs text-muted-foreground">智能分析與建議</p>
                        </div>
                        <div class="tooltip-content">AI智能助理</div>
                    </button>
                </div>

                <!-- 聊天與待辦事項並列容器 -->
                <div class="grid grid-cols-2 gap-2 mb-3">
                    <button id="chat-toggle"
                        class="tooltip-trigger group flex flex-col items-center justify-center px-3 py-3 text-sm font-medium rounded-xl text-muted-foreground hover:bg-gradient-to-br hover:from-green-50 hover:to-emerald-50 hover:text-green-700 relative transition-all duration-200 border border-transparent hover:border-green-200 hover:shadow-sm has-notifications">
                        <div class="relative mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 flex-shrink-0 transition-transform duration-200 group-hover:scale-110" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
                            <span class="chat-badge absolute -top-1.5 -right-1.5 inline-flex items-center justify-center w-4 h-4 text-xs font-bold text-red-800 bg-red-100 rounded-full shadow-lg border-2 border-white group-hover:scale-110 transition-transform duration-200">3</span>
                        </div>
                        <span class="nav-link-text text-xs font-medium">聊天</span>
                        <div class="collapsed-indicator"></div>
                        <div class="tooltip-content">
                            即時聊天
                            <span class="inline-flex items-center justify-center w-5 h-5 ml-2 text-xs font-medium text-white bg-red-500 rounded-full">3</span>
                        </div>
                    </button>
                    
                    <button id="todo-toggle"
                        class="tooltip-trigger group flex flex-col items-center justify-center px-3 py-3 text-sm font-medium rounded-xl text-muted-foreground hover:bg-gradient-to-br hover:from-orange-50 hover:to-amber-50 hover:text-orange-700 relative transition-all duration-200 border border-transparent hover:border-orange-200 hover:shadow-sm has-notifications">
                        <div class="relative mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 flex-shrink-0 transition-transform duration-200 group-hover:scale-110" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                            </svg>
                            <span class="todo-badge absolute -top-1.5 -right-1.5 inline-flex items-center justify-center w-4 h-4 text-xs font-bold text-orange-800 bg-orange-100 rounded-full shadow-lg border-2 border-white group-hover:scale-110 transition-transform duration-200">5</span>
                        </div>
                        <span class="nav-link-text text-xs font-medium">待辦</span>
                        <div class="collapsed-indicator"></div>
                        <div class="tooltip-content">
                            待辦事項
                            <span class="inline-flex items-center justify-center w-5 h-5 ml-2 text-xs font-medium text-white bg-orange-500 rounded-full">5</span>
                        </div>
                    </button>
                </div>
                <!-- 使用者區域 -->
                <div class="border-t border-gray-100 pt-3 mt-3">
                    <!-- 使用者資訊與選項 -->
                    <div class="relative" id="user-menu-container">
                        <button id="user-menu-toggle" class="tooltip-trigger user-avatar-container w-full flex items-center px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50 hover:text-gray-800 relative transition-all duration-200">
                            <img class="h-8 w-8 rounded-full flex-shrink-0"
                                src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=80&h=80&fit=crop&q=80"
                                alt="User Avatar">
                            <div class="ml-3 user-avatar-info nav-link-text flex-1 text-left">
                                <p class="text-sm font-medium text-gray-700">John Doe</p>
                                <p class="text-xs text-gray-500"><EMAIL></p>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 nav-link-text flex-shrink-0 transition-transform duration-200 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                            <div class="tooltip-content">John Doe<br/><EMAIL><br/>點擊查看選項</div>
                        </button>
                        
                        <!-- 使用者選單 -->
                        <div id="user-menu" class="absolute bottom-full left-0 right-0 mb-2 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible transform scale-95 transition-all duration-200 z-50">
                            <div class="py-2">
                                <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-800 transition-colors duration-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    個人資料
                                </a>
                                <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-800 transition-colors duration-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    設定
                                </a>
                                <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-800 transition-colors duration-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    說明中心
                                </a>
                                <div class="border-t border-gray-100 my-1"></div>
                                <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-500 hover:bg-red-50 hover:text-red-600 transition-colors duration-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                    </svg>
                                    登出
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>



        <!-- Chat Sidebar -->
        <aside id="chat-sidebar" class="chat-sidebar bg-white border-r border-border flex flex-col hidden flex-shrink-0 floating">
            <!-- Chat List View -->
            <div id="chat-list-view" class="flex flex-col h-full">
                <!-- Chat Header -->
                <div class="flex items-center justify-between h-16 px-4 border-b border-border">
                    <h2 class="text-lg font-semibold">即時聊天</h2>
                    <div class="flex items-center gap-2">
                        <button id="chat-pin-btn" class="sidebar-tooltip-trigger pin-btn p-1 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 2L16 10" />
                            </svg>
                            <div class="sidebar-tooltip-content">固定側欄</div>
                        </button>
                        <button id="new-chat-btn" class="sidebar-tooltip-trigger p-1 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            <div class="sidebar-tooltip-content">新增對話</div>
                        </button>
                        <button id="chat-close" class="sidebar-tooltip-trigger p-1 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            <div class="sidebar-tooltip-content">關閉聊天</div>
                        </button>
                    </div>
                </div>
                
                <!-- Chat Conversations List -->
                <div class="flex-1 overflow-y-auto">
                    <div class="p-4">
                        <h3 class="text-sm font-medium text-muted-foreground mb-3">最近對話</h3>
                        <div class="space-y-2" id="chat-conversations">
                

                            <!-- Conversation Items -->
                            <div class="chat-conversation-item flex items-center p-3 rounded-lg hover:bg-green-50 cursor-pointer" data-chat-id="1" data-user-name="李設計師" data-user-avatar="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80">
                                <img class="h-10 w-10 rounded-full flex-shrink-0" 
                                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80" 
                                    alt="李設計師">
                                <div class="ml-3 flex-1 min-w-0">
                                    <p class="text-sm font-medium text-foreground truncate">李設計師</p>
                                    <p class="text-xs text-muted-foreground truncate">關於台北信義豪宅的設計方案...</p>
                                </div>
                                <div class="flex flex-col items-end">
                                    <span class="text-xs text-muted-foreground">14:32</span>
                                    <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-green-600 rounded-full mt-1">2</span>
                                </div>
                            </div>
                            
                            <div class="chat-conversation-item flex items-center p-3 rounded-lg hover:bg-green-50 cursor-pointer" data-chat-id="2" data-user-name="王專案經理" data-user-avatar="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&q=80">
                                <img class="h-10 w-10 rounded-full flex-shrink-0" 
                                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&q=80" 
                                    alt="王專案經理">
                                <div class="ml-3 flex-1 min-w-0">
                                    <p class="text-sm font-medium text-foreground truncate">王專案經理</p>
                                    <p class="text-xs text-muted-foreground truncate">明天的會議準備好了嗎？</p>
                                </div>
                                <div class="flex flex-col items-end">
                                    <span class="text-xs text-muted-foreground">昨天</span>
                                </div>
                            </div>
                            
                            <div class="chat-conversation-item flex items-center p-3 rounded-lg hover:bg-green-50 cursor-pointer" data-chat-id="3" data-user-name="陳工程師" data-user-avatar="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80">
                                <img class="h-10 w-10 rounded-full flex-shrink-0" 
                                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80" 
                                    alt="陳工程師">
                                <div class="ml-3 flex-1 min-w-0">
                                    <p class="text-sm font-medium text-foreground truncate">陳工程師</p>
                                    <p class="text-xs text-muted-foreground truncate">工程進度報告已完成</p>
                                </div>
                                <div class="flex flex-col items-end">
                                    <span class="text-xs text-muted-foreground">週一</span>
                                    <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-red-500 rounded-full mt-1">1</span>
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>

            <!-- Team Members View (for starting new conversations) -->
            <div id="team-members-view" class="flex flex-col h-full hidden">
                <!-- Header -->
                <div class="flex items-center justify-between h-16 px-4 border-b border-border">
                    <div class="flex items-center gap-2">
                        <button id="back-to-chats" class="p-1 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700" title="返回對話">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <h2 class="text-lg font-semibold">選擇聊天對象</h2>
                    </div>
                    <button id="chat-close-2" class="sidebar-tooltip-trigger p-1 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        <div class="sidebar-tooltip-content">關閉聊天</div>
                    </button>
                </div>
                
                <!-- Team Members List -->
                <div class="flex-1 overflow-y-auto">
                    <div class="p-4">
                        <h3 class="text-sm font-medium text-muted-foreground mb-3">團隊成員</h3>
                        <div class="space-y-2" id="team-members">
                            <div class="team-member-item flex items-center p-3 rounded-lg hover:bg-green-50 cursor-pointer" data-user-name="李設計師" data-user-avatar="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80" data-user-role="主任設計師">
                                <img class="h-10 w-10 rounded-full flex-shrink-0" 
                                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80" 
                                    alt="李設計師">
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-foreground">李設計師</p>
                                    <p class="text-xs text-muted-foreground">主任設計師 · 有對話記錄</p>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-green-600 rounded-full">2</span>
                                    <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                                </div>
                            </div>
                            
                            <div class="team-member-item flex items-center p-3 rounded-lg hover:bg-green-50 cursor-pointer" data-user-name="王專案經理" data-user-avatar="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&q=80" data-user-role="專案經理">
                                <img class="h-10 w-10 rounded-full flex-shrink-0" 
                                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&q=80" 
                                    alt="王專案經理">
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-foreground">王專案經理</p>
                                    <p class="text-xs text-muted-foreground">專案經理 · 有對話記錄</p>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                                </div>
                            </div>
                            
                            <div class="team-member-item flex items-center p-3 rounded-lg hover:bg-green-50 cursor-pointer" data-user-name="陳工程師" data-user-avatar="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80" data-user-role="工程監督">
                                <img class="h-10 w-10 rounded-full flex-shrink-0" 
                                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80" 
                                    alt="陳工程師">
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-foreground">陳工程師</p>
                                    <p class="text-xs text-muted-foreground">工程監督 · 有對話記錄</p>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-red-500 rounded-full">1</span>
                                    <span class="w-3 h-3 bg-yellow-500 rounded-full"></span>
                                </div>
                            </div>
                            
                            <div class="team-member-item flex items-center p-3 rounded-lg hover:bg-green-50 cursor-pointer" data-user-name="張助理" data-user-avatar="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&q=80" data-user-role="行政助理">
                                <img class="h-10 w-10 rounded-full flex-shrink-0" 
                                    src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&q=80" 
                                    alt="張助理">
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-foreground">張助理</p>
                                    <p class="text-xs text-muted-foreground">行政助理</p>
                                </div>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 bg-gray-400 rounded-full"></span>
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>

            <!-- Chat Messages View -->
            <div id="chat-messages-view" class="flex flex-col h-full hidden">
                <!-- Chat Header -->
                <div class="flex items-center justify-between h-16 px-4 border-b border-border">
                    <div class="flex items-center gap-3">
                        <button id="back-to-list" class="p-1 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700" title="返回對話列表">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <img id="current-chat-avatar" class="h-8 w-8 rounded-full flex-shrink-0" src="" alt="">
                        <div>
                            <h2 id="current-chat-name" class="text-lg font-semibold"></h2>
                        </div>
                    </div>
                    <button id="chat-close-3" class="sidebar-tooltip-trigger p-1 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        <div class="sidebar-tooltip-content">關閉聊天</div>
                    </button>
                </div>
                
                <!-- Chat Messages -->
                <div id="chat-messages-container" class="flex-1 overflow-y-auto p-4 space-y-4">
                    <!-- Messages will be dynamically loaded here -->
                </div>
                
                <!-- Chat Input -->
                <div class="p-4 border-t border-border">
                    <div class="flex items-center gap-2">
                        <input id="chat-input" type="text" placeholder="輸入訊息..." 
                            class="flex-1 px-3 py-2 text-sm border border-input rounded-md bg-transparent focus:outline-none focus:ring-2 focus:ring-green-500">
                        <button id="send-message" class="p-2 text-white bg-green-600 rounded-md hover:bg-green-700 flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Todo Sidebar -->
        <aside id="todo-sidebar" class="todo-sidebar bg-white border-r border-border flex flex-col hidden flex-shrink-0 floating">
            <!-- Todo List View -->
            <div id="todo-list-view" class="flex flex-col h-full">
                <!-- Todo Header -->
                <div class="flex items-center justify-between h-16 px-4 border-b border-border">
                    <h2 class="text-lg font-semibold">待辦事項</h2>
                    <div class="flex items-center gap-2">
                        <button id="todo-pin-btn" class="sidebar-tooltip-trigger pin-btn p-1 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 2L16 10" />
                            </svg>
                            <div class="sidebar-tooltip-content">固定側欄</div>
                        </button>
                        <button id="new-todo-btn" class="sidebar-tooltip-trigger p-1 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            <div class="sidebar-tooltip-content">新增待辦</div>
                        </button>
                        <button id="todo-close" class="sidebar-tooltip-trigger p-1 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            <div class="sidebar-tooltip-content">關閉待辦</div>
                        </button>
                    </div>
                </div>
                
                <!-- Todo Items List -->
                <div class="flex-1 overflow-y-auto">
                    <div class="p-4">
                        <h3 class="text-sm font-medium text-muted-foreground mb-3">今日待辦</h3>
                        <div class="space-y-3" id="todo-items">
                            <!-- Todo Items -->
                            <div class="todo-item flex items-start p-3 rounded-lg border border-border hover:bg-green-50">
                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-foreground">完成台北信義豪宅設計方案</p>
                                    <p class="text-xs text-muted-foreground mt-1">截止日期：今日 18:00</p>
                                    <div class="flex items-center mt-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">高優先級</span>
                                        <span class="ml-2 text-xs text-muted-foreground">設計部門</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="todo-item flex items-start p-3 rounded-lg border border-border hover:bg-green-50">
                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-foreground">準備明天的客戶會議</p>
                                    <p class="text-xs text-muted-foreground mt-1">截止日期：明日 09:00</p>
                                    <div class="flex items-center mt-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">中優先級</span>
                                        <span class="ml-2 text-xs text-muted-foreground">業務部門</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="todo-item flex items-start p-3 rounded-lg border border-border hover:bg-green-50">
                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded" checked>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-muted-foreground line-through">更新專案進度報告</p>
                                    <p class="text-xs text-muted-foreground mt-1">已完成</p>
                                    <div class="flex items-center mt-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">已完成</span>
                                        <span class="ml-2 text-xs text-muted-foreground">專案管理</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="todo-item flex items-start p-3 rounded-lg border border-border hover:bg-green-50">
                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-foreground">審核工程預算</p>
                                    <p class="text-xs text-muted-foreground mt-1">截止日期：本週五</p>
                                    <div class="flex items-center mt-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">低優先級</span>
                                        <span class="ml-2 text-xs text-muted-foreground">財務部門</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="todo-item flex items-start p-3 rounded-lg border border-border hover:bg-green-50">
                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-foreground">聯絡供應商確認材料</p>
                                    <p class="text-xs text-muted-foreground mt-1">截止日期：下週一</p>
                                    <div class="flex items-center mt-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">中優先級</span>
                                        <span class="ml-2 text-xs text-muted-foreground">採購部門</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Backdrop -->
        <div id="backdrop" class="backdrop fixed inset-0 bg-black bg-opacity-50 hidden"></div>

        <!-- AI Chat Modal -->
        <div id="ai-chat-modal" class="fixed inset-0 z-50 flex items-center justify-center p-4 hidden">
            <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
            <div class="relative bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 w-full max-w-3xl h-[85vh] flex flex-col transform transition-all duration-300 ease-out">
                <!-- Chat Header -->
                <div class="flex items-center justify-between p-5 border-b border-gray-200/50">
                    <div class="flex items-center space-x-3">
                        <div class="w-9 h-9 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                        </div>
                        <div class="flex items-baseline space-x-2">
                            <h3 class="text-lg font-semibold text-gray-800">HorizAI 智能助理</h3>
                            <div class="flex items-center space-x-1">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse shadow-sm"></div>
                                <span class="text-xs text-green-600 font-medium">在線</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1">
                        <button id="clear-chat" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100/70 rounded-xl transition-all duration-200" title="清除對話">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                        <button id="close-ai-chat" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100/70 rounded-xl transition-all duration-200" title="關閉對話">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Chat Messages Container -->
                <div id="ai-chat-messages" class="flex-1 overflow-y-auto p-5 space-y-4 bg-gradient-to-b from-gray-50/50 to-white/50">
                    <!-- Welcome Message -->
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-md border border-gray-200/50">
                                <p class="text-sm text-gray-800 font-medium">👋 您好！我是HorizAI智能助理，很高興為您服務！</p>
                                <p class="text-sm text-gray-600 mt-2 font-medium">我可以幫您：</p>
                                <div class="text-sm text-gray-600 mt-2 space-y-1">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                                        <span>分析專案進度和風險</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                                        <span>優化工作流程和成本</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                                        <span>生成報告和建議</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                                        <span>回答專案相關問題</span>
                                    </div>
                                </div>
                            </div>
                            <span class="text-xs text-gray-500 mt-2 block font-medium">剛剛</span>
                        </div>
                    </div>
                </div>

                <!-- Chat Input and Quick Actions -->
                <div class="px-5 py-4 border-t border-gray-200/50 bg-white/70 backdrop-blur-sm rounded-b-2xl">
                    <div class="quick-actions-container mb-4">
                        <div class="flex items-center space-x-2 mb-3">
                            <span class="text-sm text-gray-600 font-medium">快速操作</span>
                            <div class="flex-1 h-px bg-gradient-to-r from-gray-200 to-transparent"></div>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <button class="quick-action-btn px-4 py-2 text-xs font-medium bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 shadow-sm transition-all duration-200 transform hover:scale-105" data-action="分析台北信義豪宅專案進度">
                                📊 專案分析
                            </button>
                            <button class="quick-action-btn px-4 py-2 text-xs font-medium bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 shadow-sm transition-all duration-200 transform hover:scale-105" data-action="生成本週工作報告">
                                📋 生成報告
                            </button>
                            <button class="quick-action-btn px-4 py-2 text-xs font-medium bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 shadow-sm transition-all duration-200 transform hover:scale-105" data-action="建議成本優化方案">
                                💰 成本優化
                            </button>
                            <button class="quick-action-btn px-4 py-2 text-xs font-medium bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-orange-600 hover:to-orange-700 shadow-sm transition-all duration-200 transform hover:scale-105" data-action="檢查專案風險">
                                ⚠️ 風險評估
                            </button>
                        </div>
                    </div>
                    <div class="flex items-stretch space-x-3">
                        <div class="flex-1 relative">
                            <textarea 
                                id="ai-chat-input" 
                                placeholder="輸入您的問題或需求..." 
                                rows="1"
                                class="w-full pl-5 pr-12 py-4 border-0 rounded-full resize-none focus:outline-none bg-gray-100/80 backdrop-blur-sm shadow-inner transition-all duration-200 placeholder-gray-500 hover:bg-gray-100 focus:bg-white focus:shadow-lg min-h-[52px] max-h-[120px]"
                                style="box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);"
                            ></textarea>
                        </div>
                        <button 
                            id="send-ai-message" 
                            class="w-[52px] h-[52px] bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:ring-offset-2 flex items-center justify-center transition-all duration-200 transform hover:scale-110 shadow-lg hover:shadow-xl flex-shrink-0"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                        </button>
                    </div>
                    <div class="flex items-center justify-between mt-3 text-xs text-gray-500">
                        <span class="flex items-center space-x-1">
                            <kbd class="px-2 py-1 bg-gray-100 rounded text-gray-600 font-mono text-xs">Enter</kbd>
                            <span>發送</span>
                            <span class="text-gray-400">•</span>
                            <kbd class="px-2 py-1 bg-gray-100 rounded text-gray-600 font-mono text-xs">Shift+Enter</kbd>
                            <span>換行</span>
                        </span>
                        <span id="char-count" class="font-medium">0/1000</span>
                    </div>
                </div>
            </div>
        </div>



        <!-- Main Content -->
        <main id="main-content" class="flex-1 flex flex-col overflow-hidden min-w-0 transition-all duration-300">
            <!-- Top Header -->
            <header class="bg-white border-b border-border">
                <!-- Top Row: Toggle + Search + Button -->
                <div class="flex items-center justify-between h-16 px-4 sm:px-6">
                    <div class="flex items-center gap-4">
                        <button id="sidebar-toggle"
                            class="p-2 rounded-md text-muted-foreground hover:bg-green-50 hover:text-green-700 flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6h16M4 12h16M4 18h7" />
                            </svg>
                        </button>
                        <!-- Breadcrumb -->
                        <nav class="text-sm hidden sm:block" aria-label="Breadcrumb">
                            <ol class="list-none p-0 inline-flex">
                                <li class="flex items-center">
                                    <a href="#" class="text-muted-foreground hover:text-foreground">工作區</a>
                                    <svg class="h-4 w-4 text-muted-foreground mx-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </li>
                                <li class="flex items-center">
                                    <span class="text-foreground font-medium">儀表板</span>
                                </li>
                            </ol>
                        </nav>
                    </div>
                    <div class="flex items-center gap-2 sm:gap-4 min-w-0">
                        <div class="relative">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="h-5 w-5 absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <input type="text" placeholder="搜尋專案..."
                                class="w-48 sm:w-64 pl-10 pr-4 py-2 text-sm border border-input rounded-md bg-transparent focus:outline-none focus:ring-2 focus:ring-green-500">
                        </div>
                        <!-- Notification Icon -->
                        <button class="relative p-2 text-muted-foreground hover:text-foreground hover:bg-green-50 rounded-md flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                    d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            <!-- Notification Badge -->
                            <span class="notification-badge absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">2</span>
                        </button>
                        <button
                            class="flex items-center justify-center gap-2 px-3 sm:px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            <span class="hidden sm:inline">新增專案</span>
                            <span class="sm:hidden">新增</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Scrollable Content Area -->
            <div class="flex-1 overflow-y-auto p-4 sm:p-6 bg-white space-y-8">
                <!-- 頁面標題區域 -->
                <header class="flex justify-between items-center">
                    <div class="flex flex-col space-y-1">
                        <h1 class="text-2xl font-semibold tracking-tight text-zinc-900 dark:text-zinc-50">歡迎回來，John Doe</h1>
                        <p class="text-sm text-zinc-500 dark:text-zinc-400">今天是 <span class="font-medium">2024年11月14日 週四</span>，您有 <span class="text-green-600 font-medium">3個重要任務</span> 需要處理</p>
                    </div>


                </header>

                <!-- AI助理綜合儀表板 -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div class="flex items-center justify-between p-4 cursor-pointer" id="ai-assistant-header">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">HorizAI 智能助理</h3>
                                <p class="text-sm text-blue-700 dark:text-blue-300">正在分析台北信義豪宅工地照片 - 75% 完成</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <div class="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                                運行中
                            </span>
                            <button id="open-ai-chat" class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                                與AI對話
                            </button>
                            <button id="ai-assistant-toggle" class="p-2 text-blue-600 hover:bg-blue-100 rounded-md transition-colors duration-200">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- AI動態活動流 -->
                    <div id="ai-assistant-content" class="px-4 pb-4 transition-all duration-300 ease-in-out">
                        <div class="space-y-2">
                            <div class="flex items-center space-x-3 text-sm p-2 bg-white/50 rounded-md">
                                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span class="text-zinc-700 flex-1">正在分析台北信義豪宅專案的工地照片</span>
                                <span class="text-xs text-zinc-500">2分鐘前</span>
                            </div>
                            <div class="flex items-center space-x-3 text-sm p-2 bg-white/50 rounded-md">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="text-zinc-700 flex-1">已生成新北三重商辦的進度報告草稿</span>
                                <span class="text-xs text-zinc-500">15分鐘前</span>
                            </div>
                            <div class="flex items-center space-x-3 text-sm p-2 bg-white/50 rounded-md">
                                <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                                <span class="text-zinc-700 flex-1">完成材料成本優化建議</span>
                                <span class="text-xs text-zinc-500">1小時前</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 統計數據和快速操作 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- 進行中專案 -->
                    <div class="bg-card text-card-foreground rounded-lg border border-border group hover:shadow-lg transition-all duration-300">
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <div class="space-y-1">
                                    <h3 class="text-sm font-medium text-zinc-500">進行中專案</h3>
                                    <p class="text-2xl font-semibold tracking-tight text-zinc-900">12</p>
                                </div>
                                <div class="rounded-full p-2 bg-green-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                    </svg>
                                </div>
                            </div>
                            <p class="text-xs text-zinc-500 mt-2">+2 比上週</p>
                        </div>
                    </div>

                    <!-- 待我審批 -->
                    <div class="bg-card text-card-foreground rounded-lg border border-border group hover:shadow-lg transition-all duration-300">
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <div class="space-y-1">
                                    <h3 class="text-sm font-medium text-zinc-500">待我審批</h3>
                                    <p class="text-2xl font-semibold tracking-tight text-zinc-900">8</p>
                                </div>
                                <div class="rounded-full p-2 bg-orange-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </div>
                            <p class="text-xs text-zinc-500 mt-2">需緊急處理：3項</p>
                        </div>
                    </div>

                    <!-- 本週完成 -->
                    <div class="bg-card text-card-foreground rounded-lg border border-border group hover:shadow-lg transition-all duration-300">
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <div class="space-y-1">
                                    <h3 class="text-sm font-medium text-zinc-500">本週完成</h3>
                                    <p class="text-2xl font-semibold tracking-tight text-zinc-900">24</p>
                                </div>
                                <div class="rounded-full p-2 bg-blue-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </div>
                            <p class="text-xs text-zinc-500 mt-2">效率提升 15%</p>
                        </div>
                    </div>

                    <!-- AI洞察報告 -->
                    <div class="bg-card text-card-foreground rounded-lg border border-border group hover:shadow-lg transition-all duration-300">
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <div class="space-y-1">
                                    <h3 class="text-sm font-medium text-zinc-500">AI洞察報告</h3>
                                    <p class="text-2xl font-semibold tracking-tight text-zinc-900">15</p>
                                </div>
                                <div class="rounded-full p-2 bg-purple-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                </div>
                            </div>
                            <p class="text-xs text-zinc-500 mt-2">本週新增：3個風險警示</p>
                        </div>
                    </div>
                </div>

                <!-- 今日優先任務與最新動態 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 今日優先任務 -->
                    <div class="bg-card text-card-foreground rounded-lg border border-border">
                        <div class="p-6 border-b border-border">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">今日優先任務</h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">3 項重要</span>
                            </div>
                        </div>
                        <div class="p-6 space-y-4">
                            <div class="flex items-start space-x-3 p-3 border border-red-200 rounded-lg bg-red-50">
                                <div class="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                                <div class="flex-1 flex justify-between items-center">
                                    <div>
                                        <h4 class="text-sm font-medium text-red-900">台北信義豪宅廚房設計確認</h4>
                                        <p class="text-xs text-red-700 mt-1">截止時間：今日 18:00 • 可能影響施工進度</p>
                                    </div>
                                    <button class="px-3 py-1 bg-red-600 text-white text-xs rounded-md hover:bg-red-700 flex-shrink-0">立即處理</button>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3 p-3 border border-orange-200 rounded-lg bg-orange-50">
                                <div class="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                                <div class="flex-1 flex justify-between items-center">
                                    <div>
                                        <h4 class="text-sm font-medium text-orange-900">新北三重商辦進度會議</h4>
                                        <p class="text-xs text-orange-700 mt-1">時間：今日 15:00 • 會議室B</p>
                                    </div>
                                    <button class="px-3 py-1 bg-orange-600 text-white text-xs rounded-md hover:bg-orange-700 flex-shrink-0">準備資料</button>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3 p-3 border border-blue-200 rounded-lg bg-blue-50">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                <div class="flex-1 flex justify-between items-center">
                                    <div>
                                        <h4 class="text-sm font-medium text-blue-900">材料採購方案審核</h4>
                                        <p class="text-xs text-blue-700 mt-1">AI建議節省15%成本 • 3個方案待選擇</p>
                                    </div>
                                    <button class="px-3 py-1 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 flex-shrink-0">查看詳情</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 專案動態與通知 -->
                    <div class="bg-card text-card-foreground rounded-lg border border-border">
                        <div class="p-6 border-b border-border">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">專案動態</h3>
                                <button class="text-sm text-green-600 hover:text-green-700">查看全部</button>
                            </div>
                        </div>
                        <div class="p-6 space-y-4 max-h-80 overflow-y-auto">
                            <div class="flex items-start space-x-3">
                                <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80" alt="李設計師">
                                <div class="flex-1">
                                    <p class="text-sm"><span class="font-medium">李設計師</span> 上傳了台北信義豪宅的最新設計圖</p>
                                    <p class="text-xs text-zinc-500">10分鐘前</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm">桃園辦公室專案進度 <span class="font-medium text-green-600">已完成90%</span></p>
                                    <p class="text-xs text-zinc-500">30分鐘前</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&q=80" alt="王專案經理">
                                <div class="flex-1">
                                    <p class="text-sm"><span class="font-medium">王專案經理</span> 審批了材料採購申請</p>
                                    <p class="text-xs text-zinc-500">1小時前</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4.5c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm">台中餐廳專案出現 <span class="font-medium text-red-600">進度延遲警告</span></p>
                                    <p class="text-xs text-zinc-500">2小時前</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm">AI助理生成了新的成本優化建議報告</p>
                                    <p class="text-xs text-zinc-500">3小時前</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 視覺化進度追蹤與待我審批區域 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 左側工地照片牆區域 -->
                    <div class="lg:col-span-2 space-y-8">
                        <!-- 工地照片牆 -->
                        <div class="bg-card text-card-foreground rounded-lg border border-border">
                            <div class="p-6 border-b border-border">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-semibold">工地進度追蹤</h3>
                                        <p class="text-sm text-muted-foreground">AI實時分析工地照片與進度</p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <div class="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                                            AI分析中
                                        </span>
                                        <button class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                            <span>上傳照片</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <!-- 工地照片卡片 -->
                                    <div class="relative group">
                                        <img class="w-full h-40 object-cover rounded-lg" 
                                            src="https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=400&h=300&fit=crop" 
                                            alt="台北信義豪宅 - 廚房施工">
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                                            <button class="px-3 py-1 bg-white text-black rounded-md text-sm font-medium">查看分析</button>
                                        </div>
                                        <div class="absolute top-2 left-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">75% 完成</span>
                                        </div>
                                        <div class="absolute top-2 right-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">AI已分析</span>
                                        </div>
                                        <div class="mt-2">
                                            <h4 class="text-sm font-medium text-zinc-900">台北信義豪宅 - 廚房</h4>
                                            <p class="text-xs text-zinc-500">今日 14:30 • AI檢測：進度正常</p>
                                        </div>
                                    </div>
                                    
                                    <div class="relative group">
                                        <img class="w-full h-40 object-cover rounded-lg" 
                                            src="https://images.unsplash.com/photo-1484154218962-a197022b5858?w=400&h=300&fit=crop" 
                                            alt="新北三重商辦 - 大廳">
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                                            <button class="px-3 py-1 bg-white text-black rounded-md text-sm font-medium">查看分析</button>
                                        </div>
                                        <div class="absolute top-2 left-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">100% 完成</span>
                                        </div>
                                        <div class="absolute top-2 right-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">AI已分析</span>
                                        </div>
                                        <div class="mt-2">
                                            <h4 class="text-sm font-medium text-zinc-900">新北三重商辦 - 大廳</h4>
                                            <p class="text-xs text-zinc-500">昨日 16:45 • AI檢測：品質優良</p>
                                        </div>
                                    </div>
                                    
                                    <div class="relative group">
                                        <img class="w-full h-40 object-cover rounded-lg" 
                                            src="https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?w=400&h=300&fit=crop" 
                                            alt="台中西區餐廳 - 用餐區">
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                                            <button class="px-3 py-1 bg-white text-black rounded-md text-sm font-medium">查看分析</button>
                                        </div>
                                        <div class="absolute top-2 left-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">10% 完成</span>
                                        </div>
                                        <div class="absolute top-2 right-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">需關注</span>
                                        </div>
                                        <div class="mt-2">
                                            <h4 class="text-sm font-medium text-zinc-900">台中西區餐廳 - 用餐區</h4>
                                            <p class="text-xs text-zinc-500">3天前 09:15 • AI檢測：進度延遲</p>
                                        </div>
                                    </div>
                                    
                                    <div class="relative group">
                                        <img class="w-full h-40 object-cover rounded-lg" 
                                            src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop" 
                                            alt="高雄住宅 - 浴室">
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                                            <button class="px-3 py-1 bg-white text-black rounded-md text-sm font-medium">查看分析</button>
                                        </div>
                                        <div class="absolute top-2 left-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">60% 完成</span>
                                        </div>
                                        <div class="absolute top-2 right-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">分析中</span>
                                        </div>
                                        <div class="mt-2">
                                            <h4 class="text-sm font-medium text-zinc-900">高雄住宅 - 浴室</h4>
                                            <p class="text-xs text-zinc-500">1小時前 • AI正在分析中...</p>
                                        </div>
                                    </div>
                                    
                                    <div class="relative group">
                                        <img class="w-full h-40 object-cover rounded-lg" 
                                            src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop" 
                                            alt="桃園辦公室 - 會議室">
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                                            <button class="px-3 py-1 bg-white text-black rounded-md text-sm font-medium">查看分析</button>
                                        </div>
                                        <div class="absolute top-2 left-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">90% 完成</span>
                                        </div>
                                        <div class="absolute top-2 right-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">AI已分析</span>
                                        </div>
                                        <div class="mt-2">
                                            <h4 class="text-sm font-medium text-zinc-900">桃園辦公室 - 會議室</h4>
                                            <p class="text-xs text-zinc-500">昨日 11:20 • AI檢測：即將完工</p>
                                        </div>
                                    </div>
                                    
                                    <div class="relative group">
                                        <img class="w-full h-40 object-cover rounded-lg" 
                                            src="https://images.unsplash.com/photo-1615874694520-474822394e73?w=400&h=300&fit=crop" 
                                            alt="台南店面 - 外觀">
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                                            <button class="px-3 py-1 bg-white text-black rounded-md text-sm font-medium">查看分析</button>
                                        </div>
                                        <div class="absolute top-2 left-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">45% 完成</span>
                                        </div>
                                        <div class="absolute top-2 right-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">AI已分析</span>
                                        </div>
                                        <div class="mt-2">
                                            <h4 class="text-sm font-medium text-zinc-900">台南店面 - 外觀</h4>
                                            <p class="text-xs text-zinc-500">2天前 15:00 • AI檢測：按計畫進行</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右側待我審批區域 -->
                    <div class="lg:col-span-1 space-y-8">
                        <!-- 待我審批列表 -->
                        <div class="bg-card text-card-foreground rounded-lg border border-border">
                            <div class="p-6 border-b border-border">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-semibold">待我審批</h3>
                                        <p class="text-sm text-muted-foreground">AI生成內容等待確認</p>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">8 項待審</span>
                                </div>
                            </div>
                            <div class="p-6 space-y-4">
                                <!-- AI生成報告審批 -->
                                <div class="p-4 border border-orange-200 rounded-lg bg-orange-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="text-sm font-medium text-orange-900">台北信義豪宅進度報告</h4>
                                            <p class="text-xs text-orange-700 mt-1">AI已生成本週工程進度分析報告，包含風險評估和建議</p>
                                            <div class="flex items-center space-x-2 mt-3">
                                                <button class="px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700">批准</button>
                                                <button class="px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300">修改</button>
                                                <button class="px-3 py-1 bg-red-100 text-red-700 text-xs rounded-md hover:bg-red-200">拒絕</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 成本優化建議 -->
                                <div class="p-4 border border-blue-200 rounded-lg bg-blue-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="text-sm font-medium text-blue-900">材料採購優化方案</h4>
                                            <p class="text-xs text-blue-700 mt-1">AI分析發現可節省15%成本的替代供應商方案</p>
                                            <div class="flex items-center space-x-2 mt-3">
                                                <button class="px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700">採用</button>
                                                <button class="px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300">查看詳情</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 施工排程建議 -->
                                <div class="p-4 border border-purple-200 rounded-lg bg-purple-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="text-sm font-medium text-purple-900">新北三重排程調整</h4>
                                            <p class="text-xs text-purple-700 mt-1">AI建議調整施工順序，預計提前2天完工</p>
                                            <div class="flex items-center space-x-2 mt-3">
                                                <button class="px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700">確認</button>
                                                <button class="px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300">討論</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 風險預警 -->
                                <div class="p-4 border border-red-200 rounded-lg bg-red-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4.5c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="text-sm font-medium text-red-900">台中餐廳風險警告</h4>
                                            <p class="text-xs text-red-700 mt-1">AI檢測到進度嚴重延遲，建議立即介入處理</p>
                                            <div class="flex items-center space-x-2 mt-3">
                                                <button class="px-3 py-1 bg-red-600 text-white text-xs rounded-md hover:bg-red-700">緊急處理</button>
                                                <button class="px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300">查看原因</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 品質檢測結果 -->
                                <div class="p-4 border border-green-200 rounded-lg bg-green-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="text-sm font-medium text-green-900">桃園辦公室品質報告</h4>
                                            <p class="text-xs text-green-700 mt-1">AI檢測品質達標，建議進入下階段施工</p>
                                            <div class="flex items-center space-x-2 mt-3">
                                                <button class="px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700">同意</button>
                                                <button class="px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300">查看詳情</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const sidebar = document.getElementById('sidebar');
            const toggleButton = document.getElementById('sidebar-toggle');
            const chatSidebar = document.getElementById('chat-sidebar');
            const todoSidebar = document.getElementById('todo-sidebar');
            const backdrop = document.getElementById('backdrop');
            const chatToggle = document.getElementById('chat-toggle');
            const todoToggle = document.getElementById('todo-toggle');
            const chatClose = document.getElementById('chat-close');
            const chatClose2 = document.getElementById('chat-close-2');
            const chatClose3 = document.getElementById('chat-close-3');
            const todoClose = document.getElementById('todo-close');
            const chatPinBtn = document.getElementById('chat-pin-btn');
            const todoPinBtn = document.getElementById('todo-pin-btn');

            // Chat views
            const chatListView = document.getElementById('chat-list-view');
            const teamMembersView = document.getElementById('team-members-view');
            const chatMessagesView = document.getElementById('chat-messages-view');

            // Navigation buttons
            const newChatBtn = document.getElementById('new-chat-btn');
            const backToChats = document.getElementById('back-to-chats');
            const backToList = document.getElementById('back-to-list');

            // Chat elements
            const sendButton = document.getElementById('send-message');
            const chatInput = document.getElementById('chat-input');
            const chatMessagesContainer = document.getElementById('chat-messages-container');
            const currentChatAvatar = document.getElementById('current-chat-avatar');
            const currentChatName = document.getElementById('current-chat-name');

            // Chat state
            let currentView = 'list';
            let currentChatId = null;
            let currentChatData = null;

            // Mock chat data
            const chatData = {
                '1': {
                    name: '李設計師',
                    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80',
                    messages: [
                        {
                            id: 1,
                            sender: 'other',
                            content: '關於台北信義豪宅的設計方案，我有一些想法想跟您討論',
                            time: '14:30',
                            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80'
                        },
                        {
                            id: 2,
                            sender: 'me',
                            content: '好的，我們可以安排時間詳細討論',
                            time: '14:32',
                            avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=80&h=80&fit=crop&q=80'
                        }
                    ]
                },
                '2': {
                    name: '王專案經理',
                    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&q=80',
                    messages: [
                        {
                            id: 1,
                            sender: 'other',
                            content: '明天的會議準備好了嗎？',
                            time: '昨天',
                            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&q=80'
                        }
                    ]
                },
                '3': {
                    name: '陳工程師',
                    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80',
                    messages: [
                        {
                            id: 1,
                            sender: 'other',
                            content: '工程進度報告已完成',
                            time: '週一',
                            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&q=80'
                        }
                    ]
                }
            };

            // Check if all required elements exist
            if (!sidebar || !toggleButton || !chatSidebar || !todoSidebar || !backdrop || !chatToggle || !todoToggle) {
                console.error('Some required elements are missing');
                return;
            }

            // Sidebar toggle functionality
            const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
            sidebar.dataset.collapsed = isCollapsed;

            toggleButton.addEventListener('click', () => {
                const collapsed = sidebar.dataset.collapsed === 'true';
                sidebar.dataset.collapsed = !collapsed;
                localStorage.setItem('sidebar-collapsed', !collapsed);
            });

            // Chat view management
            function showView(viewName) {
                // Hide all views
                if (chatListView) chatListView.classList.add('hidden');
                if (teamMembersView) teamMembersView.classList.add('hidden');
                if (chatMessagesView) chatMessagesView.classList.add('hidden');
                
                // Show requested view
                switch(viewName) {
                    case 'list':
                        if (chatListView) chatListView.classList.remove('hidden');
                        currentView = 'list';
                        break;
                    case 'members':
                        if (teamMembersView) teamMembersView.classList.remove('hidden');
                        currentView = 'members';
                        break;
                    case 'messages':
                        if (chatMessagesView) chatMessagesView.classList.remove('hidden');
                        currentView = 'messages';
                        break;
                }
            }

            // Chat management
            function openChat(chatId, userData = null) {
                if (userData) {
                    // Check if there's already an existing chat for this user
                    const existingChatId = findExistingChatByName(userData.name);
                    if (existingChatId) {
                        // Use existing chat
                        currentChatId = existingChatId;
                        currentChatData = chatData[existingChatId];
                    } else {
                        // Create new chat data
                        currentChatId = null;
                        currentChatData = {
                            name: userData.name,
                            avatar: userData.avatar,
                            messages: []
                        };
                    }
                } else {
                    // Existing chat by ID
                    currentChatId = chatId;
                    currentChatData = chatData[chatId];
                }
                
                if (currentChatData && currentChatAvatar && currentChatName) {
                    currentChatAvatar.src = currentChatData.avatar;
                    currentChatAvatar.alt = currentChatData.name;
                    currentChatName.textContent = currentChatData.name;
                    
                    loadMessages();
                    showView('messages');
                }
            }

            // Helper function to find existing chat by user name
            function findExistingChatByName(userName) {
                for (const [chatId, chatInfo] of Object.entries(chatData)) {
                    if (chatInfo.name === userName) {
                        return chatId;
                    }
                }
                return null;
            }

            function loadMessages() {
                if (!currentChatData || !chatMessagesContainer) return;
                
                chatMessagesContainer.innerHTML = '';
                
                currentChatData.messages.forEach(message => {
                    const messageDiv = document.createElement('div');
                    
                    if (message.sender === 'me') {
                        messageDiv.className = 'flex items-start justify-end';
                        messageDiv.innerHTML = `
                            <div class="mr-3 max-w-xs">
                                <div class="bg-green-600 text-white rounded-lg px-3 py-2">
                                    <p class="text-sm">${message.content}</p>
                                </div>
                                <p class="text-xs text-muted-foreground mt-1 text-right">${message.time}</p>
                            </div>
                            <img class="h-8 w-8 rounded-full flex-shrink-0" 
                                src="${message.avatar}" 
                                alt="You">
                        `;
                    } else {
                        messageDiv.className = 'flex items-start';
                        messageDiv.innerHTML = `
                            <img class="h-8 w-8 rounded-full flex-shrink-0" 
                                src="${message.avatar}" 
                                alt="${currentChatData.name}">
                            <div class="ml-3 max-w-xs">
                                <div class="bg-gray-100 rounded-lg px-3 py-2">
                                    <p class="text-sm">${message.content}</p>
                                </div>
                                <p class="text-xs text-muted-foreground mt-1">${message.time}</p>
                            </div>
                        `;
                    }
                    
                    chatMessagesContainer.appendChild(messageDiv);
                });
                
                // Scroll to bottom
                chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight;
            }

            // Sidebar functionality
            const isChatOpen = localStorage.getItem('chat-sidebar-open') === 'true';
            const isTodoOpen = localStorage.getItem('todo-sidebar-open') === 'true';
            const isChatPinned = localStorage.getItem('chat-sidebar-pinned') === 'true';
            const isTodoPinned = localStorage.getItem('todo-sidebar-pinned') === 'true';
            
            // Initialize chat sidebar
            if (isChatOpen) {
                openChatSidebar();
            }
            if (isChatPinned) {
                pinChatSidebar();
            }
            
            // Initialize todo sidebar
            if (isTodoOpen) {
                openTodoSidebar();
            }
            if (isTodoPinned) {
                pinTodoSidebar();
            }

            // Initialize badge counts and notification indicators
            const initialChatCount = parseInt(document.querySelector('.chat-badge')?.textContent) || 0;
            const initialTodoCount = parseInt(document.querySelector('.todo-badge')?.textContent) || 0;
            updateChatBadgeCount(initialChatCount);
            updateTodoBadgeCount(initialTodoCount);

            // Project list functionality
            initializeProjectList();

            // Initialize quick actions
            initializeQuickActions();

            // Initialize AI assistant collapsible
            initializeAiAssistantCollapse();

            // Initialize user menu
            initializeUserMenu();

            function openChatSidebar() {
                // Close todo sidebar if open
                if (todoSidebar.classList.contains('show')) {
                    closeTodoSidebar();
                }
                
                chatSidebar.classList.remove('hidden');
                // No backdrop needed since floating mode now behaves like pinned mode
                setTimeout(() => {
                    chatSidebar.classList.add('show');
                }, 10);
                showView('list');
                localStorage.setItem('chat-sidebar-open', 'true');
            }

            function closeChatSidebar() {
                chatSidebar.classList.remove('show');
                // No backdrop management needed since floating mode now behaves like pinned mode
                setTimeout(() => {
                    chatSidebar.classList.add('hidden');
                }, 300);
                localStorage.setItem('chat-sidebar-open', 'false');
            }

            function openTodoSidebar() {
                // Close chat sidebar if open
                if (chatSidebar.classList.contains('show')) {
                    closeChatSidebar();
                }
                
                todoSidebar.classList.remove('hidden');
                // No backdrop needed since floating mode now behaves like pinned mode
                setTimeout(() => {
                    todoSidebar.classList.add('show');
                }, 10);
                localStorage.setItem('todo-sidebar-open', 'true');
            }

            function closeTodoSidebar() {
                todoSidebar.classList.remove('show');
                // No backdrop management needed since floating mode now behaves like pinned mode
                setTimeout(() => {
                    todoSidebar.classList.add('hidden');
                }, 300);
                localStorage.setItem('todo-sidebar-open', 'false');
            }

            function pinChatSidebar() {
                chatSidebar.classList.remove('floating');
                chatSidebar.classList.add('pinned');
                chatPinBtn.classList.add('pinned');
                const chatPinTooltip = chatPinBtn.querySelector('.sidebar-tooltip-content');
                if (chatPinTooltip) chatPinTooltip.textContent = '取消固定';
                backdrop.classList.remove('show');
                backdrop.classList.add('hidden');
                localStorage.setItem('chat-sidebar-pinned', 'true');
            }

            function unpinChatSidebar() {
                chatSidebar.classList.remove('pinned');
                chatSidebar.classList.add('floating');
                chatPinBtn.classList.remove('pinned');
                const chatPinTooltip = chatPinBtn.querySelector('.sidebar-tooltip-content');
                if (chatPinTooltip) chatPinTooltip.textContent = '固定側欄';
                // No backdrop needed in floating mode anymore since it behaves like pinned mode
                backdrop.classList.remove('show');
                backdrop.classList.add('hidden');
                localStorage.setItem('chat-sidebar-pinned', 'false');
            }

            function pinTodoSidebar() {
                todoSidebar.classList.remove('floating');
                todoSidebar.classList.add('pinned');
                todoPinBtn.classList.add('pinned');
                const todoPinTooltip = todoPinBtn.querySelector('.sidebar-tooltip-content');
                if (todoPinTooltip) todoPinTooltip.textContent = '取消固定';
                backdrop.classList.remove('show');
                backdrop.classList.add('hidden');
                localStorage.setItem('todo-sidebar-pinned', 'true');
            }

            function unpinTodoSidebar() {
                todoSidebar.classList.remove('pinned');
                todoSidebar.classList.add('floating');
                todoPinBtn.classList.remove('pinned');
                const todoPinTooltip = todoPinBtn.querySelector('.sidebar-tooltip-content');
                if (todoPinTooltip) todoPinTooltip.textContent = '固定側欄';
                // No backdrop needed in floating mode anymore since it behaves like pinned mode
                backdrop.classList.remove('show');
                backdrop.classList.add('hidden');
                localStorage.setItem('todo-sidebar-pinned', 'false');
            }

            function sendMessage() {
                if (!chatInput || !currentChatData) return;
                
                const message = chatInput.value.trim();
                if (message) {
                    console.log('Sending message:', message);
                    
                    // Add message to current chat
                    const newMessage = {
                        id: Date.now(),
                        sender: 'me',
                        content: message,
                        time: new Date().toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' }),
                        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=80&h=80&fit=crop&q=80'
                    };
                    
                    currentChatData.messages.push(newMessage);
                    
                    // Update chat data if it's an existing chat
                    if (currentChatId && chatData[currentChatId]) {
                        chatData[currentChatId] = currentChatData;
                    }
                    
                    loadMessages();
                    chatInput.value = '';
                }
            }

            // Function to update chat badge count
            function updateChatBadgeCount(newCount) {
                const chatToggle = document.getElementById('chat-toggle');
                const chatBadge = document.querySelector('.chat-badge');
                const chatTooltipBadge = document.querySelector('#chat-toggle .tooltip-content span');
                
                if (chatBadge && chatTooltipBadge) {
                    chatBadge.textContent = newCount;
                    chatTooltipBadge.textContent = newCount;
                }
                
                // Update notification indicator class
                if (chatToggle) {
                    if (newCount > 0) {
                        chatToggle.classList.add('has-notifications');
                    } else {
                        chatToggle.classList.remove('has-notifications');
                    }
                }
            }

            // Function to update todo badge count
            function updateTodoBadgeCount(newCount) {
                const todoToggle = document.getElementById('todo-toggle');
                const todoBadge = document.querySelector('.todo-badge');
                const todoTooltipBadge = document.querySelector('#todo-toggle .tooltip-content span');
                
                if (todoBadge && todoTooltipBadge) {
                    todoBadge.textContent = newCount;
                    todoTooltipBadge.textContent = newCount;
                }
                
                // Update notification indicator class
                if (todoToggle) {
                    if (newCount > 0) {
                        todoToggle.classList.add('has-notifications');
                    } else {
                        todoToggle.classList.remove('has-notifications');
                    }
                }
            }

            // Initialize project list functionality
            function initializeProjectList() {
                const projectItems = document.querySelectorAll('#my-projects-list a');
                
                projectItems.forEach(item => {
                    item.addEventListener('click', function(e) {
                        e.preventDefault();
                        
                        // Remove active state from all items
                        projectItems.forEach(i => i.classList.remove('bg-green-100', 'text-green-800'));
                        
                        // Add active state to clicked item
                        this.classList.add('bg-green-100', 'text-green-800');
                        
                        // Get project name
                        const projectName = this.querySelector('.nav-link-text').textContent.trim();
                        
                        // Here you can add logic to navigate to project details
                        console.log('Navigating to project:', projectName);
                        
                        // Optional: Show a subtle feedback
                        const originalBg = this.style.backgroundColor;
                        this.style.backgroundColor = 'rgba(34, 197, 94, 0.2)';
                        setTimeout(() => {
                            this.style.backgroundColor = originalBg;
                        }, 200);
                    });
                    
                    // Add hover effect for the status indicator
                    const statusDot = item.querySelector('.w-2');
                    if (statusDot) {
                        item.addEventListener('mouseenter', function() {
                            statusDot.style.transform = 'scale(1.2)';
                        });
                        
                        item.addEventListener('mouseleave', function() {
                            statusDot.style.transform = 'scale(1)';
                        });
                    }
                });
            }

            // Initialize quick actions functionality
            function initializeQuickActions() {
                const quickAiChatBtn = document.getElementById('quick-ai-chat');
                
                if (quickAiChatBtn) {
                    quickAiChatBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        
                        // Show loading state
                        const originalContent = this.innerHTML;
                        const iconDiv = this.querySelector('.w-6');
                        
                        if (iconDiv) {
                            iconDiv.innerHTML = `
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5 text-white animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            `;
                        }
                        
                        // Simulate loading and open AI chat
                        setTimeout(() => {
                            openAiChatModal();
                            
                            // Restore original content
                            setTimeout(() => {
                                this.innerHTML = originalContent;
                                initializeQuickActions(); // Re-initialize after restoration
                            }, 100);
                        }, 300);
                    });
                }
            }

            // Initialize AI assistant collapsible functionality
            function initializeAiAssistantCollapse() {
                const aiHeader = document.getElementById('ai-assistant-header');
                const aiToggle = document.getElementById('ai-assistant-toggle');
                const aiContent = document.getElementById('ai-assistant-content');
                
                let isExpanded = true;
                
                function toggleCollapse() {
                    isExpanded = !isExpanded;
                    
                    if (isExpanded) {
                        aiContent.style.maxHeight = aiContent.scrollHeight + 'px';
                        aiContent.style.opacity = '1';
                        aiToggle.style.transform = 'rotate(0deg)';
                    } else {
                        aiContent.style.maxHeight = '0px';
                        aiContent.style.opacity = '0';
                        aiToggle.style.transform = 'rotate(-90deg)';
                    }
                }
                
                if (aiHeader && aiToggle && aiContent) {
                    // Set initial state
                    aiContent.style.transition = 'max-height 0.3s ease-in-out, opacity 0.3s ease-in-out';
                    aiContent.style.overflow = 'hidden';
                    aiContent.style.maxHeight = aiContent.scrollHeight + 'px';
                    
                    aiHeader.addEventListener('click', toggleCollapse);
                    aiToggle.addEventListener('click', function(e) {
                        e.stopPropagation();
                        toggleCollapse();
                    });
                }
            }

            // Initialize user menu functionality
            function initializeUserMenu() {
                const userMenuToggle = document.getElementById('user-menu-toggle');
                const userMenu = document.getElementById('user-menu');
                const arrow = userMenuToggle?.querySelector('svg');
                
                let isMenuOpen = false;
                
                function toggleUserMenu() {
                    isMenuOpen = !isMenuOpen;
                    
                    if (isMenuOpen) {
                        userMenu.style.opacity = '1';
                        userMenu.style.visibility = 'visible';
                        userMenu.style.transform = 'scale(1)';
                        arrow.style.transform = 'rotate(180deg)';
                    } else {
                        userMenu.style.opacity = '0';
                        userMenu.style.visibility = 'hidden';
                        userMenu.style.transform = 'scale(0.95)';
                        arrow.style.transform = 'rotate(0deg)';
                    }
                }
                
                if (userMenuToggle && userMenu) {
                    userMenuToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        toggleUserMenu();
                    });
                    
                    // Close menu when clicking outside
                    document.addEventListener('click', function(e) {
                        if (isMenuOpen && !userMenuToggle.contains(e.target) && !userMenu.contains(e.target)) {
                            toggleUserMenu();
                        }
                    });
                    
                    // Close menu when clicking on menu items
                    userMenu.addEventListener('click', function(e) {
                        if (e.target.tagName === 'A') {
                            toggleUserMenu();
                        }
                    });
                }
            }

            // Event listeners
            chatToggle.addEventListener('click', () => {
                if (chatSidebar.classList.contains('show')) {
                    closeChatSidebar();
                } else {
                    openChatSidebar();
                }
            });

            todoToggle.addEventListener('click', () => {
                if (todoSidebar.classList.contains('show')) {
                    closeTodoSidebar();
                } else {
                    openTodoSidebar();
                }
            });

            // Close buttons
            chatClose.addEventListener('click', closeChatSidebar);
            if (chatClose2) chatClose2.addEventListener('click', closeChatSidebar);
            if (chatClose3) chatClose3.addEventListener('click', closeChatSidebar);
            todoClose.addEventListener('click', closeTodoSidebar);

            // Pin buttons
            chatPinBtn.addEventListener('click', () => {
                if (chatSidebar.classList.contains('pinned')) {
                    unpinChatSidebar();
                } else {
                    pinChatSidebar();
                }
            });

            todoPinBtn.addEventListener('click', () => {
                if (todoSidebar.classList.contains('pinned')) {
                    unpinTodoSidebar();
                } else {
                    pinTodoSidebar();
                }
            });

            // Backdrop is no longer used since floating mode behaves like pinned mode
            // Outside clicks are handled by document click listener above

            // Navigation buttons
            if (newChatBtn) {
                newChatBtn.addEventListener('click', () => showView('members'));
            }
            
            if (backToChats) {
                backToChats.addEventListener('click', () => showView('list'));
            }
            
            if (backToList) {
                backToList.addEventListener('click', () => showView('list'));
            }

            // Send message
            if (sendButton) {
                sendButton.addEventListener('click', sendMessage);
            }

            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }



            // Handle clicks outside floating sidebars to close them
            document.addEventListener('click', function(e) {
                // Check if click is outside floating chat sidebar
                if (chatSidebar.classList.contains('show') && 
                    chatSidebar.classList.contains('floating') && 
                    !chatSidebar.contains(e.target) && 
                    !chatToggle.contains(e.target)) {
                    closeChatSidebar();
                    return;
                }
                
                // Check if click is outside floating todo sidebar
                if (todoSidebar.classList.contains('show') && 
                    todoSidebar.classList.contains('floating') && 
                    !todoSidebar.contains(e.target) && 
                    !todoToggle.contains(e.target)) {
                    closeTodoSidebar();
                    return;
                }
                
                // Chat conversation and member clicks (existing functionality)
                const conversationItem = e.target.closest('.chat-conversation-item');
                if (conversationItem) {
                    const chatId = conversationItem.dataset.chatId;
                    openChat(chatId);
                }
                
                const memberItem = e.target.closest('.team-member-item');
                if (memberItem) {
                    const userData = {
                        name: memberItem.dataset.userName,
                        avatar: memberItem.dataset.userAvatar
                    };
                    openChat(null, userData);
                }
            });

            // Todo item checkbox functionality
            document.addEventListener('change', function(e) {
                if (e.target.type === 'checkbox' && e.target.closest('.todo-item')) {
                    const todoItem = e.target.closest('.todo-item');
                    const titleElement = todoItem.querySelector('.text-sm.font-medium');
                    const statusElement = todoItem.querySelector('.text-xs.text-muted-foreground');
                    const badgeElement = todoItem.querySelector('.inline-flex.items-center');
                    
                    if (e.target.checked) {
                        // Mark as completed
                        titleElement.classList.add('text-muted-foreground', 'line-through');
                        statusElement.textContent = '已完成';
                        badgeElement.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800';
                        badgeElement.textContent = '已完成';
                        
                        // Update todo badge count
                        const todoBadge = document.querySelector('.todo-badge');
                        if (todoBadge) {
                            const currentCount = parseInt(todoBadge.textContent) || 0;
                            const newCount = Math.max(0, currentCount - 1);
                            updateTodoBadgeCount(newCount);
                        }
                    } else {
                        // Mark as pending
                        titleElement.classList.remove('text-muted-foreground', 'line-through');
                        statusElement.textContent = statusElement.getAttribute('data-original-text') || '待完成';
                        badgeElement.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800';
                        badgeElement.textContent = '待完成';
                        
                        // Update todo badge count
                        const todoBadge = document.querySelector('.todo-badge');
                        if (todoBadge) {
                            const currentCount = parseInt(todoBadge.textContent) || 0;
                            const newCount = currentCount + 1;
                            updateTodoBadgeCount(newCount);
                        }
                    }
                }
            });

            // AI Chat Modal functionality
            const aiChatModal = document.getElementById('ai-chat-modal');
            const openAiChatBtn = document.getElementById('open-ai-chat');
            const closeAiChatBtn = document.getElementById('close-ai-chat');
            const clearChatBtn = document.getElementById('clear-chat');
            const aiChatInput = document.getElementById('ai-chat-input');
            const sendAiMessageBtn = document.getElementById('send-ai-message');
            const aiChatMessages = document.getElementById('ai-chat-messages');
            const charCount = document.getElementById('char-count');

            // AI Chat state
            let aiMessages = [];

            // AI Chat Functions
            function formatAiMessage(content) {
                // Split content by double newlines to create paragraphs
                const paragraphs = content.split('\n\n');
                let formattedContent = '';
                
                paragraphs.forEach((paragraph, index) => {
                    if (paragraph.trim() === '') return;
                    
                    // Check if paragraph contains bullet points
                    if (paragraph.includes('•') || paragraph.includes('◦') || paragraph.includes('-')) {
                        const lines = paragraph.split('\n');
                        let listItems = '';
                        let currentText = '';
                        
                        lines.forEach(line => {
                            const trimmedLine = line.trim();
                            if (trimmedLine.startsWith('•') || trimmedLine.startsWith('◦') || trimmedLine.startsWith('-')) {
                                // This is a list item
                                const bulletContent = trimmedLine.replace(/^[•◦-]\s*/, '');
                                listItems += `<div class="flex items-start space-x-2 mb-2">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span class="flex-1">${bulletContent}</span>
                                </div>`;
                            } else if (trimmedLine !== '') {
                                // This is regular text before or after list
                                if (listItems === '') {
                                    currentText += trimmedLine + '<br>';
                                } else {
                                    // Text after list items
                                    formattedContent += `<div class="mb-3">${currentText}</div>`;
                                    formattedContent += `<div class="mb-3">${listItems}</div>`;
                                    formattedContent += `<div class="mb-3">${trimmedLine}</div>`;
                                    currentText = '';
                                    listItems = '';
                                    return;
                                }
                            }
                        });
                        
                        if (currentText && listItems) {
                            formattedContent += `<div class="mb-3">${currentText}</div>`;
                            formattedContent += `<div class="mb-3">${listItems}</div>`;
                        } else if (listItems) {
                            formattedContent += `<div class="mb-3">${listItems}</div>`;
                        } else if (currentText) {
                            formattedContent += `<div class="mb-3">${currentText}</div>`;
                        }
                    } else {
                        // Regular paragraph - check for emojis and special formatting
                        let formattedParagraph = paragraph.trim();
                        
                        // Handle single line breaks within paragraph
                        formattedParagraph = formattedParagraph.replace(/\n/g, '<br>');
                        
                        // Style emojis and special symbols
                        formattedParagraph = formattedParagraph.replace(/(📊|💰|⚠️|✅|🔄|⏰|📈|🧠|📝|🚀|⌨️|🤖)/g, '<span class="text-lg">$1</span>');
                        
                        // Bold important text patterns
                        formattedParagraph = formattedParagraph.replace(/：([^：\n]+)/g, '：<strong>$1</strong>');
                        
                        formattedContent += `<div class="mb-3">${formattedParagraph}</div>`;
                    }
                });
                
                return formattedContent;
            }

            function openAiChatModal() {
                if (aiChatModal) {
                    aiChatModal.classList.remove('hidden');
                    document.body.style.overflow = 'hidden';
                    
                    // Add animation classes
                    const modalContent = aiChatModal.querySelector('.relative');
                    if (modalContent) {
                        modalContent.style.transform = 'scale(0.9) translateY(20px)';
                        modalContent.style.opacity = '0';
                        
                        setTimeout(() => {
                            modalContent.style.transform = 'scale(1) translateY(0)';
                            modalContent.style.opacity = '1';
                        }, 10);
                    }
                    
                    // Focus on input
                    setTimeout(() => {
                        if (aiChatInput) {
                            aiChatInput.focus();
                        }
                    }, 300);
                }
            }

            function closeAiChatModal() {
                if (aiChatModal) {
                    aiChatModal.classList.add('hidden');
                    document.body.style.overflow = '';
                }
            }

            function addAiMessage(content, isUser = false, timestamp = null) {
                const messageElement = document.createElement('div');
                messageElement.className = 'flex items-start space-x-3';
                
                const time = timestamp || new Date().toLocaleTimeString('zh-TW', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                });

                if (isUser) {
                    // Format user content with line breaks
                    const formattedUserContent = content.replace(/\n/g, '<br>');
                    messageElement.innerHTML = `
                        <div class="w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl p-4 shadow-lg">
                                <div class="text-sm font-medium">${formattedUserContent}</div>
                            </div>
                            <span class="text-xs text-gray-500 mt-2 block font-medium">${time}</span>
                        </div>
                    `;
                } else {
                    // Format content with proper line breaks and structure
                    const formattedContent = formatAiMessage(content);
                    messageElement.innerHTML = `
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-md border border-gray-200/50">
                                <div class="text-sm text-gray-800 space-y-3">${formattedContent}</div>
                            </div>
                            <span class="text-xs text-gray-500 mt-2 block font-medium">${time}</span>
                        </div>
                    `;
                }

                if (aiChatMessages) {
                    aiChatMessages.appendChild(messageElement);
                    aiChatMessages.scrollTop = aiChatMessages.scrollHeight;
                }

                // Store message
                aiMessages.push({
                    content,
                    isUser,
                    timestamp: time
                });
            }

            function simulateAiResponse(userMessage) {
                // Show typing indicator
                const typingElement = document.createElement('div');
                typingElement.className = 'flex items-start space-x-3 typing-indicator';
                typingElement.innerHTML = `
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                    </div>
                    <div class="flex-1">
                        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                            <div class="flex items-center space-x-1">
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                            </div>
                        </div>
                    </div>
                `;

                if (aiChatMessages) {
                    aiChatMessages.appendChild(typingElement);
                    aiChatMessages.scrollTop = aiChatMessages.scrollHeight;
                }

                // Generate AI response based on user input
                setTimeout(() => {
                    if (aiChatMessages) {
                        aiChatMessages.removeChild(typingElement);
                    }

                    let response = '';
                    const userMsg = userMessage.toLowerCase();
                    
                    if (userMsg.includes('專案') || userMsg.includes('進度')) {
                        response = '根據目前的專案分析，我發現以下幾個重點：\n\n• 台北信義豪宅專案進度75%，預計本週完成廚房設計確認\n• 新北三重商辦已完成100%，品質檢測結果優良\n• 台中餐廳專案出現延遲，建議立即安排進度會議\n\n您希望我針對哪個專案提供更詳細的分析嗎？';
                    } else if (userMsg.includes('成本') || userMsg.includes('預算')) {
                        response = '💰 成本優化建議：\n\n根據AI分析，我為您找到以下節省機會：\n• 材料採購：可節省15%成本，建議採用替代供應商方案\n• 工期優化：調整施工順序可提前2天完工，節省人工成本\n• 設備租賃：批量租賃可獲得8%折扣\n\n預計總計可節省約25萬元。需要我生成詳細的成本分析報告嗎？';
                    } else if (userMsg.includes('風險') || userMsg.includes('問題')) {
                        response = '⚠️ 風險評估報告：\n\n目前檢測到以下風險：\n• 高風險：台中餐廳專案進度嚴重延遲\n• 中風險：材料供應鏈可能受天氣影響\n• 低風險：部分設備維護週期即將到期\n\n建議優先處理台中專案的進度問題，需要我安排緊急會議嗎？';
                    } else if (userMsg.includes('報告') || userMsg.includes('總結')) {
                        response = '📊 本週工作總結：\n\n✅ 已完成：24項任務，效率提升15%\n🔄 進行中：12個專案，整體進度良好\n⏰ 待處理：8項審批，3項為緊急\n📈 AI洞察：15份報告生成，3個新風險警示\n\n整體表現優秀！有什麼特定的項目需要我深入分析嗎？';
                    } else {
                        response = '我理解您的問題。作為HorizAI智能助理，我已經分析了您的需求。基於當前的專案數據和進度情況，我建議：\n\n1. 優先處理緊急任務\n2. 關注進度延遲的專案\n3. 利用AI建議優化工作流程\n\n您還有其他需要協助的地方嗎？我可以提供更具體的專案分析、成本優化建議或風險評估。';
                    }

                    addAiMessage(response, false);
                }, 1500 + Math.random() * 1000);
            }

            function sendAiMessage() {
                if (!aiChatInput) return;
                
                const message = aiChatInput.value.trim();
                if (message) {
                    // Add user message
                    addAiMessage(message, true);
                    
                    // Clear input
                    aiChatInput.value = '';
                    updateCharCount();
                    
                    // Generate AI response
                    simulateAiResponse(message);
                }
            }

            function clearAiChat() {
                if (aiChatMessages) {
                    // Keep only the welcome message
                    const welcomeMessage = aiChatMessages.firstElementChild;
                    aiChatMessages.innerHTML = '';
                    if (welcomeMessage) {
                        aiChatMessages.appendChild(welcomeMessage);
                    }
                }
                aiMessages = [];
            }

            function updateCharCount() {
                if (aiChatInput && charCount) {
                    const count = aiChatInput.value.length;
                    charCount.textContent = `${count}/1000`;
                    
                    if (count >= 900) {
                        charCount.className = 'font-medium text-red-500';
                    } else if (count >= 700) {
                        charCount.className = 'font-medium text-orange-500';
                    } else {
                        charCount.className = 'font-medium text-gray-500';
                    }
                }
            }

            // Auto-resize textarea function
            function autoResizeTextarea(textarea) {
                textarea.style.height = 'auto';
                const minHeight = parseInt(textarea.style.minHeight) || 52;
                const maxHeight = parseInt(textarea.style.maxHeight) || 120;
                const scrollHeight = textarea.scrollHeight;
                
                if (scrollHeight > minHeight) {
                    textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';
                } else {
                    textarea.style.height = minHeight + 'px';
                }
            }

            // AI Chat Event Listeners
            if (openAiChatBtn) {
                openAiChatBtn.addEventListener('click', openAiChatModal);
            }

            if (closeAiChatBtn) {
                closeAiChatBtn.addEventListener('click', closeAiChatModal);
            }

            if (clearChatBtn) {
                clearChatBtn.addEventListener('click', clearAiChat);
            }

            if (sendAiMessageBtn) {
                sendAiMessageBtn.addEventListener('click', sendAiMessage);
            }

            if (aiChatInput) {
                aiChatInput.addEventListener('input', function() {
                    updateCharCount();
                    autoResizeTextarea(aiChatInput);
                });
                aiChatInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendAiMessage();
                    }
                });
                // Initial resize
                autoResizeTextarea(aiChatInput);
            }

            // Quick action buttons
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('quick-action-btn')) {
                    const action = e.target.dataset.action;
                    if (aiChatInput) {
                        aiChatInput.value = action;
                        aiChatInput.focus();
                        updateCharCount();
                    }
                }
            });

            // Close modal when clicking outside
            if (aiChatModal) {
                aiChatModal.addEventListener('click', function(e) {
                    if (e.target === aiChatModal) {
                        closeAiChatModal();
                    }
                });
            }

            // Floating AI Chat functionality
            const floatingAiChat = document.getElementById('floating-ai-chat');
            const floatingAiToggle = document.getElementById('floating-ai-toggle');
            const floatingAiInputContainer = document.getElementById('floating-ai-input-container');
            const floatingAiInput = document.getElementById('floating-ai-input');
            const floatingSendMessage = document.getElementById('floating-send-message');
            const floatingCharCount = document.getElementById('floating-char-count');
            const floatingExpandChat = document.getElementById('floating-expand-chat');
            const floatingAiPulse = document.getElementById('floating-ai-pulse');

            let isFloatingChatOpen = false;

            // Floating AI Chat Functions
            function toggleFloatingAiChat() {
                isFloatingChatOpen = !isFloatingChatOpen;
                
                if (isFloatingChatOpen) {
                    floatingAiInputContainer.classList.add('show');
                    floatingAiToggle.classList.add('active');
                    floatingAiPulse.style.display = 'none';
                    
                    // Focus on input after animation
                    setTimeout(() => {
                        if (floatingAiInput) {
                            floatingAiInput.focus();
                        }
                    }, 300);
                } else {
                    floatingAiInputContainer.classList.remove('show');
                    floatingAiToggle.classList.remove('active');
                    floatingAiInput.value = '';
                    updateFloatingCharCount();
                }
            }

            function sendFloatingAiMessage() {
                if (!floatingAiInput) return;
                
                const message = floatingAiInput.value.trim();
                if (message) {
                    // Close floating input and open full modal with message
                    toggleFloatingAiChat();
                    
                    // Open full modal
                    setTimeout(() => {
                        openAiChatModal();
                        
                        // Add the message to full chat
                        setTimeout(() => {
                            addAiMessage(message, true);
                            simulateAiResponse(message);
                        }, 100);
                    }, 300);
                }
            }

            function updateFloatingCharCount() {
                if (floatingAiInput && floatingCharCount) {
                    const count = floatingAiInput.value.length;
                    floatingCharCount.textContent = `${count}/500`;
                    
                    if (count >= 450) {
                        floatingCharCount.className = 'text-red-500';
                    } else if (count >= 350) {
                        floatingCharCount.className = 'text-orange-500';
                    } else {
                        floatingCharCount.className = 'text-gray-500';
                    }
                }
            }

            // Floating AI Event Listeners
            if (floatingAiToggle) {
                floatingAiToggle.addEventListener('click', toggleFloatingAiChat);
            }

            if (floatingSendMessage) {
                floatingSendMessage.addEventListener('click', sendFloatingAiMessage);
            }

            if (floatingAiInput) {
                floatingAiInput.addEventListener('input', updateFloatingCharCount);
                floatingAiInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendFloatingAiMessage();
                    }
                });
            }

            if (floatingExpandChat) {
                floatingExpandChat.addEventListener('click', function() {
                    const message = floatingAiInput ? floatingAiInput.value.trim() : '';
                    toggleFloatingAiChat();
                    
                    setTimeout(() => {
                        openAiChatModal();
                        if (message && aiChatInput) {
                            aiChatInput.value = message;
                            aiChatInput.focus();
                            updateCharCount();
                        }
                    }, 300);
                });
            }

            // Floating Quick action buttons
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('floating-quick-action-btn')) {
                    const action = e.target.dataset.action;
                    if (floatingAiInput) {
                        floatingAiInput.value = action;
                        floatingAiInput.focus();
                        updateFloatingCharCount();
                    }
                }
            });

            // Close floating chat when clicking outside
            document.addEventListener('click', function(e) {
                if (isFloatingChatOpen && 
                    !floatingAiChat.contains(e.target) && 
                    !e.target.closest('#floating-ai-chat')) {
                    toggleFloatingAiChat();
                }
            });

            // Show pulse notification initially
            setTimeout(() => {
                if (floatingAiPulse) {
                    floatingAiPulse.style.display = 'block';
                }
            }, 2000);

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Escape key to close AI chat modal
                if (e.key === 'Escape' && !aiChatModal.classList.contains('hidden')) {
                    closeAiChatModal();
                }
                
                // Escape key to close sidebars
                if (e.key === 'Escape') {
                    if (chatSidebar.classList.contains('show') && !chatSidebar.classList.contains('pinned')) {
                        closeChatSidebar();
                    }
                    if (todoSidebar.classList.contains('show') && !todoSidebar.classList.contains('pinned')) {
                        closeTodoSidebar();
                    }
                }
                
                // Ctrl/Cmd + Shift + C to toggle chat
                if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'C') {
                    e.preventDefault();
                    if (chatSidebar.classList.contains('show')) {
                        closeChatSidebar();
                    } else {
                        openChatSidebar();
                    }
                }
                
                // Ctrl/Cmd + Shift + T to toggle todo
                if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                    e.preventDefault();
                    if (todoSidebar.classList.contains('show')) {
                        closeTodoSidebar();
                    } else {
                        openTodoSidebar();
                    }
                }

                // Ctrl/Cmd + Shift + A to toggle floating AI chat
                if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
                    e.preventDefault();
                    if (isFloatingChatOpen) {
                        toggleFloatingAiChat();
                    } else {
                        toggleFloatingAiChat();
                    }
                }

                // Ctrl/Cmd + Shift + F to open full AI chat modal
                if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'F') {
                    e.preventDefault();
                    if (isFloatingChatOpen) {
                        toggleFloatingAiChat();
                    }
                    openAiChatModal();
                }
            });
        });
    </script>
</body>

</html>