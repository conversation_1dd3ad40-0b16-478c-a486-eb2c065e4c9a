import { Modu<PERSON> } from '@nestjs/common';
import { PermissionsService } from './permissions.service';
import { PermissionSyncService } from './permission-sync.service';
import { PermissionsController } from './permissions.controller';
import { PrismaModule } from '../../core/prisma/prisma.module';
import { CaslModule } from '../../../casl/casl.module';
import { CommonModule } from '../../../common/common.module';

@Module({
  imports: [PrismaModule, CaslModule, CommonModule],
  controllers: [PermissionsController],
  providers: [PermissionsService, PermissionSyncService],
  exports: [PermissionsService, PermissionSyncService],
})
export class PermissionsModule {}
