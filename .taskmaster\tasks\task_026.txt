# Task ID: 26
# Title: 建立 Agent 工具管理系統的資料庫結構
# Status: pending
# Dependencies: 19
# Priority: high
# Description: 創建支援 Agent 自定義的核心資料庫架構，包括 AiTool 模型用於註冊系統工具，AiBotTool 關聯表建立 Agent 與工具的多對多關係，以及擴展 AiBot 模型支援執行類型區分。
# Details:
在 schema.prisma 中新增:
1. **AiTool 模型**: 包含 id, key (程式化名稱), name (顯示名稱), description (工具描述), input_schema (JSON Schema), is_enabled 等欄位
2. **AiBotTool 關聯表**: 多對多關聯表，連接 AiBot 和 AiTool，包含 bot_id, tool_id, created_at
3. **擴展 AiBot 模型**: 新增 execution_type 欄位 (SINGLE_CALL | GRAPH)，以及 tools 反向關聯
4. 建立資料庫遷移並同步 Prisma Client
5. 確保租戶隔離：AiTool 可能需要 tenant_id 或設為系統級別資源

# Test Strategy:
建立遷移後驗證新的模型關係。測試 AiBot 與 AiTool 的多對多關聯創建和查詢。驗證 execution_type 欄位的枚舉值限制。確認租戶隔離機制正確應用。
