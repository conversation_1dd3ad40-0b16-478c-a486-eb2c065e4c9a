import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AiModelsService } from '../../../ai/models/configuration/models/ai-models.service';
import { ai_agents, ai_models, ai_keys } from '@prisma/client';

type FullAgent = ai_agents & { ai_models: ai_models; ai_keys: ai_keys };

interface AgentStatusDetails {
  database: boolean;
  aiModels: boolean;
  tools: boolean;
  lastError?: string;
}

@Injectable()
export class AgentStatusService {
  private readonly logger = new Logger(AgentStatusService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly aiModelsService: AiModelsService,
  ) {}

  /**
   * 獲取 Agent 系統狀態
   */
  async getAgentStatus(): Promise<{
    status: string;
    message: string;
    details?: AgentStatusDetails;
  }> {
    const details: AgentStatusDetails = {
      database: false,
      aiModels: false,
      tools: false,
    };

    try {
      // 1. 檢查資料庫連接
      await this.checkDatabaseConnection();
      details.database = true;

      // 2. 檢查 AI 模型服務
      const aiModelsHealthy = await this.checkAiModelsService();
      details.aiModels = aiModelsHealthy;

      // 3. 檢查工具系統（簡化版本）
      details.tools = true; // 目前總是返回 true，因為工具系統已簡化

      const allHealthy = details.database && details.aiModels && details.tools;

      return {
        status: allHealthy ? 'healthy' : 'degraded',
        message: allHealthy
          ? 'Agent service is fully operational'
          : 'Agent service is running but some components may be degraded',
        details,
      };
    } catch (error) {
      this.logger.error(`Agent status check failed: ${error.message}`);
      details.lastError = error.message;

      return {
        status: 'unhealthy',
        message: `Agent service error: ${error.message}`,
        details,
      };
    }
  }

  /**
   * 更新 Agent 啟用狀態
   */
  async updateAgentStatus(id: string, isEnabled: boolean): Promise<FullAgent> {
    try {
      const agent = await this.prisma.ai_agents.update({
        where: { id },
        data: {
          is_enabled: isEnabled,
          updated_at: new Date(),
        },
        include: {
          ai_models: true,
          ai_keys: true,
        },
      });

      this.logger.log(`Agent ${id} status updated to ${isEnabled ? 'enabled' : 'disabled'}`);
      return agent;
    } catch (error) {
      this.logger.error(`Failed to update agent ${id} status:`, error);
      throw error;
    }
  }

  /**
   * 獲取 Agent 詳細狀態
   */
  async getAgentDetailedStatus(agentId: string): Promise<{
    agent: FullAgent | null;
    isHealthy: boolean;
    issues: string[];
  }> {
    try {
      const agent = await this.prisma.ai_agents.findUnique({
        where: { id: agentId },
        include: {
          ai_models: true,
          ai_keys: true,
        },
      });

      if (!agent) {
        return {
          agent: null,
          isHealthy: false,
          issues: ['Agent not found'],
        };
      }

      const issues: string[] = [];

      // 檢查基本配置
      if (!agent.system_prompt || agent.system_prompt.trim().length === 0) {
        issues.push('Missing or empty system prompt');
      }

      if (!agent.ai_models) {
        issues.push('No AI model associated');
      }

      if (!agent.ai_keys) {
        issues.push('No API key associated');
      }

      if (!agent.is_enabled) {
        issues.push('Agent is disabled');
      }

      return {
        agent,
        isHealthy: issues.length === 0,
        issues,
      };
    } catch (error) {
      this.logger.error(`Failed to get detailed status for agent ${agentId}:`, error);
      return {
        agent: null,
        isHealthy: false,
        issues: [`Status check failed: ${error.message}`],
      };
    }
  }

  /**
   * 私有方法：檢查資料庫連接
   */
  private async checkDatabaseConnection(): Promise<void> {
    await this.prisma.$queryRaw`SELECT 1`;
  }

  /**
   * 私有方法：檢查 AI 模型服務
   */
  private async checkAiModelsService(): Promise<boolean> {
    try {
      await this.aiModelsService.findAll();
      return true;
    } catch (error) {
      this.logger.warn(`AI Models service check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * 獲取系統健康度摘要
   */
  async getHealthSummary(): Promise<{
    totalAgents: number;
    enabledAgents: number;
    disabledAgents: number;
    healthyAgents: number;
    systemStatus: 'healthy' | 'degraded' | 'unhealthy';
  }> {
    try {
      const totalAgents = await this.prisma.ai_agents.count();
      const enabledAgents = await this.prisma.ai_agents.count({
        where: { is_enabled: true },
      });
      const disabledAgents = totalAgents - enabledAgents;

      // 簡化的健康度檢查
      const healthyAgents = enabledAgents; // 假設所有啟用的 Agent 都是健康的

      let systemStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      if (totalAgents === 0 || enabledAgents === 0) {
        systemStatus = 'unhealthy';
      } else if (healthyAgents < enabledAgents * 0.8) {
        systemStatus = 'degraded';
      }

      return {
        totalAgents,
        enabledAgents,
        disabledAgents,
        healthyAgents,
        systemStatus,
      };
    } catch (error) {
      this.logger.error('Failed to get health summary:', error);
      return {
        totalAgents: 0,
        enabledAgents: 0,
        disabledAgents: 0,
        healthyAgents: 0,
        systemStatus: 'unhealthy',
      };
    }
  }
}
