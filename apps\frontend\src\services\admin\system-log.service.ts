import { httpService } from '@/services/http.service';
import type {
  SystemLog,
  SystemLogQuery,
  SystemLogStats,
  AvailableLogAction,
  SystemLogReportDto,
} from '@/types/models/system-log.model';
import { objectToQueryString } from '@/utils/url.util';

const BASE_URL = '/admin/system-logs';

interface BackendResponse<T> {
  data: T;
  meta?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface PaginatedLogsResponse {
  items: SystemLog[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

class SystemLogService {
  /**
   * 獲取系統日誌列表 (分頁)
   * @param query - 查詢參數
   */
  async getLogs(query: SystemLogQuery): Promise<PaginatedLogsResponse> {
    const queryString = objectToQueryString(query);
    const response = await httpService.get<BackendResponse<SystemLog[]>>(`${BASE_URL}?${queryString}`);
    
    return {
      items: response.data || [],
      total: response.meta?.total || 0,
      page: response.meta?.page || 1,
      pageSize: response.meta?.limit || 50,
      totalPages: response.meta?.totalPages || 0,
    };
  }

  /**
   * 獲取系統日誌統計數據
   */
  async getStats(): Promise<SystemLogStats> {
    const response = await httpService.get<BackendResponse<SystemLogStats>>(`${BASE_URL}/stats`);
    return response.data;
  }

  /**
   * 獲取所有可用的操作類型
   */
  async getAvailableActions(): Promise<AvailableLogAction[]> {
    const response = await httpService.get<BackendResponse<AvailableLogAction[]>>(`${BASE_URL}/actions`);
    return response.data || [];
  }

  /**
   * 生成並下載報告
   * @param reportDto - 報告參數
   */
  async generateReport(reportDto: SystemLogReportDto): Promise<Blob> {
    const response = await httpService.post(`${BASE_URL}/reports/generate`, reportDto, {
      responseType: 'blob',
    });
    return response;
  }
}

export const systemLogService = new SystemLogService();
export default systemLogService; 