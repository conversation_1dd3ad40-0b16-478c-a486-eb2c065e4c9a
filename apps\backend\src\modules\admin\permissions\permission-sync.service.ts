import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs';
import * as path from 'path';
import {
  SyncPermissionsDto,
  ScanPermissionsDto,
  SyncResultDto,
  ScanResultDto,
  SyncStatusDto,
} from './dto/sync.dto';

const execAsync = promisify(exec);

@Injectable()
export class PermissionSyncService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 執行權限同步（通過CLI）
   */
  async syncPermissions(forceOverwrite = false) {
    console.log('=== 開始執行權限同步 ===');
    console.log('強制覆蓋模式:', forceOverwrite);

    try {
      // 執行同步腳本
      console.log('執行權限同步腳本...');
      console.log('工作目錄:', process.cwd());

      // 如果已經在 backend 目錄，直接使用；否則拼接路徑
      const currentDir = process.cwd();
      const isInBackendDir = currentDir.includes('/apps/backend') || currentDir.endsWith('backend');
      const backendDir = isInBackendDir ? currentDir : path.join(currentDir, 'apps', 'backend');

      const command = forceOverwrite ? 'pnpm run db:sync-perms:force' : 'pnpm run db:sync-perms';

      console.log('執行命令:', command);
      console.log('在目錄:', backendDir);

      const { stdout, stderr } = await execAsync(command, {
        cwd: backendDir,
        env: {
          ...process.env,
          NODE_ENV: 'development',
          DATABASE_URL: process.env.DATABASE_URL,
        },
      });

      console.log('腳本輸出:', stdout);
      if (stderr) {
        console.error('腳本錯誤:', stderr);
      }

      // 讀取生成的報告
      const reportPath = path.join(backendDir, 'reports', 'permission-sync-report.json');
      console.log('報告路徑:', reportPath);

      if (fs.existsSync(reportPath)) {
        const reportContent = fs.readFileSync(reportPath, 'utf-8');
        const report = JSON.parse(reportContent);

        console.log('報告內容:', JSON.stringify(report, null, 2));

        // 計算建立和更新的數量 (需要從同步腳本的輸出中解析)
        let created = report.created || 0;
        let updated = report.updated || 0;
        const errors = report.errors || 0;

        // 如果報告中沒有這些欄位，從 stdout 中解析
        if (!report.created && !report.updated) {
          console.log('報告中沒有 created/updated 欄位，從輸出解析...');

          // 從 stdout 中解析建立和更新的數量
          const createdMatch = stdout.match(/✅ 建立新權限: (\d+)/);
          const updatedMatch = stdout.match(/✅ 更新權限: (\d+)/);
          const skippedMatch = stdout.match(/⏭️  跳過權限: (\d+)/);
          const newCreatedMatch = stdout.match(/新增 (\d+) 個/);
          const newUpdatedMatch = stdout.match(/更新 (\d+) 個/);

          if (createdMatch) {
            created = parseInt(createdMatch[1], 10);
          } else if (newCreatedMatch) {
            created = parseInt(newCreatedMatch[1], 10);
          }

          if (updatedMatch) {
            updated = parseInt(updatedMatch[1], 10);
          } else if (newUpdatedMatch) {
            updated = parseInt(newUpdatedMatch[1], 10);
          }
        }

        console.log('同步結果 - 建立:', created, '更新:', updated, '錯誤:', errors);
        console.log('=== 權限同步完成 ===');

        // 返回前端期望的格式
        return {
          total: report.total || 0,
          created: created,
          updated: updated,
          errors: errors,
          timestamp: report.timestamp || new Date().toISOString(),
          byScope: report.byScope || {},
          byCategory: report.byCategory || {},
        };
      } else {
        console.warn('權限同步報告不存在於:', reportPath);
      }

      // 如果報告不存在，返回默認值
      console.warn('權限同步報告不存在');
      return {
        total: 0,
        created: 0,
        updated: 0,
        errors: 0,
        timestamp: new Date().toISOString(),
        byScope: {},
        byCategory: {},
      };
    } catch (error) {
      console.error('同步權限時發生錯誤:', error);
      // 拋出異常而不是返回錯誤結果，讓上層服務能正確處理
      throw new Error(`權限同步執行失敗: ${error.message}`);
    }
  }

  /**
   * 獲取權限同步狀態
   */
  async getSyncStatus(): Promise<SyncStatusDto> {
    try {
      // 獲取資料庫中的權限數量
      const dbPermissions = await this.prisma.permissions.count();

      // 掃描程式碼中的權限定義
      const codePermissions = await this.scanCodePermissions();

      // 執行預覽模式的同步來檢查實際差異
      let needsSync = false;
      let pendingChanges = 0;

      try {
        // 執行預覽模式的 CLI 命令來獲取精確的差異信息
        const currentDir = process.cwd();
        const isInBackendDir =
          currentDir.includes('/apps/backend') || currentDir.endsWith('backend');
        const backendDir = isInBackendDir ? currentDir : path.join(currentDir, 'apps', 'backend');

        const command = 'pnpm db:sync-perms --dry-run';

        const { stdout, stderr } = await execAsync(command, {
          cwd: backendDir,
          env: {
            ...process.env,
            NODE_ENV: 'development',
            DATABASE_URL: process.env.DATABASE_URL,
          },
        });

        // 從輸出中解析實際的變更信息
        const createdMatch = stdout.match(/新增\s*(\d+)\s*個/);
        const updatedMatch = stdout.match(/更新\s*(\d+)\s*個/);
        const deprecatedMatch = stdout.match(/標記廢棄\s*(\d+)\s*個/);

        const created = createdMatch ? parseInt(createdMatch[1], 10) : 0;
        const updated = updatedMatch ? parseInt(updatedMatch[1], 10) : 0;
        const deprecated = deprecatedMatch ? parseInt(deprecatedMatch[1], 10) : 0;

        pendingChanges = created + updated + deprecated;
        needsSync = pendingChanges > 0;

        console.log(
          `同步狀態檢查: 新增=${created}, 更新=${updated}, 廢棄=${deprecated}, 需要同步=${needsSync}`,
        );
      } catch (error) {
        console.warn('執行預覽檢查失敗，回退到數量比較:', error.message);
        // 回退到簡單的數量比較
        needsSync = codePermissions.length !== dbPermissions;
        pendingChanges = Math.abs(codePermissions.length - dbPermissions);
      }

      // 獲取最後同步時間（從系統日誌中）
      const lastSyncLog = await this.prisma.system_logs.findFirst({
        where: {
          action: 'permission-sync',
          message: { contains: 'finished' },
        },
        orderBy: { created_at: 'desc' },
      });

      return {
        last_sync_time: lastSyncLog?.created_at.toISOString(),
        needs_sync: needsSync,
        pending_changes: pendingChanges,
        total_permissions: dbPermissions,
        code_permissions: codePermissions.length,
        db_permissions: dbPermissions,
      };
    } catch (error) {
      console.error('獲取同步狀態失敗:', error);
      throw new Error('無法獲取權限同步狀態');
    }
  }

  /**
   * 掃描程式碼中的權限定義
   */
  async scanPermissions(scanDto: ScanPermissionsDto): Promise<ScanResultDto> {
    try {
      const startTime = Date.now();

      // 掃描程式碼中的權限
      const permissions = await this.scanCodePermissions();
      const filesScanned = await this.countScannedFiles();

      // 檢查硬編碼警告
      const hardcodedWarnings = await this.checkHardcodedPermissions();

      return this.toScanResultDto(filesScanned, permissions, hardcodedWarnings);
    } catch (error) {
      console.error('掃描權限失敗:', error);
      return this.toErrorScanResultDto(error);
    }
  }

  /**
   * 掃描程式碼中的權限定義（實際實現）
   */
  private async scanCodePermissions(): Promise<any[]> {
    try {
      // 執行權限掃描腳本
      const currentDir = process.cwd();
      const isInBackendDir = currentDir.includes('/apps/backend') || currentDir.endsWith('backend');
      const backendDir = isInBackendDir ? currentDir : path.join(currentDir, 'apps', 'backend');

      const command = 'pnpm run db:scan-perms --no-cache';

      const { stdout, stderr } = await execAsync(command, {
        cwd: backendDir,
        env: {
          ...process.env,
          NODE_ENV: 'development',
          DATABASE_URL: process.env.DATABASE_URL,
        },
      });

      // 讀取掃描報告
      const reportPath = path.join(backendDir, 'reports', 'permission-scan-report.json');

      if (fs.existsSync(reportPath)) {
        const reportContent = fs.readFileSync(reportPath, 'utf-8');
        const report = JSON.parse(reportContent);

        // 返回掃描到的權限列表
        return report.permissions || [];
      } else {
        console.warn('權限掃描報告不存在於:', reportPath);
        return [];
      }
    } catch (error) {
      console.error('掃描程式碼權限失敗:', error);
      // 如果掃描失敗，返回空陣列而不是拋出錯誤
      return [];
    }
  }

  /**
   * 計算掃描的檔案數量
   */
  private async countScannedFiles(): Promise<number> {
    try {
      // 執行權限掃描腳本並讀取報告
      const currentDir = process.cwd();
      const isInBackendDir = currentDir.includes('/apps/backend') || currentDir.endsWith('backend');
      const backendDir = isInBackendDir ? currentDir : path.join(currentDir, 'apps', 'backend');

      const reportPath = path.join(backendDir, 'reports', 'permission-scan-report.json');

      if (fs.existsSync(reportPath)) {
        const reportContent = fs.readFileSync(reportPath, 'utf-8');
        const report = JSON.parse(reportContent);

        return report.filesScanned || 0;
      }

      return 0;
    } catch (error) {
      console.error('計算掃描檔案數量失敗:', error);
      return 0;
    }
  }

  /**
   * 檢查硬編碼權限
   */
  private async checkHardcodedPermissions(): Promise<any[]> {
    try {
      // 執行權限掃描腳本並讀取報告
      const currentDir = process.cwd();
      const isInBackendDir = currentDir.includes('/apps/backend') || currentDir.endsWith('backend');
      const backendDir = isInBackendDir ? currentDir : path.join(currentDir, 'apps', 'backend');

      const reportPath = path.join(backendDir, 'reports', 'permission-scan-report.json');

      if (fs.existsSync(reportPath)) {
        const reportContent = fs.readFileSync(reportPath, 'utf-8');
        const report = JSON.parse(reportContent);

        return report.warnings || [];
      }

      return [];
    } catch (error) {
      console.error('檢查硬編碼權限失敗:', error);
      return [];
    }
  }

  private toScanResultDto(
    filesScanned: number,
    permissions: any[],
    hardcodedWarnings: any[],
  ): ScanResultDto {
    return {
      success: true,
      files_scanned: filesScanned,
      permissions_found: permissions.length,
      hardcoded_warnings: hardcodedWarnings.length,
      timestamp: new Date().toISOString(),
      permissions,
      warnings: hardcodedWarnings,
    };
  }

  private toErrorScanResultDto(error: any): ScanResultDto {
    return {
      success: false,
      files_scanned: 0,
      permissions_found: 0,
      hardcoded_warnings: 0,
      timestamp: new Date().toISOString(),
      warnings: [`掃描失敗: ${error.message}`],
    };
  }

  private toSyncResultDto(result: any): SyncResultDto {
    return {
      success: true,
      total: result.total || 0,
      created: result.created || 0,
      updated: result.updated || 0,
      deprecated: result.deprecated || 0,
      errors: result.errors || 0,
      timestamp: new Date().toISOString(),
      changes: result.changes || [],
      error_details: [],
    };
  }
}
