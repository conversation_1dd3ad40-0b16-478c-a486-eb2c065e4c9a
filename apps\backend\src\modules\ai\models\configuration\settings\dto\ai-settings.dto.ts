import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNumber,
  IsString,
  IsOptional,
  ValidateNested,
  IsObject,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';

export class ProviderConfig {
  @ApiProperty({ description: 'API Key ID' })
  @IsUUID()
  keyId: string;

  @ApiProperty({ description: '是否為預設金鑰', required: false })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;
}

export class BotBinding {
  @ApiProperty({ description: 'Bot ID' })
  @IsString()
  agentId: string;

  @ApiProperty({ description: '功能名稱' })
  @IsString()
  feature: string;

  @ApiProperty({ description: 'API Key ID', required: false })
  @IsUUID()
  @IsOptional()
  keyId?: string;
}

export class AIFeatureConfig {
  @ApiProperty({ description: '功能是否啟用' })
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({ description: 'API Key ID', required: false })
  @IsUUID()
  @IsOptional()
  keyId?: string;

  @ApiProperty({ description: 'Bot ID', required: false })
  @IsUUID()
  @IsOptional()
  agentId?: string;
}

export class AIFeaturesDto {
  @ApiProperty({ description: 'AI 提示詞優化設定' })
  @ValidateNested()
  @Type(() => AIFeatureConfig)
  promptOptimization: AIFeatureConfig;

  @ApiProperty({ description: 'AI 寫作助手設定' })
  @ValidateNested()
  @Type(() => AIFeatureConfig)
  writingAssistant: AIFeatureConfig;

  @ApiProperty({ description: 'AI 圖像生成設定' })
  @ValidateNested()
  @Type(() => AIFeatureConfig)
  imageGeneration: AIFeatureConfig;

  @ApiProperty({ description: 'AI 專案助手設定' })
  @ValidateNested()
  @Type(() => AIFeatureConfig)
  projectAssistant: AIFeatureConfig;

  @ApiProperty({ description: 'AI 文件分析設定' })
  @ValidateNested()
  @Type(() => AIFeatureConfig)
  documentAnalysis: AIFeatureConfig;

  @ApiProperty({ description: 'AI 對話助手設定' })
  @ValidateNested()
  @Type(() => AIFeatureConfig)
  chatAssistant: AIFeatureConfig;
}

export class UpdateAISettingsDto {
  @ApiProperty({ description: '當前使用的提供商', required: true })
  @IsString()
  provider: string;

  @ApiProperty({ description: 'Provider 設定', required: true })
  @IsObject()
  @ValidateNested()
  @Type(() => ProviderConfig)
  providerConfigs: Record<string, ProviderConfig>;

  @ApiProperty({ description: '每月使用額度' })
  @IsNumber()
  monthlyQuota: number;

  @ApiProperty({ description: '預設模型', required: false })
  @IsString()
  @IsOptional()
  defaultModel?: string;

  @ApiProperty({ description: '功能設定' })
  @ValidateNested()
  @Type(() => AIFeaturesDto)
  features: AIFeaturesDto;

  @ApiProperty({ description: '是否啟用 AI 功能（已棄用）' })
  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @ApiProperty({ description: '是否啟用 AI 提示詞優化（已棄用）' })
  @IsBoolean()
  @IsOptional()
  enableAiPromptOptimization?: boolean;

  @ApiProperty({ description: '是否啟用 AI 寫作助手（已棄用）' })
  @IsBoolean()
  @IsOptional()
  enableAiWritingAssistant?: boolean;

  @ApiProperty({ description: '是否啟用 AI 圖像生成（已棄用）' })
  @IsBoolean()
  @IsOptional()
  enableAiImageGeneration?: boolean;

  @ApiProperty({ description: '是否啟用 AI 專案助手（已棄用）' })
  @IsBoolean()
  @IsOptional()
  enableAiProjectAssistant?: boolean;

  @ApiProperty({ description: '是否啟用 AI 文件分析（已棄用）' })
  @IsBoolean()
  @IsOptional()
  enableAiDocumentAnalysis?: boolean;

  @ApiProperty({ description: '是否啟用 AI 對話助手（已棄用）' })
  @IsBoolean()
  @IsOptional()
  enableAiChatAssistant?: boolean;

  @ApiProperty({ description: 'Bot 綁定設定（已棄用）' })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => BotBinding)
  botBindings?: BotBinding[];
}

export class UpdateAIFeatureDto {
  @ApiProperty({ description: '功能是否啟用' })
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({ description: 'API Key ID', required: false })
  @IsUUID()
  @IsOptional()
  keyId?: string;

  @ApiProperty({ description: 'Bot ID', required: false })
  @IsUUID()
  @IsOptional()
  agentId?: string;
}

export class UpdateGlobalSettingsDto {
  @ApiProperty({ description: '是否全域啟用 AI 功能' })
  @IsBoolean()
  isAiGloballyEnabled: boolean;

  @ApiProperty({ description: '每月全域 Token 配額' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  globalMonthlyQuotaTokens?: number;

  @ApiProperty({ description: '每月全域 API 呼叫配額' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  globalMonthlyQuotaCalls?: number;
}

export class UpdateFeatureConfigDto {
  @ApiProperty({ description: '功能是否啟用' })
  @IsBoolean()
  isEnabled: boolean;

  @ApiProperty({ description: 'Bot ID' })
  @IsUUID()
  @IsOptional()
  agentId?: string;
}
