-- Create<PERSON>num
CREATE TYPE "WorkflowVisibility" AS ENUM ('PRIVATE', 'WORKSPACE', 'TENANT', 'PUBLIC');

-- CreateEnum
CREATE TYPE "WorkflowStatus" AS ENUM ('DRAFT', 'PUBLISHED', 'ARCHIVED', 'DEPRECATED');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "WorkflowNodeType" AS ENUM ('INPUT', 'OUTPUT', 'AI_BOT', 'AI_ANALYSIS', 'PROMPT_TEMPLATE', 'DATA_TRANSFORM', 'DATA_FILTER', 'DATA_MERGE', 'CONDITION', 'LOOP', 'PARALLEL', 'API_CALL', 'DATABASE', 'FILE_OPERATION', 'NOTIFICATION', 'EMAIL', 'WEBHOOK');

-- CreateEnum
CREATE TYPE "WorkflowExecutionStatus" AS ENUM ('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT');

-- CreateTable
CREATE TABLE "ai_service_workflows" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "version" TEXT NOT NULL DEFAULT '1.0.0',
    "is_published" BOOLEAN NOT NULL DEFAULT false,
    "is_template" BOOLEAN NOT NULL DEFAULT false,
    "config" JSONB,
    "input_schema" JSONB,
    "output_schema" JSONB,
    "visibility" "WorkflowVisibility" NOT NULL DEFAULT 'PRIVATE',
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT,
    "tenant_id" TEXT,
    "workspace_id" TEXT,
    "status" "WorkflowStatus" NOT NULL DEFAULT 'DRAFT',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "published_at" TIMESTAMP(3),

    CONSTRAINT "ai_service_workflows_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_nodes" (
    "id" TEXT NOT NULL,
    "workflow_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "node_type" "WorkflowNodeType" NOT NULL,
    "position_x" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "position_y" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "config" JSONB NOT NULL,
    "execution_order" INTEGER,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workflow_nodes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "node_connections" (
    "id" TEXT NOT NULL,
    "source_node_id" TEXT NOT NULL,
    "target_node_id" TEXT NOT NULL,
    "source_port" TEXT NOT NULL DEFAULT 'output',
    "target_port" TEXT NOT NULL DEFAULT 'input',
    "config" JSONB,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "node_connections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_executions" (
    "id" TEXT NOT NULL,
    "workflow_id" TEXT NOT NULL,
    "input_data" JSONB NOT NULL,
    "output_data" JSONB,
    "execution_log" JSONB,
    "status" "WorkflowExecutionStatus" NOT NULL DEFAULT 'PENDING',
    "error_message" TEXT,
    "executed_by" TEXT NOT NULL,
    "executor_type" TEXT NOT NULL,
    "tenant_id" TEXT,
    "workspace_id" TEXT,
    "started_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMP(3),

    CONSTRAINT "workflow_executions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ai_service_workflows_tenant_id_idx" ON "ai_service_workflows"("tenant_id");

-- CreateIndex
CREATE INDEX "ai_service_workflows_workspace_id_idx" ON "ai_service_workflows"("workspace_id");

-- CreateIndex
CREATE INDEX "ai_service_workflows_created_by_idx" ON "ai_service_workflows"("created_by");

-- CreateIndex
CREATE INDEX "ai_service_workflows_status_idx" ON "ai_service_workflows"("status");

-- CreateIndex
CREATE INDEX "ai_service_workflows_is_published_idx" ON "ai_service_workflows"("is_published");

-- CreateIndex
CREATE INDEX "workflow_nodes_workflow_id_idx" ON "workflow_nodes"("workflow_id");

-- CreateIndex
CREATE INDEX "workflow_nodes_node_type_idx" ON "workflow_nodes"("node_type");

-- CreateIndex
CREATE INDEX "workflow_nodes_execution_order_idx" ON "workflow_nodes"("execution_order");

-- CreateIndex
CREATE INDEX "node_connections_source_node_id_idx" ON "node_connections"("source_node_id");

-- CreateIndex
CREATE INDEX "node_connections_target_node_id_idx" ON "node_connections"("target_node_id");

-- CreateIndex
CREATE UNIQUE INDEX "node_connections_source_node_id_target_node_id_source_port__key" ON "node_connections"("source_node_id", "target_node_id", "source_port", "target_port");

-- CreateIndex
CREATE INDEX "workflow_executions_workflow_id_idx" ON "workflow_executions"("workflow_id");

-- CreateIndex
CREATE INDEX "workflow_executions_tenant_id_idx" ON "workflow_executions"("tenant_id");

-- CreateIndex
CREATE INDEX "workflow_executions_workspace_id_idx" ON "workflow_executions"("workspace_id");

-- CreateIndex
CREATE INDEX "workflow_executions_executed_by_executor_type_idx" ON "workflow_executions"("executed_by", "executor_type");

-- CreateIndex
CREATE INDEX "workflow_executions_status_idx" ON "workflow_executions"("status");

-- CreateIndex
CREATE INDEX "workflow_executions_started_at_idx" ON "workflow_executions"("started_at");

-- AddForeignKey
ALTER TABLE "ai_service_workflows" ADD CONSTRAINT "ai_service_workflows_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_service_workflows" ADD CONSTRAINT "ai_service_workflows_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_nodes" ADD CONSTRAINT "workflow_nodes_workflow_id_fkey" FOREIGN KEY ("workflow_id") REFERENCES "ai_service_workflows"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "node_connections" ADD CONSTRAINT "node_connections_source_node_id_fkey" FOREIGN KEY ("source_node_id") REFERENCES "workflow_nodes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "node_connections" ADD CONSTRAINT "node_connections_target_node_id_fkey" FOREIGN KEY ("target_node_id") REFERENCES "workflow_nodes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_executions" ADD CONSTRAINT "workflow_executions_workflow_id_fkey" FOREIGN KEY ("workflow_id") REFERENCES "ai_service_workflows"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_executions" ADD CONSTRAINT "workflow_executions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_executions" ADD CONSTRAINT "workflow_executions_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;
