# CASL 權限系統

HorizAI SaaS 平台的完整權限管理系統，基於 CASL (Code Access Security Layer) 實作。

## 🎯 功能特色

- **多種權限檢查方式** - 支援 `@CheckPolicies` 和 `@RequirePermissions` 裝飾器
- **租戶隔離** - 自動驗證多租戶環境下的資源隔離
- **詳細日誌記錄** - 完整的權限檢查日誌和錯誤追蹤
- **高效能** - 內建快取和最佳化的權限檢查邏輯
- **類型安全** - 完整的 TypeScript 類型定義

## 🚀 快速開始

### 基本使用

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { 
  UnifiedPermissionGuard, 
  RequireRead, 
  CheckPolicies 
} from '@/casl';

@Controller('users')
@UseGuards(UnifiedPermissionGuard)
export class UsersController {
  
  // 使用 @RequireRead 裝飾器
  @Get()
  @RequireRead('User')
  async getUsers() {
    return this.usersService.findAll();
  }

  // 使用 @CheckPolicies 裝飾器
  @Get(':id')
  @CheckPolicies((ability) => ability.can('read', 'User'))
  async getUser(@Param('id') id: string) {
    return this.usersService.findOne(id);
  }
}
```

### 租戶隔離

```typescript
import { TenantIsolated, RequireUpdate } from '@/casl';

@Controller('tenant/:tenantId/users')
@UseGuards(UnifiedPermissionGuard)
export class TenantUsersController {
  
  @Put(':id')
  @TenantIsolated() // 確保租戶隔離
  @RequireUpdate('User', { tenantId: '{{tenantId}}' })
  async updateUser(
    @Param('tenantId') tenantId: string,
    @Param('id') id: string,
    @Body() updateDto: UpdateUserDto
  ) {
    return this.usersService.update(id, updateDto);
  }
}
```

## 📋 裝飾器參考

### @RequirePermissions

要求特定權限的裝飾器，提供比 `@CheckPolicies` 更簡潔的 API。

```typescript
@RequirePermissions([
  { action: 'read', subject: 'User' },
  { action: 'update', subject: 'User', conditions: { id: '{{userId}}' } }
])
async updateUser() { ... }
```

### 便利裝飾器

```typescript
// 基本權限
@RequireRead('User')           // 讀取權限
@RequireCreate('User')         // 建立權限
@RequireUpdate('User')         // 更新權限
@RequireDelete('User')         // 刪除權限
@RequireManage('User')         // 管理權限
@RequireAccess('AdminPanel')   // 訪問權限

// 帶條件的權限
@RequireUpdate('User', { id: '{{userId}}' })
@RequireRead('Workspace', { tenantId: '{{tenantId}}' })
```

### 租戶上下文裝飾器

```typescript
@TenantIsolated()              // 強制租戶隔離
@CrossTenant()                 // 允許跨租戶訪問
@TenantContext({               // 自訂租戶驗證
  required: true,
  sources: ['params', 'query'],
  validator: async (req, user) => {
    // 自訂驗證邏輯
    return true;
  }
})
```

## 🛡️ 守衛選擇

### UnifiedPermissionGuard (推薦)

整合所有功能的統一守衛，支援：
- `@CheckPolicies` 和 `@RequirePermissions` 裝飾器
- 租戶上下文驗證
- 詳細的日誌記錄和錯誤處理

```typescript
@UseGuards(UnifiedPermissionGuard)
export class MyController { ... }
```

### PoliciesGuard

基本的權限守衛，僅支援 `@CheckPolicies` 裝飾器。

```typescript
@UseGuards(PoliciesGuard)
export class MyController { ... }
```

### EnhancedPermissionGuard

增強的權限守衛，支援兩種裝飾器但不包含租戶驗證。

```typescript
@UseGuards(EnhancedPermissionGuard)
export class MyController { ... }
```

## 🔧 服務使用

### PermissionCheckerService

提供程式化的權限檢查功能。

```typescript
import { PermissionCheckerService } from '@/casl';

@Injectable()
export class MyService {
  constructor(
    private permissionChecker: PermissionCheckerService
  ) {}

  async someMethod(user: JwtUser) {
    // 檢查單一權限
    const canRead = await this.permissionChecker.canRead(user, 'User');
    
    // 批量檢查權限
    const result = await this.permissionChecker.checkPermissions(user, [
      { action: 'read', subject: 'User' },
      { action: 'update', subject: 'User' }
    ]);
    
    // 檢查管理員身份
    const isAdmin = this.permissionChecker.isSystemAdmin(user);
  }
}
```

## 🏗️ 架構說明

### 權限檢查流程

1. **認證檢查** - 驗證用戶是否已認證
2. **能力建立** - 根據用戶角色和權限建立 CASL Ability 實例
3. **租戶驗證** - 檢查租戶上下文和資源隔離
4. **權限檢查** - 執行具體的權限驗證
5. **結果記錄** - 記錄檢查結果和執行時間

### 條件變數替換

在權限條件中可以使用以下變數：

- `{{userId}}` - 當前用戶 ID
- `{{tenantId}}` - 當前用戶的租戶 ID
- `{{userType}}` - 用戶類型 (system/tenant)

```typescript
@RequireUpdate('User', { 
  $or: [
    { id: '{{userId}}' },           // 用戶可以更新自己
    { managerId: '{{userId}}' }     // 或更新下屬
  ]
})
```

## 🚨 最佳實踐

### 1. 選擇合適的守衛

- 新專案使用 `UnifiedPermissionGuard`
- 簡單場景使用 `PoliciesGuard`
- 需要租戶隔離時必須使用 `UnifiedPermissionGuard`

### 2. 權限粒度設計

```typescript
// ✅ 好的做法 - 明確的權限定義
@RequireUpdate('User')
@RequireRead('UserProfile', { userId: '{{userId}}' })

// ❌ 避免 - 過於寬泛的權限
@RequireManage('all')
```

### 3. 錯誤處理

```typescript
try {
  const result = await this.permissionChecker.checkPermission(user, 'read', 'User');
  if (!result.granted) {
    throw new ForbiddenException(result.reason);
  }
} catch (error) {
  this.logger.error('Permission check failed:', error);
  throw new InternalServerErrorException('權限檢查失敗');
}
```

### 4. 效能最佳化

- 使用批量權限檢查減少資料庫查詢
- 在控制器層級設定守衛，避免重複檢查
- 合理使用快取機制

## 🔍 除錯和監控

### 日誌級別

- `DEBUG` - 詳細的權限檢查過程
- `WARN` - 權限拒絕和異常情況
- `ERROR` - 系統錯誤和未預期的異常

### 監控指標

- 權限檢查執行時間
- 權限拒絕率
- 錯誤發生頻率
- 租戶隔離違規次數

## 📚 進階用法

### 自訂權限策略

```typescript
const customPolicy = (ability: AppAbility) => {
  return ability.can('read', 'User') && 
         ability.can('update', 'UserProfile');
};

@CheckPolicies(customPolicy)
async complexOperation() { ... }
```

### 動態權限檢查

```typescript
@Get(':id')
async getUser(@Param('id') id: string, @Req() req: Request) {
  const ability = req.ability;
  const user = await this.usersService.findOne(id);
  
  if (!ability.can('read', user)) {
    throw new ForbiddenException('無權訪問此用戶資料');
  }
  
  return user;
}
```
