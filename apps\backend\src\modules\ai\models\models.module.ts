import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { CaslModule } from '@/casl/casl.module';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { AuthModule } from '@/modules/core/auth/auth.module';
import { AiModelsController } from './configuration/models/ai-models.controller';
import { AiModelsService } from './configuration/models/ai-models.service';
import { AiKeysController } from './configuration/keys/ai-keys.controller';
import { AiKeysService } from './configuration/keys/ai-keys.service';
import { AiSettingsController } from './configuration/settings/ai-settings.controller';
import { AiSettingsService } from './configuration/settings/ai-settings.service';
import { AiUsageController } from './configuration/usage/ai-usage.controller';
import { UsageStatisticsService } from '../../core/usage-tracking/services/usage-statistics.service';
import { AiPriceCrawlerService } from './configuration/pricing/ai-price-crawler.service';
import { AiPricingService } from './configuration/pricing/ai-pricing.service';
import { TenantQuotaService } from '../../core/usage-tracking/services/tenant-quota.service';
import { AiIntegrationsService } from '../../core/ai-integrations/ai-integrations.service';
import { LlmModule } from '../llm/llm.module';
import { AiProviderFactory } from './core/providers/factory';
import { AiToolsModule } from './configuration/tools/ai-tools.module';

@Module({
  imports: [HttpModule, CaslModule, PrismaModule, AuthModule, AiToolsModule, LlmModule],
  controllers: [AiModelsController, AiKeysController, AiSettingsController, AiUsageController],
  providers: [
    AiModelsService,
    AiKeysService,
    AiSettingsService,
    UsageStatisticsService,
    AiPriceCrawlerService,
    AiPricingService,
    TenantQuotaService,
    AiIntegrationsService,
    AiProviderFactory,
  ],
  exports: [
    AiModelsService,
    AiKeysService,
    AiSettingsService,
    UsageStatisticsService,
    AiPriceCrawlerService,
    AiPricingService,
    TenantQuotaService,
    AiIntegrationsService,
    AiProviderFactory,
    AiToolsModule,
    LlmModule,
  ],
})
export class ModelsModule {}
