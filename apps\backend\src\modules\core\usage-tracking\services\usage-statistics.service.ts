import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import {
  AiUsageQueryDto,
  AiUsageStatisticsDto,
  DetailedAiUsageStatisticsDto,
} from '../dto/usage.dto';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class UsageStatisticsService {
  constructor(private readonly prisma: PrismaService) {}

  private buildWhereClause(query: AiUsageQueryDto): any {
    const where: any = {};

    if (query.startDate) {
      const prev =
        typeof where.request_timestamp === 'object' && where.request_timestamp !== null
          ? where.request_timestamp
          : {};
      where.request_timestamp = {
        ...prev,
        gte: query.startDate,
      };
    }

    if (query.endDate) {
      const prev =
        typeof where.request_timestamp === 'object' && where.request_timestamp !== null
          ? where.request_timestamp
          : {};
      where.request_timestamp = {
        ...prev,
        lte: query.endDate,
      };
    }

    if (query.tenant_id) {
      where.tenant_id = query.tenant_id;
    }

    if (query.userId) {
      where.user_id = query.userId;
    }

    if (query.agentId) {
      where.agent_id = query.agentId;
    }

    if (query.featureKey) {
      where.feature_key = query.featureKey;
    }

    if (query.apiKeyId) {
      where.api_key_id = query.apiKeyId;
    }

    if (query.provider) {
      where.provider = query.provider;
    }

    if (query.modelName) {
      where.model_name = query.modelName;
    }

    return where;
  }

  private async calculateStatistics(logs: any[]): Promise<AiUsageStatisticsDto> {
    if (!logs.length) {
      return {
        totalCalls: 0,
        totalInputTokens: 0,
        totalOutputTokens: 0,
        totalCost: 0,
        successfulCalls: 0,
        failedCalls: 0,
        averageResponseTime: 0,
      };
    }

    const totalCalls = logs.length;
    const totalInputTokens = logs.reduce((sum, log) => sum + log.input_tokens, 0);
    const totalOutputTokens = logs.reduce((sum, log) => sum + log.output_tokens, 0);
    const totalCost = logs.reduce((sum, log) => sum + Number(log.estimated_cost), 0);
    const successfulCalls = logs.filter((log) => log.is_success).length;
    const failedCalls = totalCalls - successfulCalls;

    const responseTimes = logs
      .filter((log) => log.response_timestamp && log.request_timestamp)
      .map(
        (log) =>
          new Date(log.response_timestamp).getTime() - new Date(log.request_timestamp).getTime(),
      );

    const averageResponseTime = responseTimes.length
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;

    return {
      totalCalls,
      totalInputTokens,
      totalOutputTokens,
      totalCost,
      successfulCalls,
      failedCalls,
      averageResponseTime,
    };
  }

  async getDetailedStatistics(query: AiUsageQueryDto): Promise<DetailedAiUsageStatisticsDto> {
    const where = this.buildWhereClause(query);

    // 讀取所有日誌
    const logs = await this.prisma.ai_usage_logs.findMany({
      where,
    });

    // 計算總體統計
    const overall = await this.calculateStatistics(logs);

    // 依日期分組
    const byDate = await this.prisma.ai_usage_logs.groupBy({
      by: ['request_timestamp'],
      where,
      _sum: {
        input_tokens: true,
        output_tokens: true,
        call_count: true,
        estimated_cost: true,
      },
      _count: {
        is_success: true,
      },
    });

    // 依 Provider 分組
    const byProvider = await this.prisma.ai_usage_logs.groupBy({
      by: ['provider'],
      where,
      _sum: {
        input_tokens: true,
        output_tokens: true,
        call_count: true,
        estimated_cost: true,
      },
      _count: {
        is_success: true,
      },
    });

    // 依 Model 分組
    const byModel = await this.prisma.ai_usage_logs.groupBy({
      by: ['model_name'],
      where,
      _sum: {
        input_tokens: true,
        output_tokens: true,
        call_count: true,
        estimated_cost: true,
      },
      _count: {
        is_success: true,
      },
    });

    // 依功能分組
    const byFeature = await this.prisma.ai_usage_logs.groupBy({
      by: ['feature_key'],
      where,
      _sum: {
        input_tokens: true,
        output_tokens: true,
        call_count: true,
        estimated_cost: true,
      },
      _count: {
        is_success: true,
      },
    });

    // 依 Agent 分組
    const byAgent = await this.prisma.ai_usage_logs.groupBy({
      by: ['agent_id'],
      where,
      _sum: {
        input_tokens: true,
        output_tokens: true,
        call_count: true,
        estimated_cost: true,
      },
      _count: {
        is_success: true,
      },
    });

    return {
      overall,
      byDate: byDate.map((group) => ({
        date: group.request_timestamp,
        totalCalls: group._sum.call_count || 0,
        totalInputTokens: group._sum.input_tokens || 0,
        totalOutputTokens: group._sum.output_tokens || 0,
        totalCost: Number(group._sum.estimated_cost) || 0,
        successfulCalls: group._count.is_success,
        failedCalls: (group._sum.call_count || 0) - group._count.is_success,
        averageResponseTime: 0,
      })),
      byProvider: byProvider.map((group) => ({
        provider: group.provider,
        totalCalls: group._sum.call_count || 0,
        totalInputTokens: group._sum.input_tokens || 0,
        totalOutputTokens: group._sum.output_tokens || 0,
        totalCost: Number(group._sum.estimated_cost) || 0,
        successfulCalls: group._count.is_success,
        failedCalls: (group._sum.call_count || 0) - group._count.is_success,
        averageResponseTime: 0,
      })),
      byModel: byModel.map((group) => ({
        modelName: group.model_name,
        totalCalls: group._sum.call_count || 0,
        totalInputTokens: group._sum.input_tokens || 0,
        totalOutputTokens: group._sum.output_tokens || 0,
        totalCost: Number(group._sum.estimated_cost) || 0,
        successfulCalls: group._count.is_success,
        failedCalls: (group._sum.call_count || 0) - group._count.is_success,
        averageResponseTime: 0,
      })),
      byFeature: byFeature.map((group) => ({
        featureKey: group.feature_key ?? '',
        totalCalls: group._sum.call_count || 0,
        totalInputTokens: group._sum.input_tokens || 0,
        totalOutputTokens: group._sum.output_tokens || 0,
        totalCost: Number(group._sum.estimated_cost) || 0,
        successfulCalls: group._count.is_success,
        failedCalls: (group._sum.call_count || 0) - group._count.is_success,
        averageResponseTime: 0,
      })),
      byAgent: byAgent.map((group) => ({
        agentId: group.agent_id,
        agentName: 'Unknown',
        totalCalls: group._sum.call_count || 0,
        totalInputTokens: group._sum.input_tokens || 0,
        totalOutputTokens: group._sum.output_tokens || 0,
        totalCost: Number(group._sum.estimated_cost) || 0,
        successfulCalls: group._count.is_success,
        failedCalls: (group._sum.call_count || 0) - group._count.is_success,
        averageResponseTime: 0,
      })),
    };
  }
}
