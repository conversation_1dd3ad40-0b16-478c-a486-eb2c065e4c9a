import {
  IsString,
  IsNumber,
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsEnum,
  Min,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum BillingCycle {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export class PlanFeature {
  @ApiProperty({ description: '功能 ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ description: '功能名稱' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '功能描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '是否包含此功能' })
  @IsBoolean()
  @IsOptional()
  included?: boolean;
}

export class PlanLimits {
  @ApiProperty({ description: '使用者數上限', default: 5 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  users?: number;

  @ApiProperty({ description: '專案數上限', default: 3 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  projects?: number;

  @ApiProperty({ description: '儲存空間上限(GB)', default: 1 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  storage?: number;
}

export class CreatePlanDto {
  @ApiProperty({ description: '方案名稱' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '方案描述' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ description: '方案價格' })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({ description: '計費週期', enum: BillingCycle, default: BillingCycle.MONTHLY })
  @IsEnum(BillingCycle)
  @IsOptional()
  billingCycle?: BillingCycle;

  @ApiProperty({ description: '功能列表', type: [PlanFeature] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PlanFeature)
  @IsOptional()
  features?: PlanFeature[];

  @ApiProperty({ description: '使用者數上限', default: 5 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  maxUsers?: number;

  @ApiProperty({ description: '專案數上限', default: 3 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  maxProjects?: number;

  @ApiProperty({ description: '儲存空間上限(GB)', default: 1 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  maxStorage?: number;

  @ApiProperty({ description: '是否為熱門方案', default: false })
  @IsBoolean()
  @IsOptional()
  isPopular?: boolean;

  @ApiProperty({ description: '是否為企業版方案', default: false })
  @IsBoolean()
  @IsOptional()
  isEnterprise?: boolean;

  @ApiProperty({ description: '方案限制', type: PlanLimits })
  @ValidateNested()
  @Type(() => PlanLimits)
  @IsOptional()
  limits?: PlanLimits;
}
