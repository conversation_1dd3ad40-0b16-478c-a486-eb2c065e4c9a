import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsDateString,
  IsNumberString,
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsInt,
  Min,
  IsArray,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class SystemLogQueryDto {
  @ApiPropertyOptional({ description: '頁碼', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每頁數量', default: 50 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 50;

  @ApiPropertyOptional({ description: '日誌等級' })
  @IsOptional()
  @IsString()
  level?: string;

  @ApiPropertyOptional({ description: '使用者 ID' })
  @IsOptional()
  @IsString()
  user_id?: string;

  @ApiPropertyOptional({ description: '操作類型列表', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  action_types?: string[];

  @ApiPropertyOptional({ description: '開始日期 (YYYY-MM-DD)' })
  @IsOptional()
  @IsString()
  start_date?: string;

  @ApiPropertyOptional({ description: '結束日期 (YYYY-MM-DD)' })
  @IsOptional()
  @IsString()
  end_date?: string;

  @ApiPropertyOptional({ description: '狀態' })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({ description: '目標資源' })
  @IsOptional()
  @IsString()
  target_resource?: string;

  @ApiPropertyOptional({ description: '搜尋關鍵字' })
  @IsOptional()
  @IsString()
  search?: string;
}

export class SystemLogReportDto {
  @ApiPropertyOptional({ description: '報告格式', enum: ['json', 'csv', 'xlsx'] })
  @IsIn(['json', 'csv', 'xlsx'])
  format: 'json' | 'csv' | 'xlsx';

  @ApiPropertyOptional({ description: '查詢條件', type: SystemLogQueryDto })
  @IsOptional()
  @Type(() => SystemLogQueryDto)
  query?: Omit<SystemLogQueryDto, 'page' | 'limit'>;
}

export class SystemLogDto {
  @ApiProperty({ description: '日誌 ID' })
  id: string;

  @ApiProperty({ description: '日誌等級' })
  level: string;

  @ApiProperty({ description: '日誌訊息' })
  message: string;

  @ApiPropertyOptional({ description: '堆疊追蹤' })
  stack?: string | null;

  @ApiPropertyOptional({ description: '請求路徑' })
  path?: string | null;

  @ApiPropertyOptional({ description: 'HTTP 方法' })
  method?: string | null;

  @ApiPropertyOptional({ description: '使用者 ID' })
  user_id?: string | null;

  @ApiPropertyOptional({ description: 'IP 位址' })
  ip?: string | null;

  @ApiProperty({ description: '建立時間' })
  created_at: Date;

  @ApiPropertyOptional({ description: '操作類型' })
  action?: string | null;

  @ApiPropertyOptional({ description: '詳細資訊' })
  details?: any;

  @ApiPropertyOptional({ description: '錯誤訊息' })
  error_message?: string | null;

  @ApiPropertyOptional({ description: '狀態' })
  status?: string | null;

  @ApiPropertyOptional({ description: '目標資源' })
  target_resource?: string | null;

  @ApiPropertyOptional({ description: '目標資源 ID' })
  target_resource_id?: string | null;

  @ApiPropertyOptional({ description: '租戶 ID' })
  tenant_id?: string | null;
}

export class SystemLogListResponseDto {
  @ApiProperty({ description: '總數量' })
  total: number;

  @ApiProperty({ description: '日誌列表', type: [SystemLogDto] })
  logs: SystemLogDto[];
}
