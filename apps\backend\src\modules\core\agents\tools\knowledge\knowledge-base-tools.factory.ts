import { z } from 'zod';
import { RAGIngestionService } from '../../../../ai/rag/rag-ingestion.service';
import { RAGSecurityService } from '../../../../ai/rag/rag-security.service';
import { ToolExecutionContext, ToolConfig } from '../core/tool-registry.interface';
import { ToolExecutionError } from '../core/tool.errors';
import { SecurityAuditLog } from './security-audit-log.type';

export interface KnowledgeBaseConfig {
  maxResults?: number;
  defaultSimilarityThreshold?: number;
  enableSecurityLogging?: boolean;
  maxQueryLength?: number;
}

export interface KnowledgeBaseInput {
  query: string;
  maxResults?: number;
  similarityThreshold?: number;
}

export enum KnowledgeBaseErrorType {
  INVALID_INPUT = 'INVALID_INPUT',
  ACCESS_DENIED = 'ACCESS_DENIED',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  SERVICE_ERROR = 'SERVICE_ERROR',
  TENANT_ISOLATION_VIOLATION = 'TENANT_ISOLATION_VIOLATION',
}

/**
 * 創建知識庫搜尋工具
 */
export function createKnowledgeBaseSearchTool(
  ragIngestionService: RAGIngestionService,
  ragSecurityService: RAGSecurityService,
  context: ToolExecutionContext,
  config?: KnowledgeBaseConfig,
) {
  const inputSchema = z.object({
    query: z
      .string()
      .min(1)
      .max(config?.maxQueryLength || 1000)
      .describe('知識庫搜尋查詢字串'),
    maxResults: z
      .number()
      .min(1)
      .max(50)
      .optional()
      .default(config?.maxResults || 10)
      .describe('最大結果數量（1-50）'),
    similarityThreshold: z
      .number()
      .min(0)
      .max(1)
      .optional()
      .default(config?.defaultSimilarityThreshold || 0.7)
      .describe('相似度門檻（0-1）'),
  });

  const securityAuditLogs: SecurityAuditLog[] = [];

  return {
    name: 'knowledge_base_search',
    description: `知識庫搜尋工具 - 安全搜尋知識庫中的相關文件和資訊

此工具提供安全的知識庫存取功能，具備嚴格的租戶隔離和安全審計。

功能特色：
- 嚴格的租戶和工作區隔離
- 全面的安全審計日誌記錄
- 輸入驗證和清理
- 詳細的錯誤處理和上下文資訊
- 進階的相似度搜尋

使用方式：
- query: 搜尋查詢字串（必要）
- maxResults: 最大結果數量 1-50（可選，預設 ${config?.maxResults || 10}）
- similarityThreshold: 相似度門檻 0-1（可選，預設 ${config?.defaultSimilarityThreshold || 0.7}）

支援格式：
1. 簡單查詢：直接提供搜尋字串
2. 結構化查詢：
   {
     "query": "如何使用機器學習",
     "maxResults": 15,
     "similarityThreshold": 0.8
   }`,
    schema: inputSchema,
    call: async (input: KnowledgeBaseInput) => {
      const requestId = generateRequestId();
      const startTime = Date.now();

      try {
        // 1. 驗證租戶上下文
        await validateTenantContext(requestId, context);

        // 2. 驗證輸入
        const searchInput = await validateSearchInput(input, requestId, config);

        // 3. 安全驗證
        await performSecurityValidation(
          ragSecurityService,
          searchInput.query,
          requestId,
          context,
        );

        // 4. 執行搜尋（具租戶隔離）
        const results = await ragIngestionService.searchSimilarDocuments(
          searchInput.query,
          context.tenantId,
          context.workspaceId,
          searchInput.maxResults,
          searchInput.similarityThreshold,
        );

        // 5. 驗證結果存取權限
        const filteredResults = await validateAndFilterResults(results, requestId, context);

        // 6. 格式化回應
        const response = formatSearchResults(filteredResults, requestId, results.length);

        const duration = Date.now() - startTime;
        
        // 7. 記錄安全審計日誌
        if (config?.enableSecurityLogging !== false) {
          addSecurityAuditLog(securityAuditLogs, {
            requestId,
            timestamp: new Date().toISOString(),
            tenantId: context.tenantId,
            workspaceId: context.workspaceId,
            action: 'knowledge_base_search',
            input: searchInput.query,
            event: 'search_completed',
            details: {
              searchParams: searchInput,
              resultsCount: filteredResults.length,
              originalResultsCount: results.length,
              duration,
            },
            outcome: 'success',
            success: true,
            securityLevel: 'low',
          });
        }

        return response;
      } catch (error: any) {
        const duration = Date.now() - startTime;
        return handleKnowledgeBaseError(error, requestId, duration, securityAuditLogs, context, config);
      }
    },
  };
}

async function validateTenantContext(requestId: string, context: ToolExecutionContext): Promise<void> {
  // UUID格式驗證
  if (!isValidUUID(context.tenantId)) {
    throw new ToolExecutionError(
      'knowledge_base_search',
      'Invalid tenant ID format',
      { tenantId: context.tenantId, requestId },
    );
  }

  if (context.workspaceId && !isValidUUID(context.workspaceId)) {
    throw new ToolExecutionError(
      'knowledge_base_search',
      'Invalid workspace ID format',
      { workspaceId: context.workspaceId, requestId },
    );
  }
}

async function validateSearchInput(
  input: KnowledgeBaseInput,
  requestId: string,
  config?: KnowledgeBaseConfig,
): Promise<Required<KnowledgeBaseInput>> {
  const { query, maxResults = config?.maxResults || 10, similarityThreshold = config?.defaultSimilarityThreshold || 0.7 } = input;

  if (!query || typeof query !== 'string' || query.trim().length === 0) {
    throw new ToolExecutionError(
      'knowledge_base_search',
      'Search query cannot be empty',
      { input, requestId },
    );
  }

  if (query.length > (config?.maxQueryLength || 1000)) {
    throw new ToolExecutionError(
      'knowledge_base_search',
      `Search query too long (max ${config?.maxQueryLength || 1000} characters)`,
      { queryLength: query.length, requestId },
    );
  }

  return {
    query: query.trim(),
    maxResults,
    similarityThreshold,
  };
}

async function performSecurityValidation(
  ragSecurityService: RAGSecurityService,
  query: string,
  requestId: string,
  context: ToolExecutionContext,
): Promise<void> {
  try {
    // 基本安全檢查（如果 RAGSecurityService 沒有 validateQuery 方法）
    if (query.length > 1000) {
      throw new ToolExecutionError(
        'knowledge_base_search',
        'Query too long for security validation',
        { queryLength: query.length, requestId },
      );
    }

    // 檢查潛在的惡意模式
    const dangerousPatterns = [
      /script\s*>/i,
      /<\s*iframe/i,
      /javascript\s*:/i,
      /on\w+\s*=/i,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(query)) {
        throw new ToolExecutionError(
          'knowledge_base_search',
          'Query contains potentially dangerous content',
          { 
            query: query.substring(0, 100) + '...', // 限制日誌中的查詢長度
            requestId 
          },
        );
      }
    }
  } catch (error: any) {
    if (error instanceof ToolExecutionError) {
      throw error;
    }
    
    throw new ToolExecutionError(
      'knowledge_base_search',
      `Security validation error: ${error.message}`,
      { originalError: error, requestId },
    );
  }
}

async function validateAndFilterResults(
  results: any[],
  requestId: string,
  context: ToolExecutionContext,
): Promise<any[]> {
  if (!Array.isArray(results)) {
    throw new ToolExecutionError(
      'knowledge_base_search',
      'Invalid results format from search service',
      { resultType: typeof results, requestId },
    );
  }

  // 過濾和清理結果
  const filteredResults = results
    .filter((result) => {
      // 確保結果屬於正確的租戶
      return result.tenantId === context.tenantId &&
             (!context.workspaceId || result.workspaceId === context.workspaceId);
    })
    .map((result) => ({
      ...result,
      metadata: sanitizeMetadata(result.metadata),
    }));

  return filteredResults;
}

function formatSearchResults(results: any[], requestId: string, originalCount: number): string {
  if (results.length === 0) {
    return `=== 知識庫搜尋結果 ===
請求 ID: ${requestId}

未找到相關文件。您可以嘗試：
- 使用不同的關鍵字
- 降低相似度門檻
- 檢查查詢字串的拼寫

搜尋完成時間: ${new Date().toLocaleString('zh-TW')}`;
  }

  const sections = [
    `=== 知識庫搜尋結果 ===`,
    `請求 ID: ${requestId}`,
    `找到 ${results.length} 個相關文件${originalCount > results.length ? ` (共 ${originalCount} 個結果，已過濾)` : ''}`,
    ``,
  ];

  results.forEach((result, index) => {
    const similarity = result.similarity ? ` (相似度: ${(result.similarity * 100).toFixed(1)}%)` : '';
    
    sections.push(
      `${index + 1}. ${result.title || '未命名文件'}${similarity}`,
      `   來源: ${result.source || '未知'}`,
    );

    if (result.content) {
      const preview = result.content.length > 200 
        ? result.content.substring(0, 200) + '...'
        : result.content;
      sections.push(`   內容: ${preview}`);
    }

    if (result.tags && result.tags.length > 0) {
      sections.push(`   標籤: ${result.tags.join(', ')}`);
    }

    if (result.lastModified) {
      sections.push(`   最後修改: ${new Date(result.lastModified).toLocaleDateString('zh-TW')}`);
    }

    sections.push('');
  });

  sections.push(`搜尋完成時間: ${new Date().toLocaleString('zh-TW')}`);

  return sections.join('\n');
}

function sanitizeMetadata(metadata: any): any {
  if (!metadata || typeof metadata !== 'object') {
    return {};
  }

  // 移除敏感欄位
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  const sanitized = { ...metadata };

  Object.keys(sanitized).forEach((key) => {
    if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
      delete sanitized[key];
    }
  });

  return sanitized;
}

function handleKnowledgeBaseError(
  error: any,
  requestId: string,
  duration: number,
  securityAuditLogs: SecurityAuditLog[],
  context: ToolExecutionContext,
  config?: KnowledgeBaseConfig,
): string {
  let errorType = KnowledgeBaseErrorType.SERVICE_ERROR;
  let securityLevel: 'low' | 'medium' | 'high' = 'medium';

  if (error instanceof ToolExecutionError) {
    if (error.message.includes('Security validation')) {
      errorType = KnowledgeBaseErrorType.SECURITY_VIOLATION;
      securityLevel = 'high';
    } else if (error.message.includes('Access denied') || error.message.includes('permission')) {
      errorType = KnowledgeBaseErrorType.ACCESS_DENIED;
      securityLevel = 'medium';
    } else if (error.message.includes('Invalid') || error.message.includes('validation')) {
      errorType = KnowledgeBaseErrorType.INVALID_INPUT;
      securityLevel = 'low';
    }
  }

  // 記錄錯誤的安全審計日誌
  if (config?.enableSecurityLogging !== false) {
    addSecurityAuditLog(securityAuditLogs, {
      requestId,
      timestamp: new Date().toISOString(),
      tenantId: context.tenantId,
      workspaceId: context.workspaceId,
      action: 'knowledge_base_search',
      input: 'error_occurred',
      event: 'search_failed',
      details: {
        errorType,
        errorMessage: error.message,
        duration,
      },
      outcome: 'failure',
      success: false,
      securityLevel,
    });
  }

  return `❌ 知識庫搜尋失敗

請求 ID: ${requestId}
錯誤類型: ${translateErrorType(errorType)}
錯誤訊息: ${error.message}

建議：
- 檢查您的查詢字串格式
- 確認您有存取該知識庫的權限
- 如果問題持續發生，請聯繫系統管理員

錯誤時間: ${new Date().toLocaleString('zh-TW')}`;
}

function translateErrorType(errorType: KnowledgeBaseErrorType): string {
  const typeMap: Record<KnowledgeBaseErrorType, string> = {
    [KnowledgeBaseErrorType.INVALID_INPUT]: '輸入格式錯誤',
    [KnowledgeBaseErrorType.ACCESS_DENIED]: '存取被拒絕',
    [KnowledgeBaseErrorType.SECURITY_VIOLATION]: '安全性違規',
    [KnowledgeBaseErrorType.SERVICE_ERROR]: '服務錯誤',
    [KnowledgeBaseErrorType.TENANT_ISOLATION_VIOLATION]: '租戶隔離違規',
  };
  return typeMap[errorType] || errorType;
}

function generateRequestId(): string {
  return `kb_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
}

function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

function addSecurityAuditLog(logs: SecurityAuditLog[], log: SecurityAuditLog): void {
  logs.push(log);
  
  // 保持日誌數量限制（防止記憶體洩漏）
  if (logs.length > 1000) {
    logs.splice(0, logs.length - 1000);
  }
}

/**
 * 知識庫工具Factory類別
 */
export class KnowledgeBaseToolsFactory {
  static createKnowledgeBaseSearchTool(
    ragIngestionService: RAGIngestionService,
    ragSecurityService: RAGSecurityService,
    context: ToolExecutionContext,
    config?: KnowledgeBaseConfig,
  ) {
    return createKnowledgeBaseSearchTool(ragIngestionService, ragSecurityService, context, config);
  }

  static createLangChainTools(
    ragIngestionService: RAGIngestionService,
    ragSecurityService: RAGSecurityService,
    context: ToolExecutionContext,
    config?: KnowledgeBaseConfig,
  ) {
    return [createKnowledgeBaseSearchTool(ragIngestionService, ragSecurityService, context, config)];
  }
} 