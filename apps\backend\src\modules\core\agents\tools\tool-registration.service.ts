import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { ToolRegistryService } from './core/tool-registry.service';

/**
 * 工具註冊服務
 *
 * 負責在應用啟動時自動註冊所有可用的工具實作。
 * 實作 OnApplicationBootstrap 介面，確保在應用完全啟動後執行註冊。
 * 
 * 注意：所有工具已成功遷移至新的Factory函數架構！
 * - 檔案讀取工具：FileReaderToolsFactory
 * - 網路搜尋工具：WebSearchToolsFactory  
 * - 通知工具：NotificationToolsFactory
 * - 專案資訊工具：ProjectInfoToolsFactory
 * - 建立任務工具：CreateTaskToolsFactory
 * - 進度更新工具：ProgressUpdateToolsFactory
 * - 知識庫工具：KnowledgeBaseToolsFactory
 * 
 * 新架構的工具可直接從 './implementations/index.ts' 導入並使用。
 * 所有工具都支援適當的依賴注入和類型安全。
 */
@Injectable()
export class ToolRegistrationService implements OnApplicationBootstrap {
  private readonly logger = new Logger(ToolRegistrationService.name);

  constructor(
    private readonly toolRegistry: ToolRegistryService,
  ) {}

  async onApplicationBootstrap() {
    this.logger.log('工具遷移已完成！所有工具已轉移至現代Factory函數架構。');
    this.logger.log('可用工具：');
    this.logger.log('- FileReaderToolsFactory (檔案讀取)');
    this.logger.log('- WebSearchToolsFactory (網路搜尋)');
    this.logger.log('- NotificationToolsFactory (通知)');
    this.logger.log('- ProjectInfoToolsFactory (專案資訊)');
    this.logger.log('- CreateTaskToolsFactory (建立任務)');
    this.logger.log('- ProgressUpdateToolsFactory (進度更新)');
    this.logger.log('- KnowledgeBaseToolsFactory (知識庫搜尋)');
    this.logger.log('所有工具可從 "./implementations/index.ts" 導入使用');
  }

  /**
   * 獲取已遷移的工具列表
   */
  getRegisteredTools(): string[] {
    return [
      'file_reader',
      'web_search', 
      'send_message',
      'create_notification',
      'get_notifications',
      'mark_notification_read',
      'project_info',
      'create_task',
      'progress_update',
      'knowledge_base_search'
    ];
  }

  /**
   * 檢查工具是否已遷移
   */
  isToolRegistered(key: string): boolean {
    return this.getRegisteredTools().includes(key);
  }
}
