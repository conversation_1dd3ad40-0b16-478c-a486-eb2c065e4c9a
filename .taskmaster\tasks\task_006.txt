# Task ID: 6
# Title: Implement `<PERSON><PERSON>ey` and `AiModel` Management
# Status: done
# Dependencies: 1, 2
# Priority: high
# Description: Core `AiKey` (for LLM provider API keys) and `AiModel` (for configuring specific LLM models) management functionalities, including CRUD operations, API endpoints, API key encryption, and 'Test Key' functionality, have been implemented. This task now focuses on completing the implementation by adding tenant isolation, establishing the `AiKey`-`AiModel` relationship, and developing comprehensive unit tests.
# Details:
The initial implementation phase has successfully delivered:
- Core Prisma schemas for `ai_keys` and `ai_models` with essential fields.
- `AiKeysService` and `AiModelsService` providing foundational CRUD operations.
- `AiKeysController` and `AiModelsController` exposing the necessary API endpoints.
- Secure storage of API keys via encryption using an `EncryptionService`.
- A 'Test Key' functionality supporting validation for multiple AI providers (OpenAI, Anthropic, Google Gemini).
- Integration of these components within the `AiAdminModule`.

The remaining work to finalize this feature includes:
1.  **Tenant Isolation:** Add a `tenant_id` field to both the `ai_keys` and `ai_models` Prisma schemas. Update services, controllers, and data access logic to ensure strict tenant-based data separation and access control.
2.  **AI Key - AI Model Association:** Add an `aiKeyId` field to the `ai_models` Prisma schema to establish a foreign key relationship with `ai_keys`. Update services and controllers to manage this association correctly during CRUD operations.
3.  **Unit Tests:** Develop comprehensive unit tests covering:
    a.  CRUD operations for both `AiKey` and `AiModel` entities in their respective services.
    b.  API key encryption and decryption logic within the `EncryptionService`.

# Test Strategy:
1.  Manually verify the existing 'Test Key' functionality with various supported providers (OpenAI, Anthropic, Google Gemini) to ensure it remains operational.
2.  Implement and execute unit tests for all CRUD operations (Create, Read, Update, Delete) for `AiKey` and `AiModel` entities, ensuring all business logic and edge cases are covered.
3.  Implement and execute unit tests for the API key encryption and decryption mechanisms to confirm data security.
4.  Perform integration testing to verify tenant isolation: ensure that `AiKey` and `AiModel` records are strictly scoped to the correct tenant and that users cannot access or manipulate data belonging to other tenants.
5.  Test the `AiModel` to `AiKey` association: verify that models are correctly linked to their respective AI keys, that this link is maintained across updates, and that models cannot be created without a valid key (if required by business logic).

# Subtasks:
## 6-1. Define core Prisma Schemas for `AiKey` (e.g., `id`, `provider`, `apiKey`) and `AiModel` (e.g., `id`, `name`, `modelIdentifier`, `config`) entities. [done]
### Dependencies: None
### Description: 
### Details:


## 6-2. Implement `AiKeysService` and `AiModelsService` for complete CRUD operations. [done]
### Dependencies: None
### Description: 
### Details:


## 6-3. Implement `AiKeysController` and `AiModelsController` for complete API endpoints. [done]
### Dependencies: None
### Description: 
### Details:


## 6-4. Implement secure API Key storage using `EncryptionService`. [done]
### Dependencies: None
### Description: 
### Details:


## 6-5. Implement 'Test Key' functionality supporting OpenAI, Anthropic, and Google Gemini. [done]
### Dependencies: None
### Description: 
### Details:


## 6-6. Integrate `AiKeysService`, `AiModelsService`, `AiKeysController`, and `AiModelsController` into `AiAdminModule`. [done]
### Dependencies: None
### Description: 
### Details:


## 6-7. Enhance `AiKey` and `AiModel` Prisma schemas by adding the `tenant_id` field. Update services and controllers to implement tenant isolation logic. [done]
### Dependencies: None
### Description: 
### Details:


## 6-8. Enhance `AiModel` Prisma schema by adding the `aiKeyId` field to establish the foreign key relationship with `AiKey`. Update services and controllers to manage this association. [done]
### Dependencies: None
### Description: 
### Details:


## 6-9. Implement comprehensive unit tests for CRUD operations in `AiKeysService` and `AiModelsService`. [done]
### Dependencies: None
### Description: 
### Details:


## 6-10. Implement unit tests for API key encryption and decryption logic within `EncryptionService`. [done]
### Dependencies: None
### Description: 
### Details:


