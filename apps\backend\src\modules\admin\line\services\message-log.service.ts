// New file: MessageLogService for recording and querying message logs
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { line_bots, line_message_logs, LineMessageDirection, line_users } from '@prisma/client';
import { LineUsersService } from './line-users.service';
import { Profile } from '@line/bot-sdk';

type LineBot = line_bots;

@Injectable()
export class MessageLogService {
  private readonly logger = new Logger(MessageLogService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly lineUsersService: LineUsersService,
  ) {}

  /**
   * Record raw webhook payloads for debugging (optional).
   */
  async recordWebhookEvent(bot: LineBot, rawEvents: any): Promise<void> {
    this.logger.log(`Recording raw webhook events for bot ${bot.id}`);
    await this.prisma.system_logs.create({
      data: {
        id: `line_webhook_${bot.id}_${Date.now()}`,
        level: 'info',
        message: JSON.stringify(rawEvents),
        path: `/admin/line-bots/callback/${bot.id}`,
        method: 'POST',
      },
    });
  }

  /**
   * Record an individual message event to the LineMessageLog table.
   * Honors privacy setting via LINE_LOG_MESSAGE_CONTENT env flag.
   */
  async recordMessageEvent(
    bot: LineBot,
    event: any,
    direction: LineMessageDirection,
    getProfile: () => Promise<Profile>,
  ): Promise<void> {
    const lineUserId = event.source?.userId;
    if (!lineUserId) {
      this.logger.warn('Event source has no userId, skipping message log.');
      return;
    }

    const user = await this.lineUsersService.findOrCreateUser(lineUserId, getProfile);

    const shouldLogContent = this.configService.get<boolean>('LINE_LOG_MESSAGE_CONTENT', true);
    const messageContent = shouldLogContent ? event.message : null;
    const groupId = event.source.type === 'group' ? event.source.group_id : null;

    // Build base data
    const data: any = {
      id: `msg_${bot.id}_${event.message.id}_${Date.now()}`,
      bot: { connect: { id: bot.id } },
      group_id: groupId,
      user: { connect: { id: user.id } },
      message_id: event.message.id,
      message_type: event.message.type,
      message_content: messageContent,
      direction,
      processed_at: new Date(),
    };

    // Attach tenant if available
    if (bot.tenant_id) {
      data.tenant = { connect: { id: bot.tenant_id } };
    }

    // Attach workspace if available, either from bot or group verification
    if (bot.workspace_id) {
      data.workspace = { connect: { id: bot.workspace_id } };
    } else if (groupId) {
      const verification = await this.prisma.line_group_verifications.findUnique({
        where: { bot_id_group_id: { bot_id: bot.id, group_id: groupId } },
      });
      if (verification?.workspace_id) {
        data.workspace = { connect: { id: verification.workspace_id } };
      }
      if (!data.tenant && verification?.tenant_id) {
        data.tenant = { connect: { id: verification.tenant_id } };
      }
    }

    // Persist log
    await this.prisma.line_message_logs.create({ data });
  }

  /**
   * List message logs for a given bot, filtered by tenant if provided.
   */
  async listLogs(botId: string, options: { skip?: number; take?: number; tenant_id?: string }) {
    const where: any = { bot_id: botId };
    if (options.tenant_id) where.tenant_id = options.tenant_id;

    const [items, total] = await Promise.all([
      this.prisma.line_message_logs.findMany({
        where,
        skip: options.skip,
        take: options.take,
        orderBy: { processed_at: 'desc' },
      }),
      this.prisma.line_message_logs.count({ where }),
    ]);

    return { items, total };
  }

  /**
   * Stub for compatibility: incoming events logging
   */
  async logIncomingEvent(bot: LineBot, event: any): Promise<void> {
    return this.recordWebhookEvent(bot, event);
  }

  /**
   * Stub for compatibility: outgoing messages logging
   */
  async logOutgoingMessages(
    bot: LineBot,
    to: string,
    messages: any[],
    response: any,
  ): Promise<void> {
    this.logger.log(
      `Logging outgoing messages for bot ${bot.id} to ${to}: ${JSON.stringify(response)}`,
    );
    // Optionally record via recordMessageEvent if needed
  }
}
