/*
  Warnings:

  - You are about to drop the column `contextLength` on the `ai_models` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `ai_models` table. All the data in the column will be lost.
  - You are about to drop the column `features` on the `ai_models` table. All the data in the column will be lost.
  - You are about to drop the column `modelId` on the `ai_models` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `ai_models` table. All the data in the column will be lost.
  - You are about to drop the column `order` on the `ai_models` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `ai_models` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[provider,modelName]` on the table `ai_models` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `displayName` to the `ai_models` table without a default value. This is not possible if the table is not empty.
  - Added the required column `modelName` to the `ai_models` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "ai_models_modelId_key";

-- DropIndex
DROP INDEX "ai_models_status_idx";

-- AlterTable
ALTER TABLE "ai_models" DROP COLUMN "contextLength",
DROP COLUMN "description",
DROP COLUMN "features",
DROP COLUMN "modelId",
DROP COLUMN "name",
DROP COLUMN "order",
DROP COLUMN "status",
ADD COLUMN     "displayName" TEXT NOT NULL,
ADD COLUMN     "enabled" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "modelName" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "ai_keys" (
    "id" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "apiKey" TEXT NOT NULL,
    "apiUrl" TEXT,
    "models" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "lastTest" TIMESTAMP(3),
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_keys_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ai_models_provider_modelName_key" ON "ai_models"("provider", "modelName");
