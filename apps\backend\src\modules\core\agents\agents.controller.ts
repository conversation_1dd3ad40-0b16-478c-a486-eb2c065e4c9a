import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Get,
  Put,
  Delete,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpCode,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AgentsService } from './agents.service';
import {
  CreateAgentDto,
  UpdateAgentDto,
  TestAgentDto,
  OptimizePromptDto,
  ExecuteAgentDto,
  AssignToolsToAgentDto,
  AgentToolResponseDto,
} from './dto/agent.dto';
import { JwtAuthGuard } from '../auth/guards/auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { AiAgentScope } from '@prisma/client';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';
import { JwtUser } from '../../../types/jwt-user.type';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';

@ApiTags('admin/ai/agents')
@UseGuards(JwtAuthGuard, PoliciesGuard)
@Controller('admin/ai/agents')
export class AgentsController {
  private readonly logger = new Logger(AgentsController.name);

  constructor(private readonly agentsService: AgentsService) {}

  @Get()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '讀取所有 AI Agent' })
  findAll(
    @Query('scope') scope?: AiAgentScope,
    @Query('tenant_id') tenant_id?: string,
    @Query('workspace_id') workspace_id?: string,
  ) {
    return this.agentsService.findAll(tenant_id, workspace_id, scope);
  }

  @Get(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '根據 ID 讀取 AI Agent' })
  async findOne(@Param('id') id: string) {
    return this.agentsService.findOne(id);
  }

  @Post()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, 'ai_agents'))
  @ApiOperation({ summary: '建立新 AI Agent' })
  async create(@Body(ValidationPipe) createAgentDto: CreateAgentDto, @CurrentUser() user: JwtUser) {
    return this.agentsService.create(createAgentDto, user.id);
  }

  @Put(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '更新 AI Agent' })
  async update(
    @Param('id') id: string,
    @Body(ValidationPipe) updateAgentDto: UpdateAgentDto,
    @CurrentUser() user: JwtUser,
  ) {
    return this.agentsService.update(id, updateAgentDto, user.id);
  }

  @Delete(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, 'ai_agents'))
  @ApiOperation({ summary: '刪除 AI Agent' })
  async remove(@Param('id') id: string) {
    return this.agentsService.delete(id);
  }

  @Post('test')
  @ApiOperation({ summary: '測試 Agent 設定' })
  async testAgent(@Body() dto: TestAgentDto) {
    return await this.agentsService.testAgent(
      dto.agent_id,
      dto.message,
      dto.prompt,
      dto.temperature,
    );
  }

  @Post('optimize-prompt')
  @ApiOperation({ summary: '優化提示詞' })
  async optimizePrompt(@Body() dto: OptimizePromptDto) {
    return await this.agentsService.optimizePrompt(dto);
  }

  @Post(':id/chat')
  @ApiOperation({ summary: '使用指定 Agent 進行對話' })
  async chat(
    @Param('id') id: string,
    @Body()
    body: {
      message: string;
      temperature?: number;
      prompt?: string;
      systemPrompt?: string;
    },
  ) {
    this.logger.log(`使用 Agent ${id} 進行對話，訊息: ${body.message}`);
    try {
      const response = await this.agentsService.testAgent(
        id,
        body.message,
        body.prompt,
        body.temperature,
      );
      return response;
    } catch (error) {
      this.logger.error(`Agent ${id} 對話失敗: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: '對話失敗',
          error: error.message,
          timestamp: new Date().toISOString(),
          details: JSON.stringify(body, null, 2),
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post(':id/execute')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.EXECUTE, 'ai_agents'))
  @HttpCode(200)
  @ApiOperation({ summary: '執行 Agent' })
  async execute(@Param('id') id: string, @Body() body: ExecuteAgentDto) {
    try {
      const response = await this.agentsService.execute(id, body);
      return response;
    } catch (error) {
      this.logger.error(`執行 Agent ${id} 失敗:`, error);
      throw error;
    }
  }

  @Put(':id/status')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '更新 Agent 啟用狀態' })
  async updateStatus(@Param('id') id: string, @Body('isEnabled') isEnabled: boolean) {
    return this.agentsService.updateStatus(id, isEnabled);
  }

  @Get(':id/tools')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '取得 Agent 的工具列表' })
  @ApiResponse({
    status: 200,
    description: '成功取得 Agent 的工具列表',
    type: [AgentToolResponseDto],
  })
  @ApiResponse({ status: 404, description: '找不到指定的 Agent' })
  async getAgentTools(@Param('id') id: string): Promise<AgentToolResponseDto[]> {
    return this.agentsService.getAgentTools(id);
  }

  @Put(':id/tools')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '為 Agent 指派工具' })
  @ApiResponse({ status: 200, description: '成功指派工具給 Agent' })
  @ApiResponse({ status: 400, description: '請求參數錯誤' })
  @ApiResponse({ status: 404, description: '找不到指定的 Agent 或工具' })
  async assignToolsToAgent(
    @Param('id') id: string,
    @Body() assignToolsDto: AssignToolsToAgentDto,
  ): Promise<{ message: string; assignedCount: number }> {
    await this.agentsService.assignToolsToAgent(id, assignToolsDto.toolIds);
    return {
      message: '成功指派工具給 Agent',
      assignedCount: assignToolsDto.toolIds.length,
    };
  }

  @Delete(':id/tools/:toolId')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '移除 Agent 的特定工具' })
  @ApiResponse({ status: 204, description: '成功移除工具' })
  @ApiResponse({ status: 404, description: '找不到指定的 Agent 或工具關聯' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async removeToolFromAgent(
    @Param('id') id: string,
    @Param('toolId') toolId: string,
  ): Promise<void> {
    return this.agentsService.removeToolFromAgent(id, toolId);
  }

  @Post(':id/run')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.EXECUTE, 'ai_agents'))
  @ApiOperation({
    summary: '執行 Agent',
    description: '使用指定的 Agent 配置執行 LangChain agent，返回處理結果',
  })
  @ApiResponse({
    status: 200,
    description: 'Agent 執行成功',
    schema: {
      type: 'object',
      properties: {
        result: { type: 'string', description: 'Agent 執行結果' },
        executionTime: { type: 'number', description: '執行時間（毫秒）' },
        tenantId: { type: 'string', description: '租戶 ID' },
        userId: { type: 'string', description: '用戶 ID' },
      },
    },
  })
  @ApiResponse({ status: 400, description: '輸入參數無效' })
  @ApiResponse({ status: 401, description: '未授權' })
  @ApiResponse({ status: 403, description: '權限不足' })
  @ApiResponse({ status: 500, description: '內部伺服器錯誤' })
  async runAgent(
    @Param('id') agentId: string,
    @Body() body: { input: string; sessionId?: string },
    @CurrentUser() user: JwtUser,
  ) {
    const startTime = Date.now();

    if (!user || !user.tenant_id) {
      throw new Error('User context or tenant ID not found');
    }

    const result = await this.agentsService.runAgent(body.input, user, agentId, body.sessionId);

    const executionTime = Date.now() - startTime;

    return {
      result,
      executionTime,
      tenantId: user.tenant_id,
      userId: user.id,
    };
  }

  @Get('tools/available')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({
    summary: '獲取可用工具',
    description: '獲取當前用戶可以使用的 Agent 工具列表',
  })
  @ApiResponse({
    status: 200,
    description: '可用工具列表',
    schema: {
      type: 'object',
      properties: {
        tools: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string', description: '工具名稱' },
              description: { type: 'string', description: '工具描述' },
            },
          },
        },
        tenantId: { type: 'string', description: '租戶 ID' },
      },
    },
  })
  @ApiResponse({ status: 401, description: '未授權' })
  @ApiResponse({ status: 403, description: '權限不足' })
  async getAvailableTools(@CurrentUser() user: JwtUser) {
    if (!user || !user.tenant_id) {
      throw new Error('User context or tenant ID not found');
    }

    const tools = await this.agentsService.getAvailableTools(user.tenant_id, user);

    return {
      tools,
      tenantId: user.tenant_id,
    };
  }

  @Get('status')
  @ApiOperation({
    summary: '獲取 Agent 狀態',
    description: '檢查 Agent 服務的健康狀態',
  })
  @ApiResponse({
    status: 200,
    description: 'Agent 狀態資訊',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['healthy', 'degraded', 'unhealthy'] },
        message: { type: 'string', description: '狀態描述' },
      },
    },
  })
  async getAgentStatus() {
    return this.agentsService.getAgentStatus();
  }
}
