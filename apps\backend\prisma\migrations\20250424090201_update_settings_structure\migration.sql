/*
  Warnings:

  - You are about to drop the column `accentColor` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `auditLogRetentionDays` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `defaultLanguage` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `emailProvider` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `faviconUrl` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `fromEmailAddress` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `fromName` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `ipWhitelist` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `logoUrl` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `maintenanceAllowedIps` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `maintenanceEndTime` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `maintenanceMessage` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `maintenanceMode` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `maintenanceNotification` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `maintenanceStartTime` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `passwordMinLength` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `primaryColor` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `requireMFA` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `sessionTimeout` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `siteDescription` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `siteName` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `smtpHost` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `smtpPassword` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `smtpPort` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `smtpSecure` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `smtpUser` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `storageAccessKey` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `storageBucket` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `storageEndpoint` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `storageProvider` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `storageRegion` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `storageSecretKey` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `supportEmail` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `theme` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `tillwoApiKey` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `timezone` on the `settings` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[name,type]` on the table `settings` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `name` to the `settings` table without a default value. This is not possible if the table is not empty.
  - Added the required column `value` to the `settings` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "settings" DROP COLUMN "accentColor",
DROP COLUMN "auditLogRetentionDays",
DROP COLUMN "defaultLanguage",
DROP COLUMN "emailProvider",
DROP COLUMN "faviconUrl",
DROP COLUMN "fromEmailAddress",
DROP COLUMN "fromName",
DROP COLUMN "ipWhitelist",
DROP COLUMN "logoUrl",
DROP COLUMN "maintenanceAllowedIps",
DROP COLUMN "maintenanceEndTime",
DROP COLUMN "maintenanceMessage",
DROP COLUMN "maintenanceMode",
DROP COLUMN "maintenanceNotification",
DROP COLUMN "maintenanceStartTime",
DROP COLUMN "passwordMinLength",
DROP COLUMN "primaryColor",
DROP COLUMN "requireMFA",
DROP COLUMN "sessionTimeout",
DROP COLUMN "siteDescription",
DROP COLUMN "siteName",
DROP COLUMN "smtpHost",
DROP COLUMN "smtpPassword",
DROP COLUMN "smtpPort",
DROP COLUMN "smtpSecure",
DROP COLUMN "smtpUser",
DROP COLUMN "storageAccessKey",
DROP COLUMN "storageBucket",
DROP COLUMN "storageEndpoint",
DROP COLUMN "storageProvider",
DROP COLUMN "storageRegion",
DROP COLUMN "storageSecretKey",
DROP COLUMN "supportEmail",
DROP COLUMN "theme",
DROP COLUMN "tillwoApiKey",
DROP COLUMN "timezone",
ADD COLUMN     "createdBy" TEXT,
ADD COLUMN     "name" TEXT NOT NULL,
ADD COLUMN     "updatedBy" TEXT,
ADD COLUMN     "value" TEXT NOT NULL,
ALTER COLUMN "type" DROP DEFAULT;

-- CreateIndex
CREATE INDEX "settings_type_idx" ON "settings"("type");

-- CreateIndex
CREATE UNIQUE INDEX "settings_name_type_key" ON "settings"("name", "type");

-- AddForeignKey
ALTER TABLE "settings" ADD CONSTRAINT "settings_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "settings" ADD CONSTRAINT "settings_updatedBy_fkey" FOREIGN KEY ("updatedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
