import { IsString, IsN<PERSON>ber, IsObject, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

class PaymentGatewayKeysDto {
  @ApiProperty({ description: 'Stripe 公鑰' })
  @IsString()
  stripePublicKey!: string;

  @ApiProperty({ description: 'Stripe 私鑰' })
  @IsString()
  stripeSecretKey!: string;
}

class TaxSettingsDto {
  @ApiProperty({ description: '稅率' })
  @IsNumber()
  rate!: number;

  @ApiProperty({ description: '是否內含稅' })
  @IsBoolean()
  inclusive!: boolean;
}

export class UpdateBillingSettingsDto {
  @ApiProperty({ description: '預設貨幣' })
  @IsString()
  defaultCurrency!: string;

  @ApiProperty({ description: '支付閘道金鑰' })
  @IsObject()
  @Type(() => PaymentGatewayKeysDto)
  paymentGatewayKeys!: PaymentGatewayKeysDto;

  @ApiProperty({ description: '稅務設定' })
  @IsObject()
  @Type(() => TaxSettingsDto)
  taxSettings!: TaxSettingsDto;
}
