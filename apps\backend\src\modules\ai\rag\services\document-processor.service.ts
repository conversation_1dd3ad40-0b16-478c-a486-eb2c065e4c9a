import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { Document } from 'langchain/document';
import * as fs from 'fs';
import * as path from 'path';
import { extractText } from 'unpdf';
import * as mammoth from 'mammoth';

export interface ProcessedDocument {
  content: string;
  metadata: Record<string, any>;
  pageCount?: number;
  wordCount?: number;
  extractedImages?: string[];
}

/**
 * 文檔處理服務
 *
 * 負責處理各種文檔格式的內容提取和預處理：
 * - PDF 文件解析
 * - Word 文檔處理
 * - 純文本文件
 * - Markdown 文件
 * - 未來可擴展支援更多格式
 */
@Injectable()
export class DocumentProcessorService {
  private readonly logger = new Logger(DocumentProcessorService.name);

  /**
   * 根據文件類型處理文檔
   *
   * @param filePath 文件路徑
   * @param fileName 文件名稱
   * @param mimeType MIME 類型
   * @returns 處理後的文檔陣列
   */
  async processDocument(
    filePath: string,
    fileName: string,
    mimeType?: string,
  ): Promise<Document[]> {
    try {
      const fileExtension = path.extname(fileName).toLowerCase();
      const mimeTypeToUse = mimeType || this.getMimeTypeFromExtension(fileExtension);

      this.logger.debug(`Processing document: ${fileName} (${mimeTypeToUse})`);

      switch (fileExtension) {
        case '.pdf':
          return await this.processPDF(filePath, fileName);
        case '.docx':
        case '.doc':
          return await this.processWord(filePath, fileName);
        case '.txt':
          return await this.processText(filePath, fileName);
        case '.md':
        case '.markdown':
          return await this.processMarkdown(filePath, fileName);
        default:
          this.logger.warn(`Unsupported file type: ${fileExtension}, treating as text`);
          return await this.processText(filePath, fileName);
      }
    } catch (error) {
      this.logger.error(`Failed to process document ${fileName}:`, error);
      throw new Error(`Document processing failed: ${error.message}`);
    }
  }

  /**
   * 檢查文件類型是否受支援
   */
  isSupportedFileType(fileName: string, mimeType?: string): boolean {
    const extension = path.extname(fileName).toLowerCase();
    const supportedExtensions = ['.pdf', '.docx', '.doc', '.txt', '.md', '.markdown'];

    if (supportedExtensions.includes(extension)) {
      return true;
    }

    // 檢查 MIME 類型
    if (mimeType) {
      const supportedMimeTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',
        'text/plain',
        'text/markdown',
      ];
      return supportedMimeTypes.includes(mimeType);
    }

    return false;
  }

  /**
   * 獲取文件的基本資訊
   */
  async getDocumentInfo(
    filePath: string,
    fileName: string,
  ): Promise<{
    size: number;
    extension: string;
    mimeType: string;
    encoding?: string;
  }> {
    const stats = fs.statSync(filePath);
    const extension = path.extname(fileName).toLowerCase();
    const mimeType = this.getMimeTypeFromExtension(extension);

    return {
      size: stats.size,
      extension,
      mimeType,
    };
  }

  // --- 私有方法：特定格式處理 ---

  /**
   * 處理 PDF 文件
   */
  private async processPDF(filePath: string, fileName: string): Promise<Document[]> {
    try {
      const buffer = fs.readFileSync(filePath);
      const { text, totalPages } = await extractText(buffer, { mergePages: true });

      const document = new Document({
        pageContent: text,
        metadata: {
          fileName,
          filePath,
          mimeType: 'application/pdf',
          totalPages,
          wordCount: this.countWords(text),
          processingDate: new Date().toISOString(),
        },
      });

      this.logger.debug(`Extracted ${totalPages} pages from PDF: ${fileName}`);

      return [document];
    } catch (error) {
      this.logger.error(`Failed to process PDF ${fileName}:`, error);
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }

  /**
   * 處理 Word 文檔
   */
  private async processWord(filePath: string, fileName: string): Promise<Document[]> {
    try {
      const buffer = fs.readFileSync(filePath);
      const result = await mammoth.extractRawText({ buffer });

      const document = new Document({
        pageContent: result.value,
        metadata: {
          fileName,
          filePath,
          mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          wordCount: this.countWords(result.value),
          processingDate: new Date().toISOString(),
        },
      });

      this.logger.debug(`Extracted ${this.countWords(result.value)} words from Word document: ${fileName}`);
      return [document];
    } catch (error) {
      throw new BadRequestException(`Failed to process Word document: ${error.message}`);
    }
  }

  /**
   * 處理純文本文件
   */
  private async processText(filePath: string, fileName: string): Promise<Document[]> {
    try {
      // 嘗試多種編碼
      let content: string;
      try {
        content = fs.readFileSync(filePath, 'utf-8');
      } catch (error) {
        // 如果 UTF-8 失敗，嘗試其他編碼
        try {
          content = fs.readFileSync(filePath, 'latin1');
        } catch (fallbackError) {
          throw new Error(`Failed to read file with any encoding: ${fallbackError.message}`);
        }
      }

      const document = new Document({
        pageContent: content,
        metadata: {
          fileName,
          filePath,
          mimeType: 'text/plain',
          wordCount: this.countWords(content),
          processingDate: new Date().toISOString(),
        },
      });

      this.logger.debug(`Processed text file: ${fileName} (${this.countWords(content)} words)`);

      return [document];
    } catch (error) {
      throw new BadRequestException(`Failed to process text file: ${error.message}`);
    }
  }

  /**
   * 處理 Markdown 文件
   */
  private async processMarkdown(filePath: string, fileName: string): Promise<Document[]> {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');

      const document = new Document({
        pageContent: content,
        metadata: {
          fileName,
          filePath,
          mimeType: 'text/markdown',
          wordCount: this.countWords(content),
          processingDate: new Date().toISOString(),
        },
      });

      this.logger.debug(`Processed Markdown file: ${fileName}`);

      return [document];
    } catch (error) {
      this.logger.error(`Failed to process Markdown file ${fileName}:`, error);
      throw new Error(`Markdown file processing failed: ${error.message}`);
    }
  }

  // --- 私有工具方法 ---

  /**
   * 從檔案副檔名推斷 MIME 類型
   */
  private getMimeTypeFromExtension(extension: string): string {
    const mimeMap: Record<string, string> = {
      '.pdf': 'application/pdf',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.doc': 'application/msword',
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.markdown': 'text/markdown',
    };

    return mimeMap[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * 計算文字字數
   */
  private countWords(text: string): number {
    if (!text || text.trim().length === 0) {
      return 0;
    }

    // 處理中文、英文混合文本的字數統計
    const words = text
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);

    // 對於包含中文的文本，每個中文字符算作一個詞
    let totalWords = 0;
    for (const word of words) {
      const chineseChars = word.match(/[\u4e00-\u9fff]/g);
      if (chineseChars) {
        totalWords += chineseChars.length;
        const nonChineseWords = word.replace(/[\u4e00-\u9fff]/g, '').trim();
        if (nonChineseWords.length > 0) {
          totalWords += 1;
        }
      } else {
        totalWords += 1;
      }
    }

    return totalWords;
  }

  /**
   * 安全地清理文本內容
   */
  private sanitizeText(text: string): string {
    return text
      .replace(/\r\n/g, '\n') // 統一換行符
      .replace(/\r/g, '\n')
      .replace(/\u0000/g, '') // 移除空字符
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .trim();
  }
}
