<template>
  <div class="p-4 space-y-4">
    <!-- 頁面標題 -->
    <div class="flex items-center space-x-3">
      <div class="p-2 bg-primary/10 rounded-md">
        <FileText class="h-4 w-4 text-primary" />
      </div>
      <div>
        <h1 class="text-xl font-semibold text-gray-900 dark:text-white">系統日誌</h1>
        <p class="text-sm text-gray-600 dark:text-gray-400">查看系統操作記錄與安全事件</p>
      </div>
    </div>

    <!-- 統計卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      <Card class="p-4">
        <CardContent class="p-0">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md">
              <FileText class="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p class="text-xs text-gray-600 dark:text-zinc-400">總記錄數</p>
              <div v-if="stats" class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
                {{ stats.total_logs.toLocaleString() }}
              </div>
              <Skeleton v-else class="h-6 w-16" />
            </div>
          </div>
        </CardContent>
      </Card>
      <Card class="p-4">
        <CardContent class="p-0">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-md">
              <Calendar class="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p class="text-xs text-gray-600 dark:text-zinc-400">今日記錄</p>
              <div v-if="stats" class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
                {{ stats.logs_today.toLocaleString() }}
              </div>
              <Skeleton v-else class="h-6 w-16" />
            </div>
          </div>
        </CardContent>
      </Card>
      <Card class="p-4">
        <CardContent class="p-0">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-red-100 dark:bg-red-900/30 rounded-md">
              <XCircle class="h-4 w-4 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <p class="text-xs text-gray-600 dark:text-zinc-400">錯誤記錄</p>
              <div v-if="stats" class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
                {{ stats.error_logs.toLocaleString() }}
              </div>
              <Skeleton v-else class="h-6 w-16" />
            </div>
          </div>
        </CardContent>
      </Card>
      <div class="border border-gray-200 dark:border-gray-700 rounded-md p-2">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-xs text-gray-600 dark:text-gray-400">稽核記錄</p>
            <div v-if="stats" class="text-lg font-semibold text-blue-600">
              {{ stats.audit_logs.toLocaleString() }}
            </div>
            <Skeleton v-else class="h-6 w-16" />
          </div>
          <CheckCircle class="h-3 w-3 text-blue-400" />
        </div>
      </div>
      <div class="border border-gray-200 dark:border-gray-700 rounded-md p-2">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-xs text-gray-600 dark:text-gray-400">獨立使用者</p>
            <div v-if="stats" class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ stats.unique_users.toLocaleString() }}
            </div>
            <Skeleton v-else class="h-6 w-16" />
          </div>
          <Users class="h-3 w-3 text-gray-400" />
        </div>
      </div>
    </div>

    <!-- 篩選與操作 -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-md p-4">
      <div class="flex items-center gap-2 mb-4">
        <Filter class="h-4 w-4 text-gray-600" />
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">篩選與操作</h3>
      </div>
      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="space-y-2">
            <Label for="search" class="text-xs">內容搜尋</Label>
            <Input
              id="search"
              v-model="filters.search"
              placeholder="搜尋訊息、使用者 ID..."
              @keyup.enter="fetchLogs"
              class="h-10"
            />
          </div>
          <div class="space-y-2">
            <Label for="target_resource" class="text-xs">目標資源</Label>
            <Input
              id="target_resource"
              v-model="filters.target_resource"
              placeholder="例如: Project, User"
              @keyup.enter="fetchLogs"
              class="h-10"
            />
          </div>
          <div class="space-y-2">
            <Label for="user_id" class="text-xs">使用者 ID</Label>
            <Input
              id="user_id"
              v-model="filters.user_id"
              placeholder="輸入使用者 ID"
              @keyup.enter="fetchLogs"
              class="h-10"
            />
          </div>
        </div>
        <div class="flex flex-wrap gap-2">
          <Button @click="fetchLogs" :disabled="isLoading" class="h-10">
            <Search class="h-4 w-4 mr-2" />
            查詢
          </Button>
          <Button variant="outline" @click="resetFilters" :disabled="isLoading" class="h-10">
            重置
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="outline" :disabled="isExporting" class="h-10">
                <Download class="h-4 w-4 mr-2" />
                <span v-if="isExporting">匯出中...</span>
                <span v-else>匯出報告</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem @click="generateReport('json')">匯出 JSON</DropdownMenuItem>
              <DropdownMenuItem @click="generateReport('csv')">匯出 CSV</DropdownMenuItem>
              <DropdownMenuItem @click="generateReport('xlsx')">匯出 XLSX</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>

    <!-- 日誌表格 -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-md">
      <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">日誌列表</h3>
      </div>
      <div class="overflow-hidden">
        <Table>
          <TableHeader class="sticky top-0 bg-gray-50 dark:bg-gray-800">
            <TableRow>
              <TableHead class="px-4 py-3 w-[180px]">時間</TableHead>
              <TableHead class="px-4 py-3">事件</TableHead>
              <TableHead class="px-4 py-3">使用者</TableHead>
              <TableHead class="px-4 py-3 w-[120px]">IP 位址</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <template v-if="isLoading">
              <TableRow v-for="i in 10" :key="`skeleton-${i}`">
                <TableCell class="px-4 py-3"><Skeleton class="h-4 w-32" /></TableCell>
                <TableCell class="px-4 py-3"><Skeleton class="h-4 w-full" /></TableCell>
                <TableCell class="px-4 py-3"><Skeleton class="h-4 w-20" /></TableCell>
                <TableCell class="px-4 py-3"><Skeleton class="h-4 w-24" /></TableCell>
              </TableRow>
            </template>
            <template v-else-if="logs.length > 0">
              <tr
                v-for="log in logs"
                :key="log.id"
                class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50"
              >
                <td class="px-4 py-3 align-top font-mono text-xs">
                  {{ formatFullDateTime(log.created_at) }}
                </td>
                <td class="px-4 py-3 align-top">
                  <div class="flex items-center gap-2">
                    <component
                      :is="getLevelIcon(log.level)"
                      :class="getLevelColor(log.level)"
                      class="h-4 w-4 flex-shrink-0"
                    />
                    <div class="flex flex-col">
                      <p class="font-medium text-sm">{{ log.message }}</p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        <Badge variant="outline" class="mr-1 text-xs">{{ log.action }}</Badge>
                        <span v-if="log.target_resource"
                          >on <strong>{{ log.target_resource }}</strong></span
                        >
                        <Badge :variant="getStatusVariant(log.status)" class="ml-2 text-xs">{{
                          log.status
                        }}</Badge>
                      </p>
                    </div>
                  </div>
                </td>
                <td class="px-4 py-3 align-top font-mono text-xs">{{ log.user_id || 'N/A' }}</td>
                <td class="px-4 py-3 align-top font-mono text-xs">{{ log.ip || 'N/A' }}</td>
              </tr>
            </template>
            <template v-else>
              <TableRow>
                <TableCell colspan="4" class="h-24 text-center">沒有資料</TableCell>
              </TableRow>
            </template>
          </TableBody>
        </Table>
      </div>
      <div
        class="flex items-center justify-between px-4 py-3 border-t border-gray-200 dark:border-gray-700"
      >
        <div class="text-sm text-gray-500 dark:text-gray-400">
          共 {{ pagination.total.toLocaleString() }} 筆記錄
        </div>
        <Pagination
          :total="pagination.total"
          :items-per-page="pagination.limit"
          :page="pagination.page"
          @update:page="changePage"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { format, parseISO } from 'date-fns';
import { useSystemLogs } from '@/composables/features/useSystemLogs';

// UI
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Pagination } from '@/components/ui/pagination';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';

// Icons
import {
  FileText,
  Calendar,
  XCircle,
  CheckCircle,
  Users,
  Filter,
  Search,
  Download,
  Info,
  AlertCircle,
} from 'lucide-vue-next';

import type { SystemLogLevel, SystemLogStatus } from '@/types/models/system-log.model';

defineOptions({ name: 'SystemLogsView' });

const {
  logs,
  stats,
  availableActions,
  pagination,
  filters,
  isLoading,
  isExporting,
  fetchLogs,
  generateReport,
  resetFilters,
  changePage,
  changeLimit,
} = useSystemLogs();

const formatFullDateTime = (dateStr: string) => format(parseISO(dateStr), 'yyyy-MM-dd HH:mm:ss');

const getLevelIcon = (level: SystemLogLevel) => {
  const map = {
    ERROR: XCircle,
    AUDIT: CheckCircle,
    WARN: AlertCircle,
    INFO: Info,
    DEBUG: FileText,
  };
  return map[level] || FileText;
};

const getLevelColor = (level: SystemLogLevel) => {
  const map = {
    ERROR: 'text-red-600',
    AUDIT: 'text-blue-600',
    WARN: 'text-orange-600',
    INFO: 'text-gray-600',
    DEBUG: 'text-gray-600',
  };
  return map[level] || 'text-gray-600';
};

const getStatusVariant = (status: SystemLogStatus | null) => {
  if (!status) return 'outline' as const;
  const map = {
    SUCCESS: 'default' as const,
    FAILURE: 'destructive' as const,
    PENDING: 'secondary' as const,
  };
  return map[status] || ('outline' as const);
};
</script>

<style scoped>
.input {
  @apply border rounded px-2 py-1;
}
.btn {
  @apply bg-blue-500 text-white px-3 py-1 rounded disabled:opacity-50;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
