import { Logger } from '@nestjs/common';
import {
  IStorageService,
  StorageConfig,
  UploadOptions,
  FileInfo,
} from '../interfaces/storage.interface';
import * as path from 'path';
import * as crypto from 'crypto';

/**
 * 抽象基礎儲存服務
 * 提供通用的實作和輔助方法
 */
export abstract class BaseStorageService implements IStorageService {
  protected readonly logger = new Logger(this.constructor.name);
  protected config: StorageConfig;

  constructor(config: StorageConfig) {
    this.config = config;
  }

  // 抽象方法 - 子類別必須實作
  abstract uploadFile(file: Express.Multer.File | Buffer, options?: UploadOptions): Promise<string>;
  abstract uploadText(content: string, options: UploadOptions): Promise<string>;
  abstract downloadFile(path: string): Promise<Buffer>;
  abstract deleteFile(path: string): Promise<void>;
  abstract fileExists(path: string): Promise<boolean>;
  abstract getFileInfo(path: string): Promise<FileInfo>;
  abstract listFiles(path: string, recursive?: boolean): Promise<string[]>;
  abstract getPublicUrl(path: string, expiresIn?: number): Promise<string>;
  abstract copyFile(sourcePath: string, destinationPath: string): Promise<void>;
  abstract moveFile(sourcePath: string, destinationPath: string): Promise<void>;
  abstract testConnection(): Promise<boolean>;

  // 通用實作方法
  async compressFiles(
    paths: string[],
    archivePath: string,
    format: 'zip' | 'gzip' = 'zip',
  ): Promise<string> {
    throw new Error('Compression not implemented for this storage provider');
  }

  // 輔助方法
  protected generateUniqueFilename(originalName: string, prefix?: string): string {
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    const timestamp = Date.now();
    const random = crypto.randomBytes(4).toString('hex');

    const uniqueName = prefix
      ? `${prefix}_${timestamp}_${random}${ext}`
      : `${name}_${timestamp}_${random}${ext}`;

    return uniqueName;
  }

  protected sanitizePath(filePath: string): string {
    // 移除危險字符和路徑遍歷攻擊
    return filePath
      .replace(/\.\./g, '')
      .replace(/[<>:"|?*]/g, '_')
      .replace(/\\/g, '/')
      .replace(/\/+/g, '/');
  }

  protected buildPath(...segments: string[]): string {
    const cleanSegments = segments
      .filter((segment) => segment && segment.trim())
      .map((segment) => this.sanitizePath(segment.trim()));

    return cleanSegments.join('/');
  }

  protected getContentTypeFromExtension(filename: string): string {
    const ext = path.extname(filename).toLowerCase();
    const contentTypes: Record<string, string> = {
      '.json': 'application/json',
      '.csv': 'text/csv',
      '.txt': 'text/plain',
      '.pdf': 'application/pdf',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.zip': 'application/zip',
      '.gz': 'application/gzip',
    };

    return contentTypes[ext] || 'application/octet-stream';
  }

  protected validateFile(file: Express.Multer.File | Buffer): void {
    if (!file) {
      throw new Error('File is required');
    }

    if (file instanceof Buffer) {
      if (file.length === 0) {
        throw new Error('File cannot be empty');
      }
    } else {
      if (!file.buffer || Buffer.byteLength(file.buffer) === 0) {
        throw new Error('File cannot be empty');
      }
    }
  }

  protected formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  protected async handleError(operation: string, error: any): Promise<never> {
    this.logger.error(`Storage operation failed: ${operation}`, error);
    throw new Error(`Storage ${operation} failed: ${error.message}`);
  }
}
