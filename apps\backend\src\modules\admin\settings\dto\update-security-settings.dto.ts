import { Is<PERSON><PERSON>, IsBoolean, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateSecuritySettingsDto {
  // 密碼政策
  @ApiProperty({ description: '密碼最小長度', minimum: 8, maximum: 32 })
  @IsNumber()
  @Min(8)
  @Max(32)
  passwordMinLength: number;

  @ApiProperty({ description: '是否需要大寫字母' })
  @IsBoolean()
  requireUppercase: boolean;

  @ApiProperty({ description: '是否需要小寫字母' })
  @IsBoolean()
  requireLowercase: boolean;

  @ApiProperty({ description: '是否需要數字' })
  @IsBoolean()
  requireNumbers: boolean;

  @ApiProperty({ description: '是否需要特殊字元' })
  @IsBoolean()
  requireSpecialChars: boolean;

  // 登入安全
  @ApiProperty({ description: '最大登入嘗試次數', minimum: 1, maximum: 10 })
  @IsNumber()
  @Min(1)
  @<PERSON>(10)
  maxLoginAttempts: number;

  @ApiProperty({ description: '帳號鎖定時間(分鐘)', minimum: 1, maximum: 1440 })
  @IsNumber()
  @Min(1)
  @Max(1440)
  accountLockoutDuration: number;

  @ApiProperty({ description: 'IP 黑名單' })
  @IsString()
  ipBlacklist: string;

  @ApiProperty({ description: '操作紀錄保留天數', minimum: 30, maximum: 365 })
  @IsNumber()
  @Min(30)
  @Max(365)
  auditLogRetentionDays: number;

  @ApiProperty({ description: '是否強制多因素認證' })
  @IsBoolean()
  requireMFA: boolean;

  @ApiProperty({ description: '工作階段超時時間(分鐘)', minimum: 5, maximum: 1440 })
  @IsNumber()
  @Min(5)
  @Max(1440)
  sessionTimeout: number;
}
