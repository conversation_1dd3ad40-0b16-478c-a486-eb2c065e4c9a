import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
  HttpCode,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import {
  ApiT<PERSON>s,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { Request } from 'express';
import { RoleAssignmentService } from '../services/role-assignment.service';
import { RoleHierarchyService } from '../services/role-hierarchy.service';
import { UserRoleService } from '../services/user-role.service';
import {
  AssignRolesDto,
  RemoveRolesDto,
  ReplaceRolesDto,
  BatchRoleOperationDto,
  SetParentRoleDto,
  GetUserRoleDto,
  CheckUserPermissionDto,
  CheckUserPermissionsDto,
} from '../dto/role-assignment.dto';
import {
  UserType,
  RoleAssignmentResult,
  BatchRoleOperationResult,
  UserRoleInfo,
  RoleHierarchyInfo,
} from '../types/role.types';
import { JwtUser } from '../../../../types/jwt-user.type';
import { PoliciesGuard } from '../../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../../casl/decorators/check-policies.decorator';
import { RoleScope } from '@prisma/client';
import { Action } from '../../../../common/enums/action.enum';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';
import { ParseEnumPipe } from '@nestjs/common';

/**
 * 角色指派管理控制器
 * 提供角色指派、移除、查詢等功能
 */
@ApiTags('admin/role-assignment')
@ApiBearerAuth()
@Controller('admin/role-assignment')
@UseGuards(PoliciesGuard)
export class RoleAssignmentController {
  constructor(
    private readonly roleAssignmentService: RoleAssignmentService,
    private readonly roleHierarchyService: RoleHierarchyService,
    private readonly userRoleService: UserRoleService,
  ) {}

  /**
   * 指派角色給用戶
   */
  @Post('assign')
  @HttpCode(HttpStatus.OK)
  @CheckPolicies((ability) => ability.can(Action.MANAGE, 'Role'))
  @ApiOperation({ summary: '為使用者指派一個或多個角色' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色指派成功',
    type: Object,
  })
  async assignRoles(
    @Body() assignRolesDto: AssignRolesDto,
    @CurrentUser() user: JwtUser,
  ): Promise<RoleAssignmentResult> {
    return this.roleAssignmentService.assignRoles({
      user_id: assignRolesDto.userId,
      user_type: assignRolesDto.userType,
      role_ids: assignRolesDto.roleIds,
      tenant_id: assignRolesDto.tenant_id,
      reason: assignRolesDto.reason,
      assigned_by: user.id,
    });
  }

  /**
   * 移除用戶角色
   */
  @Post('remove')
  @HttpCode(HttpStatus.OK)
  @CheckPolicies((ability) => ability.can(Action.MANAGE, 'Role'))
  @ApiOperation({ summary: '從使用者身上移除一個或多個角色' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色移除成功',
    type: Object,
  })
  async removeRoles(
    @Body() removeRolesDto: RemoveRolesDto,
    @CurrentUser() user: JwtUser,
  ): Promise<RoleAssignmentResult> {
    return this.roleAssignmentService.removeRoles({
      user_id: removeRolesDto.userId,
      user_type: removeRolesDto.userType,
      role_ids: removeRolesDto.roleIds,
      reason: removeRolesDto.reason,
      removed_by: user.id,
    });
  }

  /**
   * 替換用戶角色
   */
  @Post('replace')
  @HttpCode(HttpStatus.OK)
  @CheckPolicies((ability) => ability.can(Action.MANAGE, 'Role'))
  @ApiOperation({ summary: '替換使用者的所有角色' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色替換成功',
    type: Object,
  })
  async replaceRoles(
    @Body() replaceRolesDto: ReplaceRolesDto,
    @CurrentUser() user: JwtUser,
  ): Promise<RoleAssignmentResult> {
    return this.roleAssignmentService.replaceRoles({
      user_id: replaceRolesDto.userId,
      user_type: replaceRolesDto.userType,
      role_ids: replaceRolesDto.roleIds,
      tenant_id: replaceRolesDto.tenant_id,
      reason: replaceRolesDto.reason,
      assigned_by: user.id,
    });
  }

  /**
   * 批量角色操作
   */
  @Post('batch')
  @HttpCode(HttpStatus.OK)
  @CheckPolicies((ability) => ability.can(Action.MANAGE, 'Role'))
  @ApiOperation({ summary: '批量操作使用者角色' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量操作完成',
    type: Object,
  })
  async batchRoleOperations(
    @Body() batchOperationDto: BatchRoleOperationDto,
    @CurrentUser() user: JwtUser,
  ): Promise<BatchRoleOperationResult> {
    return this.roleAssignmentService.batchRoleOperations({
      operations: batchOperationDto.operations.map((op) => ({
        user_id: op.userId,
        user_type: op.userType,
        action: op.action,
        role_ids: op.roleIds,
        tenant_id: op.tenant_id,
      })),
      reason: batchOperationDto.reason,
      operated_by: user.id,
    });
  }

  /**
   * 獲取用戶角色資訊
   */
  @Get('user/:userId/:userType')
  @CheckPolicies((ability) => ability.can(Action.READ, 'Role'))
  @ApiOperation({ summary: '獲取用戶角色資訊' })
  @ApiParam({ name: 'userId', description: '用戶 ID' })
  @ApiParam({ name: 'userType', enum: UserType, description: '用戶類型' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶角色資訊',
    type: Object,
  })
  async getUserRoleInfo(
    @Param('userId') userId: string,
    @Param('userType') userType: UserType,
  ): Promise<UserRoleInfo> {
    return this.roleAssignmentService.getUserRoleInfo(userId, userType);
  }

  /**
   * 獲取用戶角色層級資訊
   */
  @Get('user/:userId/:userType/hierarchy')
  @CheckPolicies((ability) => ability.can(Action.READ, 'Role'))
  @ApiOperation({ summary: '獲取用戶角色層級資訊' })
  @ApiParam({ name: 'userId', description: '用戶 ID' })
  @ApiParam({ name: 'userType', enum: UserType, description: '用戶類型' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶角色層級資訊',
    type: Object,
  })
  async getUserRoleHierarchy(
    @Param('userId') userId: string,
    @Param('userType') userType: UserType,
  ) {
    return this.userRoleService.getUserRoleHierarchy(userId, userType);
  }

  /**
   * 獲取用戶有效權限
   */
  @Get('user/:userId/:userType/permissions')
  @CheckPolicies((ability) => ability.can(Action.READ, 'Role'))
  @ApiOperation({ summary: '獲取用戶有效權限' })
  @ApiParam({ name: 'userId', description: '用戶 ID' })
  @ApiParam({ name: 'userType', enum: UserType, description: '用戶類型' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶有效權限列表',
    type: [String],
  })
  async getUserEffectivePermissions(
    @Param('userId') userId: string,
    @Param('userType') userType: UserType,
  ): Promise<string[]> {
    return this.userRoleService.getUserEffectivePermissions(userId, userType);
  }

  /**
   * 檢查用戶權限
   */
  @Post('check-permission')
  @HttpCode(200)
  @ApiOperation({ summary: '檢查特定用戶是否有某個權限' })
  async checkUserPermission(@Body() checkPermissionDto: CheckUserPermissionDto) {
    const hasPermission = await this.userRoleService.userHasPermission(
      checkPermissionDto.userId,
      checkPermissionDto.userType,
      checkPermissionDto.permissionId,
    );

    return { hasPermission };
  }

  /**
   * 批量檢查用戶權限
   */
  @Post('check-permissions')
  @CheckPolicies((ability) => ability.can(Action.READ, 'Role'))
  @ApiOperation({ summary: '批量檢查用戶權限' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量權限檢查結果',
    type: Object,
  })
  async checkUserPermissions(@Body() checkPermissionsDto: CheckUserPermissionsDto): Promise<{
    mode: string;
    hasPermissions: boolean;
    permissionResults: Array<{ permissionId: string; hasPermission: boolean }>;
  }> {
    const { userId, userType, permissionIds, mode = 'any' } = checkPermissionsDto;

    const effectivePermissions = await this.userRoleService.getUserEffectivePermissions(
      userId,
      userType,
    );

    const permissionResults = permissionIds.map((permissionId) => ({
      permissionId,
      hasPermission: effectivePermissions.includes(permissionId),
    }));

    const hasPermissions =
      mode === 'any'
        ? permissionResults.some((result) => result.hasPermission)
        : permissionResults.every((result) => result.hasPermission);

    return {
      mode,
      hasPermissions,
      permissionResults,
    };
  }

  /**
   * 根據範圍獲取用戶列表
   */
  @Get('users-by-scope')
  @ApiOperation({ summary: '根據範圍獲取用戶列表' })
  async getUsersByScope(
    @Query('scope', new ParseEnumPipe(RoleScope)) scope: RoleScope,
    @Query('tenant_id') tenant_id?: string,
  ): Promise<UserRoleInfo[]> {
    return this.userRoleService.getUsersByScope(scope, tenant_id);
  }

  /**
   * 獲取具有特定角色的用戶
   */
  @Get('users/by-role/:roleId')
  @CheckPolicies((ability) => ability.can(Action.READ, 'Role'))
  @ApiOperation({ summary: '獲取具有特定角色的用戶' })
  @ApiParam({ name: 'roleId', description: '角色 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶列表',
    type: [Object],
  })
  async getUsersByRole(@Param('roleId') roleId: string): Promise<UserRoleInfo[]> {
    return this.userRoleService.getUsersByRole(roleId);
  }

  /**
   * 獲取具有特定權限的用戶
   */
  @Get('users/by-permission/:permissionId')
  @CheckPolicies((ability) => ability.can(Action.READ, 'Role'))
  @ApiOperation({ summary: '獲取具有特定權限的用戶' })
  @ApiParam({ name: 'permissionId', description: '權限 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶列表',
    type: [Object],
  })
  async getUsersByPermission(@Param('permissionId') permissionId: string): Promise<UserRoleInfo[]> {
    return this.userRoleService.getUsersByPermission(permissionId);
  }

  /**
   * 設置角色的父角色
   */
  @Put('hierarchy/parent')
  @CheckPolicies((ability) => ability.can(Action.MANAGE, 'Role'))
  @ApiOperation({ summary: '設置角色的父角色' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '父角色設置成功',
  })
  async setParentRole(@Body() setParentRoleDto: SetParentRoleDto): Promise<{ message: string }> {
    await this.roleHierarchyService.setParentRole(
      setParentRoleDto.roleId,
      setParentRoleDto.parentRoleId ?? null,
    );

    return { message: '父角色設置成功' };
  }

  /**
   * 獲取角色層級資訊
   */
  @Get('hierarchy/:roleId')
  @CheckPolicies((ability) => ability.can(Action.READ, 'Role'))
  @ApiOperation({ summary: '獲取角色層級資訊' })
  @ApiParam({ name: 'roleId', description: '角色 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色層級資訊',
    type: Object,
  })
  async getRoleHierarchy(@Param('roleId') roleId: string): Promise<RoleHierarchyInfo> {
    return this.roleHierarchyService.getRoleHierarchy(roleId);
  }

  /**
   * 驗證角色層級結構
   */
  @Get('hierarchy/validate')
  @CheckPolicies((ability) => ability.can(Action.READ, 'Role'))
  @ApiOperation({ summary: '驗證角色層級結構' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '層級結構驗證結果',
    type: Object,
  })
  async validateHierarchy() {
    return this.roleHierarchyService.validateHierarchy();
  }

  /**
   * 同步用戶權限快取
   */
  @Post('sync-cache/:userId/:userType')
  @CheckPolicies((ability) => ability.can(Action.MANAGE, 'Role'))
  @ApiOperation({ summary: '同步用戶權限快取' })
  @ApiParam({ name: 'userId', description: '用戶 ID' })
  @ApiParam({ name: 'userType', enum: UserType, description: '用戶類型' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '權限快取同步成功',
  })
  async syncUserPermissionCache(
    @Param('userId') userId: string,
    @Param('userType') userType: UserType,
  ): Promise<{ message: string }> {
    await this.userRoleService.syncUserPermissionCache(userId, userType);
    return { message: '權限快取同步成功' };
  }
}
