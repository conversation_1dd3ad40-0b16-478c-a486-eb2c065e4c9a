-- 修復缺失的 Roles 類型
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'roles') THEN
        CREATE TYPE "public"."Roles" AS ENUM ('SUPER_ADMIN', 'SYSTEM_ADMIN', 'TENANT_ADMIN', 'TENANT_USER');
    END IF;
END
$$;

-- 確保 users 表格有正確的 role 欄位
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'role'
    ) THEN
        -- 確保 role 欄位是 TEXT 類型
        ALTER TABLE "users" ALTER COLUMN "role" TYPE TEXT;
        
        -- 設置默認值
        ALTER TABLE "users" ALTER COLUMN "role" SET DEFAULT 'TENANT_USER';
    END IF;
END
$$;