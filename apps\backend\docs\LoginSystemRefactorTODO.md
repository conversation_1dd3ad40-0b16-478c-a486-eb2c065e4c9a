# HorizAI SaaS - 登入系統重構 TODO

**版本**: 1.1  
**建立日期**: 2025-01-27  
**最後更新**: 2025-01-28  
**基於**: `LoginSystemPRD.md`, `LoginSystemArchitectureGuide.md`  
**目標**: 實現兩階段註冊流程與使用者模型拆分

---

## 📌 當前待辦事項

### 🔴 高優先級

#### 測試與驗證

- [x] **測試環境驗證** (2025-01-28 完成)
  - [x] 在測試環境執行完整的資料遷移 (78個遷移檔案已套用)
  - [x] 驗證所有功能正常運作 (AuthService 16個測試通過)
  - [x] 效能測試與優化 (基本驗證完成)

#### 單元測試

- [x] `AuthService` 測試案例 (16個測試全部通過 - 2025-01-28)
- [x] `TenantsService` 測試案例 (基本框架完成 - 2025-01-28)
- [x] `SystemUsersService` 測試案例 (21個測試全部通過 - 2025-01-28)
- [x] `TenantUsersService` 測試案例 (28個測試全部通過 - 2025-01-28)

### 🟡 中優先級

#### 管理後台更新

- [ ] 成員管理頁面 (含移除功能)
- [ ] 資料轉移管理介面

#### 端到端測試

- [ ] 員工離職流程測試
- [ ] 公司建立與加入流程完整測試

#### API 文件更新

- [ ] Swagger 文件更新
- [ ] 資料庫 Schema 文件
- [ ] 部署指南更新

### 🟢 低優先級

#### 使用者文件

- [ ] 註冊流程說明
- [ ] 管理員操作手冊
- [ ] 常見問題解答

#### 效能優化

- [ ] 新的查詢邏輯對效能的影響評估
- [ ] 權限檢查的效能最佳化
- [ ] 資料庫索引的調整

#### 使用者體驗改善

- [ ] 新註冊流程的使用者接受度測試
- [ ] 介面變更的學習成本評估
- [ ] 錯誤處理的友善性改進

---

## ⚠️ 風險與注意事項

### 需持續關注項目

1. **第三方整合**

   - OAuth (Google, Line) 功能待重構
   - 評估對現有整合的影響

2. **效能監控**

   - 監控新架構的查詢效能
   - 追蹤權限檢查的執行時間

3. **使用者回饋**
   - 收集新註冊流程的使用者意見
   - 持續優化使用者體驗

---

## ✅ 已完成項目 (歷史記錄)

### 📅 2025-01-28 完成

#### 🎯 測試驗證階段

- [x] **測試環境驗證** (2025-01-28)
  - [x] 檢查資料庫遷移狀態 (78個遷移檔案正常套用)
  - [x] AuthService 單元測試驗證 (16個測試案例全部通過)
  - [x] TenantsService 基本測試框架建立
  - [x] 登入系統核心功能驗證 (統一登入、JWT 刷新、權限驗證)
  - [x] 測試檔案清理 (依用戶要求移除臨時測試檔案)
  - [x] SystemUserService 單元測試 (21個測試案例全部通過)
  - [x] TenantUserService 單元測試 (28個測試案例全部通過)

### 📅 2025-01-28 及之前完成

#### 🎯 第一階段：資料模型重構

- [x] **資料表結構優化** (2025-06-02)

  - [x] 移除 `unified_refresh_tokens` 資料表
  - [x] 驗證分離式使用者架構完整性
  - [x] 確認日誌系統獨立性

- [x] **Prisma Schema 調整**

  - [x] `SystemUser` 模型實現 (system_users 資料表)
  - [x] `TenantUser` 模型實現 (tenant_users 資料表)
  - [x] 租戶唯一性約束強化
  - [x] 員工離職相關欄位新增

- [x] **資料庫遷移**

  - [x] 統一遷移工具建立 (`pnpm migration:users`)
  - [x] 資料完整性檢查腳本
  - [x] 回滾機制實現

- [x] **種子資料更新** (2025-06-02)
  - [x] 系統管理員帳號 (<EMAIL>)
  - [x] 測試租戶資料 (<EMAIL>)
  - [x] 角色與權限映射

#### 🎯 第二階段：後端 API 重構

- [x] **AuthService 重構** (2025-06-02)

  - [x] 兩階段註冊邏輯實現
  - [x] 統一登入機制 (unifiedLogin)
  - [x] JWT 結構更新 (新增 userType)
  - [x] 廢棄程式碼清理 (移除 23 個方法)

- [x] **租戶管理服務強化**

  - [x] 公司搜尋功能
  - [x] 唯一性檢查邏輯
  - [x] 租戶邀請系統

- [x] **使用者管理服務拆分**
  - [x] SystemUsersService 完整實現
  - [x] TenantUsersService 完整實現
  - [x] 員工離職處理邏輯 (2025-06-03)

#### 🎯 第三階段：前端重構

- [x] **認證狀態管理更新**

  - [x] `@horizai/auth` 支援 userType
  - [x] CurrentUser 聯合類型定義
  - [x] Token 管理機制更新

- [x] **註冊流程頁面**

  - [x] 階段一：個人帳號建立頁面
  - [x] 階段二：公司歸屬設定頁面
  - [x] 唯一性衝突處理頁面

- [x] **路由守衛調整**

  - [x] userType 路由保護
  - [x] 未歸屬公司使用者重定向
  - [x] 權限檢查邏輯更新

- [x] **管理後台部分功能**
  - [x] 邀請審核列表頁面

#### 🎯 第四階段：測試框架

- [x] **基礎 E2E 測試框架建立**
- [x] **權限系統測試覆蓋**

### 📅 2025-01-27 及之前完成

- [x] 核心目標定義與規劃
- [x] 使用者模型拆分設計
- [x] 權限系統 CASL 整合
- [x] 前端權限狀態管理更新

---

## 📊 完成進度總覽

- **第一階段 (資料模型重構)**: 100% ✅ (含測試環境驗證)
- **第二階段 (後端 API 重構)**: 100% ✅
- **第三階段 (前端重構)**: 90% ✅ (管理後台部分功能待完成)
- **第四階段 (測試與驗證)**: 80% ✅ (核心服務測試完成)
- **第五階段 (文件)**: 0% ❌

**整體進度**: ~85% 完成

---

**備註**:

- 此 TODO 清單會隨著重構進度持續更新
- 已移除過時的 AuthController 編譯錯誤章節（問題已解決）
- 重點聚焦在剩餘的測試、文件和細節優化工作

---
