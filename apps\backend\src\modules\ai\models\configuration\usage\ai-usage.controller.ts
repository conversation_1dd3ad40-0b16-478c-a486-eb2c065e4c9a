import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/core/auth/guards/auth.guard';
import { UsageStatisticsService } from '../../../../core/usage-tracking/services/usage-statistics.service';
import { AiUsageQueryDto, DetailedAiUsageStatisticsDto } from '../../../../core/usage-tracking/dto/usage.dto';

@ApiTags('admin/ai/usage')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('admin/ai/usage')
export class AiUsageController {
  constructor(private readonly usageStatisticsService: UsageStatisticsService) {}

  @Get('statistics')
  @ApiOperation({ summary: '讀取 AI 使用統計' })
  async getStatistics(@Query() query: AiUsageQueryDto): Promise<DetailedAiUsageStatisticsDto> {
    return this.usageStatisticsService.getDetailedStatistics(query);
  }
}
