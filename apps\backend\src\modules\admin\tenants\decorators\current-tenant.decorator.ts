import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ExtendedTenant } from '../tenants.service';

export const CurrentTenant = createParamDecorator(
  (data: keyof ExtendedTenant | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const tenant = request.tenant;

    if (data) {
      return tenant?.[data];
    }

    return tenant;
  },
);
