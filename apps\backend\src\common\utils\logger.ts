import { LoggerService } from '@nestjs/common';

function colorLevel(level: string) {
  switch (level) {
    case 'error':
      return '\x1b[41m\x1b[37m ERROR \x1b[0m';
    case 'warn':
      return '\x1b[43m\x1b[30m WARN  \x1b[0m';
    case 'info':
      return '\x1b[42m\x1b[30m INFO  \x1b[0m';
    case 'debug':
      return '\x1b[46m\x1b[30m DEBUG \x1b[0m';
    default:
      return level.toUpperCase();
  }
}

function formatMeta(meta: any): string {
  if (!meta) return '';
  if (Array.isArray(meta) && meta.length === 1 && typeof meta[0] === 'string') {
    return ` | context: ${meta[0]}`;
  }
  if (Array.isArray(meta) && meta.length === 1 && typeof meta[0] === 'object') {
    // 只印出重點欄位
    const obj = meta[0];
    let out = '';
    if (obj.path) out += ` | path: ${obj.path}`;
    if (obj.method) out += ` | method: ${obj.method}`;
    if (obj.user) out += ` | user: ${JSON.stringify(obj.user)}`;
    if (obj.ip) out += ` | ip: ${obj.ip}`;
    if (obj.stack) out += `\n  stack: ${obj.stack.split('\n').slice(0, 2).join('\n         ')} ...`;
    return out;
  }
  if (Array.isArray(meta)) {
    return ` | meta: ${JSON.stringify(meta)}`;
  }
  if (typeof meta === 'object') {
    return ` | meta: ${JSON.stringify(meta)}`;
  }
  return ` | meta: ${meta}`;
}

export class StructuredLogger implements LoggerService {
  log(message: any, ...optionalParams: any[]) {
    this.print('info', message, optionalParams);
  }
  error(message: any, ...optionalParams: any[]) {
    this.print('error', message, optionalParams);
  }
  warn(message: any, ...optionalParams: any[]) {
    this.print('warn', message, optionalParams);
  }
  debug(message: any, ...optionalParams: any[]) {
    this.print('debug', message, optionalParams);
  }
  verbose(message: any, ...optionalParams: any[]) {
    this.print('verbose', message, optionalParams);
  }

  private print(level: string, message: any, optionalParams: any[]) {
    const log = {
      timestamp: new Date().toISOString(),
      level,
      message,
      ...(optionalParams && optionalParams.length > 0 ? { meta: optionalParams } : {}),
    };
    if (process.env.NODE_ENV === 'production') {
      // 正式環境：單行 JSON
      console.log(JSON.stringify(log));
    } else {
      // 開發環境：重點資訊一行顯示，meta 只針對物件/錯誤自動展開
      const metaStr = formatMeta(log.meta);
      console.log(`[${log.timestamp}] ${colorLevel(level)}: ${log.message}${metaStr}`);
    }
  }
}
