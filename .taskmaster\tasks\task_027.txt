# Task ID: 27
# Title: 開發 Agent 工具註冊和管理 API
# Status: pending
# Dependencies: 26
# Priority: high
# Description: 建立後端 API 服務，支援系統管理員註冊新工具定義，以及 Agent 搭建過程中的工具選擇和配置管理功能。
# Details:
實作以下 API 服務:
1. **AiToolsService**: 提供工具的 CRUD 操作，包含 create, findAll, findByKey, update, delete 方法
2. **AiToolsController**: REST API 端點
   - GET /api/admin/ai/tools - 獲取所有可用工具
   - POST /api/admin/ai/tools - 創建新工具定義
   - PUT /api/admin/ai/tools/:id - 更新工具定義
   - DELETE /api/admin/ai/tools/:id - 刪除工具
3. **擴展 AiBotsService**: 新增工具關聯管理方法
   - assignTools(botId, toolIds) - 為 Agent 指派工具
   - removeTools(botId, toolIds) - 移除 Agent 工具
   - getBotsWithTools() - 查詢 Agent 及其工具列表
4. **權限控制**: 確保只有系統管理員可管理工具，租戶管理員可為其 Agent 選擇工具

# Test Strategy:
單元測試所有 CRUD 操作。集成測試 API 端點，包含權限驗證。測試 Agent-Tool 關聯的創建、查詢和更新。驗證租戶隔離和權限控制正確運作。
