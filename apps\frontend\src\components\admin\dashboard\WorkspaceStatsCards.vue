<template>
  <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">總工作區數</CardTitle>
        <Building2 class="h-4 w-4 text-gray-600 dark:text-zinc-400" />
      </CardHeader>
      <CardContent>
        <div class="flex flex-col space-y-1">
          <div class="text-2xl font-bold">{{ formatNumber(stats.totalWorkspaces) }}</div>
          <p class="text-xs text-gray-600 dark:text-zinc-400">
            活躍: {{ formatNumber(stats.activeWorkspaces) }} ({{
              stats.totalWorkspaces > 0
                ? Math.round((stats.activeWorkspaces / stats.totalWorkspaces) * 100)
                : 0
            }}%)
          </p>
        </div>
      </CardContent>
    </Card>

    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">本月新增</CardTitle>
        <CalendarPlus class="h-4 w-4 text-gray-600 dark:text-zinc-400" />
      </CardHeader>
      <CardContent>
        <div class="flex flex-col space-y-1">
          <div class="text-2xl font-bold">{{ formatNumber(stats.monthlyNewWorkspaces) }}</div>
          <p class="text-xs text-gray-600 dark:text-zinc-400">
            較上月 {{ stats.monthlyGrowth > 0 ? '+' : '' }}{{ stats.monthlyGrowth }}%
          </p>
        </div>
      </CardContent>
    </Card>

    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">儲存空間使用</CardTitle>
        <HardDrive class="h-4 w-4 text-gray-600 dark:text-zinc-400" />
      </CardHeader>
      <CardContent>
        <div class="flex flex-col space-y-1">
          <div class="text-2xl font-bold">{{ formatStorage(stats.storageUsed) }}</div>
          <p class="text-xs text-gray-600 dark:text-zinc-400">
            上限: {{ formatStorage(stats.storageLimit) }}
          </p>
        </div>
      </CardContent>
    </Card>

    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">活躍度</CardTitle>
        <Activity class="h-4 w-4 text-gray-600 dark:text-zinc-400" />
      </CardHeader>
      <CardContent>
        <div class="flex flex-col space-y-1">
          <div class="text-2xl font-bold">{{ stats.activityRate }}%</div>
          <p class="text-xs text-gray-600 dark:text-zinc-400">
            API 呼叫: {{ formatNumber(stats.apiCalls) }} 次
          </p>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Building2, CalendarPlus, HardDrive, Activity } from 'lucide-vue-next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface WorkspaceStats {
  totalWorkspaces: number;
  activeWorkspaces: number;
  monthlyNewWorkspaces: number;
  monthlyGrowth: number;
  storageUsed: number;
  storageLimit: number;
  activityRate: number;
  apiCalls: number;
}

const props = defineProps<{
  stats: WorkspaceStats;
}>();

import { formatNumber, formatStorage, formatPercent } from '@/utils/formatting.utils';
</script>
