-- CreateEnum
CREATE TYPE "FileCategory" AS ENUM ('DOCUMENT', 'IMAGE', 'VIDEO', 'AUDIO', 'ARCHIVE', 'SPREADSHEET', 'PRESENTATION', 'CODE', 'OTHER');

-- CreateEnum
CREATE TYPE "FileEntityType" AS ENUM ('PROJECT', 'TASK', 'PROGRESS_ENTRY', 'MILESTONE', 'COMMENT', 'WORKSPACE', 'USER_PROFILE');

-- CreateEnum
CREATE TYPE "FileVisibility" AS ENUM ('PRIVATE', 'WORKSPACE', 'PROJECT', 'TENANT', 'PUBLIC');

-- CreateEnum
CREATE TYPE "FileStatus" AS ENUM ('ACTIVE', 'ARCHIVED', 'QUARANTINED', 'BLOCKED');

-- CreateEnum
CREATE TYPE "FilePermission" AS ENUM ('VIEW', 'DOWNLOAD', 'COMMENT', 'EDIT', 'DELETE', 'SHARE', 'MANAGE');

-- CreateEnum
CREATE TYPE "ShareType" AS ENUM ('LINK', 'EMAIL', 'EMBED');

-- CreateEnum
CREATE TYPE "AccessType" AS ENUM ('VIEW', 'DOWNLOAD', 'PREVIEW', 'COMMENT', 'SHARE');

-- AlterEnum
ALTER TYPE "CommentEntityType" ADD VALUE 'SHARED_FILE';

-- CreateTable
CREATE TABLE "shared_files" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "originalName" TEXT NOT NULL,
    "description" TEXT,
    "fileType" TEXT NOT NULL,
    "fileExtension" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "filePath" TEXT NOT NULL,
    "fileUrl" TEXT,
    "category" "FileCategory" NOT NULL DEFAULT 'DOCUMENT',
    "entityType" "FileEntityType",
    "entityId" TEXT,
    "version" INTEGER NOT NULL DEFAULT 1,
    "parentFileId" TEXT,
    "isLatestVersion" BOOLEAN NOT NULL DEFAULT true,
    "thumbnailPath" TEXT,
    "previewPath" TEXT,
    "metadata" JSONB,
    "visibility" "FileVisibility" NOT NULL DEFAULT 'PRIVATE',
    "allowDownload" BOOLEAN NOT NULL DEFAULT true,
    "allowComment" BOOLEAN NOT NULL DEFAULT true,
    "expiresAt" TIMESTAMP(3),
    "uploaderId" TEXT NOT NULL,
    "uploaderType" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "workspaceId" TEXT,
    "status" "FileStatus" NOT NULL DEFAULT 'ACTIVE',
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "shared_files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "file_permissions" (
    "id" TEXT NOT NULL,
    "fileId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "userType" TEXT NOT NULL,
    "permission" "FilePermission" NOT NULL,
    "grantedBy" TEXT NOT NULL,
    "grantedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3),
    "tenantId" TEXT NOT NULL,

    CONSTRAINT "file_permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "file_shares" (
    "id" TEXT NOT NULL,
    "fileId" TEXT NOT NULL,
    "shareToken" TEXT NOT NULL,
    "shareType" "ShareType" NOT NULL DEFAULT 'LINK',
    "allowDownload" BOOLEAN NOT NULL DEFAULT true,
    "allowComment" BOOLEAN NOT NULL DEFAULT false,
    "requireAuth" BOOLEAN NOT NULL DEFAULT false,
    "password" TEXT,
    "maxDownloads" INTEGER,
    "currentDownloads" INTEGER NOT NULL DEFAULT 0,
    "expiresAt" TIMESTAMP(3),
    "sharedBy" TEXT NOT NULL,
    "sharedByType" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "file_shares_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "file_access_logs" (
    "id" TEXT NOT NULL,
    "fileId" TEXT NOT NULL,
    "shareId" TEXT,
    "userId" TEXT,
    "userType" TEXT,
    "ipAddress" TEXT NOT NULL,
    "userAgent" TEXT,
    "accessType" "AccessType" NOT NULL,
    "tenantId" TEXT NOT NULL,
    "accessedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "file_access_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "shared_files_tenantId_idx" ON "shared_files"("tenantId");

-- CreateIndex
CREATE INDEX "shared_files_workspaceId_idx" ON "shared_files"("workspaceId");

-- CreateIndex
CREATE INDEX "shared_files_uploaderId_uploaderType_idx" ON "shared_files"("uploaderId", "uploaderType");

-- CreateIndex
CREATE INDEX "shared_files_entityType_entityId_idx" ON "shared_files"("entityType", "entityId");

-- CreateIndex
CREATE INDEX "shared_files_parentFileId_idx" ON "shared_files"("parentFileId");

-- CreateIndex
CREATE INDEX "shared_files_status_idx" ON "shared_files"("status");

-- CreateIndex
CREATE INDEX "shared_files_createdAt_idx" ON "shared_files"("createdAt");

-- CreateIndex
CREATE INDEX "file_permissions_fileId_idx" ON "file_permissions"("fileId");

-- CreateIndex
CREATE INDEX "file_permissions_userId_userType_idx" ON "file_permissions"("userId", "userType");

-- CreateIndex
CREATE INDEX "file_permissions_tenantId_idx" ON "file_permissions"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "file_permissions_fileId_userId_userType_key" ON "file_permissions"("fileId", "userId", "userType");

-- CreateIndex
CREATE UNIQUE INDEX "file_shares_shareToken_key" ON "file_shares"("shareToken");

-- CreateIndex
CREATE INDEX "file_shares_fileId_idx" ON "file_shares"("fileId");

-- CreateIndex
CREATE INDEX "file_shares_shareToken_idx" ON "file_shares"("shareToken");

-- CreateIndex
CREATE INDEX "file_shares_tenantId_idx" ON "file_shares"("tenantId");

-- CreateIndex
CREATE INDEX "file_shares_expiresAt_idx" ON "file_shares"("expiresAt");

-- CreateIndex
CREATE INDEX "file_access_logs_fileId_idx" ON "file_access_logs"("fileId");

-- CreateIndex
CREATE INDEX "file_access_logs_shareId_idx" ON "file_access_logs"("shareId");

-- CreateIndex
CREATE INDEX "file_access_logs_userId_userType_idx" ON "file_access_logs"("userId", "userType");

-- CreateIndex
CREATE INDEX "file_access_logs_tenantId_idx" ON "file_access_logs"("tenantId");

-- CreateIndex
CREATE INDEX "file_access_logs_accessedAt_idx" ON "file_access_logs"("accessedAt");

-- CreateIndex
CREATE INDEX "file_access_logs_accessType_idx" ON "file_access_logs"("accessType");

-- AddForeignKey
ALTER TABLE "shared_files" ADD CONSTRAINT "shared_files_parentFileId_fkey" FOREIGN KEY ("parentFileId") REFERENCES "shared_files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shared_files" ADD CONSTRAINT "shared_files_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shared_files" ADD CONSTRAINT "shared_files_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_permissions" ADD CONSTRAINT "file_permissions_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "shared_files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_permissions" ADD CONSTRAINT "file_permissions_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_shares" ADD CONSTRAINT "file_shares_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "shared_files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_shares" ADD CONSTRAINT "file_shares_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_access_logs" ADD CONSTRAINT "file_access_logs_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "shared_files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_access_logs" ADD CONSTRAINT "file_access_logs_shareId_fkey" FOREIGN KEY ("shareId") REFERENCES "file_shares"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_access_logs" ADD CONSTRAINT "file_access_logs_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
