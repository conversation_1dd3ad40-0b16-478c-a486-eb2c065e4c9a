# Task ID: 31
# Title: Refactor @horizai/auth Package and Resolve Build Errors
# Status: done
# Dependencies: 1, 2
# Priority: high
# Description: Address critical build errors in the `@horizai/auth` package through a significant refactoring of the authentication system. This includes decoupling services, resolving type inconsistencies, and ensuring seamless compatibility with the existing CASL permission framework.
# Details:
1. **Error Identification & Analysis**:
    *   Thoroughly investigate and document all build errors originating from the `@horizai/auth` package.
    *   Analyze root causes, focusing on issues related to service coupling, TypeScript type definitions, and interactions with other modules.
2. **Auth System Refactoring**:
    *   **Service Decoupling**: Redesign authentication services to promote separation of concerns and improve modularity. Employ dependency injection (e.g., NestJS providers) effectively.
    *   **Type Safety**: Review and correct all TypeScript type mismatches and inconsistencies within the auth package and its interfaces with other parts of the application. Ensure strict type checking is satisfied.
    *   **CASL Compatibility**:
        *   Verify and adjust the authentication flow (e.g., user loading, session management, token validation) to correctly provide necessary context (user object, roles, permissions) for the CASL permission system.
        *   Ensure that changes in the auth system do not break existing CASL ability definitions or permission checks.
        *   Update any interfaces or data structures used by CASL that are sourced from the auth system.
3. **Dependency Management**:
    *   Review internal dependencies of the `@horizai/auth` package and external packages it relies on. Update or replace where necessary to resolve conflicts or improve stability.
4. **Code Quality & Maintainability**:
    *   Improve code readability, add necessary comments, and adhere to project coding standards.
    *   Ensure the refactored code is well-structured and easier to maintain.

# Test Strategy:
1. **Build Verification**:
    *   Confirm that the `@horizai/auth` package builds successfully without any errors or warnings (e.g., `npm run build` or `yarn build` within the package or monorepo context).
    *   Ensure the main application incorporating the `@horizai/auth` package also builds successfully.
2. **Unit Tests**:
    *   Execute all existing unit tests for the `@horizai/auth` package. All tests must pass.
    *   Write new unit tests for any refactored or new logic to ensure adequate coverage.
3. **Integration Tests**:
    *   Conduct integration tests focusing on:
        *   User login (various scenarios: correct credentials, incorrect credentials, locked accounts if applicable).
        *   Token generation, validation, and refresh mechanisms.
        *   Session management.
        *   Logout functionality.
        *   Interaction with user data services.
4. **CASL Integration Verification**:
    *   Test scenarios where CASL permissions are applied to resources protected by the refactored authentication system.
    *   Verify that users with different roles/permissions can access/perform actions according to CASL rules.
    *   Ensure that `ForbiddenError` or appropriate access denied responses are returned when CASL checks fail.
5. **End-to-End (E2E) Smoke Tests**:
    *   Perform basic E2E tests covering critical authentication-dependent user flows in the application.
6. **Blocker Resolution Confirmation**:
    *   Verify that tasks previously blocked by these auth issues (e.g., Task 11.5 - KnowledgeBaseTool tests) can now proceed and pass their respective tests related to authentication and authorization.
