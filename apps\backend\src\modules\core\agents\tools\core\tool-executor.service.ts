import {
  Injectable,
  Logger,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { Tool } from 'langchain/tools';
import { ToolExecutor, ToolExecutionContext, ToolConfig } from './tool-registry.interface';
import { ToolRegistryService } from './tool-registry.service';
import { AiToolsService } from '@/modules/ai/models/configuration/tools/ai-tools.service';
import { PrismaService } from '@/modules/core/prisma/prisma.service';

/**
 * 工具執行器服務
 *
 * 負責根據資料庫配置載入工具實作，並提供安全的工具執行環境。
 * 包含權限檢查、配置驗證和錯誤處理。
 */
@Injectable()
export class ToolExecutorService implements ToolExecutor {
  private readonly logger = new Logger(ToolExecutorService.name);

  constructor(
    private readonly toolRegistry: ToolRegistryService,
    private readonly aiToolsService: AiToolsService,
    private readonly prisma: PrismaService,
  ) {}

  /**
   * 載入並建立工具實例
   */
  async loadTool(toolConfig: ToolConfig, context: ToolExecutionContext): Promise<Tool> {
    const toolKey = toolConfig.key || toolConfig.name;
    this.logger.debug(`載入工具: ${toolKey} (${toolConfig.name})`);

    // 1. 檢查工具是否啟用
    if (toolConfig.isEnabled === false) {
      throw new BadRequestException(`工具 "${toolConfig.name}" 已停用`);
    }

    // 2. 取得工具實作
    const implementation = this.toolRegistry.get(toolKey);
    if (!implementation) {
      throw new NotFoundException(`找不到工具實作: ${toolKey}`);
    }

    // 3. 檢查工具權限
    await this.checkToolPermissions(toolKey, context);

    // 4. 驗證工具配置
    await this.validateToolConfig(toolConfig);

    // 5. 建立工具實例
    try {
      const tool = await implementation.createTool(context, toolConfig);
      this.logger.debug(`工具實例建立成功: ${toolKey}`);
      return tool;
    } catch (error) {
      this.logger.error(`建立工具實例失敗: ${toolKey}`, error.stack);
      throw new BadRequestException(`無法建立工具實例: ${error.message}`);
    }
  }

  /**
   * 載入多個工具
   */
  async loadTools(toolConfigs: ToolConfig[], context: ToolExecutionContext): Promise<Tool[]> {
    this.logger.debug(`載入 ${toolConfigs.length} 個工具`);

    const tools: Tool[] = [];
    const errors: Array<{ key: string; error: string }> = [];

    // 並行載入所有工具
    await Promise.allSettled(
      toolConfigs.map(async (config) => {
        try {
          const tool = await this.loadTool(config, context);
          tools.push(tool);
        } catch (error) {
          const toolKey = config.key || config.name;
          errors.push({
            key: toolKey,
            error: error.message,
          });
          this.logger.warn(`工具載入失敗: ${toolKey} - ${error.message}`);
        }
      }),
    );

    // 記錄載入結果
    this.logger.log(`工具載入完成: 成功 ${tools.length}，失敗 ${errors.length}`);

    if (errors.length > 0) {
      this.logger.warn('部分工具載入失敗:', errors);
    }

    return tools;
  }

  /**
   * 驗證工具配置
   */
  async validateToolConfig(toolConfig: ToolConfig): Promise<boolean> {
    const toolKey = toolConfig.key || toolConfig.name;

    try {
      // 1. 基本配置驗證
      if (!toolKey || !toolConfig.name) {
        throw new BadRequestException('工具配置缺少必要欄位');
      }

      // 2. 取得工具實作並進行專用驗證
      const implementation = this.toolRegistry.get(toolKey);
      if (implementation?.validateConfig) {
        const isValid = await implementation.validateConfig(toolConfig);
        if (!isValid) {
          throw new BadRequestException(`工具 "${toolKey}" 配置驗證失敗`);
        }
      }

      return true;
    } catch (error) {
      this.logger.error(`工具配置驗證失敗: ${toolKey}`, error.stack);
      throw error;
    }
  }

  /**
   * 檢查工具執行權限
   */
  async checkToolPermissions(toolKey: string, context: ToolExecutionContext): Promise<boolean> {
    try {
      // 1. 取得工具實作
      const implementation = this.toolRegistry.get(toolKey);
      if (!implementation) {
        throw new NotFoundException(`工具實作不存在: ${toolKey}`);
      }

      // 2. 執行工具特定的權限檢查
      if (implementation.checkPermissions) {
        const hasPermission = await implementation.checkPermissions(context);
        if (!hasPermission) {
          throw new ForbiddenException(`用戶無權限使用工具: ${toolKey}`);
        }
      }

      // 3. 基於 CASL 的通用權限檢查（可在此處添加）
      // 例如：context.ability.can(Actions.USE, Subjects.AI_TOOL)

      return true;
    } catch (error) {
      this.logger.error(`工具權限檢查失敗: ${toolKey}`, error.stack);
      throw error;
    }
  }

  /**
   * 根據 Bot ID 載入其關聯的所有工具
   */
  async loadBotTools(botId: string, context: ToolExecutionContext): Promise<Tool[]> {
    this.logger.debug(`載入 Bot ${botId} 的工具`);

    try {
      // 1. 從資料庫取得 Bot 關聯的工具配置
      const botTools = await this.getBotToolConfigs(botId, context.tenantId);

      if (botTools.length === 0) {
        this.logger.debug(`Bot ${botId} 沒有關聯任何工具`);
        return [];
      }

      // 2. 載入工具實例
      const tools = await this.loadTools(botTools, context);

      this.logger.log(`Bot ${botId} 成功載入 ${tools.length} 個工具`);
      return tools;
    } catch (error) {
      this.logger.error(`載入 Bot 工具失敗: ${botId}`, error.stack);
      throw new BadRequestException(`無法載入 Bot 工具: ${error.message}`);
    }
  }

  /**
   * 取得 Bot 的工具配置
   */
  private async getBotToolConfigs(botId: string, tenantId: string): Promise<ToolConfig[]> {
    try {
      const agentTools = await this.prisma.ai_agent_tools.findMany({
        where: {
          ai_agent_id: botId,
          ai_tool: {
            OR: [{ scope: 'SYSTEM' }, { scope: 'TENANT', tenant_id: tenantId }],
          },
        },
        include: {
          ai_tool: true,
        },
      });

      return agentTools.map((agentTool) => ({
        id: agentTool.ai_tool.id,
        key: agentTool.ai_tool.key,
        name: agentTool.ai_tool.name,
        description: agentTool.ai_tool.description || undefined,
        inputSchema: (agentTool.ai_tool.input_schema as Record<string, any>) || undefined,
        isEnabled: agentTool.is_enabled,
        config: (agentTool.config as Record<string, any>) || undefined,
      }));
    } catch (error) {
      this.logger.warn(`Failed to get tool configs for agent ${botId}:`, error);
      return [];
    }
  }

  /**
   * 取得可用工具列表（用於前端顯示）
   */
  async getAvailableTools(context: ToolExecutionContext): Promise<
    Array<{
      key: string;
      name: string;
      description: string;
      isRegistered: boolean;
    }>
  > {
    try {
      // 1. 從資料庫取得可用工具
      const dbTools = await this.aiToolsService.getAvailableTools(
        context.tenantId,
        context.workspaceId,
      );

      // 2. 檢查哪些工具已註冊實作
      const availableTools = dbTools.map((tool) => ({
        key: tool.key,
        name: tool.name,
        description: tool.description || '',
        isRegistered: this.toolRegistry.has(tool.key),
      }));

      return availableTools;
    } catch (error) {
      this.logger.error('取得可用工具列表失敗', error.stack);
      throw new BadRequestException(`無法取得可用工具: ${error.message}`);
    }
  }
}
