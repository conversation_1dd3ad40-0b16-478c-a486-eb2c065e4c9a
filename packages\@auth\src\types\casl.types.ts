import { type Ability, type AbilityTuple, type MongoQuery, type Subject as CaslSubject } from '@casl/ability';
import { type Prisma } from '@prisma/client';

/**
 * CASL 動作類型
 * 這些動作應該與 @horizai/permissions 中定義的 Actions 常數保持一致
 */
export type Action = 'manage' | 'create' | 'read' | 'update' | 'delete' | 'invite' | 'remove' | 'execute' | 'share' | 'access';

/**
 * CASL 主體類型
 * 這些主體應該與 @horizai/permissions 中定義的 Subjects 常數保持一致
 */
export type Subject = 'all' | 'System' | 'User' | 'Tenant' | 'TenantUser' | 'TenantInvitation' | 'Workspace' | 'workspace-member' | 'Project' | 'Client' | 'Form' | 'Permission' | 'Role' | 'Dashboard' | 'DashboardStats' | 'DashboardRecentTenants' | 'DashboardRecentOrders' | 'DashboardActiveUsers' | 'DashboardRevenue' | 'DashboardActivity' | 'AdminPanel' | 'SystemUser' | 'ai_models' | 'ai_bots' | 'Order' | 'Plan' | 'SystemSettings' | 'ai_keys' | 'AiFeatureConfig' | 'AiGlobalSetting' | 'AiUsageLog' | 'LoginLog' | 'SystemLog' | 'LineBot' | 'LineMessageLog' | 'LineLoginConfig' | 'SharedFile' | 'FilePermission' | 'FileShare' | 'Comment' | 'CommentReaction' | 'Notification' | 'CollaborationSession';

/**
 * All Prisma model names.
 *
 * @example
 * 'system_users'
 * 'tenant_users'
 * 'ai_bots'
 */
type PrismaModelName = keyof typeof Prisma.ModelName;

/**
 * CASL subject type, including 'all' and all Prisma model names.
 */
export type AppSubject = CaslSubject | PrismaModelName;

/**
 * CASL ability tuple, defining an action and a subject.
 *
 * @example
 * ['read', 'User']
 * ['manage', 'all']
 */
export type AppAbilityTuple = AbilityTuple<Action, AppSubject>;

/**
 * CASL ability instance type.
 */
export type AppAbility = Ability<AppAbilityTuple, MongoQuery>;

/**
 * Represents a serialized CASL rule that can be transferred over the API.
 * This is the JSON representation of a CASL rule.
 */
export interface CaslRule {
  action: Action | Action[];
  subject: Subject | Subject[];
  /**
   * Defines the fields of a subject that the user can access.
   * If not specified, the user can access all fields.
   */
  fields?: string[];
  /**
   * Defines the conditions that the user must meet to access the subject.
   */
  conditions?: MongoQuery;
  /**
   * If true, the rule is inverted, meaning it forbids the action.
   */
  inverted?: boolean;
  /**
   * A descriptive reason for why the rule is enforced.
   */
  reason?: string;
} 