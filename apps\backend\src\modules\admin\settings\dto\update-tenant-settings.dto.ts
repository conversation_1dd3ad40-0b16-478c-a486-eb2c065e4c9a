import { IsString, IsBoolean, IsIn } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateTenantSettingsDto {
  @ApiProperty({ description: '是否允許租戶註冊' })
  @IsBoolean()
  allowTenantRegistration!: boolean;

  @ApiProperty({ description: '是否要求子網域' })
  @IsBoolean()
  tenantDomainRequired!: boolean;

  @ApiProperty({ description: '預設方案 ID' })
  @IsString()
  defaultPlan!: string;

  @ApiProperty({ description: '預設租戶方案' })
  @IsString()
  defaultTenantPlan: string;

  @ApiProperty({ description: '預設租戶狀態' })
  @IsString()
  @IsIn(['active', 'inactive', 'pending', 'suspended'])
  defaultTenantStatus: 'active' | 'inactive' | 'pending' | 'suspended';

  @ApiProperty({ description: '是否允許修改子網域' })
  @IsBoolean()
  allowTenantSubdomainChange: boolean;

  @ApiProperty({ description: '是否要求租戶審核' })
  @IsBoolean()
  requireTenantApproval: boolean;

  @ApiProperty({ description: '是否允許方案降級' })
  @IsBoolean()
  allowPlanDowngrade: boolean;
}
