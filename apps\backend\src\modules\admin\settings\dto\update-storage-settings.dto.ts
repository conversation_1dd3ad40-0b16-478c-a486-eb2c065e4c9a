import { IsString, IsN<PERSON>ber, IsBoolean, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateStorageSettingsDto {
  @ApiProperty({ description: '儲存提供者' })
  @IsString()
  provider: string; // 'local' | 'aws-s3' | 'google-cloud-storage'

  @ApiProperty({ description: '本地儲存路徑', required: false })
  @IsString()
  diskPath?: string;

  @ApiProperty({ description: 'S3 Bucket 名稱', required: false })
  @IsString()
  s3Bucket?: string;

  @ApiProperty({ description: 'S3 區域', required: false })
  @IsString()
  s3Region?: string;

  @ApiProperty({ description: 'S3 Access Key', required: false })
  @IsString()
  s3AccessKey?: string;

  @ApiProperty({ description: 'S3 Secret Key 是否已設定', required: false })
  @IsBoolean()
  s3SecretKey?: boolean;

  @ApiProperty({ description: '預設檔案隱私', required: false })
  @IsString()
  defaultPrivacy?: string;

  @ApiProperty({ description: '最大檔案大小 (MB)', required: false })
  @IsNumber()
  maxFileSize?: number;

  @ApiProperty({ description: '允許的檔案類型', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  allowedFileTypes?: string[];
}
