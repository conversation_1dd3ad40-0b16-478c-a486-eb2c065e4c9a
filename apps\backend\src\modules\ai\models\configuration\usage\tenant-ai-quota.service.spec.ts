import { Test, TestingModule } from '@nestjs/testing';
import { TenantAiQuotaService, InsufficientCreditsException } from './tenant-ai-quota.service';
import { PrismaService } from '../../../../core/prisma/prisma.service';
import { Decimal } from '@prisma/client/runtime/library';
import { BadRequestException, NotFoundException } from '@nestjs/common';

describe('TenantAiQuotaService', () => {
  let service: TenantAiQuotaService;
  let prisma: PrismaService;

  // 新增：建立 mockPrismaService 物件，並於 beforeEach 重設 mock
  const mockPrismaService = {
    tenants: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    tenant_credit_purchases: {
      create: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  beforeEach(async () => {
    // 每次測試前重設所有 mock
    mockPrismaService.tenants.findUnique.mockReset();
    mockPrismaService.tenants.update.mockReset();
    mockPrismaService.tenant_credit_purchases.create.mockReset();
    mockPrismaService.$transaction.mockReset();
    mockPrismaService.$transaction.mockImplementation((fn) => fn(mockPrismaService));

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TenantAiQuotaService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();
    service = module.get<TenantAiQuotaService>(TenantAiQuotaService);
    // 直接將 prisma 指向 mockPrismaService 以繞過型別檢查
    prisma = mockPrismaService as any;
  });

  describe('checkAndDeductCredits', () => {
    it('應拋出 InsufficientCreditsException 當點數不足時', async () => {
      // Arrange
      const tenantId = 'tenant-1';
      const costToDeduct = new Decimal(100);
      mockPrismaService.tenants.findUnique.mockResolvedValue({
        id: tenantId,
        current_ai_credits: new Decimal(50),
        planId: 'plan-1',
      });
      // Act & Assert
      await expect(service.checkAndDeductCredits(tenantId, costToDeduct)).rejects.toThrow(
        InsufficientCreditsException,
      );
    });

    it('應回傳 true 當 costToDeduct 為 0 或負數', async () => {
      // Arrange
      const tenantId = 'tenant-1';
      const costToDeduct = new Decimal(0);
      // Act
      const result = await service.checkAndDeductCredits(tenantId, costToDeduct);
      // Assert
      expect(result).toBe(true);
    });

    it('應拋出 NotFoundException 當租戶不存在時', async () => {
      // Arrange
      mockPrismaService.tenants.findUnique.mockResolvedValue(null);
      // Act & Assert
      await expect(service.checkAndDeductCredits('not-exist', new Decimal(10))).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('resetMonthlyCredits', () => {
    it('應拋出 NotFoundException 當租戶不存在時', async () => {
      // Arrange
      mockPrismaService.tenants.findUnique.mockResolvedValue(null);
      // Act & Assert
      await expect(service.resetMonthlyCredits('not-exist')).rejects.toThrow(NotFoundException);
    });
  });

  describe('addPurchasedCredits', () => {
    it('應拋出 BadRequestException 當 creditsToAdd <= 0', async () => {
      // Arrange
      const tenantId = 'tenant-1';
      // Act & Assert
      await expect(service.addPurchasedCredits(tenantId, new Decimal(0))).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.addPurchasedCredits(tenantId, new Decimal(-10))).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('getCurrentCredits', () => {
    it('應拋出 NotFoundException 當租戶不存在時', async () => {
      // Arrange
      mockPrismaService.tenants.findUnique.mockResolvedValue(null);
      // Act & Assert
      await expect(service.getCurrentCredits('not-exist')).rejects.toThrow(NotFoundException);
    });
  });
});
