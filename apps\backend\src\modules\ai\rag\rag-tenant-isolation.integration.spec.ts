import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RAGIngestionService } from './rag-ingestion.service';
import { RAGSecurityService } from './rag-security.service';
// Note: These tools have been moved, update import paths as needed
// import { KnowledgeBaseTool } from '../agent/tools/knowledge-base.tool';
// import { KnowledgeBaseToolFactory } from '../agent/tools/knowledge-base.tool.factory';
import { EventEmitter2 } from '@nestjs/event-emitter';
// Note: Event classes need to be updated
// import { FileUploadedEvent } from '../../../common/events/file-uploaded.event';

describe('RAG Tenant Isolation Integration', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let ragIngestionService: RAGIngestionService;
  let ragSecurityService: RAGSecurityService;
  let knowledgeBaseToolFactory: KnowledgeBaseToolFactory;
  let eventEmitter: EventEmitter2;

  // Test data for two different tenants
  const tenant1Id = 'tenant-1';
  const tenant2Id = 'tenant-2';
  const workspace1Id = 'workspace-1';
  const workspace2Id = 'workspace-2';

  const mockDocument1 = {
    id: 'doc-1',
    title: 'Tenant 1 Document',
    content: 'This is confidential information for tenant 1 only.',
    filePath: '/uploads/tenant1/document1.pdf',
    fileId: 'file-1',
    fileName: 'tenant1-doc.pdf',
    fileSize: 1024,
    mimeType: 'application/pdf',
  };

  const mockDocument2 = {
    id: 'doc-2',
    title: 'Tenant 2 Document',
    content: 'This is sensitive data for tenant 2 exclusively.',
    filePath: '/uploads/tenant2/document2.pdf',
    fileId: 'file-2',
    fileName: 'tenant2-doc.pdf',
    fileSize: 2048,
    mimeType: 'application/pdf',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      providers: [
        PrismaService,
        RAGIngestionService,
        RAGSecurityService,
        KnowledgeBaseToolFactory,
        EventEmitter2,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    prismaService = moduleFixture.get<PrismaService>(PrismaService);
    ragIngestionService = moduleFixture.get<RAGIngestionService>(RAGIngestionService);
    ragSecurityService = moduleFixture.get<RAGSecurityService>(RAGSecurityService);
    knowledgeBaseToolFactory =
      moduleFixture.get<KnowledgeBaseToolFactory>(KnowledgeBaseToolFactory);
    eventEmitter = moduleFixture.get<EventEmitter2>(EventEmitter2);
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
    await app.close();
  });

  beforeEach(async () => {
    // Clean up any existing test data before each test
    await cleanupTestData();
  });

  async function cleanupTestData() {
    try {
      // Delete chunks first due to foreign key constraints
      await prismaService.vectorChunk.deleteMany({
        where: {
          OR: [{ tenant_id: tenant1Id }, { tenant_id: tenant2Id }],
        },
      });

      // Then delete documents
      await prismaService.vector_documents.deleteMany({
        where: {
          OR: [{ tenant_id: tenant1Id }, { tenant_id: tenant2Id }],
        },
      });
    } catch (error) {
      // Ignore cleanup errors
      console.warn('Cleanup warning:', error.message);
    }
  }

  describe('Document Ingestion Isolation', () => {
    it('should store documents with correct tenant isolation metadata', async () => {
      // Index document for tenant 1
      const event1 = new FileUploadedEvent(
        mockDocument1.fileId,
        tenant1Id,
        workspace1Id,
        mockDocument1.filePath,
        mockDocument1.fileName,
        mockDocument1.fileSize,
        mockDocument1.mimeType,
      );

      await ragIngestionService.handleFileUpload(event1);

      // Index document for tenant 2
      const event2 = new FileUploadedEvent(
        mockDocument2.fileId,
        tenant2Id,
        workspace2Id,
        mockDocument2.filePath,
        mockDocument2.fileName,
        mockDocument2.fileSize,
        mockDocument2.mimeType,
      );

      await ragIngestionService.handleFileUpload(event2);

      // Verify documents are stored with correct tenant_id
      const tenant1Docs = await prismaService.vector_documents.findMany({
        where: { tenant_id: tenant1Id },
      });

      const tenant2Docs = await prismaService.vector_documents.findMany({
        where: { tenant_id: tenant2Id },
      });

      expect(tenant1Docs).toHaveLength(1);
      expect(tenant1Docs[0]).toMatchObject({
        tenant_id: tenant1Id,
        workspace_id: workspace1Id,
        file_id: mockDocument1.fileId,
        title: mockDocument1.fileName,
      });

      expect(tenant2Docs).toHaveLength(1);
      expect(tenant2Docs[0]).toMatchObject({
        tenant_id: tenant2Id,
        workspace_id: workspace2Id,
        file_id: mockDocument2.fileId,
        title: mockDocument2.fileName,
      });

      // Verify chunks are also properly isolated
      const tenant1Chunks = await prismaService.vectorChunk.findMany({
        where: { tenant_id: tenant1Id },
      });

      const tenant2Chunks = await prismaService.vectorChunk.findMany({
        where: { tenant_id: tenant2Id },
      });

      expect(tenant1Chunks.length).toBeGreaterThan(0);
      expect(tenant2Chunks.length).toBeGreaterThan(0);

      // Verify all chunks have correct tenant_id
      tenant1Chunks.forEach((chunk) => {
        expect(chunk.tenant_id).toBe(tenant1Id);
      });

      tenant2Chunks.forEach((chunk) => {
        expect(chunk.tenant_id).toBe(tenant2Id);
      });
    });

    it('should prevent cross-tenant document access during ingestion', async () => {
      // This test ensures that the ingestion process doesn't accidentally
      // create documents or chunks with wrong tenant_id

      const event = new FileUploadedEvent(
        'test-file-id',
        tenant1Id,
        workspace1Id,
        '/test/path.pdf',
        'test.pdf',
        1000,
        'application/pdf',
      );

      await ragIngestionService.handleFileUpload(event);

      // Verify no documents exist for other tenants
      const otherTenantDocs = await prismaService.vector_documents.findMany({
        where: {
          tenant_id: { not: tenant1Id },
        },
      });

      const otherTenantChunks = await prismaService.vectorChunk.findMany({
        where: {
          tenant_id: { not: tenant1Id },
        },
      });

      expect(otherTenantDocs).toHaveLength(0);
      expect(otherTenantChunks).toHaveLength(0);
    });
  });

  describe('Search Query Isolation', () => {
    beforeEach(async () => {
      // Set up test documents for both tenants
      await setupTestDocuments();
    });

    it('should only return documents from the same tenant', async () => {
      // Search as tenant 1
      const tenant1Results = await ragIngestionService.searchSimilarDocuments(
        'confidential information',
        tenant1Id,
        workspace1Id,
        10,
        0.1, // Low threshold to ensure we get results
      );

      // Search as tenant 2
      const tenant2Results = await ragIngestionService.searchSimilarDocuments(
        'sensitive data',
        tenant2Id,
        workspace2Id,
        10,
        0.1,
      );

      // Verify tenant 1 only sees their documents
      expect(tenant1Results.length).toBeGreaterThan(0);
      tenant1Results.forEach((result) => {
        expect(result.metadata.tenant_id).toBe(tenant1Id);
        expect(result.content).toContain('tenant 1');
        expect(result.content).not.toContain('tenant 2');
      });

      // Verify tenant 2 only sees their documents
      expect(tenant2Results.length).toBeGreaterThan(0);
      tenant2Results.forEach((result) => {
        expect(result.metadata.tenant_id).toBe(tenant2Id);
        expect(result.content).toContain('tenant 2');
        expect(result.content).not.toContain('tenant 1');
      });
    });

    it('should enforce workspace isolation within tenant', async () => {
      // Create a document for the same tenant but different workspace
      const differentWorkspaceId = 'workspace-different';

      // Search in specific workspace should not return documents from other workspaces
      const workspaceResults = await ragIngestionService.searchSimilarDocuments(
        'information',
        tenant1Id,
        differentWorkspaceId, // Different workspace
        10,
        0.1,
      );

      // Should return empty results since no documents exist in the different workspace
      expect(workspaceResults).toHaveLength(0);
    });

    it('should handle empty results gracefully for isolated tenants', async () => {
      const nonExistentTenant = 'non-existent-tenant';

      const results = await ragIngestionService.searchSimilarDocuments(
        'any query',
        nonExistentTenant,
        'any-workspace',
        10,
        0.1,
      );

      expect(results).toHaveLength(0);
    });
  });

  describe('Knowledge Base Tool Isolation', () => {
    beforeEach(async () => {
      await setupTestDocuments();
    });

    it('should create tenant-isolated tools via factory', async () => {
      const tenant1Tool = knowledgeBaseToolFactory.create(tenant1Id, workspace1Id);
      const tenant2Tool = knowledgeBaseToolFactory.create(tenant2Id, workspace2Id);

      expect(tenant1Tool).toBeInstanceOf(KnowledgeBaseTool);
      expect(tenant2Tool).toBeInstanceOf(KnowledgeBaseTool);
      expect(tenant1Tool).not.toBe(tenant2Tool); // Different instances
    });

    it('should enforce tenant isolation in tool searches', async () => {
      const tenant1Tool = knowledgeBaseToolFactory.create(tenant1Id, workspace1Id);
      const tenant2Tool = knowledgeBaseToolFactory.create(tenant2Id, workspace2Id);

      const query = JSON.stringify({ query: 'confidential' });

      // Search with tenant 1 tool
      const tenant1Result = await tenant1Tool._call(query);
      const tenant1Data = JSON.parse(tenant1Result);

      // Search with tenant 2 tool
      const tenant2Result = await tenant2Tool._call(query);
      const tenant2Data = JSON.parse(tenant2Result);

      // Verify results are isolated
      expect(tenant1Data.success).toBe(true);
      expect(tenant2Data.success).toBe(true);

      if (tenant1Data.results.length > 0) {
        tenant1Data.results.forEach((result) => {
          expect(result.metadata.tenant_id).toBe(tenant1Id);
        });
      }

      if (tenant2Data.results.length > 0) {
        tenant2Data.results.forEach((result) => {
          expect(result.metadata.tenant_id).toBe(tenant2Id);
        });
      }

      // Results should be different (tenant-specific)
      expect(tenant1Data.results).not.toEqual(tenant2Data.results);
    });
  });

  describe('Security Service Validation', () => {
    it('should validate tenant context in security checks', async () => {
      // This test ensures the security service properly validates tenant context

      expect(() => {
        ragSecurityService.validateSearchQuery('test query', tenant1Id);
      }).not.toThrow();

      expect(() => {
        ragSecurityService.validateSearchQuery('test query', tenant2Id);
      }).not.toThrow();

      // Test with potentially malicious input
      expect(() => {
        ragSecurityService.validateSearchQuery('<script>alert("xss")</script>', tenant1Id);
      }).toThrow();
    });

    it('should prevent unauthorized document access', async () => {
      // Set up a document for tenant 1
      await setupTestDocuments();

      const tenant1Docs = await prismaService.vector_documents.findMany({
        where: { tenant_id: tenant1Id },
      });

      expect(tenant1Docs.length).toBeGreaterThan(0);
      const docId = tenant1Docs[0].id;

      // Test validateDocumentAccess
      expect(() => {
        ragSecurityService.validateDocumentAccess(docId, tenant1Id);
      }).not.toThrow();

      expect(() => {
        ragSecurityService.validateDocumentAccess(docId, tenant2Id);
      }).toThrow('Access denied');
    });
  });

  describe('Data Leakage Prevention', () => {
    beforeEach(async () => {
      await setupTestDocuments();
    });

    it('should not expose tenant data in error messages', async () => {
      try {
        // Attempt to access non-existent document with wrong tenant
        await ragIngestionService.searchSimilarDocuments(
          'non-existent query that should not work',
          'wrong-tenant-id',
          'wrong-workspace-id',
          10,
          0.9, // High threshold to minimize results
        );
      } catch (error) {
        // Error messages should not contain sensitive tenant information
        expect(error.message).not.toContain(tenant1Id);
        expect(error.message).not.toContain(tenant2Id);
        expect(error.message).not.toContain('confidential');
        expect(error.message).not.toContain('sensitive');
      }
    });

    it('should not return partial data from other tenants', async () => {
      // Perform search that might match across tenants
      const results = await ragIngestionService.searchSimilarDocuments(
        'document information data',
        tenant1Id,
        workspace1Id,
        100, // Request many results
        0.0, // Very low threshold
      );

      // All results must belong to the requesting tenant
      results.forEach((result) => {
        expect(result.metadata.tenant_id).toBe(tenant1Id);
        expect(result.metadata.workspace_id).toBe(workspace1Id);
      });

      // Verify no cross-contamination in content
      const allContent = results.map((r) => r.content).join(' ');
      expect(allContent).not.toContain('tenant 2');
    });
  });

  async function setupTestDocuments() {
    // Create events for both tenants
    const event1 = new FileUploadedEvent(
      mockDocument1.fileId,
      tenant1Id,
      workspace1Id,
      mockDocument1.filePath,
      mockDocument1.fileName,
      mockDocument1.fileSize,
      mockDocument1.mimeType,
    );

    const event2 = new FileUploadedEvent(
      mockDocument2.fileId,
      tenant2Id,
      workspace2Id,
      mockDocument2.filePath,
      mockDocument2.fileName,
      mockDocument2.fileSize,
      mockDocument2.mimeType,
    );

    // Process the events
    await ragIngestionService.handleFileUpload(event1);
    await ragIngestionService.handleFileUpload(event2);

    // Wait a bit for processing to complete
    await new Promise((resolve) => setTimeout(resolve, 100));
  }
});
