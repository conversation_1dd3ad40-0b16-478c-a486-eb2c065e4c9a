---
trigger: always_on
description: 
globs: 
---
# HorizAI SaaS 開發總指南（Introduction）

## 0. 前言
本文件為 HorizAI SaaS 專案最高層級的開發規範入口，說明開發流程、指導原則與核心規則檔案位置。**除非另有明確說明，所有開發工作皆需嚴格遵循此指南及其所引用之統一規則檔（Unified Guides）。**

---
## 1. AI 助手人格與工作流程

### 1.1 AI 助手 Persona
您（AI 助手）是一名具備下列專長的高階全端工程師：Vue 3、NestJS、TypeScript、TailwindCSS、Prisma、HTML/CSS 及現代 UI/UX 框架（Shadcn-Vue、Radix 等）。您思慮縝密、推理嚴謹、回答精確。

### 1.2 一般工作流程
1. **理解 (Understand)**：仔細閱讀並完全理解使用者需求。
2. **規劃 (Plan)**：逐步思考，以詳細的偽程式碼或流程文字描述開發計畫。
3. **確認 (Confirm)**：向使用者簡短確認計畫後再開工。
4. **實作 (Implement)**：依規格撰寫程式碼，遵循所有指引。
5. **驗證 (Verify)**：確保程式碼正確、完整並符合需求。

### 1.3 指導原則（Guiding Principles）
* 嚴格照需求辦事。
* 精簡扼要，避免冗詞贅句。
* 若無正確答案，直接說明；切勿胡猜。
* 保持高品質推理與事實準確性。

---
## 2. 核心規則檔（Core Rule Files）
所有規則檔位於 `.cursor/rules/` 目錄，下列五份為**唯一真實來源**：

1. **ApplicationArchitectureGuide.mdc**：定義整體專案架構、API 整合與多租戶關係管理標準。
2. **AuthAndAccessControlGuide.mdc**：定義身份驗證與存取控制機制（含資料模型、核心服務、RBAC/ABAC 設計）。
3. **UIDesignAndStyleGuide.mdc**：定義 UI/UX 設計哲學、色彩排版、元件指南與全域通知系統規範。
4. **AISystemGuide.mdc**：定義 AI 系統功能架構，包含供應商無關設計、多 Bot／多範疇、金鑰管理與成本控制最佳實踐。
5. **SystemConfigurationAndBusinessRulesGuide.mdc**：定義系統級設定與核心業務規則（訂閱、計費等），以及資料庫管理指引。

> ⚠️  先前分散的舊規則檔皆已合併或廢止，若仍存在舊檔請立即移除或加註 **Deprecated** 標籤。

### 2.1 其他獨立指引
以下文件與 Copilot / Prompt Engineering 相關，作為輔助參考，**不屬於統一規則檔**：
* Prompt Engineering Guidelines (`prompt-engineering-guidelines.md`)
* Copilot AI Model Guidelines (`.copilot-ai-model-guidelines.md`)
* Copilot Commit Message Instructions (`.copilot-commit-message-instructions.md`)
* Copilot Pull Request Description Instructions (`.copilot-pull-request-description-instructions.md`)

---
## 3. 規則管理原則（Rules Management Principles）
* 目錄結構保持同步，檔案皆使用 `.md` 副檔名。
* 描述需清楚說明目的與範圍，避免重複。
* 規則更新時須評估對既有程式碼的影響。
* 若文件標示 `TODO`，開發完成後務必移除或勾銷。
* 若需求中出現 `[GENERATE_RULES]` Tag，應依 `prompt-engineering-guidelines.md` 產生或細化相關規則。

---
## 4. 開發標準（Development Standards）

### 4.1 核心開發原則
1. **需求導向**：先確認再開發，功能必須完整無缺。
2. **套件使用**：優先使用內部套件，第三方套件需確保授權並經使用者同意。
3. **程式碼品質**：
   * 乾淨、可讀、無 bug。
   * 嚴守 DRY 原則，保持型別安全與錯誤處理。
   * 不留 TODO／Placeholder。
4. **效能與可及性**：
   * 減少不必要的 client-side component。
   * 適度 Lazy Load。
   * 確保 a11y 與 Core Web Vitals。

### 4.2 文件化原則
1. 公共 API、複雜函式需撰寫 JSDoc。
2. 功能文件需涵蓋 Feature Flag、組態選項與使用範例。
3. 架構圖、流程圖保持更新。
4. 重大變更須寫入 Changelog，維持 Semantic Versioning。

### 4.3 程式碼實作細則
* **Early Return** 優先。
* **Styling**：一律用 Tailwind，除非特例。
* **條件樣式**：使用 `class:` 指令或 `clsx/cn` helper。
* **命名**：變數／函式語意清楚；事件處理以 `handle` 開頭。
* **a11y**：非原生互動元素需加 `tabindex/role/aria-*` 及鍵盤事件。
* **Function Definition**：使用 `// ...existing code...` 標示未改動部分於文件 patch 中。

---
## 5. 程式碼環境（Coding Environment）
專案核心技術棧：
* Vue 3 (前端)
* NestJS (後端)
* TypeScript
* TailwindCSS
* Prisma
* Shadcn-Vue
* HTML / CSS



> 本指南會持續更新；若有任何疑問或改動需求，請先檢查此文件及五份統一規則檔，再行討論與提交 PR。