/**
 * 權限定義介面（多租戶架構）
 * @property action 權限動作（如 create、read、update、delete、manage...）
 * @property subject 權限主體（如 user、tenant、workspace...）
 * @property name 權限中文名稱
 * @property description 權限說明
 * @property zone 權限區域（admin | workspace）
 * @property scope 權限範圍（SYSTEM | TENANT | WORKSPACE | GLOBAL）
 * @property category 權限分類
 * @property isSystemDefined 是否為系統預設權限
 * @property conditions 進階條件設定，對應 Prisma Permission.conditions
 * @property fields 權限欄位限制，對應 Prisma Permission.fields
 * @property deprecated 代表此權限是否已標記為廢棄
 * @property filePath 權限定義/使用的檔案路徑
 * @property lineNumber 權限定義/使用的檔案行號
 */
export interface PermissionDefinition {
  action: string;
  subject: string;
  name?: string;  // 權限中文名稱
  description?: string;
  zone?: 'admin' | 'workspace';  // 權限區域
  scope?: "SYSTEM" | "TENANT" | "WORKSPACE" | "GLOBAL";
  category?: string;
  isSystemDefined?: boolean;
  /** 進階條件設定，對應 Prisma Permission.conditions */
  conditions?: any;
  /** 權限欄位限制，對應 Prisma Permission.fields */
  fields?: string[];
  /** 代表此權限是否已標記為廢棄 */
  deprecated?: boolean;
  /** 權限定義/使用的檔案路徑 */
  filePath?: string;
  /** 權限定義/使用的檔案行號 */
  lineNumber?: number;
}

/**
 * 掃描結果介面
 */
export interface ScanResult {
  permissions: PermissionDefinition[];
  stats: {
    totalFiles: number;
    scannedFiles: number;
    filesWithPermissions: number;
    totalPermissions: number;
  };
  errors: string[];
}

/**
 * 同步結果介面
 */
export interface SyncResult {
  created: number;
  updated: number;
  deprecated: number;
  errors: number;
  details: {
    createdPermissions: string[];
    updatedPermissions: string[];
    deprecatedPermissions: string[];
    errorMessages: string[];
  };
}

/**
 * 權限同步報告介面
 */
export interface PermissionSyncReport {
  timestamp: string;
  version: string;
  mode: "dry-run" | "sync";
  scanResult: ScanResult;
  syncResult?: SyncResult;
  summary: {
    total: number;
    byScope: Record<string, number>;
    byCategory: Record<string, number>;
    bySubject: Record<string, number>;
  };
  permissions: PermissionDefinition[];
}

/**
 * CLI 選項介面
 */
export interface CLIOptions {
  command: "scan" | "sync" | "report" | "validate" | "dev" | "watch" | "check" | "stats";
  dryRun?: boolean;
  force?: boolean;
  verbose?: boolean;
  output?: string;
  cache?: boolean;
  /** 是否抑制警告 (靜默模式) */
  suppressWarnings?: boolean;
  /** 是否自動同步 (用於 watch 模式) */
  autoSync?: boolean;
}

/**
 * 掃描設定介面
 */
export interface ScanConfig {
  backendPaths: string[];
  frontendPaths: string[];
  seedPaths: string[];
  excludePatterns: string[];
  cacheEnabled: boolean;
  cachePath: string;
  /** 快取過期時間（毫秒） */
  ttl?: number;
  /** 推論規則設定 */
  inference?: {
    scopes: Record<string, string[]>;
    categories: Record<string, string[]>;
  };
  /** 是否抑制警告輸出 */
  suppressWarnings?: boolean;
}
