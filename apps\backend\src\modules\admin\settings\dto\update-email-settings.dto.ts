import {
  <PERSON>S<PERSON>,
  <PERSON><PERSON><PERSON>ber,
  IsBoolean,
  IsEmail,
  IsIn,
  IsOptional,
  IsArray,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

class SendGridTemplate {
  @ApiProperty({ description: 'SendGrid 範本 ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'SendGrid 範本名稱' })
  @IsString()
  name: string;
}

export class UpdateEmailSettingsDto {
  @ApiProperty({ description: '郵件服務提供者' })
  @IsString()
  @IsIn(['smtp', 'sendgrid'])
  provider: 'smtp' | 'sendgrid';

  @ApiProperty({ description: 'SMTP 主機' })
  @IsString()
  smtpHost: string;

  @ApiProperty({ description: 'SMTP 連接埠' })
  @IsNumber()
  smtpPort: number;

  @ApiProperty({ description: 'SMTP 帳號' })
  @IsString()
  smtpUser: string;

  @ApiProperty({ description: 'SMTP 密碼' })
  @IsString()
  smtpPassword: string;

  @ApiProperty({ description: '是否使用 SSL/TLS' })
  @IsBoolean()
  smtpSecure: boolean;

  @ApiProperty({ description: 'SendGrid API 金鑰' })
  @IsString()
  sendGridApiKey: string;

  @ApiProperty({ description: '是否已設定 SendGrid 金鑰' })
  @IsBoolean()
  isSendGridKeySet: boolean;

  @ApiProperty({ description: '寄件人名稱' })
  @IsString()
  fromName: string;

  @ApiProperty({ description: '寄件人信箱' })
  @IsEmail()
  fromEmailAddress: string;

  @ApiProperty({ description: 'SendGrid 範本列表', type: [SendGridTemplate], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SendGridTemplate)
  sendGridTemplates?: SendGridTemplate[];

  @ApiProperty({ description: '選定的範本 ID', required: false })
  @IsOptional()
  @IsString()
  selectedTemplateId?: string;

  @ApiProperty({ description: '動態範本數據', required: false })
  @IsOptional()
  @IsObject()
  dynamicTemplateData?: Record<string, any>;
}
