# HorizAI SaaS - 登入系統架構指南

**版本**: 1.0
**基於**: `LoginSystemPRD.md`, `ApplicationArchitectureUnifiedGuide.mdc`, `AuthAndAccessControlGuide.mdc`

## 1. 系統概述與目標

### 1.1 概述

本登入系統是 HorizAI SaaS 平台的統一入口，負責處理所有使用者的身份驗證、帳號生命週期管理以及與租戶的歸屬關係。系統設計旨在提供安全、流暢且具擴展性的使用者登入與註冊體驗。

### 1.2 核心目標

- **安全的身份驗證**: 確保只有合法使用者可以存取系統。
- **自助式註冊與租戶開通**: 允許企業用戶自行註冊並建立新的公司租戶。
- **公司唯一性保證**: 防止同一公司重複註冊，確保資料的準確性與管理效率。
- **靈活的使用者歸屬**: 支援個人帳號獨立存在，並提供清晰的流程引導使用者加入或建立公司。
- **無縫的單點登入體驗 (未來)**: 規劃支援 OAuth (Google, Line) 及企業級 SSO。
- **精細化權限整合**: 與平台的權限控制系統 (CASL) 緊密整合，確保使用者登入後獲得正確的存取權限。

## 2. 系統整體架構

本登入系統是整體 SaaS 平台架構的一部分，主要涉及以下層級與組件：

```
┌─────────────────────────────────────────────────────────────┐
│                    前端層 (Presentation Layer)                │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  登入/註冊頁面   │ │ 公司歸屬設定頁面  │ │  帳號管理介面   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │ (HTTPS, RESTful API)
┌─────────────────────────────────────────────────────────────┐
│                    API 閘道層 (API Gateway Layer)             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   /api/auth/... │ │ /api/users/...  │ │/api/tenants/... │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   業務邏輯層 (Business Logic Layer)           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  AuthService    │ │  UserService    │ │ TenantService   │ │
│  │  (登入,註冊,JWT) │ │  (使用者管理)   │ │ (租戶建立,驗證) │ │
│  │  MfaService     │ │  CaslAbility    │ │ InvitationServ. │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    資料存取層 (Data Access Layer)             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   Prisma ORM    │ │   PostgreSQL DB │ │   Redis Cache   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**核心互動流程**:

1.  **前端請求**: 使用者在前端介面執行登入、註冊等操作。
2.  **API 呼叫**: 前端透過 HTTPS 向後端 API (例如 `/api/auth/login`) 發送請求。
3.  **業務邏輯處理**: NestJS 後端相應的服務 (如 `AuthService`) 處理請求，包括驗證、資料庫操作等。
4.  **資料庫互動**: 透過 Prisma ORM 與 PostgreSQL 資料庫進行資料讀寫。
5.  **回應前端**: 後端將處理結果 (如 JWT Token, 使用者資訊) 回傳給前端。

## 3. 模組或子系統劃分

登入系統主要由以下後端模組構成 (位於 `apps/backend/src/modules/core/auth` 及相關模組)：

### 3.1 `AuthModule` (核心認證模組)

- **`AuthService`**:
  - 處理使用者 Email/密碼登入、註冊。
  - JWT (Access Token, Refresh Token) 的簽發、驗證、刷新。
  - 密碼管理 (雜湊比對、忘記密碼、重設密碼、變更密碼)。
  - 第三方登入 (Google, Line) 邏輯的整合點。
  - 登出處理。
- **`JwtStrategy` / `LocalStrategy`**: Passport.js 策略，用於驗證 JWT 和本地登入憑證。
- **`JwtAuthGuard`**: NestJS Guard，用於保護需要 JWT 驗證的 API 端點。
- **`MfaService`**: (若啟用) 處理多因素認證的邏輯。
- **`AuthController`**: 提供登入、註冊、登出、刷新 Token 等 API 端點。

### 3.2 `UserModule` (使用者管理模組 - 系統層級與租戶層級)

- **`UsersService`**:
  - 管理使用者帳號的 CRUD 操作。
  - 處理使用者基本資料、狀態、Email 驗證。
  - 根據 PRD，需支援**個人帳號獨立存在** (即 `tenantId` 可為 `null`)。
  - 與 `TenantModule` 協作處理使用者與租戶的關聯。
- **`UsersController`**: 提供管理使用者資訊的 API 端點 (如 `/api/auth/me`, `/api/admin/users`)。

### 3.3 `TenantModule` (租戶管理模組)

- **`TenantsService`**:
  - 處理新租戶的建立與資訊驗證。
  - **公司唯一性檢查邏輯** (基於公司名稱+國家/地區、公司 Email 網域等)。
  - 管理租戶狀態 (pending, active, suspended)。
- **`TenantsController`**: 提供租戶相關操作的 API 端點 (如 `/api/tenants`, `/api/admin/tenants`)。

### 3.4 `TenantInvitationsModule` (租戶邀請模組)

- **`TenantInvitationsService`**:
  - 處理邀請使用者加入現有租戶的邏輯。
  - 產生邀請連結/Token，驗證邀請。
  - 管理邀請狀態。
- **`TenantInvitationsController`**: 提供邀請相關的 API 端點。

### 3.5 `CaslModule` (權限管理模組)

- **`CaslAbilityFactory`**: 根據使用者角色和權限定義，產生 CASL `AppAbility` 實例，供後端 API 進行權限檢查。

### 3.6 `MailModule` (郵件服務模組)

- **`MailService`**: 負責發送系統郵件，如 Email 驗證信、密碼重設郵件、邀請郵件等。

## 4. 技術選型

與整體應用程式架構一致：

- **後端**: NestJS, Prisma, PostgreSQL
- **認證機制**: JWT (Access Token + Refresh Token), bcryptjs (密碼雜湊)
- **權限控制**: CASL
- **前端**: Vue 3, Pinia, Vue Router, Shadcn-Vue, Tailwind CSS
- **HTTP 客戶端**: `@horizai/auth` 套件中封裝的 `httpService` (基於 Axios 或 Fetch)

## 5. 通訊協定與 API 設計原則

### 5.1 通訊協定

- 所有前後端通訊均使用 **HTTPS**。
- API 設計遵循 **RESTful** 原則。

### 5.2 核心 API 端點 (摘錄)

- **個人帳號**:
  - `POST /api/auth/register`: 建立個人使用者帳號。
  - `POST /api/auth/login`: Email/密碼登入。
  - `POST /api/auth/logout`: 登出。
  - `GET /api/auth/me`: 獲取當前登入使用者資訊 (包含角色、權限規則、`userType` 及 `tenantId`)。
  - `POST /api/auth/refresh-token`: 刷新 Access Token。
  - `POST /api/auth/forgot-password`: 請求密碼重設。
  - `POST /api/auth/reset-password`: 使用 Token 重設密碼。
  - `POST /api/auth/change-password`: 修改已登入使用者的密碼。
  - `POST /api/auth/send-verification-email`: 重發 Email 驗證信。
  - `GET /api/auth/verify-email?token={token}`: 驗證 Email。
- **公司歸屬與租戶**:
  - `GET /api/tenants/search?query={query}`: 搜尋現有公司。
  - `POST /api/tenants`: 建立新的公司租戶 (使用者將成為此租戶的 Admin)。
  - `POST /api/tenants/:tenantId/invitations`: 申請加入現有公司租戶 (發送邀請給 Tenant Admin)。
  - `GET /api/tenant-invitations`: (Tenant Admin) 獲取待審核的加入申請。
  - `POST /api/tenant-invitations/:inviteId/accept`: (Tenant Admin) 同意加入申請。
  - `POST /api/tenant-invitations/:inviteId/reject`: (Tenant Admin) 拒絕加入申請。
  - `POST /api/users/me/set-tenant`: 使用者設定或變更其所屬租戶 (用於「公司歸屬設定」流程)。
- **OAuth (未來擴展)**:
  - `GET /api/auth/google`: 重定向至 Google 登入。
  - `GET /api/auth/google/callback`: Google 回呼處理。
  - `GET /api/auth/line`: 重定向至 Line 登入。
  - `GET /api/auth/line/callback`: Line 回呼處理。

### 5.3 API 回應結構

標準化 JSON 回應，包含 `success` 狀態、`data`、`message` 及 `errors`。
Access Token 通常在登入成功後的回應 Body 中返回，Refresh Token 透過 HttpOnly Cookie 設定。

## 6. 資料流與控制流程

### 6.1 使用者註冊流程 (階段一：建立個人帳號)

1.  **前端**: 使用者在「建立帳號」頁面輸入姓名、Email、密碼。
2.  **API**: 前端呼叫 `POST /api/auth/register`。
3.  **後端 (`AuthService`)**:
    a. 驗證輸入資料 (DTO)。
    b. 檢查 Email 是否已存在於 `User` 表。
    c. 若不存在，雜湊密碼。
    d. 在 `User` 表中建立新記錄，`tenantId` 設為 `NULL`，狀態設為 `pending_email_verification`。
    e. 產生 Email 驗證 Token。
    f. 透過 `MailService` 發送驗證郵件。
    g. 回應成功訊息。
4.  **前端**: 提示使用者檢查信箱。
5.  **使用者**: 點擊郵件中的驗證連結 (例如 `GET /api/auth/verify-email?token={token}`)。
6.  **後端 (`AuthService`)**:
    a. 驗證 Token 的有效性與時效。
    b. 若有效，更新 `User` 記錄狀態為 `active`。
    c. 回應驗證成功頁面/訊息。

### 6.2 使用者登入流程

1.  **前端**: 使用者在登入頁面輸入 Email、密碼。
2.  **API**: 前端呼叫 `POST /api/auth/login`。
3.  **後端 (`AuthService`)**:
    a. 驗證輸入資料 (DTO)。
    b. 查詢 `User` 表，比對 Email 和雜湊後的密碼。
    c. 檢查使用者狀態是否為 `active`。
    d. (若啟用 MFA) 執行 MFA 驗證流程。
    e. 若驗證成功：
    i. 產生 JWT Access Token (包含 `userId`, `email`, `roles`, `tenantId`, `userType`)。
    ii. 產生 Refresh Token，雜湊後存入 `RefreshToken` 表，原始 Refresh Token 透過 HttpOnly Cookie 回傳。
    iii.更新 `lastLoginAt`, `lastLoginIp`。
    iv. 記錄登入日誌 (`LoginLog`)。
    v. 回傳 Access Token 及簡化版使用者資訊 (不含密碼)。
    f. 若驗證失敗，回傳錯誤訊息。
4.  **前端 (`auth.store.ts`)**:
    a. 儲存 Access Token (例如在記憶體或 localStorage)。
    b. 儲存使用者資訊，更新 `isAuthenticated` 狀態。
    c. 呼叫 `/api/auth/me` 獲取完整使用者資訊及 CASL 權限規則，並更新 `AppAbility`。
    d. 根據 `user.tenantId` 和 `userType` 決定後續導向：- 若 `tenantId` 為空，導向「公司歸屬設定」頁面。- 若 `tenantId` 存在，導向對應租戶的儀表板。

### 6.3 公司歸屬設定流程 (階段二)

1.  **前端**: 使用者在「公司歸屬設定」頁面選擇操作路徑。
    - **路徑 A: 加入現有公司**
      1.  輸入公司名稱/網域進行搜尋 (`GET /api/tenants/search`)。
      2.  選擇公司，提交加入申請 (`POST /api/tenants/:tenantId/invitations`)。
      3.  系統通知該公司 Tenant Admin。
    - **路徑 B: 建立新公司**
      1.  填寫公司表單 (`POST /api/tenants`)。
      2.  後端進行唯一性檢查 (公司名稱+國家，公司 Email 網域)。
      3.  若通過，建立 `Tenant` 記錄，並將當前使用者設為 `TENANT_ADMIN` (更新 `User.tenantId` 及 `UserRoleMapping`)。
    - **路徑 C: 稍後設定**
      1.  使用者跳過，`User.tenantId` 保持 `NULL`。
      2.  系統功能受限，每次登入後提示設定。

### 6.4 員工離職流程 (簡述)

1.  **觸發**: 員工主動離開或 Tenant Admin 移除。
2.  **後端核心處理**:
    a. 將該 `User` 的 `tenantId` 設為 `NULL`。
    b. 移除其在該租戶內的所有 `UserRoleMapping`。
    c. (可選) 清理與該租戶相關的個人配置。
3.  **個人帳號保留**: 使用者核心帳號不受影響，下次登入重新進入「公司歸屬設定」流程。
4.  **資料處理**: 離職員工在租戶內產生的業務資料預設保留，提供轉移機制給 Tenant Admin。

## 7. 安全性考量

- **密碼安全**:
  - 使用 `bcryptjs` 進行密碼雜湊，加鹽處理。
  - 實施密碼複雜性策略 (長度、字元類型)。
  - 密碼重設流程使用安全的 Token 機制，Token 具時效性且一次性使用。
- **Token 安全**:
  - JWT Access Token: 短效期，透過 HTTPS 傳輸。Payload 中不含過度敏感資訊。使用強簽名算法 (RS256 優先)。
  - Refresh Token: 長效期，儲存在 HttpOnly, Secure, SameSite=Strict 的 Cookie 中，防止 XSS 竊取。後端儲存其雜湊值。實施 Refresh Token Rotation 機制。
- **傳輸安全**: 全站強制 HTTPS。
- **輸入驗證**: 所有 API 輸入使用 NestJS DTO (Data Transfer Objects) 配合 `class-validator` 和 `class-transformer` 進行嚴格驗證。
- **速率限制 (Rate Limiting)**: 對登入、註冊、密碼重設等敏感 API 端點實施速率限制，防止暴力破解和 DoS 攻擊 (使用 `@nestjs/throttler`)。
- **CSRF 防護**: 由於 Refresh Token 使用 HttpOnly Cookie，需確保有 CSRF 防護機制 (例如 Double Submit Cookie 或 Synchronizer Token Pattern，或依賴 NestJS 內建防護)。
- **Session 管理**: 雖然主要依賴 Token，但 Refresh Token 的管理某種程度上類似 Session。登出時應明確使 Refresh Token 失效。
- **多因素認證 (MFA)**: (建議規劃) 支援 TOTP 等 MFA 方式，增強帳戶安全。
- **日誌與監控**:
  - 詳細記錄所有成功的登入、失敗的登入嘗試、密碼重設請求、帳號鎖定等安全相關事件。
  - 建立告警機制，偵測異常登入行為。
- **OWASP Top 10**: 針對常見 Web 應用安全風險進行防護。

## 8. 資料模型 (相關核心模型摘錄)

(參考 `LoginSystemPRD.md` 第 11 節的資料表設計，以及 `schema.prisma` 的實際內容)

### `User` (或拆分後的 `SystemUser`, `TenantUser`)

- **核心欄位**: `id`, `email` (unique), `password` (hashed), `name`, `status` (`pending_email_verification`, `active`, `suspended`), `tenantId` (**nullable**), `role` (or `userRoles` mapping), `lastLoginAt`, `lastLoginIp`, `mfaEnabled`, `mfaSecret`.
- **`userType`**: (若拆分 User 模型) 在 API 回應中明確指示是 `'system'` 或 `'tenant'` 使用者。

### `Tenant`

- **核心欄位**: `id`, `name` (公司名稱), `domain` (公司 Email 網域, unique), `status` (`pending`, `active`, `suspended`).

### `Role` & `UserRoleMapping`

- 用於定義使用者在系統或租戶內的角色。`Role.scope` (`SYSTEM`, `TENANT`, `WORKSPACE`) 至關重要。

### `RefreshToken`

- **核心欄位**: `id`, `user_id`, `token` (hashed), `is_valid`, `expires_at`.

### `TenantInvite`

- **核心欄位**: `id`, `email`, `tenantId`, `roleId` (邀請時預設角色), `status` (`pending`, `accepted`, `expired`), `token` (unique, for verification).

### `LoginLog`

- **核心欄位**: `id`, `userId`, `ipAddress`, `userAgent`, `loginAt`, `status` (`success`, `failure`), `failureReason`.

## 9. 前端整合

### 9.1 狀態管理 (`@horizai/auth` 中的 `auth.store.ts`)

- **核心 State**: `user: CurrentUser | null` (包含 `id`, `email`, `name`, `tenantId`, `userType`, `roles`, `abilityRules`), `accessToken: string | null`, `isAuthenticated: boolean`, `loading: boolean`.
- **核心 Actions**:
  - `login()`: 呼叫登入 API，處理 Token 和使用者狀態。
  - `logout()`: 清除本地狀態，呼叫登出 API。
  - `register()`: 呼叫註冊 API。
  - `fetchCurrentUser()`: 呼叫 `/api/auth/me` 初始化或刷新使用者狀態和權限。
  - `initAuth()`: 應用程式啟動時調用，嘗試自動登入或初始化狀態。
  - `updateAbility(rules)`: 使用後端傳來的規則更新前端 CASL `AppAbility`。

### 9.2 HTTP 客戶端 (`httpService`)

- 由 `@horizai/auth` 提供，自動處理 Access Token 的附加。
- 實現請求攔截器，用於 Access Token 過期時自動使用 Refresh Token 刷新。
- 實現回應攔截器，處理 401 (Unauthorized) 等錯誤，例如自動登出或導向登入頁。

### 9.3 路由保護 (`apps/frontend/src/router/guards/permission.guard.ts`)

- **`beforeEach` 全域導航守衛**:
  - 檢查路由 `meta` 資訊 (`requiresAuth`, `permission` 等)。
  - 若路由需要認證但使用者未登入 (`!isAuthenticated`)，重定向到登入頁。
  - 登入後，若 `user.tenantId` 為空，強制導向「公司歸屬設定」頁面。
  - 根據 `user.roles` 和 `AppAbility` 進行細粒度的權限檢查。

### 9.4 UI 組件

- **登入/註冊表單**: (`apps/frontend/src/views/auth/`)
- **公司歸屬設定頁面**: 新建，用於引導使用者選擇加入或建立公司。
- **個人資料/帳號設定頁面**: 允許使用者修改密碼、管理 MFA (若啟用)、查看登入歷史等。

## 10. 部署與環境規劃

與整體應用程式架構一致，區分開發、測試、生產環境。後端 API 服務應為無狀態，方便水平擴展。

## 11. 其他建議

- **系統日誌**: 登入系統的所有關鍵操作 (登入、註冊、密碼更改、角色變更、邀請接受/拒絕) 都應記錄到 `SystemLog` 表中，以供稽核和追蹤。
- **國際化 (i18n)**: 登入/註冊相關的頁面和提示訊息應支援多語言 (雖然初期可能僅繁體中文)。
- **可測試性**: 為 `AuthService` 和相關邏輯編寫全面的單元測試和整合測試。

---

這份文件涵蓋了登入系統的主要架構設計。您可以檢視一下，看看是否有需要調整或補充的地方。
