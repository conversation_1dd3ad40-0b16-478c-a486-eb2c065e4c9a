/**
 * Debug 配置文件
 * 用於控制應用程序的日誌輸出級別
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'none';

export interface DebugConfig {
  logLevel: LogLevel;
  enableRouterLogs: boolean;
  enableAuthLogs: boolean;
  enablePermissionLogs: boolean;
  enableApiLogs: boolean;
  enableComponentLogs: boolean;
}

// 從環境變數獲取配置
const getEnvLogLevel = (): LogLevel => {
  const envLevel = (import.meta as any).env.VITE_LOG_LEVEL;
  const validLevels: LogLevel[] = ['debug', 'info', 'warn', 'error', 'none'];
  
  if (envLevel && validLevels.includes(envLevel as LogLevel)) {
    return envLevel as LogLevel;
  }
  
  // 根據MODE決定默認級別
  const mode = (import.meta as any).env.MODE;
  switch (mode) {
    case 'development':
      return 'debug';
    case 'staging':
      return 'info';
    case 'production':
      return 'error';
    default:
      return 'warn';
  }
};

const getBooleanEnv = (key: string, defaultValue: boolean): boolean => {
  const value = (import.meta as any).env[key];
  if (value === 'true') return true;
  if (value === 'false') return false;
  return defaultValue;
};

// 默認配置
export const debugConfig: DebugConfig = {
  logLevel: getEnvLogLevel(),
  enableRouterLogs: getBooleanEnv('VITE_ENABLE_ROUTER_LOGS', (import.meta as any).env.MODE === 'development'),
  enableAuthLogs: getBooleanEnv('VITE_ENABLE_AUTH_LOGS', (import.meta as any).env.MODE === 'development'),
  enablePermissionLogs: getBooleanEnv('VITE_ENABLE_PERMISSION_LOGS', false),
  enableApiLogs: getBooleanEnv('VITE_ENABLE_API_LOGS', (import.meta as any).env.MODE !== 'production'),
  enableComponentLogs: getBooleanEnv('VITE_ENABLE_COMPONENT_LOGS', false),
};

/**
 * 統一的日誌工具
 */
export class Logger {
  private category: string;
  
  constructor(category: string) {
    this.category = category;
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error', 'none'];
    const currentLevelIndex = levels.indexOf(debugConfig.logLevel);
    const requestedLevelIndex = levels.indexOf(level);
    
    return requestedLevelIndex >= currentLevelIndex && debugConfig.logLevel !== 'none';
  }

  private formatMessage(level: string, message: string, ...args: any[]): void {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[${timestamp}] [${this.category}] ${level.toUpperCase()}:`;
    
    if (args.length > 0) {
      console.log(prefix, message, ...args);
    } else {
      console.log(prefix, message);
    }
  }

  debug(message: string, ...args: any[]): void {
    if (this.shouldLog('debug')) {
      this.formatMessage('debug', message, ...args);
    }
  }

  info(message: string, ...args: any[]): void {
    if (this.shouldLog('info')) {
      this.formatMessage('info', message, ...args);
    }
  }

  warn(message: string, ...args: any[]): void {
    if (this.shouldLog('warn')) {
      this.formatMessage('warn', message, ...args);
    }
  }

  error(message: string, ...args: any[]): void {
    if (this.shouldLog('error')) {
      this.formatMessage('error', message, ...args);
    }
  }
}

/**
 * 建立專門的日誌記錄器
 */
export const createLogger = (category: string): Logger => new Logger(category);

/**
 * 預定義的日誌記錄器
 */
export const loggers = {
  auth: createLogger('Auth'),
  router: createLogger('Router'),
  permission: createLogger('Permission'),
  api: createLogger('API'),
  component: createLogger('Component'),
  main: createLogger('Main'),
} as const;

// 在開發環境下輸出當前配置
if ((import.meta as any).env.MODE === 'development') {
  console.log('[Debug Config] 當前日誌配置:', {
    mode: (import.meta as any).env.MODE,
    logLevel: debugConfig.logLevel,
    enableRouterLogs: debugConfig.enableRouterLogs,
    enableAuthLogs: debugConfig.enableAuthLogs,
    enablePermissionLogs: debugConfig.enablePermissionLogs,
    enableApiLogs: debugConfig.enableApiLogs,
    enableComponentLogs: debugConfig.enableComponentLogs,
  });
} 