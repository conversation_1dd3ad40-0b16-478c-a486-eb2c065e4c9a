import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON>oolsController } from '../ai-tools.controller';
import { AiToolsService } from '../ai-tools.service';
import { PrismaService } from '../../../../../core/prisma/prisma.service';
import { CaslAbilityFactory } from '../../../../../../casl/ability/casl-ability.factory';
import { PoliciesGuard } from '../../../../../../casl/guards/permission.guard';
import { PermissionCheckerService } from '../../../../../../casl/services/permission-checker.service';
import { Reflector } from '@nestjs/core';
import { JwtAuthGuard } from '../../../../../core/auth/guards/auth.guard';
import { CreateAiToolDto, UpdateAiToolDto, AiToolQueryDto } from '../dto/ai-tools.dto';
import { AiToolScope } from '@prisma/client';
import { Role } from '../../../../../../common/enums/role.enum';

// Mock PrismaService
const mockPrismaService = {
  ai_tools: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
};

describe('AiToolsController', () => {
  let controller: AiToolsController;
  let service: AiToolsService;
  let prismaService: PrismaService;
  let caslAbilityFactory: CaslAbilityFactory;
  let permissionCheckerService: PermissionCheckerService;

  const mockUser = {
    id: 'user-123',
    sub: 'user-123',
    email: '<EMAIL>',
    user_type: 'system' as const,
    tenant_id: null,
    role: Role.SUPER_ADMIN,
  };

  const mockAiTool = {
    id: 'tool-123',
    key: 'test_tool',
    name: 'Test Tool',
    description: 'Test tool description',
    input_schema: {
      type: 'object',
      properties: {
        input: { type: 'string' },
      },
      required: ['input'],
    },
    scope: AiToolScope.SYSTEM,
    is_enabled: true,
    tenant_id: null,
    workspace_id: null,
    created_at: new Date(),
    updated_at: new Date(),
    created_by: 'user-123',
    updated_by: null,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AiToolsController],
      providers: [
        {
          provide: AiToolsService,
          useValue: {
            findAll: jest.fn().mockResolvedValue([mockAiTool]),
            findOne: jest.fn().mockResolvedValue(mockAiTool),
            create: jest.fn().mockResolvedValue(mockAiTool),
            update: jest.fn().mockResolvedValue(mockAiTool),
            remove: jest.fn().mockResolvedValue(mockAiTool),
          },
        },
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: CaslAbilityFactory,
          useValue: {
            createForUser: jest.fn().mockResolvedValue({
              can: jest.fn().mockReturnValue(true),
              rules: [],
            }),
          },
        },
        {
          provide: PermissionCheckerService,
          useValue: {
            checkPermission: jest.fn().mockResolvedValue({ granted: true }),
            canManage: jest.fn().mockResolvedValue(true),
            canRead: jest.fn().mockResolvedValue(true),
            canCreate: jest.fn().mockResolvedValue(true),
            canUpdate: jest.fn().mockResolvedValue(true),
            canDelete: jest.fn().mockResolvedValue(true),
          },
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn().mockReturnValue([]),
            getAllAndOverride: jest.fn().mockReturnValue([]),
            getAllAndMerge: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: PoliciesGuard,
          useValue: {
            canActivate: jest.fn().mockResolvedValue(true),
          },
        },
        {
          provide: JwtAuthGuard,
          useValue: {
            canActivate: jest.fn().mockResolvedValue(true),
          },
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .overrideGuard(PoliciesGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    controller = module.get<AiToolsController>(AiToolsController);
    service = module.get<AiToolsService>(AiToolsService);
    prismaService = module.get<PrismaService>(PrismaService);
    caslAbilityFactory = module.get<CaslAbilityFactory>(CaslAbilityFactory);
    permissionCheckerService = module.get<PermissionCheckerService>(PermissionCheckerService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
    expect(prismaService).toBeDefined();
    expect(caslAbilityFactory).toBeDefined();
    expect(permissionCheckerService).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of AI tools', async () => {
      const query: AiToolQueryDto = {};
      const result = await controller.findAll(query);
      expect(result).toEqual([mockAiTool]);
      expect(service.findAll).toHaveBeenCalledWith(query);
    });
  });

  describe('findOne', () => {
    it('should return a single AI tool', async () => {
      const result = await controller.findOne('tool-123');
      expect(result).toEqual(mockAiTool);
      expect(service.findOne).toHaveBeenCalledWith('tool-123');
    });
  });

  describe('create', () => {
    it('should create a new AI tool', async () => {
      const createDto: CreateAiToolDto = {
        key: 'test_tool',
        name: 'Test Tool',
        description: 'Test tool description',
        inputSchema: {
          type: 'object',
          properties: {
            input: { type: 'string' },
          },
          required: ['input'],
        },
        scope: AiToolScope.SYSTEM,
        isEnabled: true,
      };

      const result = await controller.create(createDto, mockUser);
      expect(result).toEqual(mockAiTool);
      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.id);
    });
  });

  describe('update', () => {
    it('should update an AI tool', async () => {
      const updateDto: UpdateAiToolDto = {
        name: 'Updated Tool Name',
        description: 'Updated description',
        isEnabled: false,
      };

      const result = await controller.update('tool-123', updateDto, mockUser);
      expect(result).toEqual(mockAiTool);
      expect(service.update).toHaveBeenCalledWith('tool-123', {
        ...updateDto,
        updatedBy: mockUser.id,
      });
    });
  });

  describe('remove', () => {
    it('should delete an AI tool', async () => {
      const result = await controller.remove('tool-123');
      expect(result).toEqual(mockAiTool);
      expect(service.remove).toHaveBeenCalledWith('tool-123');
    });
  });
});
