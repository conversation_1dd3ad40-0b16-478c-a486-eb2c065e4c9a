/**
 * 通用設定項目接口
 */
export interface SettingItem {
  key: string;
  value: any;
}

/**
 * 設定項目查詢參數
 */
export interface SettingQueryParams {
  category: string;
  key?: string;
}

/**
 * 設定項目更新參數
 */
export interface SettingUpdateParams {
  category: string;
  key: string;
  value: any;
  user_id?: string;
}

/**
 * 設定項目批量更新參數
 */
export interface CategoryUpdateParams {
  category: string;
  data: Record<string, any>;
  user_id?: string;
}

/**
 * 通用回應物件，包含設定資料
 */
export interface SettingsResponse<T = Record<string, any>> {
  success: boolean;
  data: T;
  message?: string;
}
