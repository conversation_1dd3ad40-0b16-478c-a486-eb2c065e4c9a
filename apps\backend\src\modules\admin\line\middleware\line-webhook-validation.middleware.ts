import { Injectable, NestMiddleware, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as rawBody from 'raw-body';
import { validateSignature } from '@line/bot-sdk';
import { LineBotService } from '../services/line-bot.service';

@Injectable()
export class LineWebhookValidationMiddleware implements NestMiddleware {
  constructor(private readonly lineBotService: LineBotService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const botId = req.params.bot_id;
    if (!botId) {
      throw new HttpException('Bot ID is missing in path', HttpStatus.BAD_REQUEST);
    }

    const signature = req.headers['x-line-signature'] as string;
    if (!signature) {
      throw new HttpException('Missing X-Line-Signature header', HttpStatus.BAD_REQUEST);
    }

    let decryptedChannelSecret: string | null;
    try {
      decryptedChannelSecret = await this.lineBotService.getDecryptedChannelSecret(botId);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      // Log the original error for debugging: console.error('Error fetching secret:', error);
      throw new HttpException('Failed to retrieve bot secret', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    if (!decryptedChannelSecret) {
      // This case implies bot not found by service or secret explicitly null after attempt
      throw new HttpException('Bot configuration or secret not found.', HttpStatus.NOT_FOUND);
    }

    let bodyBuffer: Buffer;
    try {
      bodyBuffer = await rawBody.default(req, {
        length: req.headers['content-length'],
        limit: '2mb', // LINE Platform webhook events can be up to 2MB
        encoding: null,
      });
    } catch (error: any) {
      throw new HttpException(`Failed to read raw body: ${error.message}`, HttpStatus.BAD_REQUEST);
    }

    const isValid = validateSignature(bodyBuffer, decryptedChannelSecret, signature);
    if (!isValid) {
      throw new HttpException('Invalid signature', HttpStatus.FORBIDDEN);
    }

    try {
      req.body = JSON.parse(bodyBuffer.toString('utf8'));
    } catch (error) {
      throw new HttpException('Invalid JSON body', HttpStatus.BAD_REQUEST);
    }

    next();
  }
}
