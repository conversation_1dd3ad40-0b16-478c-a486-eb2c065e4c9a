import { Modu<PERSON> } from '@nestjs/common';
import { RolesService } from './services/roles.service';
import { RolesController } from './controllers/roles.controller';
import { PrismaModule } from '../../core/prisma/prisma.module';
import { CaslModule } from '../../../casl/casl.module';
import { RoleAssignmentService } from './services/role-assignment.service';
import { RoleHierarchyService } from './services/role-hierarchy.service';
import { UserRoleService } from './services/user-role.service';
import { RoleAssignmentController } from './controllers/role-assignment.controller';
@Module({
  imports: [PrismaModule, CaslModule],
  controllers: [RolesController, RoleAssignmentController],
  providers: [RolesService, RoleAssignmentService, RoleHierarchyService, UserRoleService],
  exports: [RolesService, RoleAssignmentService, RoleHierarchyService, UserRoleService],
})
export class RolesModule {}
