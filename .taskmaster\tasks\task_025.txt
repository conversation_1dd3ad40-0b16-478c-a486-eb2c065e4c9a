# Task ID: 25
# Title: Frontend - Internal Agent Testing Page
# Status: pending
# Dependencies: 12, 21, 22, 23, 24
# Priority: medium
# Description: Develop a minimal frontend page (Vue 3 + TypeScript + Pinia + Shadcn-Vue) for internal testing. It should allow sending input to the `AgentRunnerService` and displaying the agent's response and logs.
# Details:
Vue 3 page with input field for query, dropdowns for `tenantId` and `agentConfigId`/`modelId`. Submit button calls backend API (e.g., `POST /api/agent/invoke`) triggering `AgentRunnerService.runAgent`. Display agent response and logs. Use Pinia for state, `axios`/`fetch` for API calls.

# Test Strategy:
Manually test page: send queries, verify requests/responses, check log display. Test with different tenants/agent configs.
