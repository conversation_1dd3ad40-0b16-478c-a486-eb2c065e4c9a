import { Test, TestingModule } from '@nestjs/testing';
import { CreateTaskToolFactory } from './create-task-tool.factory';
import { TasksService } from '../../../workspace/tasks/tasks.service';
import { CreateTaskTool } from './create-task.tool';

describe('CreateTaskToolFactory', () => {
  let factory: CreateTaskToolFactory;
  let tasksService: jest.Mocked<TasksService>;

  const mockTasksService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateTaskToolFactory,
        {
          provide: TasksService,
          useValue: mockTasksService,
        },
      ],
    }).compile();

    factory = module.get<CreateTaskToolFactory>(CreateTaskToolFactory);
    tasksService = module.get<TasksService>(TasksService) as jest.Mocked<TasksService>;
  });

  describe('create', () => {
    it('should create a CreateTaskTool instance with correct parameters', () => {
      const tenantId = 'test-tenant-id';
      const userId = 'test-user-id';
      const mockAbility = { can: jest.fn().mockReturnValue(true) } as any;

      const tool = factory.create(tenantId, userId, mockAbility);

      expect(tool).toBeInstanceOf(CreateTaskTool);
      expect(tool.name).toBe('CreateTaskTool');
      expect(tool.description).toContain('建立新任務的工具');
    });

    it('should throw error when tenantId is not provided', () => {
      const userId = 'test-user-id';
      const mockAbility = { can: jest.fn().mockReturnValue(true) } as any;

      expect(() => {
        factory.create('', userId, mockAbility);
      }).toThrow('TenantId and UserId are required for CreateTaskTool');
    });

    it('should throw error when userId is not provided', () => {
      const tenantId = 'test-tenant-id';
      const mockAbility = { can: jest.fn().mockReturnValue(true) } as any;

      expect(() => {
        factory.create(tenantId, '', mockAbility);
      }).toThrow('TenantId and UserId are required for CreateTaskTool');
    });

    it('should throw error when both tenantId and userId are not provided', () => {
      const mockAbility = { can: jest.fn().mockReturnValue(true) } as any;

      expect(() => {
        factory.create('', '', mockAbility);
      }).toThrow('TenantId and UserId are required for CreateTaskTool');
    });

    it('should throw error when ability is not provided', () => {
      const tenantId = 'test-tenant-id';
      const userId = 'test-user-id';

      expect(() => {
        factory.create(tenantId, userId, null as any);
      }).toThrow('Ability is required for CreateTaskTool permission checking');
    });

    it('should create multiple independent tool instances', () => {
      const tenantId1 = 'tenant-1';
      const userId1 = 'user-1';
      const tenantId2 = 'tenant-2';
      const userId2 = 'user-2';
      const mockAbility = { can: jest.fn().mockReturnValue(true) } as any;

      const tool1 = factory.create(tenantId1, userId1, mockAbility);
      const tool2 = factory.create(tenantId2, userId2, mockAbility);

      expect(tool1).toBeInstanceOf(CreateTaskTool);
      expect(tool2).toBeInstanceOf(CreateTaskTool);
      expect(tool1).not.toBe(tool2); // 應該是不同的實例
    });
  });

  describe('dependency injection', () => {
    it('should inject TasksService correctly', () => {
      expect(factory).toBeDefined();
      expect(tasksService).toBeDefined();
    });
  });
});
