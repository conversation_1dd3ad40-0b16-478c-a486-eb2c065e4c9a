import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ToolRegistry, ToolImplementation } from './tool-registry.interface';

/**
 * 工具註冊表服務
 *
 * 負責管理所有工具實作的註冊、查詢和生命週期管理。
 * 採用單例模式，確保全應用只有一個工具註冊表實例。
 */
@Injectable()
export class ToolRegistryService implements ToolRegistry, OnModuleInit {
  private readonly logger = new Logger(ToolRegistryService.name);
  private readonly implementations = new Map<string, ToolImplementation>();

  async onModuleInit() {
    this.logger.log('工具註冊表服務初始化完成');
  }

  /**
   * 註冊工具實作
   */
  register(implementation: ToolImplementation): void {
    const key = implementation.key;

    if (this.implementations.has(key)) {
      this.logger.warn(`工具 "${key}" 已存在，將被覆蓋`);
    }

    this.implementations.set(key, implementation);
    this.logger.log(`工具實作已註冊: ${key} (${implementation.name})`);
  }

  /**
   * 取消註冊工具
   */
  unregister(key: string): void {
    if (this.implementations.has(key)) {
      this.implementations.delete(key);
      this.logger.log(`工具實作已取消註冊: ${key}`);
    } else {
      this.logger.warn(`嘗試取消註冊不存在的工具: ${key}`);
    }
  }

  /**
   * 取得工具實作
   */
  get(key: string): ToolImplementation | undefined {
    return this.implementations.get(key);
  }

  /**
   * 取得所有已註冊的工具
   */
  getAll(): Map<string, ToolImplementation> {
    return new Map(this.implementations);
  }

  /**
   * 檢查工具是否已註冊
   */
  has(key: string): boolean {
    return this.implementations.has(key);
  }

  /**
   * 取得已註冊工具的統計資訊
   */
  getStats(): {
    totalTools: number;
    registeredTools: string[];
  } {
    return {
      totalTools: this.implementations.size,
      registeredTools: Array.from(this.implementations.keys()),
    };
  }

  /**
   * 清空所有註冊的工具（主要用於測試）
   */
  clear(): void {
    const count = this.implementations.size;
    this.implementations.clear();
    this.logger.log(`已清空 ${count} 個工具實作`);
  }
}
