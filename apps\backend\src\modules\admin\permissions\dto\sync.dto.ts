import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsArray } from 'class-validator';

export class SyncPermissionsDto {
  @ApiProperty({
    description: '是否為預覽模式（不實際執行同步）',
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  dry_run?: boolean = false;

  @ApiProperty({
    description: '是否強制覆蓋現有權限屬性',
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  force?: boolean = false;

  @ApiProperty({
    description: '是否禁用快取',
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  no_cache?: boolean = false;
}

export class ScanPermissionsDto {
  @ApiProperty({
    description: '是否禁用快取',
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  no_cache?: boolean = false;

  @ApiProperty({
    description: '指定掃描的檔案路徑（可選）',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  paths?: string[];
}

export class SyncResultDto {
  @ApiProperty({ description: '同步是否成功' })
  success: boolean;

  @ApiProperty({ description: '總權限數量' })
  total: number;

  @ApiProperty({ description: '新增權限數量' })
  created: number;

  @ApiProperty({ description: '更新權限數量' })
  updated: number;

  @ApiProperty({ description: '標記為廢棄的權限數量' })
  deprecated: number;

  @ApiProperty({ description: '錯誤數量' })
  errors: number;

  @ApiProperty({ description: '同步時間戳' })
  timestamp: string;

  @ApiProperty({ description: '變更詳情', type: 'array' })
  changes?: any[];

  @ApiProperty({ description: '錯誤詳情', type: 'array' })
  error_details?: any[];
}

export class ScanResultDto {
  @ApiProperty({ description: '掃描是否成功' })
  success: boolean;

  @ApiProperty({ description: '掃描的檔案數量' })
  files_scanned: number;

  @ApiProperty({ description: '發現的權限數量' })
  permissions_found: number;

  @ApiProperty({ description: '硬編碼權限警告數量' })
  hardcoded_warnings: number;

  @ApiProperty({ description: '掃描時間戳' })
  timestamp: string;

  @ApiProperty({ description: '發現的權限列表', type: 'array' })
  permissions?: any[];

  @ApiProperty({ description: '硬編碼權限警告', type: 'array' })
  warnings?: any[];
}

export class SyncStatusDto {
  @ApiProperty({ description: '最後同步時間' })
  last_sync_time?: string;

  @ApiProperty({ description: '是否需要同步' })
  needs_sync: boolean;

  @ApiProperty({ description: '待同步的權限數量' })
  pending_changes: number;

  @ApiProperty({ description: '系統權限總數' })
  total_permissions: number;

  @ApiProperty({ description: '程式碼中的權限數量' })
  code_permissions: number;

  @ApiProperty({ description: '資料庫中的權限數量' })
  db_permissions: number;
}
