import { z } from 'zod';
import { MessageCenterService } from '@/modules/workspace/message-center/services/message-center.service';

// Zod schema for marking notifications as read
const markNotificationReadSchema = z.object({
  notification_id: z.string().min(1).describe('The ID of the notification to mark as read'),
  recipient_id: z.string().optional().describe('The ID of the user (for additional verification)'),
});

/**
 * Creates a MarkNotificationReadTool using a simple function-based approach
 * @param messageCenterService - The message center service instance
 * @param tenantId - The tenant ID for isolation
 * @param currentUserId - The current user ID for default recipient
 * @returns A simple tool object compatible with LangChain
 */
export function createMarkNotificationReadTool(
  messageCenterService: MessageCenterService,
  tenantId: string,
  currentUserId: string,
) {
  return {
    name: 'mark_notification_read',
    description: `Mark a specific notification as read.

This tool allows the AI agent to mark notifications as read in the user's notification center.

Required fields:
- notification_id: The ID of the notification to mark as read

Optional fields:
- recipient_id: The ID of the user (for additional verification, defaults to current user)`,
    schema: markNotificationReadSchema,
    call: async (input: any) => {
      try {
        const recipientId = input.recipient_id || currentUserId;
        
        await messageCenterService.markNotificationAsRead(
          input.notification_id,
          tenantId,
          recipientId,
        );

        return `Successfully marked notification ${input.notification_id} as read for user ${recipientId}.`;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        throw new Error(`Failed to mark notification as read: ${errorMessage}`);
      }
    },
  };
}

// Export the schema for use in other tools or tests
export { markNotificationReadSchema }; 