import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/modules/core/prisma/prisma.service';
import { Actions, Subjects } from '@horizai/permissions';
import { SystemUserRole, TenantUserStatus } from '@prisma/client';
import cuid from 'cuid';
import * as bcrypt from 'bcrypt';

describe('RBAC (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let adminToken: string;
  let regularUserToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    prisma = app.get(PrismaService);

    // 1. Clean up database
    await prisma.role_permissions.deleteMany({});
    await prisma.system_user_roles.deleteMany({});
    await prisma.tenant_user_roles.deleteMany({});
    await prisma.system_users.deleteMany({});
    await prisma.tenant_users.deleteMany({});
    await prisma.permissions.deleteMany({});
    await prisma.roles.deleteMany({});
    await prisma.tenants.deleteMany({});

    // 2. Create Permissions
    const permission = await prisma.permissions.create({
      data: {
        id: cuid(),
        action: Actions.READ,
        subject: Subjects.SYSTEM_USER,
      },
    });

    // 3. Create Roles
    const adminRole = await prisma.roles.create({
      data: {
        id: cuid(),
        name: 'E2E_SYSTEM_ADMIN',
        display_name: 'E2E System Admin',
      },
    });
    const userRole = await prisma.roles.create({
      data: {
        id: cuid(),
        name: 'E2E_TENANT_USER',
        display_name: 'E2E Tenant User',
      },
    });

    // 4. Assign Permissions to Roles
    await prisma.role_permissions.create({
      data: {
        id: cuid(),
        role_id: adminRole.id,
        permission_id: permission.id,
      },
    });

    // 5. Create a Tenant for the regular user
    const tenant = await prisma.tenants.create({
      data: {
        id: cuid(),
        name: 'E2E Test Tenant',
        domain: 'e2e-test.com',
        status: 'active',
      },
    });

    // 6. Hash password for secure storage
    const hashedPassword = await bcrypt.hash('password123', 10);

    // 7. Create Users directly in database with hashed passwords
    const adminUser = await prisma.system_users.create({
      data: {
        id: cuid(),
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'E2E Admin',
        role: SystemUserRole.SYSTEM_ADMIN,
      },
    });

    const regularUser = await prisma.tenant_users.create({
      data: {
        id: cuid(),
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'E2E User',
        tenant_id: tenant.id,
        status: TenantUserStatus.ACTIVE,
      },
    });

    // 8. Assign Roles to Users
    await prisma.system_user_roles.create({
      data: {
        id: cuid(),
        system_user_id: adminUser.id,
        role_id: adminRole.id,
      },
    });
    await prisma.tenant_user_roles.create({
      data: {
        id: cuid(),
        tenant_user_id: regularUser.id,
        role_id: userRole.id,
      },
    });

    // 9. Login users to get tokens (using the hashed password for login)
    const adminLoginRes = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' })
      .expect(200);
    adminToken = adminLoginRes.body.access_token;

    const userLoginRes = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' })
      .expect(200);
    regularUserToken = userLoginRes.body.access_token;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should allow an admin with correct permissions to access a protected route', () => {
    return request(app.getHttpServer())
      .get('/admin/system-users')
      .set('Authorization', `Bearer ${adminToken}`)
      .expect(200);
  });

  it('should forbid a user with incorrect permissions from accessing a protected route', () => {
    return request(app.getHttpServer())
      .get('/admin/system-users')
      .set('Authorization', `Bearer ${regularUserToken}`)
      .expect(403);
  });

  it('should reject access to a protected route without a token', () => {
    return request(app.getHttpServer())
      .get('/admin/system-users')
      .expect(401);
  });
}); 