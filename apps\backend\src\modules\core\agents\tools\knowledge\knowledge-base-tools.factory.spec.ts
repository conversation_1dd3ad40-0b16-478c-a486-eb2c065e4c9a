import { 
  KnowledgeBaseToolsFactory, 
  createKnowledgeBaseSearchTool, 
  KnowledgeBaseConfig 
} from './knowledge-base-tools.factory';
import { ToolExecutionContext } from '../core/tool-registry.interface';

// Simple mock services
const mockRAGIngestionService = {
  searchSimilarDocuments: jest.fn(),
} as any;

const mockRAGSecurityService = {
  validateSearchQuery: jest.fn(),
} as any;

describe('KnowledgeBaseToolsFactory', () => {
  let mockContext: ToolExecutionContext;

  beforeEach(() => {
    mockContext = {
      tenantId: '12345678-1234-1234-1234-123456789012',
      userId: '87654321-4321-4321-4321-210987654321',
      workspaceId: 'abcdefgh-1234-1234-1234-abcdefghijkl',
      workspaceRoot: '/test/workspace',
      ability: {} as any,
      user: {} as any,
    };

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('createKnowledgeBaseSearchTool', () => {
    it('should create a knowledge base search tool with correct configuration', () => {
      const tool = createKnowledgeBaseSearchTool(
        mockRAGIngestionService,
        mockRAGSecurityService,
        mockContext
      );

      expect(tool).toBeDefined();
      expect(tool.name).toBe('knowledge_base_search');
      expect(tool.description).toContain('知識庫搜尋工具');
      expect(tool.schema).toBeDefined();
      expect(tool.call).toBeDefined();
    });

    it('should create tool with custom configuration', () => {
      const config: KnowledgeBaseConfig = { 
        maxResults: 5,
        defaultSimilarityThreshold: 0.8,
        enableSecurityLogging: true,
        maxQueryLength: 500
      };
      
      const tool = createKnowledgeBaseSearchTool(
        mockRAGIngestionService,
        mockRAGSecurityService,
        mockContext,
        config
      );

      expect(tool.description).toContain('5');
      expect(tool.description).toContain('0.8');
    });

    it('should validate input schema correctly', () => {
      const tool = createKnowledgeBaseSearchTool(
        mockRAGIngestionService,
        mockRAGSecurityService,
        mockContext
      );

      // Valid input
      const validInput = { query: 'test search', maxResults: 5, similarityThreshold: 0.8 };
      const parsed = tool.schema.parse(validInput);
      expect(parsed.query).toBe('test search');
      expect(parsed.maxResults).toBe(5);
      expect(parsed.similarityThreshold).toBe(0.8);

      // Invalid input - maxResults out of range
      expect(() => {
        tool.schema.parse({ query: 'test', maxResults: 100 });
      }).toThrow();

      // Invalid input - similarityThreshold out of range
      expect(() => {
        tool.schema.parse({ query: 'test', similarityThreshold: 1.5 });
      }).toThrow();
    });
  });

  describe('KnowledgeBaseToolsFactory static methods', () => {
    it('should create knowledge base search tool via static method', () => {
      const tool = KnowledgeBaseToolsFactory.createKnowledgeBaseSearchTool(
        mockRAGIngestionService,
        mockRAGSecurityService,
        mockContext
      );
      
      expect(tool).toBeDefined();
      expect(tool.name).toBe('knowledge_base_search');
    });

    it('should create LangChain tools array', () => {
      const tools = KnowledgeBaseToolsFactory.createLangChainTools(
        mockRAGIngestionService,
        mockRAGSecurityService,
        mockContext
      );
      
      expect(Array.isArray(tools)).toBe(true);
      expect(tools).toHaveLength(1);
      expect(tools[0].name).toBe('knowledge_base_search');
    });

    it('should create tools with custom config', () => {
      const config: KnowledgeBaseConfig = { maxResults: 15 };
      
      const tools = KnowledgeBaseToolsFactory.createLangChainTools(
        mockRAGIngestionService,
        mockRAGSecurityService,
        mockContext,
        config
      );
      
      expect(tools[0].description).toContain('15');
    });
  });
}); 