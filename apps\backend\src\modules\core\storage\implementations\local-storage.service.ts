import { Injectable } from '@nestjs/common';
import { BaseStorageService } from '../base/base-storage.service';
import { StorageConfig, UploadOptions, FileInfo } from '../interfaces/storage.interface';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as zlib from 'zlib';
import { promisify } from 'util';

/**
 * 本地檔案系統儲存服務
 */
@Injectable()
export class LocalStorageService extends BaseStorageService {
  private readonly basePath: string;

  constructor(config: StorageConfig) {
    super(config);
    this.basePath = config.basePath || 'uploads';
    this.ensureBaseDirectory();
  }

  private async ensureBaseDirectory(): Promise<void> {
    try {
      await fs.access(this.basePath);
    } catch {
      await fs.mkdir(this.basePath, { recursive: true });
      this.logger.log(`Created base directory: ${this.basePath}`);
    }
  }

  async uploadFile(
    file: Express.Multer.File | Buffer,
    options: UploadOptions = {},
  ): Promise<string> {
    try {
      this.validateFile(file);

      let buffer: Buffer;
      let originalName: string;

      if (file instanceof Buffer) {
        buffer = file;
        originalName = options.filename || 'file';
      } else {
        buffer = Buffer.from(file.buffer);
        originalName = (file as Express.Multer.File).originalname || 'file';
      }

      const filename = options.filename || this.generateUniqueFilename(originalName);
      const relativePath = this.buildPath(options.path || '', filename);
      const fullPath = path.join(this.basePath, relativePath);

      // 確保目錄存在
      await fs.mkdir(path.dirname(fullPath), { recursive: true });

      // 壓縮檔案（如果啟用）
      if (options.compress) {
        const gzipAsync = promisify(zlib.gzip);
        buffer = await gzipAsync(buffer);
      }

      await fs.writeFile(fullPath, buffer);

      this.logger.log(`File uploaded successfully: ${relativePath}`);
      return relativePath;
    } catch (error) {
      return this.handleError('uploadFile', error);
    }
  }

  async uploadText(content: string, options: UploadOptions): Promise<string> {
    try {
      const buffer = Buffer.from(content, 'utf8');
      return this.uploadFile(buffer, options);
    } catch (error) {
      return this.handleError('uploadText', error);
    }
  }

  async downloadFile(filePath: string): Promise<Buffer> {
    try {
      const fullPath = path.join(this.basePath, filePath);
      const buffer = await fs.readFile(fullPath);

      this.logger.log(`File downloaded successfully: ${filePath}`);
      return buffer;
    } catch (error) {
      return this.handleError('downloadFile', error);
    }
  }

  async deleteFile(filePath: string): Promise<void> {
    try {
      const fullPath = path.join(this.basePath, filePath);
      await fs.unlink(fullPath);

      this.logger.log(`File deleted successfully: ${filePath}`);
    } catch (error) {
      return this.handleError('deleteFile', error);
    }
  }

  async fileExists(filePath: string): Promise<boolean> {
    try {
      const fullPath = path.join(this.basePath, filePath);
      await fs.access(fullPath);
      return true;
    } catch {
      return false;
    }
  }

  async getFileInfo(filePath: string): Promise<FileInfo> {
    try {
      const fullPath = path.join(this.basePath, filePath);
      const stats = await fs.stat(fullPath);

      return {
        path: filePath,
        size: stats.size,
        contentType: this.getContentTypeFromExtension(filePath),
        lastModified: stats.mtime,
        metadata: {
          isDirectory: stats.isDirectory(),
          isFile: stats.isFile(),
          permissions: stats.mode,
        },
      };
    } catch (error) {
      return this.handleError('getFileInfo', error);
    }
  }

  async listFiles(dirPath: string, recursive: boolean = false): Promise<string[]> {
    try {
      const fullPath = path.join(this.basePath, dirPath);
      const files: string[] = [];

      const readDirectory = async (
        currentPath: string,
        relativePath: string = '',
      ): Promise<void> => {
        const entries = await fs.readdir(currentPath, { withFileTypes: true });

        for (const entry of entries) {
          const entryPath = path.join(currentPath, entry.name);
          const relativeEntryPath = path.join(relativePath, entry.name).replace(/\\/g, '/');

          if (entry.isFile()) {
            files.push(relativeEntryPath);
          } else if (entry.isDirectory() && recursive) {
            await readDirectory(entryPath, relativeEntryPath);
          }
        }
      };

      await readDirectory(fullPath, dirPath);
      return files;
    } catch (error) {
      return this.handleError('listFiles', error);
    }
  }

  async getPublicUrl(filePath: string, expiresIn?: number): Promise<string> {
    // 本地儲存不支援公開 URL，返回相對路徑
    return `/uploads/${filePath}`;
  }

  async copyFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      const sourceFullPath = path.join(this.basePath, sourcePath);
      const destFullPath = path.join(this.basePath, destinationPath);

      // 確保目標目錄存在
      await fs.mkdir(path.dirname(destFullPath), { recursive: true });

      await fs.copyFile(sourceFullPath, destFullPath);

      this.logger.log(`File copied: ${sourcePath} -> ${destinationPath}`);
    } catch (error) {
      return this.handleError('copyFile', error);
    }
  }

  async moveFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      const sourceFullPath = path.join(this.basePath, sourcePath);
      const destFullPath = path.join(this.basePath, destinationPath);

      // 確保目標目錄存在
      await fs.mkdir(path.dirname(destFullPath), { recursive: true });

      await fs.rename(sourceFullPath, destFullPath);

      this.logger.log(`File moved: ${sourcePath} -> ${destinationPath}`);
    } catch (error) {
      return this.handleError('moveFile', error);
    }
  }

  async compressFiles(
    paths: string[],
    archivePath: string,
    format: 'zip' | 'gzip' = 'gzip',
  ): Promise<string> {
    try {
      if (format === 'gzip' && paths.length === 1) {
        // 單檔案 gzip 壓縮
        const sourcePath = paths[0];
        const sourceBuffer = await this.downloadFile(sourcePath);
        const gzipAsync = promisify(zlib.gzip);
        const compressedBuffer = await gzipAsync(sourceBuffer);

        const gzipPath = archivePath.endsWith('.gz') ? archivePath : `${archivePath}.gz`;
        await this.uploadFile(compressedBuffer, {
          filename: path.basename(gzipPath),
          path: path.dirname(gzipPath),
        });

        return gzipPath;
      }

      throw new Error(
        `Compression format '${format}' not supported for multiple files in local storage`,
      );
    } catch (error) {
      return this.handleError('compressFiles', error);
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.ensureBaseDirectory();

      // 測試寫入權限
      const testFile = path.join(this.basePath, '.test_connection');
      await fs.writeFile(testFile, 'test');
      await fs.unlink(testFile);

      return true;
    } catch (error) {
      this.logger.error('Local storage connection test failed', error);
      return false;
    }
  }
}
