import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * 基本工具錯誤類
 */
export class BaseToolError extends HttpException {
  constructor(message: string, status: HttpStatus) {
    super(message, status);
  }
}

/**
 * 工具執行期間發生的錯誤
 */
export class ToolExecutionError extends BaseToolError {
  constructor(toolKey: string, message: string, details?: Record<string, any>) {
    super(`工具 '${toolKey}' 執行失敗: ${message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    this.name = 'ToolExecutionError';
    if (details) {
      Object.assign(this, details);
    }
  }
}

/**
 * 工具配置無效錯誤
 */
export class InvalidToolConfigError extends BaseToolError {
  constructor(toolKey: string, message: string) {
    super(`工具 '${toolKey}' 配置無效: ${message}`, HttpStatus.BAD_REQUEST);
    this.name = 'InvalidToolConfigError';
  }
}

/**
 * 權限不足錯誤
 */
export class ToolPermissionError extends BaseToolError {
  constructor(toolKey: string, message: string = '權限不足，無法執行此工具') {
    super(`工具 '${toolKey}' 操作被拒絕: ${message}`, HttpStatus.FORBIDDEN);
    this.name = 'ToolPermissionError';
  }
}

/**
 * 檔案操作相關錯誤
 */
export class FileOperationError extends ToolExecutionError {
  constructor(toolKey: string, filePath: string, operation: string, originalError?: Error) {
    const message = `檔案操作 '${operation}' 失敗於路徑 '${filePath}'.`;
    super(toolKey, originalError?.message || message, { filePath, operation, originalError });
    this.name = 'FileOperationError';
  }
}
