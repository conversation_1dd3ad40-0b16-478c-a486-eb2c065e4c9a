/*
  Warnings:

  - A unique constraint covering the columns `[name]` on the table `plans` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "plans" ADD COLUMN     "monthlyAiCreditsLimit" DECIMAL(65,30) DEFAULT 0;

-- AlterTable
ALTER TABLE "tenants" ADD COLUMN     "currentAiCredits" DECIMAL(65,30) DEFAULT 0;

-- CreateTable
CREATE TABLE "tenant_credit_purchases" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "amount" DECIMAL(65,30) NOT NULL,
    "pricePaid" DECIMAL(65,30) NOT NULL,
    "currency" TEXT NOT NULL,
    "paymentId" TEXT,
    "purchasedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,

    CONSTRAINT "tenant_credit_purchases_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "tenant_credit_purchases_tenantId_idx" ON "tenant_credit_purchases"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "plans_name_key" ON "plans"("name");

-- AddForeignKey
ALTER TABLE "tenant_credit_purchases" ADD CONSTRAINT "tenant_credit_purchases_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
