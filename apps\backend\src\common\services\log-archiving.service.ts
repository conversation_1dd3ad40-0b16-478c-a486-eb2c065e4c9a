import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { PrismaService } from '../../modules/core/prisma/prisma.service';
import { SettingsService } from '../../modules/admin/settings/settings.service';
import { StorageFactory } from '../../modules/core/storage/storage.factory';
import { IStorageService } from '../../modules/core/storage/interfaces/storage.interface';
import { ArchivingSettings } from '../../modules/admin/settings/interfaces/archiving-settings.interface';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import * as crypto from 'crypto';

export interface ArchiveResult {
  batchId: string;
  archivedCount: number;
  archiveLocation: string;
  format: 'json' | 'csv';
  compressed: boolean;
  startTime: Date;
  endTime: Date;
  status: 'success' | 'failed';
  error?: string;
}

export interface ArchiveBatch {
  id: string;
  logs: any[];
  totalSize: number;
  cutoffDate: Date;
}

/**
 * 日誌歸檔服務
 * 負責根據動態設定自動歸檔系統日誌到不同的儲存提供商
 */
@Injectable()
export class LogArchivingService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(LogArchivingService.name);
  private isArchiving = false;
  private readonly TIMEOUT_NAME = 'log-archiving-timeout';
  private currentSchedule: string | null = null;
  private scheduledTimeout: NodeJS.Timeout | null = null;

  constructor(
    private readonly prisma: PrismaService,
    private readonly settingsService: SettingsService,
    private readonly storageFactory: StorageFactory,
    private readonly eventEmitter: EventEmitter2,
    private readonly schedulerRegistry: SchedulerRegistry,
  ) {}

  /**
   * 模組初始化時設定動態排程
   */
  async onModuleInit(): Promise<void> {
    try {
      await this.setupDynamicSchedule();
      this.logger.log('LogArchivingService 初始化完成，動態排程已設定');
    } catch (error) {
      this.logger.error('LogArchivingService 初始化失敗', error);
    }
  }

  /**
   * 模組銷毀時清理排程
   */
  onModuleDestroy(): void {
    this.cleanupSchedule();
  }

  /**
   * 設定動態排程
   */
  async setupDynamicSchedule(): Promise<void> {
    try {
      const settings = await this.settingsService.getArchivingSettings();

      if (!settings.enabled) {
        this.logger.log('歸檔功能未啟用，跳過排程設定');
        this.cleanupSchedule();
        return;
      }

      // 如果排程已存在且相同，則不需要重新設定
      if (this.currentSchedule === settings.schedule) {
        return;
      }

      // 清理舊排程
      this.cleanupSchedule();

      // 計算下次執行時間
      const nextExecution = this.getNextExecutionTime(settings.schedule);
      if (nextExecution) {
        const delay = nextExecution.getTime() - Date.now();

        this.scheduledTimeout = setTimeout(async () => {
          await this.handleScheduledArchiving();
          // 重新設定下次執行
          await this.setupDynamicSchedule();
        }, delay);

        // 使用 SchedulerRegistry 來管理 timeout
        this.schedulerRegistry.addTimeout(this.TIMEOUT_NAME, this.scheduledTimeout);

        this.currentSchedule = settings.schedule;
        this.logger.log(
          `動態排程已設定: ${settings.schedule}, 下次執行: ${nextExecution.toLocaleString()}`,
        );
      }
    } catch (error) {
      this.logger.error('設定動態排程失敗', error);
    }
  }

  /**
   * 清理排程
   */
  private cleanupSchedule(): void {
    try {
      if (this.schedulerRegistry.doesExist('timeout', this.TIMEOUT_NAME)) {
        this.schedulerRegistry.deleteTimeout(this.TIMEOUT_NAME);
      }
      if (this.scheduledTimeout) {
        clearTimeout(this.scheduledTimeout);
        this.scheduledTimeout = null;
      }
      this.currentSchedule = null;
      this.logger.log('已清理舊的排程任務');
    } catch (error) {
      this.logger.error('清理排程失敗', error);
    }
  }

  /**
   * 計算下次執行時間
   * @param cronExpression cron 表達式
   * @returns 下次執行時間
   */
  private getNextExecutionTime(cronExpression: string): Date | null {
    try {
      // 簡化的 cron 解析 (格式: 分 時 日 月 週)
      const parts = cronExpression.split(' ');
      if (parts.length !== 5) {
        return null;
      }

      const [minute, hour, day, month, dayOfWeek] = parts;
      const now = new Date();
      const next = new Date(now);

      // 簡單實現：只支援每日執行 (0 2 * * *)
      if (minute !== '*' && hour !== '*' && day === '*' && month === '*' && dayOfWeek === '*') {
        const targetMinute = parseInt(minute);
        const targetHour = parseInt(hour);

        next.setHours(targetHour, targetMinute, 0, 0);

        // 如果時間已過，設定為明天
        if (next <= now) {
          next.setDate(next.getDate() + 1);
        }

        return next;
      }

      // 預設每小時執行
      next.setMinutes(0, 0, 0);
      next.setHours(next.getHours() + 1);
      return next;
    } catch (error) {
      this.logger.error('解析 cron 表達式失敗', error);
      return null;
    }
  }

  /**
   * 更新排程設定
   * 當歸檔設定更新時調用此方法
   */
  async updateSchedule(): Promise<void> {
    await this.setupDynamicSchedule();
  }

  /**
   * 定時執行歸檔任務
   */
  async handleScheduledArchiving(): Promise<void> {
    try {
      const settings = await this.settingsService.getArchivingSettings();

      // 再次檢查是否啟用歸檔（防止設定在執行期間被關閉）
      if (!settings.enabled) {
        this.logger.log('歸檔功能已被關閉，停止執行');
        return;
      }

      // 檢查是否已在執行歸檔
      if (this.isArchiving) {
        this.logger.warn('歸檔任務已在執行中，跳過此次執行');
        return;
      }

      await this.executeArchiving();
    } catch (error) {
      this.logger.error('定時歸檔任務執行失敗', error);
    }
  }

  /**
   * 手動執行歸檔
   * @param tenantId 可選的租戶 ID，僅歸檔特定租戶的日誌
   * @returns 歸檔結果
   */
  async executeArchiving(tenantId?: string): Promise<ArchiveResult> {
    const startTime = new Date();
    let batchId: string = crypto.randomUUID();

    try {
      this.isArchiving = true;
      this.logger.log(`開始執行日誌歸檔${tenantId ? ` (租戶: ${tenantId})` : ''}`);

      // 更新歸檔狀態為進行中
      await this.settingsService.updateArchivingStatus('in-progress');

      // 讀取歸檔設定
      const settings = await this.settingsService.getArchivingSettings();

      if (!settings.enabled) {
        throw new Error('歸檔功能未啟用');
      }

      // 建立儲存服務實例
      const storageService = this.storageFactory.createArchivingStorageService({
        storageProvider: settings.storageProvider,
        storagePath: settings.storagePath,
      });

      // 測試儲存服務連接
      const connectionTest = await storageService.testConnection();
      if (!connectionTest) {
        throw new Error(`儲存服務連接失敗: ${settings.storageProvider}`);
      }

      // 獲取需要歸檔的日誌
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - settings.retentionDays);

      const archiveBatch = await this.getLogsToArchive(cutoffDate, settings.batchSize, tenantId);

      if (archiveBatch.logs.length === 0) {
        this.logger.log('沒有需要歸檔的日誌');
        await this.settingsService.updateArchivingStatus('success', undefined, 0);

        return {
          batchId,
          archivedCount: 0,
          archiveLocation: '',
          format: settings.archiveFormat,
          compressed: settings.compressionEnabled,
          startTime,
          endTime: new Date(),
          status: 'success',
        };
      }

      batchId = archiveBatch.id;
      this.logger.log(`準備歸檔 ${archiveBatch.logs.length} 條日誌記錄`);

      // 執行歸檔
      const archiveLocation = await this.archiveLogsToStorage(
        archiveBatch,
        storageService,
        settings,
      );

      // 保存歸檔記錄到資料庫
      await this.saveArchivedLogs(archiveBatch, archiveLocation, settings);

      // 刪除原始記錄（如果設定為刪除）
      if (settings.deleteAfterArchive) {
        await this.deleteOriginalLogs(archiveBatch.logs.map((log) => log.id));
      }

      const endTime = new Date();
      const result: ArchiveResult = {
        batchId,
        archivedCount: archiveBatch.logs.length,
        archiveLocation,
        format: settings.archiveFormat,
        compressed: settings.compressionEnabled,
        startTime,
        endTime,
        status: 'success',
      };

      // 更新歸檔狀態
      await this.settingsService.updateArchivingStatus(
        'success',
        undefined,
        archiveBatch.logs.length,
      );

      // 發射歸檔完成事件
      this.eventEmitter.emit('logs.archived', {
        result,
        settings,
        tenantId,
      });

      this.logger.log(`歸檔完成: ${archiveBatch.logs.length} 條記錄，位置: ${archiveLocation}`);
      return result;
    } catch (error) {
      const endTime = new Date();
      const errorMessage = error.message || '未知錯誤';

      this.logger.error('日誌歸檔失敗', error);

      // 更新歸檔狀態為失敗
      await this.settingsService.updateArchivingStatus('failed', errorMessage);

      // 發射歸檔失敗事件
      this.eventEmitter.emit('logs.archive.failed', {
        batchId,
        error: errorMessage,
        tenantId,
        startTime,
        endTime,
      });

      return {
        batchId,
        archivedCount: 0,
        archiveLocation: '',
        format: 'json',
        compressed: false,
        startTime,
        endTime,
        status: 'failed',
        error: errorMessage,
      };
    } finally {
      this.isArchiving = false;
    }
  }

  /**
   * 獲取需要歸檔的日誌
   * @param cutoffDate 截止日期
   * @param batchSize 批次大小
   * @param tenantId 可選的租戶 ID
   * @returns 歸檔批次
   */
  private async getLogsToArchive(
    cutoffDate: Date,
    batchSize: number,
    tenantId?: string,
  ): Promise<ArchiveBatch> {
    const where: any = {
      created_at: {
        lt: cutoffDate,
      },
    };

    if (tenantId) {
      where.tenant_id = tenantId;
    }

    const logs = await this.prisma.system_logs.findMany({
      where,
      orderBy: {
        created_at: 'asc',
      },
      take: batchSize,
    });

    // 計算總大小（估算）
    const totalSize = logs.reduce((size, log) => {
      return size + JSON.stringify(log).length;
    }, 0);

    return {
      id: crypto.randomUUID(),
      logs,
      totalSize,
      cutoffDate,
    };
  }

  /**
   * 將日誌歸檔到儲存服務
   * @param batch 歸檔批次
   * @param storageService 儲存服務
   * @param settings 歸檔設定
   * @returns 歸檔位置
   */
  private async archiveLogsToStorage(
    batch: ArchiveBatch,
    storageService: IStorageService,
    settings: ArchivingSettings,
  ): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `audit-logs-${timestamp}-${batch.id}`;
    const extension = settings.archiveFormat === 'csv' ? 'csv' : 'json';
    const fullFilename = `${filename}.${extension}`;

    // 轉換日誌格式
    const content = this.formatLogsForArchive(batch.logs, settings.archiveFormat);

    // 上傳到儲存服務
    const archiveLocation = await storageService.uploadText(content, {
      filename: fullFilename,
      path: `${new Date().getFullYear()}/${String(new Date().getMonth() + 1).padStart(2, '0')}`,
      contentType: settings.archiveFormat === 'csv' ? 'text/csv' : 'application/json',
      compress: settings.compressionEnabled,
      metadata: {
        batchId: batch.id,
        recordCount: batch.logs.length.toString(),
        cutoffDate: batch.cutoffDate.toISOString(),
        archiveFormat: settings.archiveFormat,
        compressed: settings.compressionEnabled.toString(),
      },
    });

    return archiveLocation;
  }

  /**
   * 格式化日誌為歸檔格式
   * @param logs 日誌陣列
   * @param format 格式類型
   * @returns 格式化後的內容
   */
  private formatLogsForArchive(logs: any[], format: 'json' | 'csv'): string {
    if (format === 'csv') {
      return this.convertLogsToCSV(logs);
    } else {
      return JSON.stringify(logs, null, 2);
    }
  }

  /**
   * 將日誌轉換為 CSV 格式
   * @param logs 日誌陣列
   * @returns CSV 字串
   */
  private convertLogsToCSV(logs: any[]): string {
    if (logs.length === 0) {
      return '';
    }

    // CSV 標題行
    const headers = [
      'id',
      'level',
      'message',
      'created_at',
      'user_id',
      'tenant_id',
      'action',
      'target_resource',
      'target_resource_id',
      'status',
      'ip',
      'path',
      'method',
      'error_message',
    ];

    // CSV 內容
    const csvRows = [
      headers.join(','), // 標題行
      ...logs.map((log) => {
        return headers
          .map((header) => {
            const value = log[header];
            if (value === null || value === undefined) {
              return '';
            }
            // 處理包含逗號的值
            const stringValue = String(value);
            if (
              stringValue.includes(',') ||
              stringValue.includes('"') ||
              stringValue.includes('\n')
            ) {
              return `"${stringValue.replace(/"/g, '""')}"`;
            }
            return stringValue;
          })
          .join(',');
      }),
    ];

    return csvRows.join('\n');
  }

  /**
   * 保存歸檔記錄到資料庫
   * @param batch 歸檔批次
   * @param archiveLocation 歸檔位置
   * @param settings 歸檔設定
   */
  private async saveArchivedLogs(
    batch: ArchiveBatch,
    archiveLocation: string,
    settings: ArchivingSettings,
  ): Promise<void> {
    const archivedLogs = batch.logs.map((log) => ({
      id: crypto.randomUUID(),
      original_log_id: log.id,
      level: log.level,
      message: log.message,
      stack: log.stack,
      path: log.path,
      method: log.method,
      user_id: log.user_id,
      ip: log.ip,
      original_created_at: log.created_at,
      action: log.action,
      details: log.details,
      error_message: log.error_message,
      status: log.status,
      target_resource: log.target_resource,
      target_resource_id: log.target_resource_id,
      tenant_id: log.tenant_id,
      archived_at: new Date(),
      archive_batch_id: batch.id,
      storage_location: archiveLocation,
      archive_format: settings.archiveFormat,
      compressed: settings.compressionEnabled,
    }));

    // 批次插入歸檔記錄
    await this.prisma.archived_audit_logs.createMany({
      data: archivedLogs,
    });

    this.logger.log(`已保存 ${archivedLogs.length} 條歸檔記錄到資料庫`);
  }

  /**
   * 刪除原始日誌記錄
   * @param logIds 日誌 ID 陣列
   */
  private async deleteOriginalLogs(logIds: string[]): Promise<void> {
    await this.prisma.system_logs.deleteMany({
      where: {
        id: {
          in: logIds,
        },
      },
    });

    this.logger.log(`已刪除 ${logIds.length} 條原始日誌記錄`);
  }

  /**
   * 獲取歸檔統計資訊
   * @param tenantId 可選的租戶 ID
   * @returns 統計資訊
   */
  async getArchiveStatistics(tenantId?: string): Promise<{
    totalArchivedRecords: number;
    totalBatches: number;
    oldestArchive: Date | null;
    newestArchive: Date | null;
    storageUsed: number; // 估算的儲存使用量
  }> {
    const where: any = {};
    if (tenantId) {
      where.tenant_id = tenantId;
    }

    const [totalRecords, totalBatches, oldestArchive, newestArchive] = await Promise.all([
      this.prisma.archived_audit_logs.count({ where }),
      this.prisma.archived_audit_logs
        .groupBy({
          by: ['archive_batch_id'],
          where,
        })
        .then((groups) => groups.length),
      this.prisma.archived_audit_logs.findFirst({
        where,
        orderBy: { archived_at: 'asc' },
        select: { archived_at: true },
      }),
      this.prisma.archived_audit_logs.findFirst({
        where,
        orderBy: { archived_at: 'desc' },
        select: { archived_at: true },
      }),
    ]);

    // 估算儲存使用量（基於記錄數和平均大小）
    const averageRecordSize = 1024; // 假設每條記錄平均 1KB
    const storageUsed = totalRecords * averageRecordSize;

    return {
      totalArchivedRecords: totalRecords,
      totalBatches,
      oldestArchive: oldestArchive?.archived_at || null,
      newestArchive: newestArchive?.archived_at || null,
      storageUsed,
    };
  }

  /**
   * 清理過期的歸檔檔案
   * @param tenantId 可選的租戶 ID
   */
  async cleanupExpiredArchives(tenantId?: string): Promise<void> {
    const settings = await this.settingsService.getArchivingSettings();

    if (settings.archiveRetentionDays === 0) {
      return; // 永久保留
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - settings.archiveRetentionDays);

    const where: any = {
      archived_at: {
        lt: cutoffDate,
      },
    };

    if (tenantId) {
      where.tenant_id = tenantId;
    }

    // 獲取需要清理的歸檔記錄
    const expiredArchives = await this.prisma.archived_audit_logs.findMany({
      where,
      select: {
        storage_location: true,
        archive_batch_id: true,
      },
      distinct: ['storage_location'],
    });

    if (expiredArchives.length === 0) {
      return;
    }

    // 建立儲存服務實例
    const storageService = this.storageFactory.createArchivingStorageService({
      storageProvider: settings.storageProvider,
      storagePath: settings.storagePath,
    });

    // 刪除儲存檔案
    for (const archive of expiredArchives) {
      if (archive.storage_location) {
        try {
          await storageService.deleteFile(archive.storage_location);
          this.logger.log(`已刪除過期歸檔檔案: ${archive.storage_location}`);
        } catch (error) {
          this.logger.error(`刪除歸檔檔案失敗: ${archive.storage_location}`, error);
        }
      }
    }

    // 刪除資料庫記錄
    const deletedCount = await this.prisma.archived_audit_logs.deleteMany({
      where,
    });

    this.logger.log(`已清理 ${deletedCount.count} 條過期歸檔記錄`);
  }

  /**
   * 監聽歸檔設定更新事件
   */
  @OnEvent('archiving.settings.updated')
  async handleArchivingSettingsUpdated(payload: {
    settings: ArchivingSettings;
    userId?: string;
    timestamp: Date;
  }): Promise<void> {
    this.logger.log('收到歸檔設定更新事件，重新設定排程');
    await this.setupDynamicSchedule();
  }
}
