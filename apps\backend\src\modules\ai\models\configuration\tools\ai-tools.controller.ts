import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/core/auth/guards/auth.guard';
import { PoliciesGuard } from '@/casl/guards/permission.guard';
import { CheckPolicies } from '@/casl/decorators/check-policies.decorator';
import { AppAbility } from '@/types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';
import { CurrentUser } from '@/modules/core/auth/decorators/current-user.decorator';
import { JwtUser } from '@/types/jwt-user.type';
import { AiToolsService } from './ai-tools.service';
import {
  CreateAiToolDto,
  UpdateAiToolDto,
  AiToolQueryDto,
  AiToolResponseDto,
} from './dto/ai-tools.dto';

@ApiTags('admin/ai/tools')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, PoliciesGuard)
@Controller('admin/ai/tools')
export class AiToolsController {
  private readonly logger = new Logger(AiToolsController.name);

  constructor(private readonly aiToolsService: AiToolsService) {}

  @Get()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.AI_TOOL))
  @ApiOperation({ summary: '取得所有 AI 工具' })
  @ApiResponse({ status: 200, description: '成功取得工具列表', type: [AiToolResponseDto] })
  async findAll(@Query() query: AiToolQueryDto): Promise<AiToolResponseDto[]> {
    return this.aiToolsService.findAll(query);
  }

  @Get('available')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.AI_TOOL))
  @ApiOperation({ summary: '取得可用的 AI 工具' })
  @ApiResponse({ status: 200, description: '成功取得可用工具列表', type: [AiToolResponseDto] })
  async getAvailableTools(
    @Query('tenantId') tenantId?: string,
    @Query('workspaceId') workspaceId?: string,
  ): Promise<AiToolResponseDto[]> {
    return this.aiToolsService.getAvailableTools(tenantId, workspaceId);
  }

  @Get(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.AI_TOOL))
  @ApiOperation({ summary: '取得單一 AI 工具' })
  @ApiResponse({ status: 200, description: '成功取得工具詳情', type: AiToolResponseDto })
  @ApiResponse({ status: 404, description: '找不到指定的工具' })
  async findOne(@Param('id') id: string): Promise<AiToolResponseDto> {
    return this.aiToolsService.findOne(id);
  }

  @Post()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.AI_TOOL))
  @ApiOperation({ summary: '建立 AI 工具' })
  @ApiResponse({ status: 201, description: '成功建立工具', type: AiToolResponseDto })
  @ApiResponse({ status: 400, description: '請求參數錯誤' })
  @ApiResponse({ status: 409, description: '工具標識符已存在' })
  async create(
    @Body() createAiToolDto: CreateAiToolDto,
    @CurrentUser() user: JwtUser,
  ): Promise<AiToolResponseDto> {
    return this.aiToolsService.create(createAiToolDto, user.id);
  }

  @Put(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.AI_TOOL))
  @ApiOperation({ summary: '更新 AI 工具' })
  @ApiResponse({ status: 200, description: '成功更新工具', type: AiToolResponseDto })
  @ApiResponse({ status: 400, description: '請求參數錯誤' })
  @ApiResponse({ status: 404, description: '找不到指定的工具' })
  async update(
    @Param('id') id: string,
    @Body() updateAiToolDto: UpdateAiToolDto,
    @CurrentUser() user: JwtUser,
  ): Promise<AiToolResponseDto> {
    updateAiToolDto.updatedBy = user.id;
    return this.aiToolsService.update(id, updateAiToolDto);
  }

  @Delete(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, Subjects.AI_TOOL))
  @ApiOperation({ summary: '刪除 AI 工具' })
  @ApiResponse({ status: 204, description: '成功刪除工具' })
  @ApiResponse({ status: 404, description: '找不到指定的工具' })
  @ApiResponse({ status: 409, description: '工具正在被使用中，無法刪除' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    return this.aiToolsService.remove(id);
  }
}
