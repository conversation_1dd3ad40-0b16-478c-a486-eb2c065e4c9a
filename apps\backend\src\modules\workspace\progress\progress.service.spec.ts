import { Test, TestingModule } from '@nestjs/testing';
import { ProgressService } from './progress.service';
import { PrismaService } from '@/prisma/prisma.service';
import { NotFoundException } from '@nestjs/common';
import { ProgressEntryEntity } from './entities/progress-entry.entity';
import { CreateProgressEntryDto } from './dto/create-progress-entry.dto';
import { ProgressType } from '@prisma/client';

describe('ProgressService', () => {
  let service: ProgressService;
  let prisma: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProgressService,
        {
          provide: PrismaService,
          useValue: {
            progress_entries: {
              create: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<ProgressService>(ProgressService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create and return a progress entry entity', async () => {
      const dto: CreateProgressEntryDto = {
        title: 'Test Progress',
        progress_type: ProgressType.TASK_UPDATE,
        project_id: 'project-id-123',
      };
      const tenantId = 'tenant-id-abc';
      const userId = 'user-id-xyz';

      const mockProgressEntry = { id: '1', title: 'Test' }; // simplified mock
      (prisma.progress_entries.create as jest.Mock).mockResolvedValue(mockProgressEntry);

      const result = await service.create(dto, tenantId, userId);

      expect(prisma.progress_entries.create).toHaveBeenCalled();
      expect(result).toBeInstanceOf(ProgressEntryEntity);
    });

    it('should throw NotFoundException if no project_id or task_id is provided', async () => {
      const dto: CreateProgressEntryDto = {
        title: 'Invalid Progress Update',
        progress_type: ProgressType.MILESTONE,
      };
      await expect(service.create(dto, 'tenant', 'user')).rejects.toThrow(NotFoundException);
    });
  });
});
