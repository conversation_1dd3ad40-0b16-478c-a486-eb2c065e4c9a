import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Logger,
  UnauthorizedException,
  NotFoundException,
} from '@nestjs/common';
import { LineBotService } from '../services/line-bot.service';
import { validateSignature } from '@line/bot-sdk';

@Injectable()
export class LineSignatureGuard implements CanActivate {
  private readonly logger = new Logger(LineSignatureGuard.name);

  constructor(private readonly lineBotService: LineBotService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const botId = request.params.id; // Assuming botId is in path params as 'id'
    const signature = request.headers['x-line-signature'] as string;
    const rawBody = request.rawBody; // Provided by bodyParser in main.ts

    if (!botId) {
      this.logger.warn('LineSignatureGuard: Bot ID not found in request params.');
      throw new UnauthorizedException('無效的請求：缺少 Bot ID');
    }

    if (!signature) {
      this.logger.warn(`LineSignatureGuard: Bot ID ${botId} - X-Line-Signature header missing.`);
      throw new UnauthorizedException('無效的請求：缺少簽名標頭');
    }

    if (!rawBody) {
      this.logger.warn(`LineSignatureGuard: Bot ID ${botId} - Raw body missing.`);
      // This should ideally not happen due to main.ts setup
      throw new UnauthorizedException('無效的請求：缺少請求內容');
    }

    try {
      // const lineBot = await this.lineBotService.findOne(botId);
      // if (!lineBot || !lineBot.bot_secret) { // bot_secret from findOne is transformed and not raw/encrypted
      //   this.logger.warn(`LineSignatureGuard: Bot ID ${botId} - Bot not found or channel secret missing.`);
      //   throw new NotFoundException('找不到指定的 Bot 或其設定不完整');
      // }

      const decryptedChannelSecret = await this.lineBotService.getDecryptedChannelSecret(botId);

      if (!decryptedChannelSecret) {
        this.logger.warn(
          `LineSignatureGuard: Bot ID ${botId} - Failed to retrieve or decrypt channel secret.`,
        );
        // NotFoundException might be more appropriate if secret is null due to bot not found or no secret set.
        // UnauthorizedException if decryption itself failed due to a bad key or corrupted data, implying a configuration or integrity issue.
        // For simplicity, let's use Unauthorized as the signature cannot be validated without a valid secret.
        throw new UnauthorizedException('無法驗證請求來源：Bot 設定錯誤或遺失');
      }

      const isValid = validateSignature(rawBody, decryptedChannelSecret, signature);

      if (!isValid) {
        this.logger.warn(`LineSignatureGuard: Bot ID ${botId} - Invalid signature.`);
        throw new UnauthorizedException('簽名驗證失敗');
      }

      this.logger.log(`LineSignatureGuard: Bot ID ${botId} - Signature validated successfully.`);
      return true;
    } catch (error) {
      this.logger.error(
        `LineSignatureGuard: Bot ID ${botId} - Error during signature validation: ${error.message}`,
        error.stack,
      );
      if (error instanceof UnauthorizedException || error instanceof NotFoundException) {
        throw error;
      }
      throw new UnauthorizedException('簽名驗證過程中發生錯誤');
    }
  }
}
