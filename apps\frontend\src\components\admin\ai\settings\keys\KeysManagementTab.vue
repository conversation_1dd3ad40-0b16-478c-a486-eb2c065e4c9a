<template>
  <div class="space-y-6">
    <!-- 操作按鈕區域 -->
    <div class="flex justify-end">
      <Button
        variant="outline"
        size="sm"
        @click="$emit('create-key')"
      >
        <Plus class="h-4 w-4 mr-2" />
        新增金鑰
      </Button>
    </div>

    <!-- 載入狀態 -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <span class="text-zinc-600 dark:text-zinc-400">載入金鑰列表中...</span>
      </div>
    </div>

    <!-- 空狀態 -->
    <div v-if="!isLoading && aiKeys.length === 0" class="flex flex-col items-center justify-center py-16">
      <div class="flex flex-col items-center space-y-6 text-center max-w-lg">
        <!-- 視覺圖示 -->
        <div class="relative">
          <div class="p-6 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-2xl">
            <Key class="w-12 h-12 text-blue-600 dark:text-blue-400" />
          </div>
          <div class="absolute -top-1 -right-1 p-1 bg-yellow-100 dark:bg-yellow-900/50 rounded-full">
            <AlertTriangle class="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
        
        <!-- 文字內容 -->
        <div class="space-y-2">
          <h3 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100">尚未設定 API 金鑰</h3>
          <p class="text-sm text-zinc-600 dark:text-zinc-400 leading-relaxed">
            API 金鑰是連接 AI 服務的核心憑證。您需要新增至少一個有效的 API 金鑰<br />
            才能開始使用智能功能，如對話助理、內容生成等。
          </p>
        </div>

        <!-- 支援的提供商 -->
        <div class="flex items-center space-x-4 py-3 px-4 bg-zinc-50 dark:bg-zinc-800 rounded-lg">
          <span class="text-xs text-zinc-500 dark:text-zinc-400 font-medium">支援：</span>
          <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-1">
              <Brain class="w-4 h-4 text-green-600" />
              <span class="text-xs font-medium text-zinc-700 dark:text-zinc-300">OpenAI</span>
            </div>
            <div class="flex items-center space-x-1">
              <Zap class="w-4 h-4 text-orange-600" />
              <span class="text-xs font-medium text-zinc-700 dark:text-zinc-300">Anthropic</span>
            </div>
            <div class="flex items-center space-x-1">
              <Globe class="w-4 h-4 text-blue-600" />
              <span class="text-xs font-medium text-zinc-700 dark:text-zinc-300">Google</span>
            </div>
          </div>
        </div>
        
        <!-- 操作按鈕 -->
        <Button 
          @click="$emit('create-key')" 
          class="mt-4 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white"
          size="sm"
        >
          <Plus class="w-4 h-4 mr-2" />
          新增第一個 API 金鑰
        </Button>
      </div>
    </div>

    <!-- 金鑰列表 -->
    <div v-else class="space-y-4">
      <!-- 金鑰卡片 -->
      <div
        v-for="key in aiKeys"
        :key="key.id"
        class="group"
      >
        <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm hover:shadow-md transition-shadow duration-200">
          <!-- 金鑰標題區域 -->
          <div class="bg-zinc-50 dark:bg-zinc-800/50 p-4 border-b border-zinc-200 dark:border-zinc-700">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div :class="[
                  'p-2 rounded-md',
                  getProviderConfig(key.provider).color
                ]">
                  <component :is="getProviderConfig(key.provider).icon" class="w-4 h-4 text-white" />
                </div>
                <div>
                  <div class="flex items-center space-x-2">
                    <h4 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">
                      {{ key.name }}
                    </h4>
                    <Badge :variant="key.is_enabled ? 'default' : 'secondary'" class="text-xs">
                      {{ key.is_enabled ? '已啟用' : '已停用' }}
                    </Badge>
                  </div>
                  <div class="flex items-center space-x-2 mt-1">
                    <Badge variant="outline" class="text-xs">
                      {{ getProviderDisplayName(key.provider) }}
                    </Badge>
                    <span class="text-xs text-zinc-500 dark:text-zinc-400">
                      {{ formatDate(key.created_at) }}
                    </span>
                  </div>
                </div>
              </div>
              
              <!-- 操作按鈕 -->
              <div class="flex items-center space-x-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        @click="$emit('edit-key', key)"
                        size="sm"
                        variant="outline"
                        class="opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700"
                      >
                        <Edit class="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>編輯金鑰</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            class="opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-red-50 hover:border-red-300 hover:text-red-700"
                          >
                            <Trash2 class="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>刪除金鑰</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </AlertDialogTrigger>
                  <AlertDialogContent class="max-w-md">
                    <AlertDialogHeader>
                      <div class="flex items-center space-x-3">
                        <div class="p-2 bg-red-100 dark:bg-red-900 rounded-full">
                          <AlertTriangle class="w-5 h-5 text-red-600 dark:text-red-400" />
                        </div>
                        <div>
                          <AlertDialogTitle class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
                            確認刪除金鑰
                          </AlertDialogTitle>
                        </div>
                      </div>
                      <AlertDialogDescription class="text-sm text-zinc-600 dark:text-zinc-400 mt-3">
                        您即將刪除 API 金鑰：
                        <span class="font-semibold text-zinc-900 dark:text-zinc-100">「{{ key.name }}」</span>
                        <br /><br />
                        此操作無法復原，所有使用此金鑰的 AI 功能將會停止運作。
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter class="gap-2 pt-4">
                      <AlertDialogCancel class="px-4">取消</AlertDialogCancel>
                      <AlertDialogAction 
                        @click="$emit('delete-key', key.id)"
                        class="bg-red-500 hover:bg-red-600 text-white px-4"
                      >
                        <Trash2 class="w-4 h-4 mr-2" />
                        確認刪除
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </div>

          <!-- 金鑰詳細資訊 -->
          <div class="p-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- 基本資訊 -->
              <div class="space-y-2">
                <h5 class="text-sm font-medium text-zinc-900 dark:text-zinc-100 flex items-center">
                  <Info class="w-4 h-4 mr-2 text-blue-500" />
                  基本資訊
                </h5>
                <div class="space-y-1.5 text-xs">
                  <div class="flex justify-between">
                    <span class="text-zinc-500 dark:text-zinc-400">提供商：</span>
                    <span class="font-medium text-zinc-900 dark:text-zinc-100">
                      {{ getProviderDisplayName(key.provider) }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-zinc-500 dark:text-zinc-400">狀態：</span>
                    <div class="flex items-center space-x-1.5">
                      <div :class="[
                        'w-2 h-2 rounded-full',
                        key.is_enabled ? 'bg-green-500' : 'bg-zinc-400'
                      ]"></div>
                      <span class="font-medium text-zinc-900 dark:text-zinc-100">
                        {{ key.is_enabled ? '已啟用' : '已停用' }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 金鑰資訊 -->
              <div class="space-y-2">
                <h5 class="text-sm font-medium text-zinc-900 dark:text-zinc-100 flex items-center">
                  <Key class="w-4 h-4 mr-2 text-green-500" />
                  金鑰資訊
                </h5>
                <div class="space-y-1.5 text-xs">
                  <div class="flex justify-between items-center">
                    <span class="text-zinc-500 dark:text-zinc-400">API 金鑰：</span>
                    <div class="flex items-center space-x-2">
                      <code class="text-xs font-mono bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">
                        {{ maskApiKey(key.api_key) }}
                      </code>
                      <Button
                        @click="copyToClipboard(key.api_key)"
                        size="sm"
                        variant="outline"
                        class="h-6 w-6 p-0"
                      >
                        <Copy class="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-zinc-500 dark:text-zinc-400">建立時間：</span>
                    <span class="font-medium text-zinc-900 dark:text-zinc-100">
                      {{ formatDate(key.created_at) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { 
  Key, Edit, Trash2, Info, Copy, Plus, AlertTriangle,
  Brain, Zap, Globe, Bot
} from "lucide-vue-next";
import { useNotification } from "@/composables/shared/useNotification";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Props 定義
interface Props {
  aiKeys: any[];
  isLoading: boolean;
  keyProviders: any[];
}

const props = defineProps<Props>();

// Emits 定義
const emit = defineEmits<{
  'edit-key': [key: any];
  'delete-key': [keyId: string];
  'create-key': [];
}>();

// 狀態管理
const showDeleteDialog = ref(false);
const keyToDelete = ref<any>(null);
const notification = useNotification();

// 提供商配置
const getProviderConfig = (provider: string) => {
  const configs = {
    'openai': { 
      icon: Brain, 
      color: 'bg-gradient-to-br from-green-500 to-emerald-600',
      name: 'OpenAI'
    },
    'anthropic': { 
      icon: Zap, 
      color: 'bg-gradient-to-br from-orange-500 to-red-600',
      name: 'Anthropic'
    },
    'google': { 
      icon: Globe, 
      color: 'bg-gradient-to-br from-blue-500 to-indigo-600',
      name: 'Google'
    },
    'default': { 
      icon: Bot, 
      color: 'bg-gradient-to-br from-zinc-500 to-zinc-600',
      name: 'Unknown'
    }
  };
  return configs[provider as keyof typeof configs] || configs.default;
};

// 獲取提供商顯示名稱
const getProviderDisplayName = (provider: string) => {
  return getProviderConfig(provider).name;
};

// 遮罩 API 金鑰
const maskApiKey = (apiKey: string) => {
  if (!apiKey) return '';
  if (apiKey.length <= 8) return apiKey;
  return `${apiKey.substring(0, 4)}${'*'.repeat(apiKey.length - 8)}${apiKey.substring(apiKey.length - 4)}`;
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 複製到剪貼板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    notification.toast.success('已複製', 'API 金鑰已複製到剪貼板');
  } catch (error) {
    notification.toast.error('複製失敗', '無法複製到剪貼板');
  }
};

// 處理刪除金鑰
const handleDeleteKey = (key: any) => {
  keyToDelete.value = key;
  showDeleteDialog.value = true;
};

// 確認刪除
const confirmDelete = () => {
  if (keyToDelete.value) {
    emit('delete-key', keyToDelete.value.id);
    showDeleteDialog.value = false;
    keyToDelete.value = null;
  }
};
</script> 