# Task ID: 28
# Title: 實作前端 Agent 搭建器界面
# Status: pending
# Dependencies: 27
# Priority: high
# Description: 開發直觀的可視化 Agent 搭建器，讓管理員可以透過前端界面創建和配置 Agent，包括基本設定、模型選擇、執行類型選擇和工具配置。
# Details:
使用 Vue 3 + TypeScript + Shadcn-Vue 開發:
1. **Agent 搭建器主頁面**: 表單式界面包含多個配置區塊
2. **基本資訊區**: Agent 名稱、描述、系統提示詞的輸入
3. **大腦配置區**: 
   - AI 模型下拉選單 (從 /api/admin/ai/models 取得)
   - API 金鑰下拉選單 (從 /api/admin/ai/keys 取得)
   - 溫度、max_tokens 等參數設定
4. **執行類型選擇**: 單選按鈕 (工具型 Agent | 圖譜型 Agent)
5. **工具箱選擇區**: 
   - 當選擇工具型時：核取方塊列表顯示所有可用工具
   - 當選擇圖譜型時：下拉選單選擇預定義圖譜範本
6. **預覽和儲存**: 顯示配置摘要，儲存按鈕調用 API 創建/更新 Agent
7. **Agent 列表頁**: 顯示現有 Agent，支援編輯、啟用/停用、刪除操作

# Test Strategy:
手動測試表單的所有輸入和驗證。測試工具選擇的互動邏輯。驗證 API 調用和錯誤處理。測試不同執行類型的界面切換。確認 Agent 創建後正確顯示在列表中。
