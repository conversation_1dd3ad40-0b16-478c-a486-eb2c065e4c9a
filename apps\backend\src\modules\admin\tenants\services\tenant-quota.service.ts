import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';

export interface TenantQuota {
  tenantId: string;
  maxUsers: number;
  maxProjects: number;
  maxStorage: number; // GB
  currentUsers: number;
  currentProjects: number;
  currentStorage: number; // GB
  usagePercentage: {
    users: number;
    projects: number;
    storage: number;
  };
}

export interface QuotaCheckResult {
  allowed: boolean;
  reason?: string;
  currentUsage?: number;
  limit?: number;
}

@Injectable()
export class TenantQuotaService {
  private readonly logger = new Logger(TenantQuotaService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 獲取租戶配額使用情況
   */
  async getTenantQuota(tenantId: string): Promise<TenantQuota> {
    const tenant = await this.prisma.tenants.findUnique({
      where: { id: tenantId },
      select: {
        id: true,
        max_users: true,
        max_projects: true,
        max_storage: true,
      },
    });

    if (!tenant) {
      throw new BadRequestException('租戶不存在');
    }

    // 計算當前使用量
    const [currentUsers, currentProjects, currentStorage] = await Promise.all([
      this.getCurrentUserCount(tenantId),
      this.getCurrentProjectCount(tenantId),
      this.getCurrentStorageUsage(tenantId),
    ]);

    const usagePercentage = {
      users: tenant.max_users ? (currentUsers / tenant.max_users) * 100 : 0,
      projects: tenant.max_projects ? (currentProjects / tenant.max_projects) * 100 : 0,
      storage: tenant.max_storage ? (currentStorage / tenant.max_storage) * 100 : 0,
    };

    return {
      tenantId,
      maxUsers: tenant.max_users || 0,
      maxProjects: tenant.max_projects || 0,
      maxStorage: tenant.max_storage || 0,
      currentUsers,
      currentProjects,
      currentStorage,
      usagePercentage,
    };
  }

  /**
   * 檢查是否可以添加新用戶
   */
  async canAddUser(tenantId: string): Promise<QuotaCheckResult> {
    const quota = await this.getTenantQuota(tenantId);

    if (quota.maxUsers === 0) {
      return { allowed: true }; // 無限制
    }

    if (quota.currentUsers >= quota.maxUsers) {
      return {
        allowed: false,
        reason: '已達到最大用戶數限制',
        currentUsage: quota.currentUsers,
        limit: quota.maxUsers,
      };
    }

    return { allowed: true };
  }

  /**
   * 檢查是否可以建立新專案
   */
  async canAddProject(tenantId: string): Promise<QuotaCheckResult> {
    const quota = await this.getTenantQuota(tenantId);

    if (quota.maxProjects === 0) {
      return { allowed: true }; // 無限制
    }

    if (quota.currentProjects >= quota.maxProjects) {
      return {
        allowed: false,
        reason: '已達到最大專案數限制',
        currentUsage: quota.currentProjects,
        limit: quota.maxProjects,
      };
    }

    return { allowed: true };
  }

  /**
   * 檢查儲存空間是否足夠
   */
  async canUseStorage(tenantId: string, additionalGB: number): Promise<QuotaCheckResult> {
    const quota = await this.getTenantQuota(tenantId);

    if (quota.maxStorage === 0) {
      return { allowed: true }; // 無限制
    }

    if (quota.currentStorage + additionalGB > quota.maxStorage) {
      return {
        allowed: false,
        reason: '儲存空間不足',
        currentUsage: quota.currentStorage,
        limit: quota.maxStorage,
      };
    }

    return { allowed: true };
  }

  /**
   * 獲取租戶配額警告
   */
  async getQuotaWarnings(tenantId: string): Promise<string[]> {
    const quota = await this.getTenantQuota(tenantId);
    const warnings: string[] = [];

    if (quota.usagePercentage.users > 80) {
      warnings.push(`用戶數使用率已達 ${quota.usagePercentage.users.toFixed(1)}%`);
    }

    if (quota.usagePercentage.projects > 80) {
      warnings.push(`專案數使用率已達 ${quota.usagePercentage.projects.toFixed(1)}%`);
    }

    if (quota.usagePercentage.storage > 80) {
      warnings.push(`儲存空間使用率已達 ${quota.usagePercentage.storage.toFixed(1)}%`);
    }

    return warnings;
  }

  private async getCurrentUserCount(tenantId: string): Promise<number> {
    return this.prisma.tenant_users.count({
      where: {
        tenant_id: tenantId,
        status: 'ACTIVE',
      },
    });
  }

  private async getCurrentProjectCount(tenantId: string): Promise<number> {
    return this.prisma.projects.count({
      where: { tenant_id: tenantId },
    });
  }

  private async getCurrentStorageUsage(tenantId: string): Promise<number> {
    // 這裡需要根據實際的檔案儲存實現來計算
    // 可能需要查詢檔案表或呼叫儲存服務 API
    // 暫時返回 0，等待實際的檔案大小欄位實現
    // const result = await this.prisma.photos.aggregate({
    //     where: { tenant_id },
    //     _sum: {
    //         fileSize: true, // 假設有 fileSize 欄位，單位為 bytes
    //     },
    // });

    // 轉換為 GB，這裡先返回 0 作為示例
    return 0; // (result._sum.file_size || 0) / (1024 * 1024 * 1024);
  }
}
