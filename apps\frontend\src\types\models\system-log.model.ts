import type { IPaginatedResponse } from './index';

/**
 * 系統日誌等級
 */
export type SystemLogLevel = 'INFO' | 'WARN' | 'ERROR' | 'DEBUG' | 'AUDIT';

/**
 * 系統日誌狀態
 */
export type SystemLogStatus = 'SUCCESS' | 'FAILURE' | 'PENDING';

/**
 * SystemLog Entity DTO
 */
export interface SystemLog {
  id: string;
  level: SystemLogLevel;
  message: string;
  user_id: string | null;
  user_type: 'system_users' | 'tenant_users' | null;
  target_resource: string | null;
  action: string;
  status: SystemLogStatus | null;
  ip: string | null;
  request_method: string | null;
  request_path: string | null;
  request_body: Record<string, any> | null;
  response_status_code: number | null;
  response_body: Record<string, any> | null;
  error_message: string | null;
  details: Record<string, any> | null;
  created_at: string;
  updated_at: string;
}

/**
 * 系統日誌查詢參數
 */
export interface SystemLogQuery {
  page?: number;
  limit?: number;
  search?: string;
  user_id?: string;
  target_resource?: string;
  action_types?: string[];
  start_date?: string;
  end_date?: string;
}

/**
 * 分頁系統日誌響應
 */
export type PaginatedSystemLogs = IPaginatedResponse<SystemLog>;

/**
 * 系統日誌統計數據
 */
export interface SystemLogStats {
  total_logs: number;
  logs_today: number;
  error_logs: number;
  audit_logs: number;
  unique_users: number;
}

/**
 * 可用的日誌操作類型
 */
export interface AvailableLogAction {
  action: string;
  count: number;
}

/**
 * 系統日誌報告生成 DTO
 */
export interface SystemLogReportDto {
  format: 'json' | 'csv' | 'xlsx';
  query?: Omit<SystemLogQuery, 'page' | 'limit'>;
} 