import { PartialType } from '@nestjs/swagger';
import { IsEmail, IsString, IsOptional, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SystemUserRole } from '@prisma/client';

export class UpdateSystemUserDto {
  @ApiProperty({ description: '系統使用者名稱', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '系統使用者電子郵件', required: false })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: '系統使用者角色',
    enum: SystemUserRole,
    required: false,
  })
  @IsEnum(SystemUserRole)
  @IsOptional()
  role?: SystemUserRole;

  @ApiProperty({
    description: '系統使用者狀態',
    enum: ['active', 'inactive'],
    required: false,
  })
  @IsString()
  @IsOptional()
  status?: 'active' | 'inactive';

  @ApiProperty({ description: '電話號碼', required: false })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({ description: '頭像 URL', required: false })
  @IsString()
  @IsOptional()
  avatar?: string;
}
