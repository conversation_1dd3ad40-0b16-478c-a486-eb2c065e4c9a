import { Injectable } from '@nestjs/common';
import { BaseStorageService } from '../base/base-storage.service';
import { StorageConfig, UploadOptions, FileInfo } from '../interfaces/storage.interface';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  HeadObjectCommand,
  ListObjectsV2Command,
  CopyObjectCommand,
  NoSuchKey,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import * as zlib from 'zlib';
import { promisify } from 'util';
import * as path from 'path';

/**
 * AWS S3 儲存服務
 */
@Injectable()
export class S3StorageService extends BaseStorageService {
  private readonly s3Client: S3Client;
  private readonly bucket: string;

  constructor(config: StorageConfig) {
    super(config);

    if (!config.bucket) {
      throw new Error('S3 bucket is required');
    }

    this.bucket = config.bucket;
    this.s3Client = new S3Client({
      region: config.region || 'us-east-1',
      credentials:
        config.accessKey && config.secretKey
          ? {
              accessKeyId: config.accessKey,
              secretAccessKey: config.secretKey,
            }
          : undefined,
      endpoint: config.endpoint,
    });

    this.logger.log(`S3 Storage initialized for bucket: ${this.bucket}`);
  }

  async uploadFile(
    file: Express.Multer.File | Buffer,
    options: UploadOptions = {},
  ): Promise<string> {
    try {
      this.validateFile(file);

      let buffer: Buffer;
      let originalName: string;
      let contentType: string;

      if (file instanceof Buffer) {
        buffer = file;
        originalName = options.filename || 'file';
        contentType = options.contentType || 'application/octet-stream';
      } else {
        buffer = Buffer.from(file.buffer);
        originalName = (file as Express.Multer.File).originalname || 'file';
        contentType =
          (file as Express.Multer.File).mimetype || this.getContentTypeFromExtension(originalName);
      }

      const filename = options.filename || this.generateUniqueFilename(originalName);
      const key = this.buildPath(options.path || '', filename);

      // 壓縮檔案（如果啟用）
      if (options.compress) {
        const gzipAsync = promisify(zlib.gzip);
        buffer = await gzipAsync(buffer);
        contentType = 'application/gzip';
      }

      const putCommand = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        Metadata: options.metadata,
        ACL: options.public ? 'public-read' : 'private',
      });

      await this.s3Client.send(putCommand);

      this.logger.log(`File uploaded to S3: ${key}`);
      return key;
    } catch (error) {
      return this.handleError('uploadFile', error);
    }
  }

  async uploadText(content: string, options: UploadOptions): Promise<string> {
    try {
      const buffer = Buffer.from(content, 'utf8');
      const uploadOptions = {
        ...options,
        contentType: options.contentType || 'text/plain',
      };
      return this.uploadFile(buffer, uploadOptions);
    } catch (error) {
      return this.handleError('uploadText', error);
    }
  }

  async downloadFile(filePath: string): Promise<Buffer> {
    try {
      const getCommand = new GetObjectCommand({
        Bucket: this.bucket,
        Key: filePath,
      });

      const response = await this.s3Client.send(getCommand);

      if (!response.Body) {
        throw new Error('Empty response body');
      }

      const chunks: Uint8Array[] = [];
      const reader = response.Body.transformToWebStream().getReader();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          chunks.push(value);
        }
      } finally {
        reader.releaseLock();
      }

      const buffer = Buffer.concat(chunks);
      this.logger.log(`File downloaded from S3: ${filePath}`);
      return buffer;
    } catch (error) {
      return this.handleError('downloadFile', error);
    }
  }

  async deleteFile(filePath: string): Promise<void> {
    try {
      const deleteCommand = new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: filePath,
      });

      await this.s3Client.send(deleteCommand);
      this.logger.log(`File deleted from S3: ${filePath}`);
    } catch (error) {
      return this.handleError('deleteFile', error);
    }
  }

  async fileExists(filePath: string): Promise<boolean> {
    try {
      const headCommand = new HeadObjectCommand({
        Bucket: this.bucket,
        Key: filePath,
      });

      await this.s3Client.send(headCommand);
      return true;
    } catch (error) {
      if (error instanceof NoSuchKey) {
        return false;
      }
      throw error;
    }
  }

  async getFileInfo(filePath: string): Promise<FileInfo> {
    try {
      const headCommand = new HeadObjectCommand({
        Bucket: this.bucket,
        Key: filePath,
      });

      const response = await this.s3Client.send(headCommand);

      return {
        path: filePath,
        size: response.ContentLength || 0,
        contentType: response.ContentType,
        lastModified: response.LastModified,
        metadata: response.Metadata,
      };
    } catch (error) {
      return this.handleError('getFileInfo', error);
    }
  }

  async listFiles(dirPath: string, recursive: boolean = false): Promise<string[]> {
    try {
      const prefix = dirPath.endsWith('/') ? dirPath : `${dirPath}/`;
      const files: string[] = [];
      let continuationToken: string | undefined;

      do {
        const listCommand = new ListObjectsV2Command({
          Bucket: this.bucket,
          Prefix: prefix,
          Delimiter: recursive ? undefined : '/',
          ContinuationToken: continuationToken,
        });

        const response = await this.s3Client.send(listCommand);

        if (response.Contents) {
          for (const object of response.Contents) {
            if (object.Key && object.Key !== prefix) {
              files.push(object.Key);
            }
          }
        }

        continuationToken = response.NextContinuationToken;
      } while (continuationToken);

      return files;
    } catch (error) {
      return this.handleError('listFiles', error);
    }
  }

  async getPublicUrl(filePath: string, expiresIn: number = 3600): Promise<string> {
    try {
      const getCommand = new GetObjectCommand({
        Bucket: this.bucket,
        Key: filePath,
      });

      const signedUrl = await getSignedUrl(this.s3Client, getCommand, {
        expiresIn,
      });

      return signedUrl;
    } catch (error) {
      return this.handleError('getPublicUrl', error);
    }
  }

  async copyFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      const copyCommand = new CopyObjectCommand({
        Bucket: this.bucket,
        CopySource: `${this.bucket}/${sourcePath}`,
        Key: destinationPath,
      });

      await this.s3Client.send(copyCommand);
      this.logger.log(`File copied in S3: ${sourcePath} -> ${destinationPath}`);
    } catch (error) {
      return this.handleError('copyFile', error);
    }
  }

  async moveFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      // S3 沒有移動操作，需要複製後刪除
      await this.copyFile(sourcePath, destinationPath);
      await this.deleteFile(sourcePath);

      this.logger.log(`File moved in S3: ${sourcePath} -> ${destinationPath}`);
    } catch (error) {
      return this.handleError('moveFile', error);
    }
  }

  async compressFiles(
    paths: string[],
    archivePath: string,
    format: 'zip' | 'gzip' = 'gzip',
  ): Promise<string> {
    try {
      if (format === 'gzip' && paths.length === 1) {
        // 單檔案 gzip 壓縮
        const sourcePath = paths[0];
        const sourceBuffer = await this.downloadFile(sourcePath);
        const gzipAsync = promisify(zlib.gzip);
        const compressedBuffer = await gzipAsync(sourceBuffer);

        const gzipPath = archivePath.endsWith('.gz') ? archivePath : `${archivePath}.gz`;
        await this.uploadFile(compressedBuffer, {
          filename: path.basename(gzipPath),
          path: path.dirname(gzipPath),
          contentType: 'application/gzip',
        });

        return gzipPath;
      }

      throw new Error(
        `Compression format '${format}' not supported for multiple files in S3 storage`,
      );
    } catch (error) {
      return this.handleError('compressFiles', error);
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      // 測試列出 bucket 內容
      const listCommand = new ListObjectsV2Command({
        Bucket: this.bucket,
        MaxKeys: 1,
      });

      await this.s3Client.send(listCommand);
      return true;
    } catch (error) {
      this.logger.error('S3 storage connection test failed', error);
      return false;
    }
  }
}
