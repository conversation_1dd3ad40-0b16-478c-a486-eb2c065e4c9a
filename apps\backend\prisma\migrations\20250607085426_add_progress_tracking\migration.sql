/*
  Warnings:

  - The values [TENANT_ADMIN,TENANT_MANAGER,TENANT_USER,TENANT_VIEWER] on the enum `TenantUserRole` will be removed. If these variants are still used in the database, this will fail.

*/
-- CreateEnum
CREATE TYPE "ProgressType" AS ENUM ('TASK_UPDATE', 'MILESTONE', 'PHOTO_EVIDENCE', 'STATUS_CHANGE', 'QUALITY_CHECK', 'RESOURCE_UPDATE', 'RISK_IDENTIFIED', 'ISSUE_REPORTED');

-- CreateEnum
CREATE TYPE "MilestoneStatus" AS ENUM ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'DELAYED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "ReportType" AS ENUM ('DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'PROJECT', 'CUSTOM');

-- <PERSON><PERSON><PERSON><PERSON>
BEGIN;
CREATE TYPE "TenantUserRole_new" AS ENUM ('OWNER', 'ADMIN', 'MEMBER', 'VIEWER');
ALTER TABLE "tenant_users" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "tenant_users" ALTER COLUMN "role" TYPE "TenantUserRole_new" USING ("role"::text::"TenantUserRole_new");
ALTER TYPE "TenantUserRole" RENAME TO "TenantUserRole_old";
ALTER TYPE "TenantUserRole_new" RENAME TO "TenantUserRole";
DROP TYPE "TenantUserRole_old";
ALTER TABLE "tenant_users" ALTER COLUMN "role" SET DEFAULT 'MEMBER';
COMMIT;

-- AlterTable
ALTER TABLE "tenant_users" ALTER COLUMN "role" SET DEFAULT 'MEMBER';

-- CreateTable
CREATE TABLE "progress_entries" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "progressType" "ProgressType" NOT NULL DEFAULT 'TASK_UPDATE',
    "progressValue" DOUBLE PRECISION,
    "status" TEXT,
    "notes" TEXT,
    "photoUrls" TEXT[],
    "metadata" JSONB,
    "projectId" TEXT,
    "taskId" TEXT,
    "userId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "recordedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "progress_entries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "project_milestones" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "targetDate" TIMESTAMP(3) NOT NULL,
    "completedAt" TIMESTAMP(3),
    "status" "MilestoneStatus" NOT NULL DEFAULT 'PENDING',
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "projectId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "project_milestones_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "progress_reports" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "reportType" "ReportType" NOT NULL DEFAULT 'WEEKLY',
    "period" TEXT NOT NULL,
    "totalTasks" INTEGER NOT NULL DEFAULT 0,
    "completedTasks" INTEGER NOT NULL DEFAULT 0,
    "inProgressTasks" INTEGER NOT NULL DEFAULT 0,
    "overdueTasks" INTEGER NOT NULL DEFAULT 0,
    "completionRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "predictedCompletionDate" TIMESTAMP(3),
    "riskLevel" TEXT,
    "recommendations" JSONB,
    "projectId" TEXT,
    "tenantId" TEXT NOT NULL,
    "generatedBy" TEXT NOT NULL,
    "reportDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "progress_reports_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "progress_entries_projectId_idx" ON "progress_entries"("projectId");

-- CreateIndex
CREATE INDEX "progress_entries_taskId_idx" ON "progress_entries"("taskId");

-- CreateIndex
CREATE INDEX "progress_entries_tenantId_idx" ON "progress_entries"("tenantId");

-- CreateIndex
CREATE INDEX "progress_entries_userId_idx" ON "progress_entries"("userId");

-- CreateIndex
CREATE INDEX "progress_entries_recordedAt_idx" ON "progress_entries"("recordedAt");

-- CreateIndex
CREATE INDEX "project_milestones_projectId_idx" ON "project_milestones"("projectId");

-- CreateIndex
CREATE INDEX "project_milestones_tenantId_idx" ON "project_milestones"("tenantId");

-- CreateIndex
CREATE INDEX "project_milestones_targetDate_idx" ON "project_milestones"("targetDate");

-- CreateIndex
CREATE INDEX "progress_reports_projectId_idx" ON "progress_reports"("projectId");

-- CreateIndex
CREATE INDEX "progress_reports_tenantId_idx" ON "progress_reports"("tenantId");

-- CreateIndex
CREATE INDEX "progress_reports_reportDate_idx" ON "progress_reports"("reportDate");

-- CreateIndex
CREATE UNIQUE INDEX "progress_reports_projectId_reportType_period_key" ON "progress_reports"("projectId", "reportType", "period");

-- AddForeignKey
ALTER TABLE "progress_entries" ADD CONSTRAINT "progress_entries_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "progress_entries" ADD CONSTRAINT "progress_entries_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "progress_entries" ADD CONSTRAINT "progress_entries_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_milestones" ADD CONSTRAINT "project_milestones_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_milestones" ADD CONSTRAINT "project_milestones_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "progress_reports" ADD CONSTRAINT "progress_reports_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "progress_reports" ADD CONSTRAINT "progress_reports_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
