import { tool } from '@langchain/core/tools';
import { MessageCenterService } from '@/modules/workspace/message-center/services/message-center.service';
import { z } from 'zod';

// Zod schema for getting notifications
const getNotificationsSchema = z.object({
  recipient_id: z
    .string()
    .optional()
    .describe('The ID of the user to get notifications for (if not provided, uses current user)'),
  workspace_id: z.string().optional().describe('Filter notifications by workspace ID'),
  limit: z
    .number()
    .int()
    .min(1)
    .max(100)
    .optional()
    .default(20)
    .describe('Maximum number of notifications to retrieve (1-100)'),
  offset: z
    .number()
    .int()
    .min(0)
    .optional()
    .default(0)
    .describe('Number of notifications to skip for pagination'),
});

/**
 * Creates a GetNotificationsTool using a simple function-based approach
 * @param messageCenterService - The message center service instance
 * @param tenantId - The tenant ID for isolation
 * @param currentUserId - The current user ID for default recipient
 * @returns A simple tool object compatible with LangChain
 */
export function createGetNotificationsTool(
  messageCenterService: MessageCenterService,
  tenantId: string,
  currentUserId: string,
) {
  return {
    name: 'get_notifications',
    description: `Retrieve notifications for a user or workspace.

This tool allows the AI agent to fetch notifications from the user's notification center.

Optional fields:
- recipient_id: The ID of the user to get notifications for (defaults to current user)
- workspace_id: Filter notifications by workspace ID
- limit: Maximum number of notifications to retrieve (1-100, defaults to 20)
- offset: Number of notifications to skip for pagination (defaults to 0)

Returns a JSON object with:
- message: Summary of the operation
- totalCount: Total number of notifications retrieved
- unreadCount: Number of unread notifications
- notifications: Array of notification objects
- pagination: Pagination information`,
    schema: getNotificationsSchema,
    call: async (input: any) => {
      try {
        const recipientId = input.recipient_id || currentUserId;

        const notifications = await messageCenterService.getNotifications(
          tenantId,
          recipientId,
          input.workspace_id,
          input.limit || 20,
          input.offset || 0,
        );

        const unreadCount = notifications.filter((n) => !n.is_read).length;

        const formattedNotifications = notifications.map((notification) => ({
          id: notification.id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          priority: notification.priority,
          isRead: notification.is_read,
          createdAt: notification.created_at,
          actionUrl: notification.action_url,
          entityType: notification.entity_type,
          entityId: notification.entity_id,
        }));

        return JSON.stringify(
          {
            message: `Retrieved ${notifications.length} notifications for user ${recipientId}`,
            totalCount: notifications.length,
            unreadCount,
            notifications: formattedNotifications,
            pagination: {
              limit: input.limit || 20,
              offset: input.offset || 0,
              hasMore: notifications.length === (input.limit || 20),
            },
          },
          null,
          2,
        );
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        throw new Error(`Failed to retrieve notifications: ${errorMessage}`);
      }
    },
  };
}

// Export the schema for use in other tools or tests
export { getNotificationsSchema }; 