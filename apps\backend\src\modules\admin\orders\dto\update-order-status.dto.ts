import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { OrderStatus } from '../interfaces/order.interface';

export class UpdateOrderStatusDto {
  @ApiProperty({
    description: '訂單狀態',
    enum: ['PENDING', 'COMPLETED', 'CANCELLED'],
    example: 'COMPLETED',
  })
  @IsNotEmpty({ message: '訂單狀態不可為空' })
  @IsEnum(OrderStatus, { message: '訂單狀態必須是 PENDING、COMPLETED 或 CANCELLED 其中之一' })
  status: OrderStatus;
}
