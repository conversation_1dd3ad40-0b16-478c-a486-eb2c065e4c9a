import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import {
  CreateRoleDto,
  UpdateRoleDto,
  RoleScope,
  PermissionDto,
  PermissionCategoryDto,
} from '../dto/role.dto';
import { v4 as uuidv4 } from 'uuid';
import { Prisma, roles } from '@prisma/client';
import { RoleHierarchyService } from './role-hierarchy.service';
import { UserRoleService } from './user-role.service';

// 自定義 Permission 類型，因為 Prisma 客戶端不再導出
// interface Permission {
//   id: string;
//   action: string;
//   subject: string;
//   conditions?: any;
//   description?: string;
//   categoryId?: string;
//   created_at: Date;
//   updated_at: Date;
// }

/**
 * 角色定義管理服務
 * 負責角色的 CRUD 操作、權限管理和角色定義相關功能
 * 與 RoleAssignmentService 分工：此服務管理角色定義，RoleAssignmentService 管理用戶角色指派
 */
@Injectable()
export class RolesService {
  private readonly logger = new Logger(RolesService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly roleHierarchyService: RoleHierarchyService,
    private readonly userRoleService: UserRoleService,
  ) {}

  /**
   * 讀取所有角色，可依區域篩選
   */
  async findAll(scope?: RoleScope): Promise<roles[]> {
    const where: Prisma.rolesWhereInput = {};
    if (scope) {
      where.scope = scope;
    }
    return this.prisma.roles.findMany({
      where,
      orderBy: { created_at: 'asc' },
    });
  }

  /**
   * 根據 ID 讀取角色詳情，包含權限和層級資訊
   */
  async findOne(id: string): Promise<roles> {
    const role = await this.prisma.roles.findUnique({
      where: { id },
    });
    if (!role) {
      throw new NotFoundException(`找不到 ID 為 ${id} 的角色`);
    }
    return role;
  }

  /**
   * 建立新角色
   */
  async create(createRoleDto: CreateRoleDto): Promise<roles> {
    const {
      name,
      display_name,
      description,
      scope,
      tenant_id,
      permissions,
      is_system_role,
      parent_role_id,
    } = createRoleDto;

    const existingRole = await this.prisma.roles.findUnique({ where: { name } });
    if (existingRole) {
      throw new ConflictException(`角色名稱 ${name} 已存在`);
    }
    if ((scope === RoleScope.TENANT || scope === RoleScope.WORKSPACE) && !tenant_id) {
      throw new BadRequestException('Tenant/Workspace scope roles must have a tenant_id.');
    }
    if (scope === RoleScope.SYSTEM && tenant_id) {
      throw new BadRequestException('System scope roles cannot have a tenant_id.');
    }

    return this.prisma.$transaction(async (tx) => {
      const role = await tx.roles.create({
        data: {
          id: uuidv4(),
          name,
          display_name,
          description,
          scope,
          is_system: is_system_role ?? false,
          tenants: tenant_id ? { connect: { id: tenant_id } } : undefined,
          roles: parent_role_id ? { connect: { id: parent_role_id } } : undefined,
        },
      });

      if (permissions && permissions.length > 0) {
        await this.createRolePermissionMappings(tx, role.id, permissions);
      }
      return role;
    });
  }

  /**
   * 更新角色
   */
  async update(id: string, updateRoleDto: UpdateRoleDto): Promise<roles> {
    const { name, display_name, description, scope, tenant_id, is_system_role, parent_role_id } =
      updateRoleDto;

    await this.findOne(id);
    // You might want to add more logic here, e.g., for super admins
    // if (existingRole.is_system && !isSuperAdmin) {
    //   throw new ForbiddenException('System roles can only be modified by Super Admins.');
    // }

    const data: Prisma.rolesUpdateInput = {
      name,
      display_name,
      description,
      scope,
      is_system: is_system_role,
      roles:
        parent_role_id !== undefined
          ? parent_role_id
            ? { connect: { id: parent_role_id } }
            : { disconnect: true }
          : undefined,
      tenants:
        tenant_id !== undefined
          ? tenant_id
            ? { connect: { id: tenant_id } }
            : { disconnect: true }
          : undefined,
    };

    return this.prisma.roles.update({ where: { id }, data });
  }

  /**
   * 刪除角色
   */
  async remove(id: string): Promise<{ success: boolean; message: string }> {
    await this.findOne(id);
    await this.prisma.roles.delete({ where: { id } });
    return { success: true, message: '角色已成功刪除' };
  }

  /**
   * 更新角色權限
   */
  async updateRolePermissions(
    roleId: string,
    permissionIds: string[],
  ): Promise<{ success: boolean; message: string }> {
    const role = await this.prisma.roles.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      throw new NotFoundException(`找不到 ID 為 ${roleId} 的角色`);
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.role_permissions.deleteMany({
        where: { role_id: roleId },
      });

      if (permissionIds && permissionIds.length > 0) {
        await this.createRolePermissionMappings(tx, roleId, permissionIds);
      }
    });

    return { success: true, message: '角色權限已更新' };
  }

  /**
   * 讀取指定角色的使用者數量和詳細統計
   */
  async getUserCount(roleId: string): Promise<number> {
    const systemUserCount = await this.prisma.system_user_roles.count({
      where: { role_id: roleId },
    });

    const tenantUserCount = await this.prisma.tenant_user_roles.count({
      where: { role_id: roleId },
    });

    return systemUserCount + tenantUserCount;
  }

  /**
   * 獲取角色的詳細用戶統計資訊
   */
  async getRoleUserStatistics(roleId: string): Promise<{
    totalUsers: number;
    systemUsers: number;
    tenantUsers: number;
    usersByTenant?: Record<string, number>;
  }> {
    const [systemUserCount, tenantUserRoles] = await Promise.all([
      this.prisma.system_user_roles.count({
        where: { role_id: roleId },
      }),
      this.prisma.tenant_user_roles.findMany({
        where: { role_id: roleId },
        include: {
          tenant_user: {
            select: { tenant_id: true },
          },
        },
      }),
    ]);

    const tenantUserCount = tenantUserRoles.length;

    // 按租戶統計用戶數量
    const usersByTenant: Record<string, number> = {};
    tenantUserRoles.forEach((userRole) => {
      const tenantId = userRole.tenant_user.tenant_id;
      usersByTenant[tenantId] = (usersByTenant[tenantId] || 0) + 1;
    });

    return {
      totalUsers: systemUserCount + tenantUserCount,
      systemUsers: systemUserCount,
      tenantUsers: tenantUserCount,
      usersByTenant: Object.keys(usersByTenant).length > 0 ? usersByTenant : undefined,
    };
  }

  /**
   * 格式化權限名稱供前端顯示
   */
  private formatPermissionName(permission: { action: string; subject: string }): string {
    return `${permission.action}:${permission.subject}`;
  }

  /**
   * 建立角色權限映射
   */
  private async createRolePermissionMappings(
    tx: Prisma.TransactionClient,
    roleId: string,
    permissionIds: string[],
  ): Promise<void> {
    const data = permissionIds.map((permissionId) => ({
      id: uuidv4(),
      role_id: roleId,
      permission_id: permissionId,
    }));
    await tx.role_permissions.createMany({ data });
  }

  /**
   * 根據名稱和租戶 ID 查詢角色
   */
  async findByNameAndTenant(name: string, tenantId: string | null): Promise<roles | null> {
    return this.prisma.roles.findFirst({
      where: {
        name,
        tenant_id: tenantId,
      },
    });
  }

  /**
   * 獲取所有權限
   */
  async getAllPermissions(): Promise<PermissionDto[]> {
    const permissions = await this.prisma.permissions.findMany();
    return this.mapPermissions(permissions);
  }

  /**
   * 獲取所有權限分類
   */
  async getAllPermissionCategories(): Promise<PermissionCategoryDto[]> {
    const categories = await this.prisma.permission_categories.findMany({
      where: { is_active: true },
      orderBy: { sort_order: 'asc' },
    });
    return this.mapPermissionCategories(categories);
  }

  private mapPermissions(permissions: Prisma.permissionsGetPayload<null>[]): PermissionDto[] {
    if (!permissions || permissions.length === 0) return [];
    return permissions.map((p) => ({
      id: p.id,
      action: p.action,
      subject: p.subject,
      name: p.name || '',
      description: p.description || '',
      category_id: p.category_id || '',
      conditions: p.conditions,
    }));
  }

  private mapPermissionCategories(
    categories: Prisma.permission_categoriesGetPayload<null>[],
  ): PermissionCategoryDto[] {
    return categories.map((c) => ({
      id: c.id,
      name: c.name,
      description: c.description || '',
      icon: c.icon || undefined,
      sortOrder: c.sort_order,
      isActive: c.is_active,
    }));
  }

  // ==================== 進階角色分析功能 ====================

  /**
   * 獲取完整的角色資訊（包含層級、權限、用戶等）
   */
  async getCompleteRoleInfo(roleId: string) {
    this.logger.debug(`Fetching complete role info for role: ${roleId}`);

    try {
      const [role, hierarchy, permissions, users] = await Promise.all([
        this.findOne(roleId),
        this.roleHierarchyService.getRoleHierarchy(roleId),
        this.getRolePermissions(roleId),
        this.getRoleUsers(roleId),
      ]);

      return {
        role,
        hierarchy,
        permissions,
        users,
        metadata: {
          lastUpdated: new Date(),
          version: '1.0',
          completeness: this.calculateCompletenessScore(role, hierarchy, permissions, users),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to fetch complete role info for ${roleId}:`, error.message);
      throw error;
    }
  }

  /**
   * 獲取角色摘要資訊
   */
  async getRoleSummary(roleId: string) {
    this.logger.debug(`Generating role summary for: ${roleId}`);

    try {
      const completeInfo = await this.getCompleteRoleInfo(roleId);

      return {
        basic: {
          id: completeInfo.role.id,
          name: completeInfo.role.name,
          display_name: completeInfo.role.display_name,
          scope: completeInfo.role.scope,
          is_system: completeInfo.role.is_system,
        },
        stats: {
          totalUsers: completeInfo.users.total.length,
          systemUsers: completeInfo.users.system.length,
          tenantUsers: completeInfo.users.tenant.length,
          totalPermissions: completeInfo.permissions.direct.length,
          effectivePermissions: completeInfo.permissions.effective.length,
          hierarchyLevel: completeInfo.hierarchy.level,
          childRolesCount: completeInfo.hierarchy.child_role_ids.length,
        },
        health: {
          isActive: completeInfo.users.total.length > 0,
          hasPermissions: completeInfo.permissions.effective.length > 0,
          isWellStructured: completeInfo.hierarchy.level >= 0,
        },
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to generate role summary for ${roleId}:`, error.message);
      throw error;
    }
  }

  /**
   * 批量獲取角色摘要
   */
  async getBatchRoleSummaries(roleIds: string[]) {
    this.logger.debug(`Generating batch summaries for ${roleIds.length} roles`);

    try {
      const summaries = await Promise.allSettled(
        roleIds.map((roleId) => this.getRoleSummary(roleId)),
      );

      const results = summaries.map((result, index) => ({
        roleId: roleIds[index],
        success: result.status === 'fulfilled',
        data: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason.message : null,
      }));

      const successCount = results.filter((r) => r.success).length;
      const failureCount = results.length - successCount;

      return {
        results,
        summary: {
          total: roleIds.length,
          successful: successCount,
          failed: failureCount,
          successRate: Math.round((successCount / roleIds.length) * 100) / 100,
        },
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to generate batch role summaries:', error.message);
      throw error;
    }
  }

  /**
   * 執行角色健康檢查
   */
  async performRoleHealthCheck(roleId: string) {
    this.logger.debug(`Performing health check for role: ${roleId}`);

    try {
      const completeInfo = await this.getCompleteRoleInfo(roleId);
      const issues: string[] = [];
      const warnings: string[] = [];
      const suggestions: string[] = [];

      // 檢查循環引用
      const circularReference = await this.checkCircularReference(roleId);
      if (circularReference.hasCircularReference) {
        issues.push(`檢測到循環引用: ${circularReference.path.join(' -> ')}`);
      }

      // 檢查孤立角色
      if (
        !completeInfo.hierarchy.parent_role_id &&
        completeInfo.hierarchy.child_role_ids.length === 0 &&
        completeInfo.users.total.length === 0
      ) {
        warnings.push('此角色似乎是孤立的（無父級、子級或使用者）');
        suggestions.push('考慮刪除此角色或為其分配使用者');
      }

      // 檢查權限繼承
      const inheritanceIssues = await this.checkPermissionInheritance(roleId);
      if (inheritanceIssues.length > 0) {
        issues.push(...inheritanceIssues);
      }

      // 檢查租戶一致性
      if (completeInfo.role.scope === 'TENANT' && !completeInfo.role.tenant_id) {
        issues.push('租戶範圍的角色缺少租戶 ID');
      }

      // 檢查使用者數量
      if (completeInfo.users.total.length > 100) {
        warnings.push('此角色分配給過多使用者，可能需要拆分');
      }

      const healthScore = this.calculateHealthScore(issues, warnings);

      return {
        roleId,
        healthScore,
        status: this.determineHealthStatus(healthScore),
        checks: {
          circularReference: !circularReference.hasCircularReference,
          permissionInheritance: inheritanceIssues.length === 0,
          tenantConsistency: !(
            completeInfo.role.scope === 'TENANT' && !completeInfo.role.tenant_id
          ),
          userAssignment: completeInfo.users.total.length > 0,
          structuralIntegrity: completeInfo.hierarchy.level >= 0,
        },
        issues,
        warnings,
        suggestions,
        checkedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to perform health check for role ${roleId}:`, error.message);
      throw error;
    }
  }

  /**
   * 分析角色使用情況
   */
  async analyzeRoleUsage(roleId: string) {
    this.logger.debug(`Analyzing usage patterns for role: ${roleId}`);

    try {
      const completeInfo = await this.getCompleteRoleInfo(roleId);

      // 使用模式分析
      const patterns = {
        userDistribution: this.analyzeUserDistribution(completeInfo.users),
        permissionUtilization: this.analyzePermissionUtilization(completeInfo.permissions),
        hierarchyPosition: this.analyzeHierarchyPosition(completeInfo.hierarchy),
        temporalPatterns: await this.analyzeTemporalPatterns(roleId),
      };

      // 影響範圍分析
      const impact = {
        directUsers: completeInfo.users.total.length,
        indirectUsers: await this.calculateIndirectUsers(roleId),
        affectedTenants: this.calculateAffectedTenants(completeInfo.users),
        criticalPermissions: this.identifyCriticalPermissions(completeInfo.permissions),
        dependentRoles: completeInfo.hierarchy.child_role_ids.length,
      };

      // 風險評估
      const riskFactors = this.assessRiskFactors(patterns, impact);

      return {
        roleId,
        patterns,
        impact,
        riskAssessment: {
          overallRisk: riskFactors.overall,
          factors: riskFactors.factors,
          recommendations: this.generateUsageRecommendations(patterns, impact),
        },
        analyzedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to analyze role usage for ${roleId}:`, error.message);
      throw error;
    }
  }

  /**
   * 獲取系統概覽
   */
  async getSystemOverview() {
    this.logger.debug('Generating system overview');

    try {
      const [
        allRoles,
        totalSystemUsers,
        totalTenantUsers,
        maxDepth,
        orphanedRoles,
        roleUsageStats,
      ] = await Promise.all([
        this.findAll(),
        this.prisma.system_users.count(),
        this.prisma.tenant_users.count(),
        this.calculateMaxHierarchyDepth(),
        this.findOrphanedRoles(),
        this.getRoleUsageStatistics(),
      ]);

      const rolesByScope = {
        SYSTEM: allRoles.filter((r) => r.scope === 'SYSTEM').length,
        TENANT: allRoles.filter((r) => r.scope === 'TENANT').length,
        WORKSPACE: allRoles.filter((r) => r.scope === 'WORKSPACE').length,
      };

      const systemHealth = {
        totalRoles: allRoles.length,
        activeRoles: allRoles.length - orphanedRoles.length,
        orphanedRoles: orphanedRoles.length,
        maxHierarchyDepth: maxDepth,
        averageUsersPerRole: Math.round((totalSystemUsers + totalTenantUsers) / allRoles.length),
      };

      return {
        overview: {
          roles: {
            total: allRoles.length,
            byScope: rolesByScope,
            systemRoles: allRoles.filter((r) => r.is_system).length,
            customRoles: allRoles.filter((r) => !r.is_system).length,
          },
          users: {
            systemUsers: totalSystemUsers,
            tenantUsers: totalTenantUsers,
            total: totalSystemUsers + totalTenantUsers,
          },
          usage: roleUsageStats,
        },
        health: systemHealth,
        recommendations: this.generateSystemRecommendations(systemHealth, orphanedRoles),
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to generate system overview:', error.message);
      throw error;
    }
  }

  /**
   * 獲取角色優化建議
   */
  async getRoleOptimizationSuggestions(options?: {
    includeAutoApplicable?: boolean;
    priority?: 'low' | 'medium' | 'high';
  }) {
    this.logger.debug('Generating role optimization suggestions');

    try {
      const allRoles = await this.findAll();
      const suggestions: any[] = [];

      for (const role of allRoles) {
        try {
          const usage = await this.analyzeRoleUsage(role.id);
          const health = await this.performRoleHealthCheck(role.id);

          // 基於使用分析的建議
          if (usage.patterns.userDistribution.isEmpty) {
            suggestions.push({
              type: 'unused_role',
              roleId: role.id,
              roleName: role.name,
              priority: 'medium',
              description: '此角色未分配給任何使用者',
              action: 'consider_removal',
              autoApplicable: false,
              estimatedImpact: 'low',
            });
          }

          // 基於健康檢查的建議
          if (health.healthScore < 0.7) {
            suggestions.push({
              type: 'health_issue',
              roleId: role.id,
              roleName: role.name,
              priority: 'high',
              description: `角色健康分數較低 (${Math.round(health.healthScore * 100)}%)`,
              action: 'review_structure',
              autoApplicable: false,
              estimatedImpact: 'medium',
              issues: health.issues,
            });
          }

          // 過度使用的角色
          if (usage.impact.directUsers > 50) {
            suggestions.push({
              type: 'overused_role',
              roleId: role.id,
              roleName: role.name,
              priority: 'medium',
              description: `角色分配給過多使用者 (${usage.impact.directUsers})`,
              action: 'consider_splitting',
              autoApplicable: false,
              estimatedImpact: 'high',
            });
          }
        } catch (error) {
          // 忽略單個角色的錯誤，繼續處理其他角色
          continue;
        }
      }

      // 過濾建議
      let filteredSuggestions = suggestions;
      if (options?.priority) {
        filteredSuggestions = suggestions.filter((s) => s.priority === options.priority);
      }
      if (options?.includeAutoApplicable) {
        filteredSuggestions = filteredSuggestions.filter((s) => s.autoApplicable);
      }

      return {
        suggestions: filteredSuggestions,
        summary: {
          totalSuggestions: filteredSuggestions.length,
          highPriority: filteredSuggestions.filter((s) => s.priority === 'high').length,
          autoApplicable: filteredSuggestions.filter((s) => s.autoApplicable).length,
          estimatedImpact: this.calculateOverallImpact(filteredSuggestions),
        },
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to generate optimization suggestions:', error.message);
      throw error;
    }
  }

  /**
   * 角色比較功能
   */
  async compareRoles(roleId1: string, roleId2: string) {
    this.logger.debug(`Comparing roles: ${roleId1} vs ${roleId2}`);

    try {
      const [role1Info, role2Info] = await Promise.all([
        this.getCompleteRoleInfo(roleId1),
        this.getCompleteRoleInfo(roleId2),
      ]);

      // 比較權限
      const role1Permissions = new Set(role1Info.permissions.effective);
      const role2Permissions = new Set(role2Info.permissions.effective);

      const commonPermissions = [...role1Permissions].filter((p) => role2Permissions.has(p));
      const onlyInRole1 = [...role1Permissions].filter((p) => !role2Permissions.has(p));
      const onlyInRole2 = [...role2Permissions].filter((p) => !role1Permissions.has(p));

      // 比較用戶
      const role1UserIds = new Set(role1Info.users.total.map((u) => u.user_id));
      const role2UserIds = new Set(role2Info.users.total.map((u) => u.user_id));

      const commonUsers = [...role1UserIds].filter((u) => role2UserIds.has(u));
      const onlyInRole1Users = [...role1UserIds].filter((u) => !role2UserIds.has(u));
      const onlyInRole2Users = [...role2UserIds].filter((u) => !role1UserIds.has(u));

      // 計算相似度
      const permissionSimilarity =
        commonPermissions.length / Math.max(role1Permissions.size, role2Permissions.size);
      const userSimilarity = commonUsers.length / Math.max(role1UserIds.size, role2UserIds.size);
      const overallSimilarity = (permissionSimilarity + userSimilarity) / 2;

      return {
        role1: role1Info.role,
        role2: role2Info.role,
        differences: {
          permissions: {
            onlyInRole1,
            onlyInRole2,
            common: commonPermissions,
          },
          users: {
            onlyInRole1: onlyInRole1Users.length,
            onlyInRole2: onlyInRole2Users.length,
            common: commonUsers.length,
          },
          hierarchy: {
            levelDifference: Math.abs(role1Info.hierarchy.level - role2Info.hierarchy.level),
            differentParents:
              role1Info.hierarchy.parent_role_id !== role2Info.hierarchy.parent_role_id,
            differentChildren:
              role1Info.hierarchy.child_role_ids.length !==
              role2Info.hierarchy.child_role_ids.length,
          },
        },
        similarity: {
          permissionSimilarity: Math.round(permissionSimilarity * 100) / 100,
          userSimilarity: Math.round(userSimilarity * 100) / 100,
          overallSimilarity: Math.round(overallSimilarity * 100) / 100,
        },
        recommendations: this.generateComparisonRecommendations(
          role1Info,
          role2Info,
          overallSimilarity,
        ),
        comparedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to compare roles ${roleId1} and ${roleId2}:`, error.message);
      throw error;
    }
  }

  // ==================== 進階分析輔助方法 ====================

  private async getRolePermissions(roleId: string) {
    const directPermissions = await this.prisma.role_permissions.findMany({
      where: { role_id: roleId },
      include: { permissions: true },
    });

    // 獲取繼承的權限
    const inheritedPermissions = await this.roleHierarchyService.getInheritedPermissions(roleId);
    const effectivePermissions = await this.roleHierarchyService.getEffectivePermissions(roleId);

    return {
      direct: directPermissions.map((rp) => rp.permissions.id),
      effective: effectivePermissions,
      inherited: inheritedPermissions,
    };
  }

  private async getRoleUsers(roleId: string) {
    const [systemUsers, tenantUsers] = await Promise.all([
      this.prisma.system_user_roles.findMany({
        where: { role_id: roleId },
        include: { system_user: true },
      }),
      this.prisma.tenant_user_roles.findMany({
        where: { role_id: roleId },
        include: { tenant_user: true },
      }),
    ]);

    const systemUserData = systemUsers.map((sur) => ({
      user_id: sur.system_user.id,
      user_type: 'system' as const,
      email: sur.system_user.email,
      name: sur.system_user.name,
    }));

    const tenantUserData = tenantUsers.map((tur) => ({
      user_id: tur.tenant_user.id,
      user_type: 'tenant' as const,
      email: tur.tenant_user.email,
      name: tur.tenant_user.name,
    }));

    return {
      system: systemUserData,
      tenant: tenantUserData,
      total: [...systemUserData, ...tenantUserData],
    };
  }

  private calculateCompletenessScore(
    role: any,
    hierarchy: any,
    permissions: any,
    users: any,
  ): number {
    let score = 0;
    const maxScore = 4;

    if (role.name && role.display_name) score += 1;
    if (hierarchy.level >= 0) score += 1;
    if (permissions.effective.length > 0) score += 1;
    if (users.total.length > 0) score += 1;

    return score / maxScore;
  }

  private async checkCircularReference(
    roleId: string,
  ): Promise<{ hasCircularReference: boolean; path: string[] }> {
    const visited = new Set<string>();
    const path: string[] = [];

    const dfs = async (currentRoleId: string): Promise<boolean> => {
      if (visited.has(currentRoleId)) {
        return true; // 找到循環
      }

      visited.add(currentRoleId);
      path.push(currentRoleId);

      try {
        const hierarchy = await this.roleHierarchyService.getRoleHierarchy(currentRoleId);
        if (hierarchy.parent_role_id) {
          const hasCircular = await dfs(hierarchy.parent_role_id);
          if (hasCircular) return true;
        }
      } catch (error) {
        // 忽略錯誤，繼續
      }

      path.pop();
      visited.delete(currentRoleId);
      return false;
    };

    const hasCircularReference = await dfs(roleId);
    return { hasCircularReference, path };
  }

  private async checkPermissionInheritance(roleId: string): Promise<string[]> {
    const issues: string[] = [];
    // 實現權限繼承檢查邏輯
    // 這裡可以檢查父角色的權限是否正確繼承等
    // TODO: 實現具體的權限繼承檢查邏輯
    console.log(`檢查角色 ${roleId} 的權限繼承`);
    return issues;
  }

  private calculateHealthScore(issues: string[], warnings: string[]): number {
    const issueWeight = 0.3;
    const warningWeight = 0.1;
    const maxScore = 1.0;

    const deduction = issues.length * issueWeight + warnings.length * warningWeight;
    return Math.max(0, maxScore - deduction);
  }

  private determineHealthStatus(score: number): 'excellent' | 'good' | 'fair' | 'poor' {
    if (score >= 0.9) return 'excellent';
    if (score >= 0.7) return 'good';
    if (score >= 0.5) return 'fair';
    return 'poor';
  }

  private analyzeUserDistribution(users: any) {
    return {
      isEmpty: users.total.length === 0,
      systemUserRatio: users.system.length / Math.max(users.total.length, 1),
      tenantUserRatio: users.tenant.length / Math.max(users.total.length, 1),
      totalUsers: users.total.length,
    };
  }

  private analyzePermissionUtilization(permissions: any) {
    return {
      directCount: permissions.direct.length,
      effectiveCount: permissions.effective.length,
      inheritedCount: permissions.inherited.length,
      utilizationRatio: permissions.direct.length / Math.max(permissions.effective.length, 1),
    };
  }

  private analyzeHierarchyPosition(hierarchy: any) {
    return {
      level: hierarchy.level,
      hasParent: !!hierarchy.parent_role_id,
      hasChildren: hierarchy.child_role_ids.length > 0,
      childrenCount: hierarchy.child_role_ids.length,
    };
  }

  private async analyzeTemporalPatterns(roleId: string) {
    // 這裡可以實現時間模式分析，例如角色建立時間、最後修改時間等
    // TODO: 實現具體的時間模式分析邏輯
    console.log(`分析角色 ${roleId} 的時間模式`);
    return {
      createdRecently: false,
      modifiedRecently: false,
      usagePattern: 'stable',
    };
  }

  private async calculateIndirectUsers(roleId: string): Promise<number> {
    // 計算通過子角色間接受影響的用戶數量
    try {
      const hierarchy = await this.roleHierarchyService.getRoleHierarchy(roleId);
      let indirectCount = 0;

      for (const childRoleId of hierarchy.child_role_ids) {
        try {
          const childUsers = await this.getRoleUsers(childRoleId);
          indirectCount += childUsers.total.length;
        } catch (error) {
          // 忽略錯誤
        }
      }

      return indirectCount;
    } catch (error) {
      return 0;
    }
  }

  private calculateAffectedTenants(users: any): number {
    const tenantIds = new Set(users.tenant.map((user: any) => user.tenant_id).filter(Boolean));
    return tenantIds.size;
  }

  private identifyCriticalPermissions(permissions: any): string[] {
    // 識別關鍵權限，例如管理權限、刪除權限等
    return permissions.effective.filter(() => {
      // 這裡可以實現根據權限類型判斷是否為關鍵權限的邏輯
      return false; // 暫時返回空陣列
    });
  }

  private assessRiskFactors(patterns: any, impact: any) {
    const factors: string[] = [];
    let overallRisk: 'low' | 'medium' | 'high' = 'low';

    if (impact.directUsers > 50) {
      factors.push('高用戶數量');
      overallRisk = 'medium';
    }

    if (impact.criticalPermissions.length > 0) {
      factors.push('包含關鍵權限');
      overallRisk = 'high';
    }

    if (patterns.userDistribution.isEmpty) {
      factors.push('無用戶分配');
    }

    return { overall: overallRisk, factors };
  }

  private generateUsageRecommendations(patterns: any, impact: any): string[] {
    const recommendations: string[] = [];

    if (patterns.userDistribution.isEmpty) {
      recommendations.push('考慮刪除未使用的角色');
    }

    if (impact.directUsers > 50) {
      recommendations.push('考慮將角色拆分為更細粒度的角色');
    }

    if (patterns.permissionUtilization.utilizationRatio < 0.5) {
      recommendations.push('檢查權限配置，可能存在冗餘權限');
    }

    return recommendations;
  }

  private async calculateMaxHierarchyDepth(): Promise<number> {
    const allRoles = await this.findAll();
    let maxDepth = 0;

    for (const role of allRoles) {
      try {
        const hierarchy = await this.roleHierarchyService.getRoleHierarchy(role.id);
        maxDepth = Math.max(maxDepth, hierarchy.level);
      } catch (error) {
        // 忽略錯誤，繼續處理其他角色
      }
    }

    return maxDepth;
  }

  private async findOrphanedRoles(): Promise<string[]> {
    const allRoles = await this.findAll();
    const orphaned: string[] = [];

    for (const role of allRoles) {
      try {
        const hierarchy = await this.roleHierarchyService.getRoleHierarchy(role.id);
        if (!hierarchy.parent_role_id && hierarchy.child_role_ids.length === 0) {
          const users = await this.userRoleService.getUsersByRole(role.id);
          if (users.length === 0) {
            orphaned.push(role.id);
          }
        }
      } catch (error) {
        // 忽略錯誤
      }
    }

    return orphaned;
  }

  private async getRoleUsageStatistics() {
    const allRoles = await this.findAll();
    const roleStats: Array<{
      roleId: string;
      roleName: string;
      userCount: number;
    }> = [];

    for (const role of allRoles) {
      try {
        const userCount = await this.getUserCount(role.id);
        roleStats.push({
          roleId: role.id,
          roleName: role.name,
          userCount,
        });
      } catch (error) {
        // 忽略錯誤
      }
    }

    roleStats.sort((a, b) => b.userCount - a.userCount);

    return {
      mostUsed: roleStats.slice(0, 5),
      leastUsed: roleStats.slice(-5).reverse(),
    };
  }

  private generateSystemRecommendations(systemHealth: any, orphanedRoles: string[]): string[] {
    const recommendations: string[] = [];

    if (orphanedRoles.length > 0) {
      recommendations.push(`發現 ${orphanedRoles.length} 個孤立角色，建議檢查並清理`);
    }

    if (systemHealth.maxHierarchyDepth > 5) {
      recommendations.push('角色層級過深，建議簡化角色結構');
    }

    if (systemHealth.averageUsersPerRole < 1) {
      recommendations.push('平均每個角色的用戶數過少，建議合併相似角色');
    }

    return recommendations;
  }

  private calculateOverallImpact(suggestions: any[]): 'low' | 'medium' | 'high' {
    const highImpactCount = suggestions.filter((s) => s.estimatedImpact === 'high').length;
    const mediumImpactCount = suggestions.filter((s) => s.estimatedImpact === 'medium').length;

    if (highImpactCount > 0) return 'high';
    if (mediumImpactCount > 0) return 'medium';
    return 'low';
  }

  private generateComparisonRecommendations(
    role1Info: any,
    role2Info: any,
    similarity: number,
  ): string[] {
    const recommendations: string[] = [];

    if (similarity > 0.8) {
      recommendations.push('角色高度相似，考慮合併或重新設計');
    }

    if (role1Info.users.total.length === 0 || role2Info.users.total.length === 0) {
      recommendations.push('其中一個角色未被使用，考慮刪除');
    }

    if (Math.abs(role1Info.hierarchy.level - role2Info.hierarchy.level) > 2) {
      recommendations.push('角色層級差異較大，檢查是否需要調整層級關係');
    }

    return recommendations;
  }
}
