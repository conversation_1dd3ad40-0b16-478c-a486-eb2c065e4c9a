import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { TenantsService } from '../tenants.service';
import { IS_PUBLIC_KEY } from '../../../../common/decorators/public.decorator';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class TenantInterceptor implements NestInterceptor {
  constructor(
    private readonly tenantsService: TenantsService,
    private readonly reflector: Reflector,
    private readonly cls: ClsService,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException('使用者未授權 (攔截器)');
    }

    // 對於系統管理員（SUPER_ADMIN, SYSTEM_ADMIN），可能沒有 tenant_id
    const isSystemAdmin = ['SUPER_ADMIN', 'SYSTEM_ADMIN'].includes(user.role);

    if (user.tenantId) {
      try {
        const tenant = await this.tenantsService.findOne(user.tenantId);
        if (!tenant) {
          console.warn(`TenantInterceptor: Tenant not found for id: ${user.tenant_id}`);
          throw new UnauthorizedException('使用者關聯的租戶無效');
        }
        request.tenant = tenant;
        request.tenantId = tenant.id;
        this.cls.set('tenant_id', tenant.id);
      } catch (error) {
        console.error(`TenantInterceptor Error fetching tenant ${user.tenant_id}:`, error);
        throw new UnauthorizedException('無法驗證使用者租戶資訊');
      }
    } else if (!isSystemAdmin) {
      // 非系統管理員必須有 tenant_id
      throw new UnauthorizedException('使用者必須關聯到租戶');
    }

    return next.handle();
  }
}
