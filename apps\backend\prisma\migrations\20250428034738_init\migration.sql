/*
  Warnings:

  - The `role` column on the `users` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('SUPER_ADMIN', 'SYSTEM_ADMIN', 'TENANT_ADMIN', 'TENANT_USER');

-- AlterTable
ALTER TABLE "users" DROP COLUMN "role",
ADD COLUMN     "role" "UserRole" NOT NULL DEFAULT 'TENANT_USER';

-- DropEnum
DROP TYPE "Roles";

-- CreateIndex
CREATE INDEX "users_role_idx" ON "users"("role");
