import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * AI 服務基礎異常類
 */
export abstract class BaseAiException extends HttpException {
  public readonly provider?: string;
  public readonly model?: string;
  public readonly retryable: boolean;
  public readonly severity: 'low' | 'medium' | 'high' | 'critical';

  constructor(
    message: string,
    status: HttpStatus,
    provider?: string,
    model?: string,
    retryable: boolean = false,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
  ) {
    super(message, status);
    this.provider = provider;
    this.model = model;
    this.retryable = retryable;
    this.severity = severity;
    this.name = this.constructor.name;
  }
}

/**
 * API 金鑰相關錯誤
 */
export class AiApiKeyException extends BaseAiException {
  constructor(message = 'AI API 金鑰無效或已過期', provider?: string) {
    super(message, HttpStatus.UNAUTHORIZED, provider, undefined, false, 'high');
  }
}

/**
 * API 配額超限錯誤
 */
export class AiQuotaExceededException extends BaseAiException {
  constructor(message = 'AI API 配額已超限', provider?: string, model?: string) {
    super(message, HttpStatus.TOO_MANY_REQUESTS, provider, model, true, 'medium');
  }
}

/**
 * 模型不可用錯誤
 */
export class AiModelUnavailableException extends BaseAiException {
  constructor(message = 'AI 模型暫時不可用', provider?: string, model?: string) {
    super(message, HttpStatus.SERVICE_UNAVAILABLE, provider, model, true, 'medium');
  }
}

/**
 * 請求超時錯誤
 */
export class AiTimeoutException extends BaseAiException {
  constructor(message = 'AI 服務請求超時', provider?: string, model?: string) {
    super(message, HttpStatus.REQUEST_TIMEOUT, provider, model, true, 'medium');
  }
}

/**
 * 網路連接錯誤
 */
export class AiNetworkException extends BaseAiException {
  constructor(message = 'AI 服務網路連接失敗', provider?: string) {
    super(message, HttpStatus.BAD_GATEWAY, provider, undefined, true, 'medium');
  }
}

/**
 * 服務內部錯誤
 */
export class AiInternalException extends BaseAiException {
  constructor(message = 'AI 服務內部錯誤', provider?: string, model?: string) {
    super(message, HttpStatus.INTERNAL_SERVER_ERROR, provider, model, true, 'high');
  }
}

/**
 * 輸入驗證錯誤
 */
export class AiValidationException extends BaseAiException {
  constructor(message = 'AI 服務輸入參數無效', provider?: string, model?: string) {
    super(message, HttpStatus.BAD_REQUEST, provider, model, false, 'low');
  }
}

/**
 * 內容安全錯誤
 */
export class AiContentSafetyException extends BaseAiException {
  constructor(message = '內容違反 AI 服務安全政策', provider?: string, model?: string) {
    super(message, HttpStatus.FORBIDDEN, provider, model, false, 'medium');
  }
}

/**
 * 配置錯誤
 */
export class AiConfigurationException extends BaseAiException {
  constructor(message = 'AI 服務配置錯誤', provider?: string, model?: string) {
    super(message, HttpStatus.INTERNAL_SERVER_ERROR, provider, model, false, 'high');
  }
}

/**
 * 服務降級錯誤
 */
export class AiServiceDegradedException extends BaseAiException {
  constructor(message = 'AI 服務已降級運行', provider?: string, model?: string) {
    super(message, HttpStatus.PARTIAL_CONTENT, provider, model, false, 'medium');
  }
}

/**
 * 斷路器開啟錯誤
 */
export class AiCircuitBreakerOpenException extends BaseAiException {
  constructor(message = 'AI 服務斷路器已開啟', provider?: string, model?: string) {
    super(message, HttpStatus.SERVICE_UNAVAILABLE, provider, model, true, 'high');
  }
}

/**
 * 並發限制錯誤
 */
export class AiConcurrencyLimitException extends BaseAiException {
  constructor(message = 'AI 服務並發請求超限', provider?: string, model?: string) {
    super(message, HttpStatus.TOO_MANY_REQUESTS, provider, model, true, 'medium');
  }
}

/**
 * 錯誤映射工具
 */
export class AiErrorMapper {
  /**
   * 將原始錯誤轉換為 AI 特定異常
   */
  static mapError(error: any, provider?: string, model?: string): BaseAiException {
    const message = error.message || error.toString();

    // HTTP 狀態碼映射
    if (error.response?.status) {
      switch (error.response.status) {
        case 400:
          return new AiValidationException(message, provider, model);
        case 401:
          return new AiApiKeyException(message, provider);
        case 403:
          return new AiContentSafetyException(message, provider, model);
        case 408:
          return new AiTimeoutException(message, provider, model);
        case 429:
          return new AiQuotaExceededException(message, provider, model);
        case 502:
        case 503:
          return new AiModelUnavailableException(message, provider, model);
        case 504:
          return new AiTimeoutException(message, provider, model);
        default:
          return new AiInternalException(message, provider, model);
      }
    }

    // 錯誤訊息關鍵字映射
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('timeout') || lowerMessage.includes('超時')) {
      return new AiTimeoutException(message, provider, model);
    }

    if (
      lowerMessage.includes('network') ||
      lowerMessage.includes('connection') ||
      lowerMessage.includes('網路')
    ) {
      return new AiNetworkException(message, provider);
    }

    if (
      lowerMessage.includes('quota') ||
      lowerMessage.includes('rate limit') ||
      lowerMessage.includes('配額')
    ) {
      return new AiQuotaExceededException(message, provider, model);
    }

    if (
      lowerMessage.includes('api key') ||
      lowerMessage.includes('authorization') ||
      lowerMessage.includes('金鑰')
    ) {
      return new AiApiKeyException(message, provider);
    }

    if (lowerMessage.includes('model') && lowerMessage.includes('not found')) {
      return new AiModelUnavailableException(message, provider, model);
    }

    if (
      lowerMessage.includes('safety') ||
      lowerMessage.includes('content policy') ||
      lowerMessage.includes('安全')
    ) {
      return new AiContentSafetyException(message, provider, model);
    }

    // 預設為內部錯誤
    return new AiInternalException(message, provider, model);
  }

  /**
   * 檢查錯誤是否可重試
   */
  static isRetryable(error: BaseAiException): boolean {
    return error.retryable;
  }

  /**
   * 獲取錯誤嚴重程度
   */
  static getSeverity(error: BaseAiException): 'low' | 'medium' | 'high' | 'critical' {
    return error.severity;
  }
}
