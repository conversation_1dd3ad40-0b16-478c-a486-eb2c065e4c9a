import { ref, reactive, computed } from 'vue'
import { useNotification } from '@/composables/shared/useNotification'
import { orderService } from '@/services/admin/order.service'
import type { Order, OrderStatus } from '@/types/models/order.model'
import { ErrorService } from '@/services/error.service'

/**
 * 租戶訂單管理 Composable
 * 提供特定租戶的訂單查詢、篩選、匯出等功能
 */
export function useTenantOrders() {
  const { flash } = useNotification()

  // 狀態管理
  const orders = ref<Order[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 篩選條件
  const searchTerm = ref('')
  const status = ref<string>('all')
  const startDate = ref('')
  const endDate = ref('')

  // 統計資料
  const statistics = computed(() => {
    const total = orders.value.length
    const completed = orders.value.filter(order => order.status === 'COMPLETED').length
    const pending = orders.value.filter(order => order.status === 'PENDING').length
    const cancelled = orders.value.filter(order => order.status === 'CANCELLED').length
    const totalRevenue = orders.value
      .filter(order => order.status === 'COMPLETED')
      .reduce((sum, order) => sum + (order.amount || 0), 0)

    return {
      total,
      completed,
      pending,
      cancelled,
      totalRevenue
    }
  })

  /**
   * 載入指定租戶的訂單資料
   * @param tenantName 租戶名稱
   */
  const fetchOrders = async (tenantName: string) => {
    if (!tenantName) {
      console.warn('[useTenantOrders] fetchOrders: 租戶名稱為空')
      return
    }

    isLoading.value = true
    error.value = null

    try {
      console.log('[useTenantOrders] fetchOrders: 開始載入租戶訂單', { tenantName })

      // 建構查詢參數
      const params: any = {
        tenantName: tenantName
      }

      // 加入篩選條件
      if (searchTerm.value.trim()) {
        params.search = searchTerm.value.trim()
      }

      if (status.value && status.value !== 'all') {
        params.status = status.value.toUpperCase() as OrderStatus
      }

      if (startDate.value) {
        params.startDate = startDate.value
      }

      if (endDate.value) {
        params.endDate = endDate.value
      }

      console.log('[useTenantOrders] fetchOrders: 查詢參數', params)

      // 調用 API
      const result = await orderService.getOrders(params)
      orders.value = result || []

      console.log('[useTenantOrders] fetchOrders: 載入完成', {
        tenantName,
        ordersCount: orders.value.length
      })

    } catch (err) {
      console.error('[useTenantOrders] fetchOrders: 載入失敗', err)
      error.value = '載入訂單資料失敗'
      orders.value = []
      ErrorService.notify(err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 匯出租戶訂單資料
   * @param tenantName 租戶名稱
   */
  const exportOrders = async (tenantName: string) => {
    if (orders.value.length === 0) {
      flash.info('無訂單資料可匯出')
      return
    }

    try {
      console.log('[useTenantOrders] exportOrders: 開始匯出', { tenantName, count: orders.value.length })

      // 準備匯出資料
      const exportData = orders.value.map(order => ({
        '訂單編號': order.id,
        '租戶名稱': order.tenantName,
        '方案名稱': order.planName,
        '金額': formatCurrency(order.amount),
        '狀態': getStatusText(order.status),
        '訂閱人數': order.numberOfSubscribers,
        '期間(月)': order.period,
        '開始日期': formatDate(order.startDate),
        '結束日期': formatDate(order.endDate),
        '建立日期': formatDate(order.createdAt),
        '付款方式': order.paymentMethod,
        '付款狀態': getPaymentStatusText(order.paymentStatus),
        '備註': order.remarks || ''
      }))

      // 生成 CSV
      const headers = Object.keys(exportData[0]).join(',')
      const rows = exportData.map(row => 
        Object.values(row).map(value => 
          `"${String(value).replace(/"/g, '""')}"`
        ).join(',')
      )
      const csvContent = '\uFEFF' + headers + '\n' + rows.join('\n')

      // 下載檔案
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const fileName = `${tenantName}_orders_${new Date().toISOString().split('T')[0]}.csv`
      
      link.href = URL.createObjectURL(blob)
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      flash.success(`已匯出 ${exportData.length} 筆訂單資料`)
      console.log('[useTenantOrders] exportOrders: 匯出完成', { fileName })

    } catch (err) {
      console.error('[useTenantOrders] exportOrders: 匯出失敗', err)
      flash.error('匯出失敗')
      ErrorService.notify(err)
    }
  }

  /**
   * 重置篩選條件
   */
  const resetFilters = () => {
    searchTerm.value = ''
    status.value = 'all'
    startDate.value = ''
    endDate.value = ''
  }

  // 輔助函數
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('zh-TW', {
      style: 'currency',
      currency: 'TWD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString: string): string => {
    if (!dateString) return '-'
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return '-'
      return date.toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    } catch {
      return '-'
    }
  }

  const getStatusText = (status: OrderStatus): string => {
    switch (status) {
      case 'COMPLETED': return '已完成'
      case 'PENDING': return '待處理'
      case 'CANCELLED': return '已取消'
      default: return status
    }
  }

  const getPaymentStatusText = (paymentStatus: string): string => {
    switch (paymentStatus) {
      case 'paid': return '已付款'
      case 'pending': return '待付款'
      case 'failed': return '付款失敗'
      default: return paymentStatus
    }
  }

  return {
    // 狀態
    orders,
    isLoading,
    error,
    statistics,

    // 篩選條件
    searchTerm,
    status,
    startDate,
    endDate,

    // 方法
    fetchOrders,
    exportOrders,
    resetFilters,

    // 輔助函數
    formatCurrency,
    formatDate,
    getStatusText,
    getPaymentStatusText
  }
} 