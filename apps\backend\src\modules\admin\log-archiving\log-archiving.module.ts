import { Module } from '@nestjs/common';
import { LogArchivingController } from './log-archiving.controller';
import { LogArchivingService } from '@/common/services/log-archiving.service';
import { CommonModule } from '@/common/common.module';
import { StorageModule } from '@/modules/core/storage/storage.module';
import { SettingsModule } from '@/modules/admin/settings/settings.module';

@Module({
  imports: [CommonModule, StorageModule, SettingsModule],
  controllers: [LogArchivingController],
  providers: [LogArchivingService],
  exports: [LogArchivingService],
})
export class LogArchivingModule {}
