import { z } from 'zod';
import { NotificationService } from '@/modules/notification/notification.service';

// Zod schema for input validation
const sendMessageSchema = z.object({
  channel: z.enum(['internal', 'line', 'slack']).describe('The communication channel to use'),
  recipient_type: z.enum(['user', 'group']).describe('Whether the recipient is a user or group'),
  recipient_id: z.string().min(1).describe('The ID of the user or group'),
  message: z.string().min(1).describe('The content of the message'),
  message_type: z.string().optional().describe('Optional message type classification'),
});

/**
 * Creates a SendMessageTool using a simple function-based approach
 * @param notificationService - The notification service instance
 * @param tenantId - The tenant ID for isolation
 * @param senderId - The sender user ID
 * @returns A simple tool object compatible with LangChain
 */
export function createSendMessageTool(
  notificationService: NotificationService,
  tenantId: string,
  senderId: string,
) {
  return {
    name: 'send_message',
    description: `Send a message to a user or group through various channels (internal, LINE, Slack).

This tool allows the AI agent to send notifications and messages to users or groups using different communication channels.

Required fields:
- channel: The communication channel ("internal", "line", or "slack")
- recipient_type: Whether sending to a "user" or "group"
- recipient_id: The unique identifier of the recipient
- message: The content to send

Optional fields:
- message_type: Additional classification for the message type`,
    schema: sendMessageSchema,
    call: async (input: any) => {
      try {
        await notificationService.sendMessage(input, tenantId, senderId);
        return `Successfully sent message to ${input.recipient_type} ${input.recipient_id} via ${input.channel}.`;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        throw new Error(`Message sending failed: ${errorMessage}`);
      }
    },
  };
}

// Export the schema for use in other tools or tests
export { sendMessageSchema };
