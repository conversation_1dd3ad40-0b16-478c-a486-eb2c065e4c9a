import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';
import { Role } from '../../../../common/enums/role.enum';

export class CreateTenantUserDto {
  @ApiProperty({ description: '使用者 ID' })
  @IsUUID()
  userId: string;

  @ApiProperty({ description: '租戶 ID' })
  @IsUUID()
  tenantId: string;

  @ApiProperty({ description: '角色', enum: Role, default: 'user' })
  @IsEnum(Role)
  @IsOptional()
  role?: Role;
}
