import { Modu<PERSON> } from '@nestjs/common';
import { NLPController } from './nlp.controller';
import { NLPProcessorService } from './services/nlp-processor.service';
import { RequirementParserService } from './services/requirement-parser.service';

@Module({
  controllers: [NLPController],
  providers: [
    NLPProcessorService,
    RequirementParserService
  ],
  exports: [
    NLPProcessorService,
    RequirementParserService
  ]
})
export class NLPModule {} 