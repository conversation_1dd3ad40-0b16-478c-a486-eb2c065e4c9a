import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { KnowledgeBaseTool, KnowledgeBaseErrorType } from './knowledge-base-tool';
import { RAGIngestionService } from '../../../../rag/rag-ingestion.service';
import { RAGSecurityService } from '../../../../rag/rag-security.service';

// Test class to access protected methods
class TestableKnowledgeBaseTool extends KnowledgeBaseTool {
  public async testCall(input: string): Promise<string> {
    return this.testCall(input);
  }
}

describe('KnowledgeBaseTool - Security & Tenant Isolation', () => {
  let tool: TestableKnowledgeBaseTool;
  let ragIngestionService: jest.Mocked<RAGIngestionService>;
  let ragSecurityService: jest.Mocked<RAGSecurityService>;

  const VALID_TENANT_ID = '123e4567-e89b-12d3-a456-426614174000';
  const VALID_WORKSPACE_ID = '987fcdeb-51a2-43d7-9f8e-123456789abc';
  const INVALID_TENANT_ID = 'invalid-tenant-id';
  const DIFFERENT_TENANT_ID = '999e4567-e89b-12d3-a456-426614174999';

  beforeEach(async () => {
    const mockRagIngestionService = {
      searchSimilarDocuments: jest.fn(),
    };

    const mockRagSecurityService = {
      validateSearchQuery: jest.fn(),
      validateDocumentAccess: jest.fn(),
      validateBulkDocumentAccess: jest.fn(),
      logSecurityEvent: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: RAGIngestionService,
          useValue: mockRagIngestionService,
        },
        {
          provide: RAGSecurityService,
          useValue: mockRagSecurityService,
        },
      ],
    }).compile();

    ragIngestionService = module.get(RAGIngestionService);
    ragSecurityService = module.get(RAGSecurityService);

    // Suppress logger output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'debug').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Tenant Context Validation', () => {
    it('should reject invalid tenant ID format', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        INVALID_TENANT_ID,
        VALID_WORKSPACE_ID,
      );

      const result = await tool.testCall('{"query": "test query"}');

      expect(result).toContain('Access denied');
      expect(ragSecurityService.logSecurityEvent).toHaveBeenCalledWith(
        expect.anything(),
        INVALID_TENANT_ID,
        expect.objectContaining({
          violation: expect.anything(),
        }),
      );
    });

    it('should reject invalid workspace ID format', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        VALID_TENANT_ID,
        'invalid-workspace-id',
      );

      const result = await tool.testCall('{"query": "test query"}');

      expect(result).toContain('Access denied');
    });

    it('should accept valid UUID formats', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        VALID_TENANT_ID,
        VALID_WORKSPACE_ID,
      );

      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragIngestionService.searchSimilarDocuments.mockResolvedValue([]);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      const result = await tool.testCall('{"query": "test query"}');

      expect(result).toContain('No relevant documents found');
      expect(ragSecurityService.validateSearchQuery).toHaveBeenCalledWith(
        'test query',
        VALID_TENANT_ID,
      );
    });
  });

  describe('Security Query Validation', () => {
    beforeEach(() => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        VALID_TENANT_ID,
        VALID_WORKSPACE_ID,
      );
    });

    it('should block malicious queries', async () => {
      const maliciousQuery = '<script>alert("xss")</script>';
      ragSecurityService.validateSearchQuery.mockImplementation(() => {
        throw new Error('Invalid search query');
      });

      const result = await tool.testCall(`{"query": "${maliciousQuery}"}`);

      expect(result).toContain('Access denied');
      expect(ragSecurityService.logSecurityEvent).toHaveBeenCalledWith(
        'suspicious_query',
        VALID_TENANT_ID,
        expect.objectContaining({
          query: maliciousQuery,
          error: 'Invalid search query',
        }),
      );
    });

    it('should allow legitimate queries', async () => {
      const legitimateQuery = 'What is the project documentation about?';
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragIngestionService.searchSimilarDocuments.mockResolvedValue([]);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      const result = await tool.testCall(`{"query": "${legitimateQuery}"}`);

      expect(result).toContain('No relevant documents found');
      expect(ragSecurityService.validateSearchQuery).toHaveBeenCalledWith(
        legitimateQuery,
        VALID_TENANT_ID,
      );
    });

    it('should validate query length limits', async () => {
      const longQuery = 'a'.repeat(1001);
      ragSecurityService.validateSearchQuery.mockImplementation(() => {
        throw new Error('Search query too long');
      });

      const result = await tool.testCall(`{"query": "${longQuery}"}`);

      expect(result).toContain('Access denied');
    });
  });

  describe('Search Results Validation', () => {
    beforeEach(() => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        VALID_TENANT_ID,
        VALID_WORKSPACE_ID,
      );
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
    });

    it('should validate bulk document access', async () => {
      const mockResults = [
        {
          id: 'doc1',
          content: 'Content 1',
          tenant_id: VALID_TENANT_ID,
          workspace_id: null,
          similarity: 0.9,
          file_id: 'file1',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc2',
          content: 'Content 2',
          tenant_id: VALID_TENANT_ID,
          workspace_id: null,
          similarity: 0.8,
          file_id: 'file2',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(false);

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Access denied');
      expect(ragSecurityService.logSecurityEvent).toHaveBeenCalledWith(
        'access_violation',
        VALID_TENANT_ID,
        expect.objectContaining({
          violation: 'bulk_document_access_denied',
        }),
      );
    });

    it('should filter out documents from different tenants', async () => {
      const mockResults = [
        {
          id: 'doc1',
          content: 'Content 1',
          tenant_id: VALID_TENANT_ID,
          similarity: 0.9,
          workspace_id: null,
          file_id: 'file1',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc2',
          content: 'Content 2',
          tenant_id: DIFFERENT_TENANT_ID,
          similarity: 0.8,
          workspace_id: null,
          file_id: 'file2',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc3',
          content: 'Content 3',
          tenant_id: VALID_TENANT_ID,
          similarity: 0.7,
          workspace_id: null,
          file_id: 'file3',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess
        .mockResolvedValueOnce(true) // doc1 - valid
        .mockResolvedValueOnce(false) // doc2 - invalid (different tenant)
        .mockResolvedValueOnce(true); // doc3 - valid

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Found 2 relevant documents');
      expect(result).toContain('Content 1');
      expect(result).toContain('Content 3');
      expect(result).not.toContain('Content 2');
    });

    it('should validate workspace isolation', async () => {
      const mockResults = [
        {
          id: 'doc1',
          content: 'Content 1',
          tenant_id: VALID_TENANT_ID,
          workspace_id: VALID_WORKSPACE_ID,
          similarity: 0.9,
          file_id: 'file1',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc2',
          content: 'Content 2',
          tenant_id: VALID_TENANT_ID,
          workspace_id: 'different-workspace',
          similarity: 0.8,
          file_id: 'file2',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess
        .mockResolvedValueOnce(true) // doc1 - valid workspace
        .mockResolvedValueOnce(false); // doc2 - different workspace

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Found 1 relevant documents');
      expect(result).toContain('Content 1');
      expect(result).not.toContain('Content 2');
    });
  });

  describe('Security Audit Logging', () => {
    beforeEach(() => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        VALID_TENANT_ID,
        VALID_WORKSPACE_ID,
      );
    });

    it('should log successful operations with low security level', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragIngestionService.searchSimilarDocuments.mockResolvedValue([]);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      await tool.testCall('{"query": "legitimate query"}');

      const auditLogs = tool.getSecurityAuditLogs();
      expect(auditLogs).toHaveLength(1);
      expect(auditLogs[0].success).toBe(true);
      expect(auditLogs[0].securityLevel).toBe('low');
      expect(auditLogs[0].tenantId).toBe(VALID_TENANT_ID);
      expect(auditLogs[0].workspaceId).toBe(VALID_WORKSPACE_ID);
    });

    it('should log security violations with high security level', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {
        throw new Error('Suspicious query detected');
      });

      await tool.testCall('{"query": "malicious query"}');

      const auditLogs = tool.getSecurityAuditLogs();
      expect(auditLogs).toHaveLength(1);
      expect(auditLogs[0].success).toBe(false);
      expect(auditLogs[0].securityLevel).toBe('high');
      expect(auditLogs[0].details.errorType).toBe('SECURITY_VIOLATION');
    });

    it('should log tenant isolation violations with high security level', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        INVALID_TENANT_ID,
        VALID_WORKSPACE_ID,
      );

      await tool.testCall('{"query": "test"}');

      const auditLogs = tool.getSecurityAuditLogs();
      expect(auditLogs[0].securityLevel).toBe('high');
      expect(auditLogs[0].details.errorType).toBe('TENANT_ISOLATION_VIOLATION');
    });

    it('should maintain audit log size limit', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragIngestionService.searchSimilarDocuments.mockResolvedValue([]);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      // Generate more than 100 audit logs
      for (let i = 0; i < 105; i++) {
        await tool.testCall(`{"query": "test ${i}"}`);
      }

      const auditLogs = tool.getSecurityAuditLogs();
      expect(auditLogs.length).toBeLessThanOrEqual(100);
    });

    it('should allow clearing audit logs', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragIngestionService.searchSimilarDocuments.mockResolvedValue([]);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      await tool.testCall('{"query": "test"}');
      expect(tool.getSecurityAuditLogs()).toHaveLength(1);

      tool.clearSecurityAuditLogs();
      expect(tool.getSecurityAuditLogs()).toHaveLength(0);
    });
  });

  describe('Error Handling & Security', () => {
    beforeEach(() => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        VALID_TENANT_ID,
        VALID_WORKSPACE_ID,
      );
    });

    it('should not expose sensitive information in error messages', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {
        throw new Error('Database connection failed with credentials user:password@host');
      });

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Access denied');
      expect(result).not.toContain('Database connection');
      expect(result).not.toContain('credentials');
      expect(result).not.toContain('password');
    });

    it('should handle service errors gracefully', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragIngestionService.searchSimilarDocuments.mockRejectedValue(
        new Error('Internal service error'),
      );

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Service temporarily unavailable');
      expect(result).not.toContain('Internal service error');
    });

    it('should validate input parameters securely', async () => {
      const result = await tool.testCall('{"query": "", "maxResults": -1}');

      expect(result).toContain('Invalid input');
    });

    it('should handle malformed JSON input', async () => {
      const result = await tool.testCall('{"query": "test", invalid json}');

      // Should treat as simple string query
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragIngestionService.searchSimilarDocuments.mockResolvedValue([]);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      const result2 = await tool.testCall('simple query string');
      expect(result2).toContain('No relevant documents found');
    });
  });

  describe('Performance & Security Monitoring', () => {
    beforeEach(() => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        VALID_TENANT_ID,
        VALID_WORKSPACE_ID,
      );
    });

    it('should track request performance in audit logs', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragIngestionService.searchSimilarDocuments.mockImplementation(async () => {
        // Simulate some processing time
        await new Promise((resolve) => setTimeout(resolve, 10));
        return [];
      });
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      await tool.testCall('{"query": "test"}');

      const auditLogs = tool.getSecurityAuditLogs();
      expect(auditLogs[0].details.duration).toBeGreaterThan(0);
    });

    it('should include request context in audit logs', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragIngestionService.searchSimilarDocuments.mockResolvedValue([
        {
          id: 'doc1',
          content: 'test',
          similarity: 0.8,
          tenant_id: VALID_TENANT_ID,
          workspace_id: null,
          file_id: 'file1',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ]);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);

      await tool.testCall('{"query": "test query", "maxResults": 3}');

      const auditLogs = tool.getSecurityAuditLogs();
      expect(auditLogs[0].details.searchParams).toEqual({
        query: 'test query',
        maxResults: 3,
        similarity_threshold: 0.7,
      });
      expect(auditLogs[0].details.resultsCount).toBe(1);
    });
  });

  describe('Integration Security Tests', () => {
    beforeEach(() => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        VALID_TENANT_ID,
        VALID_WORKSPACE_ID,
      );
    });

    it('should enforce complete tenant isolation workflow', async () => {
      const mockResults = [
        {
          id: 'doc1',
          content: 'Tenant A content',
          tenant_id: VALID_TENANT_ID,
          workspace_id: VALID_WORKSPACE_ID,
          similarity: 0.9,
          file_id: 'file1',
          metadata: { file_name: 'doc1.txt' },
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc2',
          content: 'Tenant B content',
          tenant_id: DIFFERENT_TENANT_ID,
          workspace_id: 'different-workspace',
          similarity: 0.8,
          file_id: 'file2',
          metadata: { file_name: 'doc2.txt' },
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess
        .mockResolvedValueOnce(true) // doc1 - belongs to correct tenant
        .mockResolvedValueOnce(false); // doc2 - belongs to different tenant

      const result = await tool.testCall('{"query": "sensitive information"}');

      // Should only return documents from the correct tenant
      expect(result).toContain('Found 1 relevant documents');
      expect(result).toContain('Tenant A content');
      expect(result).not.toContain('Tenant B content');

      // Should log successful operation
      const auditLogs = tool.getSecurityAuditLogs();
      expect(auditLogs[0].success).toBe(true);
      expect(auditLogs[0].details.resultsCount).toBe(1);
    });

    it('should handle complete security violation scenario', async () => {
      const maliciousInput =
        '{"query": "<script>alert(\\"steal data\\")</script>", "maxResults": 1000}';

      ragSecurityService.validateSearchQuery.mockImplementation(() => {
        throw new Error('Malicious script detected');
      });

      const result = await tool.testCall(maliciousInput);

      // Should deny access
      expect(result).toContain('Access denied');

      // Should log security event
      expect(ragSecurityService.logSecurityEvent).toHaveBeenCalledWith(
        'suspicious_query',
        VALID_TENANT_ID,
        expect.objectContaining({
          query: '<script>alert("steal data")</script>',
          error: 'Malicious script detected',
        }),
      );

      // Should log in audit with high security level
      const auditLogs = tool.getSecurityAuditLogs();
      expect(auditLogs[0].success).toBe(false);
      expect(auditLogs[0].securityLevel).toBe('high');
    });
  });
});
