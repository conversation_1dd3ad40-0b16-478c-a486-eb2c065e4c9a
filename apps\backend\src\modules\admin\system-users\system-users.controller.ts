import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { Roles } from '../../core/auth/decorators/roles.decorator';
import { Role } from '../../../common/enums/role.enum';
import { SystemUsersService } from './system-users.service';
import { CreateSystemUserDto } from './dto/create-system-user.dto';
import { UpdateSystemUserDto } from './dto/update-system-user.dto';
import {
  RequireCreate,
  RequireDelete,
  RequireManage,
  RequireRead,
  RequireUpdate,
} from '../../../casl/decorators/check-policies.decorator';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { Actions, Subjects } from '@horizai/permissions';
import { CurrentUser } from '../../core/auth/decorators/current-user.decorator';
import { ISystemUserProfile } from '../../../types/models/system-user.model';
import { Audit } from '../../../common/decorators/audit.decorator';

@ApiTags('admin/system-users')
@ApiBearerAuth()
@Controller('admin/system-users')
@UseGuards(JwtAuthGuard, PoliciesGuard)
export class SystemUsersController {
  constructor(private readonly systemUsersService: SystemUsersService) {}

  @Post()
  @ApiOperation({ summary: '建立系統使用者' })
  @ApiResponse({ status: 201, description: '成功建立系統使用者' })
  @RequireCreate(Subjects.SYSTEM_USER)
  @Audit({
    action: 'SYSTEM_USER_CREATE',
    resource: 'system_user',
    description: 'Created new system user ${args.0.email}',
    resourceIdExtractor: (args, result) => result?.id,
  })
  async create(@Body() createSystemUserDto: CreateSystemUserDto) {
    return this.systemUsersService.create(createSystemUserDto);
  }

  @Get()
  @ApiOperation({ summary: '獲取所有系統使用者' })
  @ApiResponse({ status: 200, description: '成功獲取系統使用者列表' })
  @RequireRead(Subjects.SYSTEM_USER)
  async findAll(
    @Query('skip') skip?: string,
    @Query('take') take?: string,
    @Query('search') search?: string,
    @Query('status') status?: string,
  ) {
    const params: any = {};

    if (skip) params.skip = parseInt(skip);
    if (take) params.take = parseInt(take);

    if (search || status) {
      params.where = {};
      if (search) {
        params.where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }
      if (status) {
        params.where.status = status;
      }
    }

    return this.systemUsersService.findAll(params);
  }

  @Get('roles')
  @ApiOperation({ summary: '獲取系統角色列表' })
  @ApiResponse({ status: 200, description: '成功獲取系統角色列表' })
  @RequireRead(Subjects.ROLE)
  async getSystemRoles() {
    return this.systemUsersService.getSystemRoles();
  }

  @Get(':id')
  @ApiOperation({ summary: '根據 ID 獲取系統使用者' })
  @ApiResponse({ status: 200, description: '成功獲取系統使用者' })
  @RequireRead(Subjects.SYSTEM_USER)
  async findOne(@Param('id') id: string) {
    return this.systemUsersService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新系統使用者' })
  @ApiResponse({ status: 200, description: '成功更新系統使用者' })
  @RequireUpdate(Subjects.SYSTEM_USER)
  @Audit({
    action: 'SYSTEM_USER_UPDATE',
    resource: 'system_user',
    description: 'Updated system user ${args.0}',
    resourceIdExtractor: (args) => args[0],
    logArgs: true,
    excludeProperties: ['password'],
  })
  async update(@Param('id') id: string, @Body() updateSystemUserDto: UpdateSystemUserDto) {
    return this.systemUsersService.update(id, updateSystemUserDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '刪除系統使用者' })
  @ApiResponse({ status: 200, description: '成功刪除系統使用者' })
  @RequireDelete(Subjects.SYSTEM_USER)
  @Audit({
    action: 'SYSTEM_USER_DELETE',
    resource: 'system_user',
    description: 'Deleted system user ${args.0}',
    resourceIdExtractor: (args) => args[0],
  })
  async remove(@Param('id') id: string) {
    return this.systemUsersService.remove(id);
  }

  @Post(':id/reset-password')
  @ApiOperation({ summary: '重置系統使用者密碼' })
  @ApiResponse({ status: 200, description: '成功重置密碼' })
  @RequireUpdate(Subjects.SYSTEM_USER)
  @HttpCode(HttpStatus.OK)
  @Audit({
    action: 'SYSTEM_USER_PASSWORD_RESET',
    resource: 'system_user',
    description: 'Reset password for system user ${args.0}',
    resourceIdExtractor: (args) => args[0],
  })
  async resetPassword(@Param('id') id: string) {
    return this.systemUsersService.resetPassword(id);
  }

  @Put(':id/status')
  @ApiOperation({ summary: '更新系統使用者狀態' })
  @ApiResponse({ status: 200, description: '成功更新使用者狀態' })
  @RequireUpdate(Subjects.SYSTEM_USER)
  @Audit({
    action: 'SYSTEM_USER_STATUS_UPDATE',
    resource: 'system_user',
    description: 'Updated status to ${args.1} for system user ${args.0}',
    resourceIdExtractor: (args) => args[0],
  })
  async updateStatus(@Param('id') id: string, @Body('status') status: 'active' | 'inactive') {
    return this.systemUsersService.updateStatus(id, status);
  }

  @Post('batch')
  @ApiOperation({ summary: '批量操作系統使用者' })
  @ApiResponse({ status: 200, description: '成功執行批量操作' })
  @RequireManage(Subjects.SYSTEM_USER)
  @HttpCode(HttpStatus.OK)
  @Audit({
    action: 'SYSTEM_USER_BATCH_OPERATION',
    resource: 'system_user',
    description: 'Performed batch operation "${args.1}" on users: ${args.0.join(", ")}',
  })
  async batchOperation(
    @Body('ids') ids: string[],
    @Body('operation') operation: 'activate' | 'deactivate' | 'delete',
  ) {
    return this.systemUsersService.batchOperation(ids, operation);
  }

  @Get(':id/permissions/:action/:subject')
  @ApiOperation({ summary: '檢查系統使用者權限' })
  @ApiResponse({ status: 200, description: '權限檢查結果' })
  @RequireRead(Subjects.SYSTEM_USER)
  async checkPermission(
    @Param('id') id: string,
    @Param('action') action: string,
    @Param('subject') subject: string,
  ) {
    const canAccess = await this.systemUsersService.checkSystemPermission(id, action, subject);
    return { canAccess };
  }
}
