import { IsBoolean, IsString, <PERSON><PERSON><PERSON>ber, <PERSON>Optional, IsIn, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateArchivingSettingsDto {
  @ApiProperty({
    description: '是否啟用自動歸檔',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @ApiProperty({
    description: '歸檔排程 (cron 表達式)',
    example: '0 2 * * *', // 每天凌晨 2 點
  })
  @IsString()
  @IsOptional()
  schedule?: string;

  @ApiProperty({
    description: '日誌保留天數 (超過此天數的日誌將被歸檔)',
    example: 90,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  retentionDays?: number;

  @ApiProperty({
    description: '歸檔儲存提供商',
    example: 'local',
    enum: ['local', 's3', 'azure', 'gcs'],
  })
  @IsString()
  @IsIn(['local', 's3', 'azure', 'gcs'])
  @IsOptional()
  storageProvider?: 'local' | 's3' | 'azure' | 'gcs';

  @ApiProperty({
    description: '歸檔儲存路徑或容器名稱',
    example: 'archived-logs',
  })
  @IsString()
  @IsOptional()
  storagePath?: string;

  @ApiProperty({
    description: '是否壓縮歸檔檔案',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  compressionEnabled?: boolean;

  @ApiProperty({
    description: '歸檔檔案格式',
    example: 'json',
    enum: ['json', 'csv'],
  })
  @IsString()
  @IsIn(['json', 'csv'])
  @IsOptional()
  archiveFormat?: 'json' | 'csv';

  @ApiProperty({
    description: '批次處理大小 (每次歸檔處理的記錄數)',
    example: 1000,
    minimum: 100,
  })
  @IsNumber()
  @Min(100)
  @IsOptional()
  batchSize?: number;

  @ApiProperty({
    description: '是否在歸檔後刪除原始記錄',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  deleteAfterArchive?: boolean;

  @ApiProperty({
    description: '歸檔檔案保留天數 (0 表示永久保留)',
    example: 365,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  archiveRetentionDays?: number;
}
