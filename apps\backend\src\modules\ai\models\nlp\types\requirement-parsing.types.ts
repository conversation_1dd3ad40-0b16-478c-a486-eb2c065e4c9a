/**
 * NLP 需求解析系統的類型定義
 */

export interface RequirementDocument {
  id: string;
  title: string;
  content: string;
  metadata?: Record<string, any>;
}

export interface ParsedRequirement {
  id: string;
  type: RequirementType;
  title: string;
  description: string;
  priority: Priority;
  category: RequirementCategory;
  entities: RequirementEntity[];
  dependencies: string[];
  constraints: RequirementConstraint[];
  acceptanceCriteria: AcceptanceCriterion[];
  confidence: number;
  sourceText: string;
  extractedAt: Date;
}

export enum RequirementType {
  FUNCTIONAL = 'functional',
  NON_FUNCTIONAL = 'non_functional',
  BUSINESS = 'business',
  TECHNICAL = 'technical',
  SECURITY = 'security',
  PERFORMANCE = 'performance',
  USABILITY = 'usability',
  INTERFACE = 'interface',
  DATA = 'data',
  CONSTRAINT = 'constraint'
}

export enum Priority {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  OPTIONAL = 'optional'
}

export enum RequirementCategory {
  USER_STORY = 'user_story',
  FEATURE = 'feature',
  EPIC = 'epic',
  TASK = 'task',
  BUG = 'bug',
  IMPROVEMENT = 'improvement',
  RESEARCH = 'research'
}

export interface RequirementEntity {
  text: string;
  label: EntityLabel;
  start: number;
  end: number;
  confidence: number;
}

export enum EntityLabel {
  ACTOR = 'ACTOR',           // 用戶、系統、角色
  ACTION = 'ACTION',         // 動作、操作
  OBJECT = 'OBJECT',         // 對象、實體
  CONDITION = 'CONDITION',   // 條件、狀態
  OUTCOME = 'OUTCOME',       // 結果、目標
  CONSTRAINT = 'CONSTRAINT', // 約束、限制
  METRIC = 'METRIC',         // 指標、度量
  TIME = 'TIME',             // 時間相關
  LOCATION = 'LOCATION',     // 位置、地點
  TECHNOLOGY = 'TECHNOLOGY', // 技術、工具
  DATA = 'DATA',             // 數據、信息
  INTERFACE = 'INTERFACE',   // 界面、接口
  PROCESS = 'PROCESS',       // 流程、步驟
  RULE = 'RULE',             // 規則、政策
  QUALITY = 'QUALITY'        // 質量屬性
}

export interface RequirementConstraint {
  type: ConstraintType;
  description: string;
  value?: string | number;
  unit?: string;
}

export enum ConstraintType {
  TIME = 'time',
  BUDGET = 'budget',
  RESOURCE = 'resource',
  TECHNOLOGY = 'technology',
  COMPLIANCE = 'compliance',
  PERFORMANCE = 'performance',
  SECURITY = 'security',
  USABILITY = 'usability',
  SCALABILITY = 'scalability',
  AVAILABILITY = 'availability'
}

export interface AcceptanceCriterion {
  id: string;
  description: string;
  type: AcceptanceCriterionType;
  testable: boolean;
}

export enum AcceptanceCriterionType {
  GIVEN_WHEN_THEN = 'given_when_then',
  SCENARIO = 'scenario',
  RULE = 'rule',
  EXAMPLE = 'example',
  CONDITION = 'condition'
}

export interface NLPProcessingResult {
  tokens: Token[];
  sentences: Sentence[];
  entities: RequirementEntity[];
  dependencies: DependencyRelation[];
  sentiment: SentimentAnalysis;
  keywords: Keyword[];
  topics: Topic[];
}

export interface Token {
  text: string;
  pos: string;        // Part of Speech
  lemma: string;
  index: number;
  isStop: boolean;
  isAlpha: boolean;
  isDigit: boolean;
}

export interface Sentence {
  text: string;
  start: number;
  end: number;
  tokens: Token[];
  sentiment: number;
}

export interface DependencyRelation {
  head: string;
  dependent: string;
  relation: string;
  headIndex: number;
  dependentIndex: number;
}

export interface SentimentAnalysis {
  polarity: number;    // -1 to 1
  subjectivity: number; // 0 to 1
  label: 'positive' | 'negative' | 'neutral';
  confidence: number;
}

export interface Keyword {
  text: string;
  score: number;
  frequency: number;
}

export interface Topic {
  label: string;
  keywords: string[];
  score: number;
}

export interface RequirementParsingOptions {
  language?: string;
  includeEntities?: boolean;
  includeDependencies?: boolean;
  includeSentiment?: boolean;
  includeKeywords?: boolean;
  includeTopics?: boolean;
  confidenceThreshold?: number;
  maxRequirements?: number;
  customPatterns?: RequirementPattern[];
}

export interface RequirementPattern {
  name: string;
  pattern: string | RegExp;
  type: RequirementType;
  priority: Priority;
  extractionRules: ExtractionRule[];
}

export interface ExtractionRule {
  field: string;
  pattern: string | RegExp;
  required: boolean;
  transform?: (value: string) => any;
}

export interface RequirementParsingResult {
  requirements: ParsedRequirement[];
  statistics: ParsingStatistics;
  errors: ParsingError[];
  warnings: ParsingWarning[];
}

export interface ParsingStatistics {
  totalSentences: number;
  totalTokens: number;
  requirementsFound: number;
  averageConfidence: number;
  processingTime: number;
  entitiesExtracted: number;
}

export interface ParsingError {
  type: string;
  message: string;
  location?: {
    line: number;
    column: number;
    text: string;
  };
  severity: 'error' | 'warning' | 'info';
}

export interface ParsingWarning {
  type: string;
  message: string;
  suggestion?: string;
  location?: {
    line: number;
    column: number;
    text: string;
  };
} 