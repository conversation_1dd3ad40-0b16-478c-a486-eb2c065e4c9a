# ADMIN 後台 UI/UX 設計標準

**版本**: 2.0
**基於**: [ui-design-standards.md](mdc:docs/ui-design-standards.md)
**核心理念**: 在通用設計標準的基礎上，針對後台管理介面，強調資訊密度、操作效率與功能清晰度。

---

## 1. 核心原則

1.  **資訊優先 (Information First)**: 介面設計的核心是清晰、準確地展示資料。移除所有不必要的視覺干擾，讓使用者能快速聚焦於數據和管理任務。
2.  **效率導向 (Efficiency-Oriented)**: 操作流程應極度簡化。複雜的功能（如表單填寫、資料篩選、多頁籤管理）應提供最直接、最少點擊的路徑。
3.  **緊湊佈局 (Compact Layout)**: 後台介面通常需要處理大量資料。採用更緊湊的間距、字體和元件尺寸，以在有限的空間內呈現更多有效資訊，同時保持良好的可讀性。
4.  **功能清晰，避免冗餘 (Functional Clarity, No Redundancy)**:
    *   每個 UI 元素都應有明確的功能目的。
    *   避免在不同位置重複顯示相同的資訊。例如，在列表的展開詳細視圖中，不應再重複顯示已在列表行中呈現的標題或狀態。

---

## 2. 佈局與結構 (Layout & Structure)

### 2.1. 頁面佈局

- **頁面內間距**: 所有頁面的主容器應使用 `p-4`。
- **區塊間距**: 頁面內各個內容區塊之間的垂直間距應統一使用 `space-y-4`。

### 2.2. 頁面標題 (Page Title)

所有後台頁面**必須**使用統一的標題元件結構，以確保視覺一致性。

- **結構**: 一個包含圖示和文字的 `flex` 容器。
- **樣式**:
    - **容器**: `<div class="flex items-center space-x-3">`
    - **圖示容器**: `<div class="p-2 bg-primary/10 rounded-md">`
    - **圖示**: Lucide 圖示，`<Icon class="w-4 h-4 text-primary" />`
    - **標題文字 (`<h1>`)**: `<h1 class="text-xl font-semibold text-gray-900 dark:text-zinc-100">`
    - **描述文字 (`<p>`)**: `<p class="text-sm text-gray-600 dark:text-zinc-400 mt-0.5">`

### 2.3. 內容容器 (Content Containers)

- **原則**: **全面棄用 `Card` 組件**，改用更簡潔、靈活的 `div` 容器。
- **標準實現**:
    - 使用 `<div class="border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-zinc-800">` 作為所有內容區塊（如表格、篩選器、表單）的基礎容器。
    - 這種方法移除了 `CardHeader` 和 `CardContent` 帶來的不必要內間距，給予內部佈局更大的自由度。

---

## 3. 組件標準 (Component Standards)

### 3.1. Sheet vs. Dialog

- **Sheet (首選)**: **所有**涉及 **CRUD 操作、複雜表單、資料篩選、或多頁籤內容** 的場景，**必須**使用 `Sheet` 組件。
    - **優勢**: 提供更大的垂直空間，更適合複雜內容；從側面滑出的互動更現代，且不會完全遮擋主頁面上下文。
    - **強制設定**: Sheet **必須**設定為點擊外部不可關閉 (`:close-on-outside-click="false"`)，以防止用戶誤觸導致表單資料遺失。

- **Dialog**: 僅用於**極簡的確認、提示或警告**，內容應非常簡短（例如，刪除確認）。

### 3.2. 統計卡片 (Statistic Cards)

作為內容容器的一種特例，統計卡片追求極致的緊湊與資訊清晰。

- **實作**:
    - **容器**: 使用標準內容容器 `div`，但內部 `padding` 應為 `p-2`。
    - **佈局**: `<div class="flex items-center gap-2">`
    - **圖示容器**: `<div class="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-md">`
    - **圖示**: `<Icon class="w-3 h-3 text-blue-600 dark:text-blue-400" />`
    - **標題**: `<p class="text-xs text-gray-600 dark:text-zinc-400">`
    - **數值**: `<p class="text-lg font-semibold text-gray-900 dark:text-zinc-100">`

### 3.3. 表格 (Tables)

- **固定表頭**: 對於可能出現滾動的長表格，表頭 `<thead>` **必須**使用 `sticky top-0` 和背景色，以確保滾動時可見。
- **緊湊間距**:
    - `TableHead`: `px-4 py-3`
    - `TableCell`: `px-4 py-3`

### 3.4. 表單 (Forms)

- **統一高度**: 所有表單輸入框 (`Input`)、選擇器 (`Select`)、日期選擇器等，高度**必須**統一為 `h-10`。
- **圖示內置**: 為提升空間利用率和視覺指引，圖示應使用 `absolute` 定位放置在輸入框**內部左側**。輸入框本身需增加左側 padding (`pl-8` 或 `pl-10`) 以避免文字與圖示重疊。
- **標題與說明**: `SheetHeader` 應保持極簡，**不應**帶有額外背景色。一個簡單的底部邊框 (`border-b`) 即可。

### 3.5. 按鈕 (Buttons)

- **統一高度**: 在工具列 (Toolbar) 或表單中的主要操作按鈕，高度應與輸入框保持一致，即 `h-10`。
- **圖示按鈕**: 用於表格行操作的圖示按鈕，尺寸應為 `h-8 w-8`，以提供足夠的點擊區域，同時保持緊湊。

### 3.6. 頁籤 (Tabs)

- **置中佈局**: `TabsList` **應**使用 `flex justify-center` 實現頁籤標題的置中顯示。
- **數量徽章**: 統計數量應以 `Badge` 的形式，緊湊地顯示在頁籤標題文字的右側。
- **移除圖示**: 頁籤標題**不應**包含圖示，保持文字的簡潔性。

---

## 4. 視覺語言 (Visual Language)

### 4.1. 字體層級 (Typography)

- **頁面標題 (`<h1>`)**: `text-xl font-semibold`
- **區塊標題 (`<h2>`)**: `text-sm font-semibold`
- **內文/表格文字**: `text-sm`
- **輔助/描述文字**: `text-xs`

### 4.2. 顏色與邊框 (Colors & Borders)

- **顏色使用**:
    - **避免使用 `muted-foreground`**。應直接使用具體的灰階顏色（如 `text-gray-600 dark:text-zinc-400`），以確保在不同主題下顏色語義的明確性和一致性。
- **邊框**:
    - 所有容器和元件的邊框應統一使用 `border-gray-200 dark:border-gray-700`。

### 4.3. 圖示 (Icons)

- **頁面標題圖示**: `w-4 h-4`
- **統計卡片圖示**: `w-3 h-3`
- **按鈕內圖示**: `w-4 h-4`
- **表格內文圖示**: `w-4 h-4`

---

## 5. 通知系統 (Notification System)

ADMIN 後台的所有操作反饋和系統訊息，**必須**使用統一的 `useNotification()` Composable (`@/composables/shared/useNotification`)。

### 5.1. 通知類型選擇

#### **Toast 通知 (輕量提示)**
- **用途**: 用於非阻塞性的、操作成功後的**即時反饋**。例如「設定已儲存」、「使用者已刪除」等。
- **調用方式**: `notification.toast.success('操作成功', '您的變更已儲存。')`

#### **Flash 通知 (頁面級提示)**
- **用途**: 用於需要用戶明確注意的**重要狀態變更**或**關鍵錯誤**。例如，整個系統進入維護模式、API 連線中斷等影響全局的事件。
- **調用方式**: `notification.flash.error('系統錯誤：無法連接至伺服器，請稍後再試。')`

### 5.2. 使用場景與規範

| 場景 | 推薦類型 | 範例調用 | 說明 |
| --- | --- | --- | --- |
| **表單提交成功** | `Toast` | `notification.toast.success('儲存成功')` | 最常見的用法，給予使用者操作成功的確認。 |
| **刪除操作成功** | `Toast` | `notification.toast.success('租戶已刪除')` | 同上，提供快速反饋。 |
| **API 請求失敗 (可恢復)** | `Toast` | `notification.toast.error('載入失敗', '請檢查您的網路連線。')` | 當單一元件或資料載入失敗，但不影響整個頁面功能時使用。 |
| **表單驗證失敗** | **不使用通知** | - | 驗證錯誤應直接顯示在對應的表單欄位下方，而非使用全域通知。 |
| **權限不足 (403)** | `Flash` 或頁面提示 | `notification.flash.error('權限不足，無法執行此操作。')` | 若操作失敗是因為權限問題，應給予明確的頁面級提示。 |
| **系統級嚴重錯誤 (500)** | `Flash` | `notification.flash.error('發生未預期的錯誤，請聯繫系統管理員。')` | 當發生嚴重後端錯誤時，使用 Flash 通知。 |
| **操作進行中** | **不使用通知** | - | 長時間操作應使用元件內部的 `Loader` 或 `Spinner` 來顯示載入狀態。 |
| **資訊提示** | `Toast` | `notification.toast.info('提醒', '匯出任務已開始，完成後將通知您。')` | 用於告知用戶一個非關鍵性的背景任務或資訊。 |

### 5.3. 最佳實踐

1.  **禁止濫用**: 只有在需要明確告知用戶一個已完成的操作結果或重要狀態時才使用通知。
2.  **訊息清晰**: `Toast` 的 `title` 應是結論（如「成功」），`description` 是具體內容。
3.  **統一入口**: **所有**通知都必須通過 `useNotification()` 觸發。
4.  **錯誤處理整合**: 在服務層或全局錯誤處理中統一調用通知，避免在組件中重複編寫邏輯。
5.  **後端訊息驅動**: 操作結果的通知訊息，**應**直接使用後端回應中的 `message` 欄位。

---

## 6. 總結

本文件旨在將近期 UI/UX 優化的成功實踐轉化為 ADMIN 後台的統一開發標準。所有後台相關的介面開發，皆應以此文件為準繩，確保平台提供一致、高效、專業的管理體驗。 