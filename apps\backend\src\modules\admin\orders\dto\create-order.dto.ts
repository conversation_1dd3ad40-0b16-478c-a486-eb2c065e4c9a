import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsUUID,
  IsOptional,
  IsPositive,
  Min,
  IsISO8601,
  IsEnum,
} from 'class-validator';

export class CreateOrderDto {
  @ApiProperty({
    description: '租戶 ID',
    example: 'e4c8e5d0-e4d6-4d8c-8b19-3f43e8a22a0d',
  })
  @IsNotEmpty({ message: '租戶 ID 不可為空' })
  @IsUUID(4, { message: '租戶 ID 格式不正確' })
  tenantId: string;

  @ApiProperty({
    description: '方案 ID',
    example: 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6',
  })
  @IsNotEmpty({ message: '方案 ID 不可為空' })
  @IsUUID(4, { message: '方案 ID 格式不正確' })
  planId: string;

  @ApiProperty({
    description: '租戶名稱',
    example: '範例企業',
  })
  @IsNotEmpty({ message: '租戶名稱不可為空' })
  @IsString({ message: '租戶名稱必須是字串' })
  tenantName: string;

  @ApiProperty({
    description: '方案名稱',
    example: '企業方案',
  })
  @IsNotEmpty({ message: '方案名稱不可為空' })
  @IsString({ message: '方案名稱必須是字串' })
  planName: string;

  @ApiProperty({
    description: '訂單金額',
    example: 30000,
  })
  @IsNotEmpty({ message: '訂單金額不可為空' })
  @IsNumber({}, { message: '訂單金額必須是數字' })
  @IsPositive({ message: '訂單金額必須大於零' })
  amount: number;

  @ApiProperty({
    description: '訂閱期間（月）',
    example: 12,
  })
  @IsNotEmpty({ message: '訂閱期間不可為空' })
  @IsNumber({}, { message: '訂閱期間必須是數字' })
  @Min(1, { message: '訂閱期間至少為 1 個月' })
  period: number;

  @ApiProperty({
    description: '訂閱人數',
    example: 10,
  })
  @IsNotEmpty({ message: '訂閱人數不可為空' })
  @IsNumber({}, { message: '訂閱人數必須是數字' })
  @Min(1, { message: '訂閱人數至少為 1 人' })
  numberOfSubscribers: number;

  @ApiProperty({
    description: '訂閱開始日期',
    example: '2023-06-01',
  })
  @IsNotEmpty({ message: '訂閱開始日期不可為空' })
  @IsISO8601({}, { message: '訂閱開始日期格式不正確' })
  startDate: string;

  @ApiProperty({
    description: '訂閱結束日期',
    example: '2024-05-31',
    required: false,
  })
  @IsOptional()
  @IsISO8601({}, { message: '訂閱結束日期格式不正確' })
  endDate?: string;

  @ApiProperty({
    description: '帳單週期',
    example: 'monthly',
    enum: ['monthly', 'yearly'],
    default: 'monthly',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '帳單週期必須是字串' })
  @IsEnum(['monthly', 'yearly'], { message: '帳單週期必須是 monthly 或 yearly' })
  billingCycle?: string;

  @ApiProperty({
    description: '訂單備註',
    example: '此訂單包含優惠折扣',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '訂單備註必須是字串' })
  remarks?: string;
}
