import { Test, TestingModule } from '@nestjs/testing';
import { 
  ProgressUpdateToolsFactory, 
  createProgressUpdateTool, 
  ProgressUpdateConfig 
} from './progress-update-tools.factory';
import { ToolExecutionContext } from '../core/tool-registry.interface';
import { ToolExecutionError } from '../core/tool.errors';
import { ProgressService } from '@/modules/workspace/progress/progress.service';
import { ProgressType } from '@prisma/client';

describe('ProgressUpdateToolsFactory', () => {
  let mockContext: ToolExecutionContext;
  let mockProgressService: jest.Mocked<ProgressService>;

  beforeEach(() => {
    mockContext = {
      tenantId: 'test-tenant-id',
      userId: 'test-user-id',
      workspaceId: 'test-workspace-id',
      workspaceRoot: '/test/workspace',
      ability: {} as any,
      user: {} as any,
    };

    mockProgressService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    } as any;

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('createProgressUpdateTool', () => {
    it('should create a progress update tool with correct configuration', () => {
      const tool = createProgressUpdateTool(mockProgressService, mockContext);

      expect(tool).toBeDefined();
      expect(tool.name).toBe('progress_update');
      expect(tool.description).toContain('進度更新工具');
      expect(tool.schema).toBeDefined();
      expect(tool.call).toBeDefined();
    });

    it('should create tool with custom configuration', () => {
      const config: ProgressUpdateConfig = { 
        allowAutoTimestamp: true,
        defaultProgressType: ProgressType.TASK_UPDATE,
        maxPhotoUrls: 5
      };
      
      const tool = createProgressUpdateTool(mockProgressService, mockContext, config);

      expect(tool.description).toContain('進度更新工具');
      expect(tool.description).toContain(Object.values(ProgressType).join(', '));
    });

    it('should successfully create progress update with project ID', async () => {
      const mockProgressEntry = {
        id: 'progress-123',
        title: 'Test Progress',
        description: null,
        progress_type: ProgressType.TASK_UPDATE,
        progress_value: 75,
        project_id: 'project-123',
        task_id: null,
        user_id: 'test-user-id',
        tenant_id: 'test-tenant-id',
        status: null,
        notes: null,
        photo_urls: null,
        metadata: null,
        recorded_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockProgressService.create.mockResolvedValue(mockProgressEntry);

      const tool = createProgressUpdateTool(mockProgressService, mockContext);
      const result = await tool.call({
        title: 'Test Progress',
        progressType: ProgressType.TASK_UPDATE,
        progressValue: 75,
        projectId: 'project-123',
      });

      expect(typeof result).toBe('string');
      expect(result).toContain('進度更新成功');
      expect(result).toContain('Test Progress');
      expect(mockProgressService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Progress',
          progress_type: ProgressType.TASK_UPDATE,
          progress_value: 75,
          project_id: 'project-123',
          task_id: undefined,
        }),
        mockContext.tenantId,
        mockContext.userId
      );
    });

    it('should successfully create progress update with task ID', async () => {
      const mockProgressEntry = {
        id: 'progress-456',
        title: 'Task Progress',
        description: null,
        progress_type: ProgressType.MILESTONE,
        progress_value: null,
        project_id: null,
        task_id: 'task-456',
        user_id: 'test-user-id',
        tenant_id: 'test-tenant-id',
        status: null,
        notes: null,
        photo_urls: null,
        metadata: null,
        recorded_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockProgressService.create.mockResolvedValue(mockProgressEntry);

      const tool = createProgressUpdateTool(mockProgressService, mockContext);
      const result = await tool.call({
        title: 'Task Progress',
        progressType: ProgressType.MILESTONE,
        taskId: 'task-456',
      });

      expect(result).toContain('進度更新成功');
      expect(mockProgressService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Task Progress',
          progress_type: ProgressType.MILESTONE,
          task_id: 'task-456',
          project_id: undefined,
        }),
        mockContext.tenantId,
        mockContext.userId
      );
    });

    it('should throw error when neither projectId nor taskId is provided', async () => {
      const tool = createProgressUpdateTool(mockProgressService, mockContext);

      await expect(tool.call({
        title: 'Test Progress',
        progressType: ProgressType.TASK_UPDATE,
      })).rejects.toThrow(ToolExecutionError);
    });

    it('should throw error when both projectId and taskId are provided', async () => {
      const tool = createProgressUpdateTool(mockProgressService, mockContext);

      await expect(tool.call({
        title: 'Test Progress',
        progressType: ProgressType.TASK_UPDATE,
        projectId: 'project-123',
        taskId: 'task-456',
      })).rejects.toThrow(ToolExecutionError);
    });

    it('should handle optional fields correctly', async () => {
      const mockProgressEntry = {
        id: 'progress-789',
        title: 'Detailed Progress',
        description: 'Detailed description',
        progress_type: ProgressType.PHOTO_EVIDENCE,
        progress_value: 50,
        project_id: 'project-789',
        task_id: null,
        user_id: 'test-user-id',
        tenant_id: 'test-tenant-id',
        notes: 'Some notes',
        status: 'in-progress',
        photo_urls: ['https://example.com/photo1.jpg'],
        metadata: { key: 'value' },
        recorded_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockProgressService.create.mockResolvedValue(mockProgressEntry);

      const tool = createProgressUpdateTool(mockProgressService, mockContext);
      const result = await tool.call({
        title: 'Detailed Progress',
        description: 'Detailed description',
        progressType: ProgressType.PHOTO_EVIDENCE,
        progressValue: 50,
        projectId: 'project-789',
        photoUrls: ['https://example.com/photo1.jpg'],
        notes: 'Some notes',
        status: 'in-progress',
        metadata: { key: 'value' },
      });

      expect(result).toContain('進度更新成功');
      expect(result).toContain('Detailed Progress');
      expect(result).toContain('Detailed description');
      expect(result).toContain('50%');
      expect(result).toContain('Some notes');
      expect(result).toContain('in-progress');
    });

    it('should handle custom recordedAt timestamp', async () => {
      const customTimestamp = '2024-01-15T10:30:00.000Z';
      const mockProgressEntry = {
        id: 'progress-timestamp',
        title: 'Timestamped Progress',
        description: null,
        progress_type: ProgressType.TASK_UPDATE,
        progress_value: null,
        project_id: 'project-123',
        task_id: null,
        user_id: 'test-user-id',
        tenant_id: 'test-tenant-id',
        status: null,
        notes: null,
        photo_urls: null,
        metadata: null,
        recorded_at: new Date(customTimestamp),
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockProgressService.create.mockResolvedValue(mockProgressEntry);

      const tool = createProgressUpdateTool(mockProgressService, mockContext);
      await tool.call({
        title: 'Timestamped Progress',
        progressType: ProgressType.TASK_UPDATE,
        projectId: 'project-123',
        recordedAt: customTimestamp,
      });

      expect(mockProgressService.create).toHaveBeenCalledWith(
        expect.objectContaining({
          recorded_at: new Date(customTimestamp),
        }),
        mockContext.tenantId,
        mockContext.userId
      );
    });

    it('should handle service errors gracefully', async () => {
      mockProgressService.create.mockRejectedValue(new Error('Database error'));

      const tool = createProgressUpdateTool(mockProgressService, mockContext);

      await expect(tool.call({
        title: 'Test Progress',
        progressType: ProgressType.TASK_UPDATE,
        projectId: 'project-123',
      })).rejects.toThrow(ToolExecutionError);
    });

    it('should validate input schema correctly', () => {
      const tool = createProgressUpdateTool(mockProgressService, mockContext);

      // Valid input
      const validInput = {
        title: 'Test Progress',
        progressType: ProgressType.TASK_UPDATE,
        progressValue: 75,
        projectId: 'project-123',
      };
      const parsed = tool.schema.parse(validInput);
      expect(parsed.title).toBe('Test Progress');
      expect(parsed.progressType).toBe(ProgressType.TASK_UPDATE);
      expect(parsed.progressValue).toBe(75);

      // Invalid input - empty title
      expect(() => {
        tool.schema.parse({ title: '', progressType: ProgressType.TASK_UPDATE });
      }).toThrow();

      // Invalid input - progressValue out of range
      expect(() => {
        tool.schema.parse({
          title: 'Test',
          progressType: ProgressType.TASK_UPDATE,
          progressValue: 150,
        });
      }).toThrow();

      // Invalid input - invalid URL in photoUrls
      expect(() => {
        tool.schema.parse({
          title: 'Test',
          progressType: ProgressType.TASK_UPDATE,
          photoUrls: ['not-a-url'],
        });
      }).toThrow();
    });

    it('should enforce maxPhotoUrls limit', () => {
      const config: ProgressUpdateConfig = { maxPhotoUrls: 2 };
      const tool = createProgressUpdateTool(mockProgressService, mockContext, config);

      expect(() => {
        tool.schema.parse({
          title: 'Test',
          progressType: ProgressType.TASK_UPDATE,
          photoUrls: [
            'https://example.com/1.jpg',
            'https://example.com/2.jpg',
            'https://example.com/3.jpg', // Exceeds limit
          ],
        });
      }).toThrow();
    });
  });

  describe('ProgressUpdateToolsFactory static methods', () => {
    it('should create progress update tool via static method', () => {
      const tool = ProgressUpdateToolsFactory.createProgressUpdateTool(
        mockProgressService,
        mockContext
      );
      
      expect(tool).toBeDefined();
      expect(tool.name).toBe('progress_update');
    });

    it('should create LangChain tools array', () => {
      const tools = ProgressUpdateToolsFactory.createLangChainTools(
        mockProgressService,
        mockContext
      );
      
      expect(Array.isArray(tools)).toBe(true);
      expect(tools).toHaveLength(1);
      expect(tools[0].name).toBe('progress_update');
    });

    it('should create tools with custom config', () => {
      const config: ProgressUpdateConfig = { maxPhotoUrls: 3 };
      
      const tools = ProgressUpdateToolsFactory.createLangChainTools(
        mockProgressService,
        mockContext,
        config
      );
      
      expect(tools[0]).toBeDefined();
      expect(tools[0].name).toBe('progress_update');
    });

    it('should pass service correctly to underlying tool', () => {
      const tool = ProgressUpdateToolsFactory.createProgressUpdateTool(
        mockProgressService,
        mockContext
      );

      expect(tool).toBeDefined();
      expect(typeof tool.call).toBe('function');
    });
  });

  describe('progress type validation', () => {
    it('should accept all valid ProgressType values', () => {
      const tool = createProgressUpdateTool(mockProgressService, mockContext);

      Object.values(ProgressType).forEach(progressType => {
        const input = {
          title: 'Test',
          progressType,
          projectId: 'project-123',
        };
        
        expect(() => tool.schema.parse(input)).not.toThrow();
      });
    });

    it('should reject invalid progress type', () => {
      const tool = createProgressUpdateTool(mockProgressService, mockContext);

      expect(() => {
        tool.schema.parse({
          title: 'Test',
          progressType: 'INVALID_TYPE' as any,
          projectId: 'project-123',
        });
      }).toThrow();
    });
  });
}); 