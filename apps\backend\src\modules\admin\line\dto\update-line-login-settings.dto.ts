import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean } from 'class-validator';

export class UpdateLineLoginSettingsDto {
  @ApiProperty({ description: 'LINE Channel ID', required: false })
  @IsOptional()
  @IsString()
  channelId?: string;

  @ApiProperty({ description: 'LINE Channel Secret', required: false })
  @IsOptional()
  @IsString()
  channelSecret?: string;

  @ApiProperty({ description: '是否啟用 LINE Login', required: false })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;
}
