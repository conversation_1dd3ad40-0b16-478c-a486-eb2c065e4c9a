import * as path from "path";

/**
 * 權限同步配置
 */
export const DEFAULT_CONFIG = {
  // 掃描路徑配置
  scan: {
    backend: {
      patterns: [
        "src/**/*.controller.ts",
        "src/**/*.guard.ts",
        "src/**/*.service.ts", 
        "src/**/*.decorator.ts",
        "src/**/casl/**/*.ts"
      ],
      baseDir: path.join(__dirname, "../..")
    },
    frontend: {
      patterns: [
        "../frontend/src/**/*.vue",
        "../frontend/src/**/*.ts",
        "../frontend/src/**/*.js"
      ],
      baseDir: path.join(__dirname, "../..")
    },
    seed: {
      patterns: [
        "prisma/seed.ts",
        "prisma/templates/**/*.ts"
      ],
      baseDir: path.join(__dirname, "../..")
    }
  },

  // 快取配置
  cache: {
    enabled: true,
    path: ".cache/permissions.hash",
    ttl: 3600000 // 1 小時
  },

  // 報告配置
  reports: {
    outputDir: path.join(__dirname, "../../reports"),
    formats: ["json", "markdown"],
    includeDetails: true
  },

  // 排除模式
  exclude: {
    directories: ["node_modules", "dist", ".git", ".cache", "reports", "test", "tests"],
    files: ["*.test.ts", "*.spec.ts", "*.d.ts"]
  },

  // 權限推斷規則
  inference: {
    scopes: {
      system: ["system", "admin", "global"],
      tenant: ["tenant", "organization", "org"],
      workspace: ["workspace", "project", "space"],
      global: [] // 預設
    },
    categories: {
      user: ["user", "account", "profile"],
      tenant: ["tenant", "organization", "org"],
      workspace: ["workspace", "project", "space"],
      role: ["role", "permission", "auth"],
      bot: ["bot", "ai", "assistant"],
      audit: ["audit", "log", "history"],
      system: ["system", "admin", "config"]
    }
  }
};

/**
 * 取得掃描配置
 */
export function getScanConfig(overrides?: any) {
  const config = {
    backendPaths: DEFAULT_CONFIG.scan.backend.patterns,
    frontendPaths: DEFAULT_CONFIG.scan.frontend.patterns,
    seedPaths: DEFAULT_CONFIG.scan.seed.patterns,
    excludePatterns: [
      ...DEFAULT_CONFIG.exclude.directories,
      ...DEFAULT_CONFIG.exclude.files
    ],
    cacheEnabled: DEFAULT_CONFIG.cache.enabled,
    cachePath: DEFAULT_CONFIG.cache.path,
    /** 快取過期時間（毫秒） */
    ttl: DEFAULT_CONFIG.cache.ttl,
    inference: DEFAULT_CONFIG.inference,
    ...overrides
  };
  // 如果後端與前端掃描路徑都為空，則不掃描 Seed 檔案
  if ((overrides?.backendPaths?.length === 0) && (overrides?.frontendPaths?.length === 0)) {
    config.seedPaths = [];
  }
  return config;
}
