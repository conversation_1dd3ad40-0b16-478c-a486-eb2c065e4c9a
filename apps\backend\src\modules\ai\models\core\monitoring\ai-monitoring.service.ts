import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { AiPricingService } from '../../configuration/pricing/ai-pricing.service';
import { AiErrorHandlerService } from './ai-error-handler.service';

/**
 * 監控指標
 */
interface MetricData {
  timestamp: number;
  provider: string;
  model?: string;
  operation: string;
  duration: number;
  success: boolean;
  errorType?: string;
  errorMessage?: string;
  requestSize?: number;
  responseSize?: number;
  tokens?: {
    input: number;
    output: number;
    total: number;
  };
  cost?: number;
  retryCount?: number;
  circuitBreakerState?: string;
}

/**
 * 聚合統計
 */
interface AggregatedStats {
  provider: string;
  model?: string;
  timeWindow: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  successRate: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  totalTokens: number;
  totalCost: number;
  errorBreakdown: Record<string, number>;
  circuitBreakerTriggers: number;
  retryAttempts: number;
}

/**
 * 警報配置
 */
interface AlertConfig {
  name: string;
  condition:
    | 'error_rate'
    | 'response_time'
    | 'failure_count'
    | 'availability'
    | 'cost_threshold'
    | 'circuit_breaker_open';
  threshold: number;
  timeWindow: number; // minutes
  enabled: boolean;
  severity: 'info' | 'warning' | 'error' | 'critical';
  cooldown: number; // minutes
  providers?: string[]; // 特定供應商
  models?: string[]; // 特定模型
}

/**
 * 警報事件
 */
interface AlertEvent {
  id: string;
  alertName: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  provider: string;
  model?: string;
  currentValue: number;
  threshold: number;
  acknowledged: boolean;
  resolvedAt?: number;
}

/**
 * 性能基準
 */
interface PerformanceBenchmark {
  provider: string;
  model?: string;
  expectedResponseTime: number;
  expectedSuccessRate: number;
  expectedCostPerToken: number;
  lastUpdated: number;
}

@Injectable()
export class AiMonitoringService implements OnModuleDestroy {
  private readonly logger = new Logger(AiMonitoringService.name);
  private readonly metrics: MetricData[] = [];
  private readonly maxMetricsRetention = 10000; // 保留最近 10k 條記錄
  private readonly alertEvents: AlertEvent[] = [];
  private readonly lastAlertTimes = new Map<string, number>();
  private readonly performanceBenchmarks = new Map<string, PerformanceBenchmark>();
  private metricsCleanupInterval?: NodeJS.Timeout;
  private alertCheckInterval?: NodeJS.Timeout;
  private benchmarkUpdateInterval?: NodeJS.Timeout;

  // 預設警報配置
  private readonly defaultAlerts: AlertConfig[] = [
    {
      name: 'high_error_rate',
      condition: 'error_rate',
      threshold: 0.1, // 10% 錯誤率
      timeWindow: 5,
      enabled: true,
      severity: 'warning',
      cooldown: 15,
    },
    {
      name: 'critical_error_rate',
      condition: 'error_rate',
      threshold: 0.25, // 25% 錯誤率
      timeWindow: 5,
      enabled: true,
      severity: 'critical',
      cooldown: 10,
    },
    {
      name: 'slow_response_time',
      condition: 'response_time',
      threshold: 30000, // 30 秒
      timeWindow: 10,
      enabled: true,
      severity: 'warning',
      cooldown: 20,
    },
    {
      name: 'very_slow_response_time',
      condition: 'response_time',
      threshold: 60000, // 60 秒
      timeWindow: 5,
      enabled: true,
      severity: 'error',
      cooldown: 15,
    },
    {
      name: 'high_failure_count',
      condition: 'failure_count',
      threshold: 10, // 10 次失敗
      timeWindow: 5,
      enabled: true,
      severity: 'error',
      cooldown: 15,
    },
    {
      name: 'low_availability',
      condition: 'availability',
      threshold: 0.95, // 95% 可用性
      timeWindow: 10,
      enabled: true,
      severity: 'error',
      cooldown: 30,
    },
    {
      name: 'high_cost',
      condition: 'cost_threshold',
      threshold: 100, // $100
      timeWindow: 60, // 1 小時
      enabled: true,
      severity: 'warning',
      cooldown: 60,
    },
    {
      name: 'circuit_breaker_open',
      condition: 'circuit_breaker_open',
      threshold: 1, // 任何斷路器開啟
      timeWindow: 1,
      enabled: true,
      severity: 'critical',
      cooldown: 5,
    },
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly prisma: PrismaService,
    private readonly pricingService: AiPricingService,
    private readonly errorHandlerService?: AiErrorHandlerService, // 可選注入，避免循環依賴
  ) {
    this.initializeService();
  }

  /**
   * 初始化服務
   */
  private initializeService(): void {
    // 定期清理舊指標
    this.metricsCleanupInterval = setInterval(() => this.cleanupOldMetrics(), 300000); // 每 5 分鐘清理一次

    // 定期檢查警報條件
    this.alertCheckInterval = setInterval(() => this.checkAlerts(), 60000); // 每分鐘檢查一次

    // 定期更新性能基準
    this.benchmarkUpdateInterval = setInterval(() => this.updatePerformanceBenchmarks(), 3600000); // 每小時更新一次

    this.logger.log('AI 監控服務已初始化');
  }

  /**
   * 模組銷毀時清理資源
   */
  onModuleDestroy(): void {
    if (this.metricsCleanupInterval) {
      clearInterval(this.metricsCleanupInterval);
    }
    if (this.alertCheckInterval) {
      clearInterval(this.alertCheckInterval);
    }
    if (this.benchmarkUpdateInterval) {
      clearInterval(this.benchmarkUpdateInterval);
    }
    this.logger.log('AI 監控服務已清理');
  }

  /**
   * 記錄 AI 操作指標
   */
  async recordMetric(metricData: Omit<MetricData, 'timestamp'>): Promise<void> {
    const metric: MetricData = {
      ...metricData,
      timestamp: Date.now(),
    };

    // 計算成本
    if (metric.tokens && !metric.cost) {
      try {
        const pricing = await this.pricingService.calculateCost(
          metric.provider,
          metric.model || 'default',
          metric.tokens.input,
          metric.tokens.output,
        );
        metric.cost = pricing.toNumber();
      } catch (error) {
        this.logger.warn(`無法計算成本: ${error.message}`);
      }
    }

    // 獲取斷路器狀態
    if (this.errorHandlerService) {
      const cbStatus = this.errorHandlerService.getCircuitBreakerStatus(
        metric.provider,
        metric.model,
      );
      if (cbStatus) {
        metric.circuitBreakerState = cbStatus.state;
      }
    }

    this.metrics.push(metric);

    // 發送事件
    this.eventEmitter.emit('ai.metric.recorded', metric);

    // 實時檢查警報條件（僅針對關鍵指標）
    if (!metric.success || metric.duration > 30000) {
      await this.checkCriticalAlerts(metric);
    }

    this.logger.debug(
      `記錄 AI 指標: ${metric.provider}/${metric.model || 'default'} - ${metric.operation}`,
    );
  }

  /**
   * 記錄 AI 操作開始
   */
  startOperation(
    provider: string,
    model?: string,
    operation = 'execute',
  ): {
    finish: (
      success: boolean,
      error?: Error,
      tokens?: { input: number; output: number },
    ) => Promise<void>;
  } {
    const startTime = Date.now();

    return {
      finish: async (
        success: boolean,
        error?: Error,
        tokens?: { input: number; output: number },
      ) => {
        const duration = Date.now() - startTime;

        await this.recordMetric({
          provider,
          model,
          operation,
          duration,
          success,
          errorType: error?.constructor.name,
          errorMessage: error?.message,
          tokens: tokens ? { ...tokens, total: tokens.input + tokens.output } : undefined,
          retryCount: error && 'retryCount' in error ? (error as any).retryCount : 0,
        });
      },
    };
  }

  /**
   * 獲取聚合統計
   */
  async getAggregatedStats(timeWindowMinutes = 60): Promise<AggregatedStats[]> {
    const cutoffTime = Date.now() - timeWindowMinutes * 60 * 1000;
    const relevantMetrics = this.metrics.filter((m) => m.timestamp >= cutoffTime);

    // 按 provider 和 model 分組
    const groupedMetrics = new Map<string, MetricData[]>();

    relevantMetrics.forEach((metric) => {
      const key = `${metric.provider}:${metric.model || 'default'}`;
      if (!groupedMetrics.has(key)) {
        groupedMetrics.set(key, []);
      }
      groupedMetrics.get(key)!.push(metric);
    });

    const stats: AggregatedStats[] = [];

    groupedMetrics.forEach((metrics, key) => {
      const [provider, model] = key.split(':');
      const successfulMetrics = metrics.filter((m) => m.success);
      const failedMetrics = metrics.filter((m) => !m.success);
      const durations = metrics.map((m) => m.duration).sort((a, b) => a - b);

      // 計算百分位數
      const p95Index = Math.floor(durations.length * 0.95);
      const p99Index = Math.floor(durations.length * 0.99);

      // 錯誤分析
      const errorBreakdown: Record<string, number> = {};
      failedMetrics.forEach((metric) => {
        const errorType = metric.errorType || 'Unknown';
        errorBreakdown[errorType] = (errorBreakdown[errorType] || 0) + 1;
      });

      // 統計斷路器觸發次數
      const circuitBreakerTriggers = metrics.filter((m) => m.circuitBreakerState === 'open').length;

      // 統計重試次數
      const retryAttempts = metrics.reduce((sum, m) => sum + (m.retryCount || 0), 0);

      stats.push({
        provider,
        model: model === 'default' ? undefined : model,
        timeWindow: timeWindowMinutes,
        totalRequests: metrics.length,
        successfulRequests: successfulMetrics.length,
        failedRequests: failedMetrics.length,
        successRate: metrics.length > 0 ? successfulMetrics.length / metrics.length : 0,
        averageResponseTime:
          durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0,
        p95ResponseTime: durations.length > 0 ? durations[p95Index] || 0 : 0,
        p99ResponseTime: durations.length > 0 ? durations[p99Index] || 0 : 0,
        totalTokens: metrics.reduce((sum, m) => sum + (m.tokens?.total || 0), 0),
        totalCost: metrics.reduce((sum, m) => sum + (m.cost || 0), 0),
        errorBreakdown,
        circuitBreakerTriggers,
        retryAttempts,
      });
    });

    return stats.sort((a, b) => b.totalRequests - a.totalRequests);
  }

  /**
   * 清理舊指標
   */
  private cleanupOldMetrics(): void {
    if (this.metrics.length > this.maxMetricsRetention) {
      const removeCount = this.metrics.length - this.maxMetricsRetention;
      this.metrics.splice(0, removeCount);
      this.logger.debug(`清理了 ${removeCount} 條舊指標`);
    }

    // 清理舊警報事件（保留最近 24 小時）
    const alertCutoff = Date.now() - 24 * 60 * 60 * 1000;
    const initialAlertCount = this.alertEvents.length;
    for (let i = this.alertEvents.length - 1; i >= 0; i--) {
      if (this.alertEvents[i].timestamp < alertCutoff) {
        this.alertEvents.splice(i, 1);
      }
    }

    if (this.alertEvents.length < initialAlertCount) {
      this.logger.debug(`清理了 ${initialAlertCount - this.alertEvents.length} 條舊警報事件`);
    }
  }

  /**
   * 檢查警報條件
   */
  private async checkAlerts(): Promise<void> {
    try {
      const stats = await this.getAggregatedStats(5); // 檢查最近 5 分鐘的數據

      for (const alertConfig of this.defaultAlerts) {
        if (!alertConfig.enabled) continue;

        for (const stat of stats) {
          // 檢查是否匹配供應商和模型過濾條件
          if (alertConfig.providers && !alertConfig.providers.includes(stat.provider)) continue;
          if (alertConfig.models && stat.model && !alertConfig.models.includes(stat.model))
            continue;

          this.evaluateAlert(alertConfig, stat);
        }
      }
    } catch (error) {
      this.logger.error(`檢查警報時發生錯誤: ${error.message}`, error.stack);
    }
  }

  /**
   * 檢查關鍵警報（實時）
   */
  private async checkCriticalAlerts(metric: MetricData): Promise<void> {
    const criticalAlerts = this.defaultAlerts.filter(
      (alert) => alert.severity === 'critical' && alert.enabled,
    );

    for (const alertConfig of criticalAlerts) {
      if (
        alertConfig.condition === 'circuit_breaker_open' &&
        metric.circuitBreakerState === 'open'
      ) {
        this.triggerAlert(
          alertConfig,
          {
            provider: metric.provider,
            model: metric.model,
            timeWindow: 1,
            totalRequests: 1,
            successfulRequests: 0,
            failedRequests: 1,
            successRate: 0,
            averageResponseTime: metric.duration,
            p95ResponseTime: metric.duration,
            p99ResponseTime: metric.duration,
            totalTokens: metric.tokens?.total || 0,
            totalCost: metric.cost || 0,
            errorBreakdown: { [metric.errorType || 'Unknown']: 1 },
            circuitBreakerTriggers: 1,
            retryAttempts: metric.retryCount || 0,
          },
          1,
        );
      }
    }
  }

  /**
   * 評估單個警報
   */
  private evaluateAlert(alertConfig: AlertConfig, stat: AggregatedStats): void {
    const alertKey = `${alertConfig.name}:${stat.provider}:${stat.model || 'default'}`;
    const lastAlertTime = this.lastAlertTimes.get(alertKey) || 0;
    const cooldownPeriod = alertConfig.cooldown * 60 * 1000;

    // 檢查冷卻期
    if (Date.now() - lastAlertTime < cooldownPeriod) {
      return;
    }

    let currentValue: number;
    let shouldAlert = false;

    switch (alertConfig.condition) {
      case 'error_rate':
        currentValue = 1 - stat.successRate;
        shouldAlert = currentValue > alertConfig.threshold;
        break;
      case 'response_time':
        currentValue = stat.p95ResponseTime;
        shouldAlert = currentValue > alertConfig.threshold;
        break;
      case 'failure_count':
        currentValue = stat.failedRequests;
        shouldAlert = currentValue > alertConfig.threshold;
        break;
      case 'availability':
        currentValue = stat.successRate;
        shouldAlert = currentValue < alertConfig.threshold;
        break;
      case 'cost_threshold':
        currentValue = stat.totalCost;
        shouldAlert = currentValue > alertConfig.threshold;
        break;
      case 'circuit_breaker_open':
        currentValue = stat.circuitBreakerTriggers;
        shouldAlert = currentValue > alertConfig.threshold;
        break;
      default:
        return;
    }

    if (shouldAlert) {
      this.triggerAlert(alertConfig, stat, currentValue);
      this.lastAlertTimes.set(alertKey, Date.now());
    }
  }

  /**
   * 觸發警報
   */
  private triggerAlert(
    alertConfig: AlertConfig,
    stat: AggregatedStats,
    currentValue: number,
  ): void {
    const alertEvent: AlertEvent = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      alertName: alertConfig.name,
      severity: alertConfig.severity,
      message: this.generateAlertMessage(alertConfig, stat, currentValue),
      timestamp: Date.now(),
      provider: stat.provider,
      model: stat.model,
      currentValue,
      threshold: alertConfig.threshold,
      acknowledged: false,
    };

    this.alertEvents.push(alertEvent);

    // 發送事件
    this.eventEmitter.emit('ai.alert.triggered', alertEvent);

    this.logger.warn(`觸發警報: ${alertEvent.message}`, {
      alertName: alertConfig.name,
      severity: alertConfig.severity,
      provider: stat.provider,
      model: stat.model,
    });

    // 對於關鍵警報，可以發送額外通知
    if (alertConfig.severity === 'critical') {
      this.eventEmitter.emit('ai.alert.critical', alertEvent);
    }
  }

  /**
   * 生成警報訊息
   */
  private generateAlertMessage(
    alertConfig: AlertConfig,
    stat: AggregatedStats,
    currentValue: number,
  ): string {
    const service = `${stat.provider}${stat.model ? `/${stat.model}` : ''}`;

    switch (alertConfig.condition) {
      case 'error_rate':
        return `${service} 錯誤率過高: ${(currentValue * 100).toFixed(2)}% (閾值: ${(alertConfig.threshold * 100).toFixed(2)}%)`;
      case 'response_time':
        return `${service} 響應時間過慢: ${currentValue}ms (閾值: ${alertConfig.threshold}ms)`;
      case 'failure_count':
        return `${service} 失敗次數過多: ${currentValue} 次 (閾值: ${alertConfig.threshold})`;
      case 'availability':
        return `${service} 可用性過低: ${(currentValue * 100).toFixed(2)}% (閾值: ${(alertConfig.threshold * 100).toFixed(2)}%)`;
      case 'cost_threshold':
        return `${service} 成本過高: $${currentValue.toFixed(2)} (閾值: $${alertConfig.threshold})`;
      case 'circuit_breaker_open':
        return `${service} 斷路器已開啟 (觸發次數: ${currentValue})`;
      default:
        return `${service} 出現異常`;
    }
  }

  /**
   * 獲取活躍警報
   */
  getActiveAlerts(): AlertEvent[] {
    return this.alertEvents.filter((alert) => !alert.acknowledged && !alert.resolvedAt);
  }

  /**
   * 確認警報
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alertEvents.find((a) => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.eventEmitter.emit('ai.alert.acknowledged', alert);
      return true;
    }
    return false;
  }

  /**
   * 解決警報
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alertEvents.find((a) => a.id === alertId);
    if (alert) {
      alert.resolvedAt = Date.now();
      this.eventEmitter.emit('ai.alert.resolved', alert);
      return true;
    }
    return false;
  }

  /**
   * 獲取即時健康狀態
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    const stats = await this.getAggregatedStats(5); // 最近 5 分鐘
    const overallHealth = {
      status: 'healthy',
      totalServices: stats.length,
      healthyServices: 0,
      degradedServices: 0,
      unhealthyServices: 0,
      alerts: this.getActiveAlerts().length,
      totalRequests: stats.reduce((sum, s) => sum + s.totalRequests, 0),
      totalCost: stats.reduce((sum, s) => sum + s.totalCost, 0),
      averageSuccessRate:
        stats.length > 0 ? stats.reduce((sum, s) => sum + s.successRate, 0) / stats.length : 1,
    };

    stats.forEach((stat) => {
      if (stat.successRate >= 0.95 && stat.p95ResponseTime < 30000) {
        overallHealth.healthyServices++;
      } else if (stat.successRate >= 0.8 && stat.p95ResponseTime < 60000) {
        overallHealth.degradedServices++;
        if (overallHealth.status === 'healthy') {
          overallHealth.status = 'degraded';
        }
      } else {
        overallHealth.unhealthyServices++;
        overallHealth.status = 'unhealthy';
      }
    });

    // 獲取斷路器狀態
    const circuitBreakerStates: Record<string, any> = {};
    if (this.errorHandlerService) {
      const healthReport = this.errorHandlerService.getHealthReport();
      Object.entries(healthReport).forEach(([key, health]) => {
        if (health.circuitBreakerStats) {
          circuitBreakerStates[key] = health.circuitBreakerStats;
        }
      });
    }

    return {
      overall: overallHealth,
      services: stats,
      activeAlerts: this.getActiveAlerts(),
      circuitBreakers: circuitBreakerStates,
      benchmarks: this.getPerformanceBenchmarks(),
    };
  }

  /**
   * 更新性能基準
   */
  private async updatePerformanceBenchmarks(): Promise<void> {
    try {
      const stats = await this.getAggregatedStats(1440); // 最近 24 小時

      stats.forEach((stat) => {
        if (stat.totalRequests >= 10) {
          // 只有足夠的樣本才更新基準
          const key = `${stat.provider}:${stat.model || 'default'}`;
          this.performanceBenchmarks.set(key, {
            provider: stat.provider,
            model: stat.model,
            expectedResponseTime: stat.p95ResponseTime,
            expectedSuccessRate: stat.successRate,
            expectedCostPerToken: stat.totalTokens > 0 ? stat.totalCost / stat.totalTokens : 0,
            lastUpdated: Date.now(),
          });
        }
      });

      this.logger.debug(`更新了 ${this.performanceBenchmarks.size} 個性能基準`);
    } catch (error) {
      this.logger.error(`更新性能基準時發生錯誤: ${error.message}`, error.stack);
    }
  }

  /**
   * 獲取性能基準
   */
  getPerformanceBenchmarks(): Record<string, PerformanceBenchmark> {
    const benchmarks: Record<string, PerformanceBenchmark> = {};
    this.performanceBenchmarks.forEach((benchmark, key) => {
      benchmarks[key] = benchmark;
    });
    return benchmarks;
  }

  /**
   * 獲取詳細報告
   */
  async getDetailedReport(timeWindowMinutes = 60): Promise<{
    summary: any;
    stats: AggregatedStats[];
    alerts: AlertEvent[];
    trends: any;
  }> {
    const stats = await this.getAggregatedStats(timeWindowMinutes);
    const previousStats = await this.getAggregatedStats(timeWindowMinutes * 2);

    // 計算趨勢
    const trends = this.calculateTrends(stats, previousStats);

    const summary = {
      timeWindow: timeWindowMinutes,
      totalProviders: new Set(stats.map((s) => s.provider)).size,
      totalRequests: stats.reduce((sum, s) => sum + s.totalRequests, 0),
      totalCost: stats.reduce((sum, s) => sum + s.totalCost, 0),
      averageSuccessRate:
        stats.length > 0 ? stats.reduce((sum, s) => sum + s.successRate, 0) / stats.length : 1,
      averageResponseTime:
        stats.length > 0
          ? stats.reduce((sum, s) => sum + s.averageResponseTime, 0) / stats.length
          : 0,
      activeAlerts: this.getActiveAlerts().length,
      totalAlerts: this.alertEvents.filter(
        (a) => a.timestamp >= Date.now() - timeWindowMinutes * 60 * 1000,
      ).length,
    };

    return {
      summary,
      stats,
      alerts: this.alertEvents.slice(-50), // 最近 50 個警報
      trends,
    };
  }

  /**
   * 計算趨勢
   */
  private calculateTrends(
    currentStats: AggregatedStats[],
    previousStats: AggregatedStats[],
  ): Record<string, any> {
    const trends: Record<string, any> = {};

    currentStats.forEach((current) => {
      const key = `${current.provider}:${current.model || 'default'}`;
      const previous = previousStats.find(
        (p) =>
          p.provider === current.provider &&
          (p.model || 'default') === (current.model || 'default'),
      );

      if (previous) {
        trends[key] = {
          requestsChange:
            ((current.totalRequests - previous.totalRequests) /
              Math.max(previous.totalRequests, 1)) *
            100,
          successRateChange: (current.successRate - previous.successRate) * 100,
          responseTimeChange:
            ((current.averageResponseTime - previous.averageResponseTime) /
              Math.max(previous.averageResponseTime, 1)) *
            100,
          costChange:
            ((current.totalCost - previous.totalCost) / Math.max(previous.totalCost, 0.01)) * 100,
        };
      } else {
        trends[key] = {
          requestsChange: 0,
          successRateChange: 0,
          responseTimeChange: 0,
          costChange: 0,
        };
      }
    });

    return trends;
  }

  /**
   * 導出指標數據
   */
  exportMetrics(timeWindowMinutes = 60): MetricData[] {
    const cutoffTime = Date.now() - timeWindowMinutes * 60 * 1000;
    return this.metrics.filter((m) => m.timestamp >= cutoffTime);
  }

  /**
   * 設定自定義警報
   */
  addCustomAlert(alertConfig: AlertConfig): void {
    this.defaultAlerts.push(alertConfig);
    this.logger.log(`添加自定義警報: ${alertConfig.name}`);
  }

  /**
   * 移除自定義警報
   */
  removeCustomAlert(alertName: string): boolean {
    const index = this.defaultAlerts.findIndex((a) => a.name === alertName);
    if (index > -1) {
      this.defaultAlerts.splice(index, 1);
      this.logger.log(`移除自定義警報: ${alertName}`);
      return true;
    }
    return false;
  }
}
