-- AlterTable
ALTER TABLE "ai_keys" ADD COLUMN     "tenant_id" TEXT;

-- AlterTable
ALTER TABLE "ai_models" ADD COLUMN     "ai_key_id" TEXT,
ADD COLUMN     "config" JSON<PERSON>,
ADD COLUMN     "tenant_id" TEXT;

-- CreateTable
CREATE TABLE "vector_documents" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "file_id" TEXT,
    "workspace_id" TEXT,
    "content" TEXT NOT NULL,
    "metadata" JSONB,
    "embedding" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vector_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vector_chunks" (
    "id" TEXT NOT NULL,
    "document_id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "chunk_index" INTEGER NOT NULL,
    "content" TEXT NOT NULL,
    "metadata" JSONB,
    "embedding" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vector_chunks_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "vector_documents_tenant_id_idx" ON "vector_documents"("tenant_id");

-- CreateIndex
CREATE INDEX "vector_documents_file_id_idx" ON "vector_documents"("file_id");

-- CreateIndex
CREATE INDEX "vector_documents_workspace_id_idx" ON "vector_documents"("workspace_id");

-- CreateIndex
CREATE INDEX "vector_chunks_document_id_idx" ON "vector_chunks"("document_id");

-- CreateIndex
CREATE INDEX "vector_chunks_tenant_id_idx" ON "vector_chunks"("tenant_id");

-- CreateIndex
CREATE UNIQUE INDEX "vector_chunks_document_id_chunk_index_key" ON "vector_chunks"("document_id", "chunk_index");

-- CreateIndex
CREATE INDEX "ai_keys_tenant_id_idx" ON "ai_keys"("tenant_id");

-- CreateIndex
CREATE INDEX "ai_models_tenant_id_idx" ON "ai_models"("tenant_id");

-- CreateIndex
CREATE INDEX "ai_models_ai_key_id_idx" ON "ai_models"("ai_key_id");

-- AddForeignKey
ALTER TABLE "ai_keys" ADD CONSTRAINT "ai_keys_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_models" ADD CONSTRAINT "ai_models_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_models" ADD CONSTRAINT "ai_models_ai_key_id_fkey" FOREIGN KEY ("ai_key_id") REFERENCES "ai_keys"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vector_documents" ADD CONSTRAINT "vector_documents_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vector_documents" ADD CONSTRAINT "vector_documents_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vector_documents" ADD CONSTRAINT "vector_documents_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "shared_files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vector_chunks" ADD CONSTRAINT "vector_chunks_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "vector_documents"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vector_chunks" ADD CONSTRAINT "vector_chunks_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
