# HorizAI UI/UX 設計標準規範 (簡潔平衡版)

本文件記錄了 HorizAI 系統的 UI/UX 設計標準，旨在確保全系統設計的一致性、簡潔性和最佳使用者體驗。

## 📏 間距與尺寸標準

### 容器間距 (Container Spacing)
```css
/* 頁面主容器 */
.page-container {
  padding: 1.5rem;           /* p-6 */
  gap: 2rem;                 /* space-y-8 */
}

/* 區塊間距 */
.section-spacing {
  gap: 1.5rem;               /* space-y-6 (主要內容區域) */
  gap: 2rem;                 /* space-y-8 (頁面級間距) */
}

/* 元件內部間距 */
.component-internal {
  padding: 1rem;             /* p-4 (緊湊區域) */
  padding: 1.25rem;          /* p-5 (標準區域) */
  padding: 1.5rem;           /* p-6 (主要內容) */
}
```

### 按鈕尺寸標準 (Button Standards)
```css
/* 主要動作按鈕 */
.primary-button {
  size: sm;                  /* 標準尺寸 */
  icon-size: 1rem;           /* w-4 h-4 */
  padding: 0.5rem 1rem;      /* px-4 py-2 */
}

/* 次要動作按鈕 */
.secondary-button {
  size: sm;                  /* 標準尺寸 */
  padding: 0.75rem;          /* p-3 */
}

/* 圖示按鈕 */
.icon-button {
  padding: 0.5rem;           /* p-2 */
  icon-size: 1rem;           /* w-4 h-4 */
}
```

## 🎨 視覺元素標準

### 圖示尺寸 (Icon Sizes)
```css
/* 頁面標題圖示 */
.page-title-icon {
  container: 0.75rem;        /* p-3 */
  icon: 1.25rem;             /* w-5 h-5 */
  border-radius: 0.5rem;     /* rounded-lg */
}

/* 導航圖示 */
.nav-icon {
  container: 0.5rem;         /* p-2 */
  icon: 1rem;                /* w-4 h-4 */
  border-radius: 0.375rem;   /* rounded-md */
}

/* 卡片圖示 */
.card-icon {
  container: 0.5rem;         /* p-2 */
  icon: 1rem;                /* w-4 h-4 */
  border-radius: 0.375rem;   /* rounded-md */
}

/* 快速操作圖示 */
.quick-action-icon {
  container: 0.75rem;        /* p-3 */
  icon: 1.5rem;              /* w-6 h-6 */
  border-radius: 0.75rem;    /* rounded-xl */
}
```

### 字體尺寸階層 (Typography Scale)
```css
/* 頁面主標題 */
.page-title {
  font-size: 1.875rem;       /* text-3xl */
  font-weight: 700;          /* font-bold */
  line-height: 1.2;
}

/* 區塊標題 */
.section-title {
  font-size: 1.25rem;        /* text-xl */
  font-weight: 600;          /* font-semibold */
}

/* 卡片標題 */
.card-title {
  font-size: 1.125rem;       /* text-lg */
  font-weight: 600;          /* font-semibold */
}

/* 導航標籤 */
.nav-label {
  font-size: 0.875rem;       /* text-sm */
  font-weight: 500;          /* font-medium */
}

/* 描述文字 */
.description-text {
  font-size: 0.875rem;       /* text-sm */
  opacity: 0.7;              /* 次要文字 */
  line-height: 1.5;
}

/* 狀態文字 */
.status-text {
  font-size: 0.75rem;        /* text-xs */
  font-weight: 500;          /* font-medium */
}
```

## 🃏 卡片設計標準 (Card Standards)

### 簡潔卡片設計
```css
/* 主要卡片 */
.primary-card {
  background: white;         /* bg-white */
  border: 1px solid #e5e7eb; /* border border-gray-200 */
  box-shadow: sm;            /* shadow-sm */
  border-radius: 0.5rem;     /* rounded-lg */
}

/* 次要卡片 */
.secondary-card {
  background: white;         /* bg-white */
  border: 1px solid #f3f4f6; /* border border-gray-100 */
  box-shadow: sm;            /* shadow-sm */
  border-radius: 0.5rem;     /* rounded-lg */
}

/* 互動卡片 */
.interactive-card {
  background: white;         /* bg-white */
  border: 1px solid #e5e7eb; /* border border-gray-200 */
  box-shadow: sm;            /* shadow-sm */
  transition: all 0.2s;     /* transition-all duration-200 */
}

.interactive-card:hover {
  box-shadow: md;            /* hover:shadow-md */
  border-color: #d1d5db;     /* hover:border-gray-300 */
}
```

### 圓角標準 (Border Radius)
```css
/* 主要容器 */
.primary-container {
  border-radius: 0.75rem;    /* rounded-xl */
}

/* 次要容器 */
.secondary-container {
  border-radius: 0.5rem;     /* rounded-lg */
}

/* 小型元件 */
.small-component {
  border-radius: 0.375rem;   /* rounded-md */
}

/* 按鈕和標籤 */
.button-radius {
  border-radius: 0.375rem;   /* rounded-md */
}

/* 狀態指示器 */
.status-indicator {
  border-radius: 9999px;     /* rounded-full */
}
```

## 🎯 互動元素標準

### 適度懸停效果 (Hover Effects)
```css
/* 輕微懸停縮放 */
.hover-scale {
  transform: scale(1.02);    /* group-hover:scale-102 */
}

/* 懸停陰影 */
.hover-shadow {
  box-shadow: md;            /* hover:shadow-md */
}

/* 懸停邊框 */
.hover-border {
  border-color: #9ca3af;     /* hover:border-gray-400 */
}
```

### 動畫過渡 (Transitions)
```css
/* 標準過渡 */
.standard-transition {
  transition: all 0.2s ease; /* transition-all duration-200 */
}

/* 快速過渡 */
.fast-transition {
  transition: all 0.15s ease; /* transition-all duration-150 */
}
```

## 📱 響應式設計標準

### 間距響應式調整
```css
/* 行動裝置 (< 768px) */
@media (max-width: 767px) {
  .responsive-padding {
    padding: 1rem;            /* p-4 */
  }
  
  .responsive-gap {
    gap: 1.5rem;              /* space-y-6 */
  }
}

/* 平板裝置 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .responsive-padding {
    padding: 1.25rem;         /* p-5 */
  }
}

/* 桌面裝置 (≥ 1024px) */
@media (min-width: 1024px) {
  .responsive-padding {
    padding: 1.5rem;          /* p-6 */
  }
}
```

## 🎨 顏色與狀態標準

### 狀態指示器尺寸
```css
/* 標準狀態指示器 */
.status-standard {
  width: 0.5rem;             /* w-2 */
  height: 0.5rem;            /* h-2 */
}

/* 大型狀態指示器 */
.status-large {
  width: 0.625rem;           /* w-2.5 */
  height: 0.625rem;          /* h-2.5 */
}
```

### 徽章設計
```css
/* 標準徽章 */
.standard-badge {
  padding: 0.25rem 0.5rem;   /* px-2 py-1 */
  font-size: 0.75rem;        /* text-xs */
  border-radius: 0.25rem;    /* rounded */
  background: #f3f4f6;       /* bg-gray-100 */
  color: #374151;            /* text-gray-700 */
}

/* 狀態徽章 */
.status-badge {
  padding: 0.25rem 0.5rem;   /* px-2 py-1 */
  font-size: 0.75rem;        /* text-xs */
  border-radius: 0.25rem;    /* rounded */
  font-weight: 500;          /* font-medium */
}
```

## 📋 子頁面設計標準

### 設定頁面表單標準
```css
/* 表單容器 */
.form-container {
  padding: 1.5rem;           /* p-6 */
  gap: 1.5rem;               /* space-y-6 */
  background: white;         /* bg-white */
  border: 1px solid #e5e7eb; /* border border-gray-200 */
  border-radius: 0.5rem;     /* rounded-lg */
}

/* 表單輸入框 */
.form-input {
  height: 2.5rem;            /* h-10 */
  font-size: 0.875rem;       /* text-sm */
  padding: 0.5rem 0.75rem;   /* px-3 py-2 */
  border: 1px solid #d1d5db; /* border border-gray-300 */
  border-radius: 0.375rem;   /* rounded-md */
}

/* 表單分組 */
.form-group {
  padding: 1.5rem;           /* p-6 */
  background: white;         /* bg-white */
  border: 1px solid #e5e7eb; /* border border-gray-200 */
  border-radius: 0.5rem;     /* rounded-lg */
  gap: 1rem;                 /* space-y-4 */
}

/* 表單標籤 */
.form-label {
  font-size: 0.875rem;       /* text-sm */
  font-weight: 500;          /* font-medium */
  margin-bottom: 0.25rem;    /* mb-1 */
  color: #374151;            /* text-gray-700 */
}
```

### 列表頁面標準
```css
/* 列表容器 */
.list-container {
  gap: 0.5rem;               /* space-y-2 */
}

/* 列表項目 */
.list-item {
  padding: 1rem;             /* p-4 */
  background: white;         /* bg-white */
  border: 1px solid #f3f4f6; /* border border-gray-100 */
  border-radius: 0.5rem;     /* rounded-lg */
  transition: all 0.2s;     /* transition-all duration-200 */
}

.list-item:hover {
  border-color: #e5e7eb;     /* hover:border-gray-200 */
  box-shadow: sm;            /* hover:shadow-sm */
}

/* 操作按鈕 */
.action-button {
  padding: 0.5rem;           /* p-2 */
  border-radius: 0.375rem;   /* rounded-md */
  transition: all 0.2s;     /* transition-all duration-200 */
}

.action-button:hover {
  background: #f9fafb;       /* hover:bg-gray-50 */
}
```

## 📋 實作範例

### 簡潔 Dashboard 卡片模板
```vue
<template>
  <Card class="bg-white border border-gray-200 shadow-sm rounded-lg">
    <CardContent class="p-6">
      <div class="flex items-center space-x-3">
        <div class="p-2 bg-blue-100 rounded-md">
          <Icon class="w-4 h-4 text-blue-600" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900">標題</h3>
          <p class="text-sm text-gray-500 mt-0.5">描述文字</p>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
```

### 簡潔導航按鈕模板
```vue
<template>
  <button
    class="w-full flex items-center space-x-3 px-4 py-3 text-left transition-colors duration-200 hover:bg-gray-50 rounded-md"
  >
    <div class="p-2 bg-gray-100 rounded-md">
      <Icon class="w-4 h-4 text-gray-600" />
    </div>
    <div class="flex-1">
      <div class="font-medium text-sm text-gray-900">標籤文字</div>
      <div class="text-xs text-gray-500 mt-0.5">描述文字</div>
    </div>
  </button>
</template>
```

### 簡潔快速操作按鈕模板
```vue
<template>
  <Button variant="outline" class="w-full h-auto p-0">
    <div class="w-full p-6 bg-white border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all duration-200">
      <div class="flex flex-col items-center space-y-3">
        <div class="p-3 bg-blue-100 rounded-xl">
          <Icon class="w-6 h-6 text-blue-600" />
        </div>
        <div class="text-center">
          <h3 class="text-lg font-semibold text-gray-900">操作標題</h3>
          <p class="text-sm text-gray-500 mt-1">操作描述</p>
        </div>
        <div class="px-2 py-1 bg-gray-100 rounded text-xs text-gray-600">
          狀態文字
        </div>
      </div>
    </div>
  </Button>
</template>
```

## 🔧 使用指南

### 設計原則
1. **簡潔優先** - 減少不必要的視覺裝飾
2. **功能導向** - 設計服務於功能，而非炫技
3. **一致性** - 保持整個系統的視覺一致性
4. **可讀性** - 確保文字和內容清晰易讀
5. **可用性** - 優先考慮用戶操作體驗

### 避免過度設計
- ❌ 過多的漸層效果
- ❌ 過重的陰影和毛玻璃效果
- ❌ 過於複雜的動畫
- ❌ 過於花俏的色彩搭配
- ❌ 過度的視覺層次

### 推薦做法
- ✅ 使用簡潔的邊框和陰影
- ✅ 適度的留白和間距
- ✅ 清晰的色彩對比
- ✅ 簡單有效的互動回饋
- ✅ 專注於內容和功能

## 📝 更新紀錄

**v2.0** (2024-12-19) - 簡潔平衡版
- 大幅簡化視覺效果，去除過度裝飾
- 減少漸層、陰影、動畫的使用
- 專注於簡潔、功能性的設計
- 保持適度的現代感而不失專業性
- 建立更平衡的設計標準

**v1.1** (2024-12-19)
- 新增 CRUD 操作設計標準
- 建立訊息與通知系統規範
- 定義頁面架構層級標準

**v1.0** (2024-12-19)
- 建立基礎設計標準
- 記錄 AIDashboard 和 AISettings 優化參數

---

*此文件將持續更新，以反映系統設計的最新標準。追求簡潔、實用、平衡的設計理念。*