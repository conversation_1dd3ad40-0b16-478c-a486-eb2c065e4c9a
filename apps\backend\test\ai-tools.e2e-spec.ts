import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/core/prisma/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { AiToolScope } from '@prisma/client';

describe('AI Tools (e2e)', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let jwtService: JwtService;
  let authToken: string;
  let testUserId: string;
  let createdToolId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prismaService = moduleFixture.get<PrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();

    // Create a test user and generate auth token
    const testUser = await prismaService.system_users.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        name: 'Test AI Tools User',
        role: 'SUPER_ADMIN',
      },
    });

    testUserId = testUser.id;
    authToken = jwtService.sign({
      sub: testUser.id,
      email: testUser.email,
      user_type: 'system',
      role: testUser.role,
    });
  });

  afterAll(async () => {
    // Clean up test data
    if (createdToolId) {
      await prismaService.ai_tools.deleteMany({
        where: { id: createdToolId },
      });
    }
    
    await prismaService.system_users.deleteMany({
      where: { id: testUserId },
    });

    await app.close();
  });

  describe('/admin/ai/tools (GET)', () => {
    it('should return all AI tools', () => {
      return request(app.getHttpServer())
        .get('/admin/ai/tools')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should return 401 without auth token', () => {
      return request(app.getHttpServer())
        .get('/admin/ai/tools')
        .expect(401);
    });
  });

  describe('/admin/ai/tools (POST)', () => {
    const createToolDto = {
      key: 'test_e2e_tool',
      name: 'E2E Test Tool',
      description: 'Tool created during E2E testing',
      input_schema: {
        type: 'object',
        properties: {
          input: {
            type: 'string',
            description: 'Test input',
          },
        },
        required: ['input'],
      },
      scope: AiToolScope.SYSTEM,
      is_enabled: true,
    };

    it('should create a new AI tool', () => {
      return request(app.getHttpServer())
        .post('/admin/ai/tools')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createToolDto)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.key).toBe(createToolDto.key);
          expect(res.body.name).toBe(createToolDto.name);
          expect(res.body.description).toBe(createToolDto.description);
          expect(res.body.scope).toBe(createToolDto.scope);
          expect(res.body.is_enabled).toBe(createToolDto.is_enabled);
          expect(res.body.created_by).toBe(testUserId);
          
          // Store the created tool ID for cleanup and further tests
          createdToolId = res.body.id;
        });
    });

    it('should return 400 for invalid data', () => {
      const invalidDto = {
        // Missing required fields
        name: 'Invalid Tool',
      };

      return request(app.getHttpServer())
        .post('/admin/ai/tools')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidDto)
        .expect(400);
    });

    it('should return 409 for duplicate key', () => {
      return request(app.getHttpServer())
        .post('/admin/ai/tools')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createToolDto)
        .expect(409);
    });
  });

  describe('/admin/ai/tools/:id (GET)', () => {
    it('should return a specific AI tool', () => {
      return request(app.getHttpServer())
        .get(`/admin/ai/tools/${createdToolId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(createdToolId);
          expect(res.body.key).toBe('test_e2e_tool');
        });
    });

    it('should return 404 for non-existent tool', () => {
      return request(app.getHttpServer())
        .get('/admin/ai/tools/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('/admin/ai/tools/:id (PATCH)', () => {
    const updateDto = {
      name: 'Updated E2E Test Tool',
      description: 'Updated description for E2E testing',
      is_enabled: false,
    };

    it('should update an AI tool', () => {
      return request(app.getHttpServer())
        .patch(`/admin/ai/tools/${createdToolId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(createdToolId);
          expect(res.body.name).toBe(updateDto.name);
          expect(res.body.description).toBe(updateDto.description);
          expect(res.body.is_enabled).toBe(updateDto.is_enabled);
          expect(res.body.updated_by).toBe(testUserId);
        });
    });

    it('should return 404 for non-existent tool', () => {
      return request(app.getHttpServer())
        .patch('/admin/ai/tools/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateDto)
        .expect(404);
    });
  });

  describe('/admin/ai/tools/:id (DELETE)', () => {
    it('should delete an AI tool', () => {
      return request(app.getHttpServer())
        .delete(`/admin/ai/tools/${createdToolId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(createdToolId);
        });
    });

    it('should return 404 for non-existent tool', () => {
      return request(app.getHttpServer())
        .delete('/admin/ai/tools/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should return 404 when trying to get deleted tool', () => {
      return request(app.getHttpServer())
        .get(`/admin/ai/tools/${createdToolId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('Bot-Tool Assignment API', () => {
    let testBotId: string;
    let testToolId: string;

    beforeAll(async () => {
      // Create a test bot
      const testBot = await prismaService.ai_bots.create({
        data: {
          name: 'Test Bot for Tools',
          description: 'Bot for testing tool assignment',
          scope: 'SYSTEM',
          provider_type: 'OPENAI',
          model_id: 'test-model',
          key_id: 'test-key',
          system_prompt: 'Test prompt',
          created_by: testUserId,
        },
      });
      testBotId = testBot.id;

      // Create a test tool
      const testTool = await prismaService.ai_tools.create({
        data: {
          key: 'test_bot_tool',
          name: 'Test Bot Tool',
          description: 'Tool for testing bot assignment',
          input_schema: { type: 'object', properties: {} },
          scope: AiToolScope.SYSTEM,
          is_enabled: true,
          created_by: testUserId,
        },
      });
      testToolId = testTool.id;
    });

    afterAll(async () => {
      // Clean up test data
      await prismaService.ai_bot_tools.deleteMany({
        where: { bot_id: testBotId },
      });
      await prismaService.ai_bots.deleteMany({
        where: { id: testBotId },
      });
      await prismaService.ai_tools.deleteMany({
        where: { id: testToolId },
      });
    });

    describe('/admin/ai/bots/:id/tools (GET)', () => {
      it('should return bot tools (initially empty)', () => {
        return request(app.getHttpServer())
          .get(`/admin/ai/bots/${testBotId}/tools`)
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200)
          .expect((res) => {
            expect(Array.isArray(res.body)).toBe(true);
            expect(res.body.length).toBe(0);
          });
      });
    });

    describe('/admin/ai/bots/:id/tools (PUT)', () => {
      it('should assign tools to bot', () => {
        return request(app.getHttpServer())
          .put(`/admin/ai/bots/${testBotId}/tools`)
          .set('Authorization', `Bearer ${authToken}`)
          .send({ tool_ids: [testToolId] })
          .expect(200)
          .expect((res) => {
            expect(res.body.message).toContain('successfully assigned');
          });
      });

      it('should return updated bot tools', () => {
        return request(app.getHttpServer())
          .get(`/admin/ai/bots/${testBotId}/tools`)
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200)
          .expect((res) => {
            expect(Array.isArray(res.body)).toBe(true);
            expect(res.body.length).toBe(1);
            expect(res.body[0].id).toBe(testToolId);
          });
      });
    });

    describe('/admin/ai/bots/:id/tools/:toolId (DELETE)', () => {
      it('should remove tool from bot', () => {
        return request(app.getHttpServer())
          .delete(`/admin/ai/bots/${testBotId}/tools/${testToolId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200)
          .expect((res) => {
            expect(res.body.message).toContain('successfully removed');
          });
      });

      it('should return empty bot tools after removal', () => {
        return request(app.getHttpServer())
          .get(`/admin/ai/bots/${testBotId}/tools`)
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200)
          .expect((res) => {
            expect(Array.isArray(res.body)).toBe(true);
            expect(res.body.length).toBe(0);
          });
      });
    });
  });
}); 