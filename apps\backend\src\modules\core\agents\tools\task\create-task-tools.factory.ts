import { z } from 'zod';
import { TasksService } from '@/modules/workspace/tasks/tasks.service';
import { CreateTaskDto } from '@/modules/workspace/tasks/dto/create-task.dto';
import { AppAbility } from '@/types/models/casl.model';
import { ToolExecutionContext, ToolConfig } from '../core/tool-registry.interface';
import { ToolExecutionError } from '../core/tool.errors';

export interface CreateTaskConfig {
  allowNaturalLanguage?: boolean;
  defaultPriority?: 'low' | 'medium' | 'high' | 'urgent';
  defaultStatus?: string;
}

export interface CreateTaskInput {
  input: string;
  title?: string;
  description?: string;
  projectId?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  assigneeId?: string;
  dueDate?: string;
  estimatedHours?: number;
}

/**
 * 創建任務建立工具
 */
export function createCreateTaskTool(
  tasksService: TasksService,
  context: ToolExecutionContext,
  config?: CreateTaskConfig,
) {
  const inputSchema = z.object({
    input: z.string().min(1).describe('任務建立輸入：自然語言描述或JSON格式'),
    title: z.string().optional().describe('任務標題（可選，會從input解析）'),
    description: z.string().optional().describe('任務描述（可選）'),
    projectId: z.string().optional().describe('專案ID（可選，會從input解析）'),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().describe('優先級（可選）'),
    assigneeId: z.string().optional().describe('指派人員ID（可選）'),
    dueDate: z.string().optional().describe('截止日期（可選）'),
    estimatedHours: z.number().optional().describe('預估工時（可選）'),
  });

  return {
    name: 'create_task',
    description: `任務建立工具 - 根據自然語言或結構化資料建立新任務

此工具允許您快速建立新任務，支援自然語言解析和結構化輸入。

功能特色：
- 支援自然語言輸入解析
- 自動提取任務關鍵資訊
- 多租戶隔離確保資料安全
- 權限檢查和驗證
- 友好的錯誤處理

使用方式：
1. 自然語言輸入：
   - "為專案 ABC 建立一個修復登入錯誤的任務，優先級高，指派給張三"
   - "建立一個優化資料庫效能的任務，預估需要 8 小時"

2. 結構化輸入：
   {
     "title": "修復登入錯誤",
     "description": "用戶回報登入時出現 500 錯誤",
     "projectId": "clxxxxxxxxxxxxx",
     "priority": "high",
     "assigneeId": "clxxxxxxxxxxxxx",
     "dueDate": "2024-12-31",
     "estimatedHours": 8
   }

必要資訊：任務標題、專案ID
可選資訊：描述、狀態、優先級、指派人、截止日期、預估工時`,
    schema: inputSchema,
    call: async (input: CreateTaskInput) => {
      try {
        const { ability } = context;

        // 1. 權限檢查
        const canCreateTask = ability.can('create', 'Task');
        const canCreateWorkOrder = ability.can('create', 'WorkOrder');
        const canCreateProject = ability.can('create', 'Project'); // 向後兼容

        if (!canCreateTask && !canCreateWorkOrder && !canCreateProject) {
          throw new ToolExecutionError(
            'create_task',
            '權限不足：您沒有建立任務的權限。請聯繫管理員獲取相應權限。',
            { userId: context.userId },
          );
        }

        // 2. 解析輸入參數
        const taskData = await parseInput(input);

        // 3. 驗證必要欄位
        if (!taskData.title) {
          throw new ToolExecutionError(
            'create_task',
            '任務標題是必要的。請提供任務標題。',
            { input },
          );
        }

        if (!taskData.projectId) {
          throw new ToolExecutionError(
            'create_task',
            '專案 ID 是必要的。請指定任務所屬的專案。',
            { input },
          );
        }

        // 4. 建立 CreateTaskDto
        const createTaskDto: CreateTaskDto = {
          title: taskData.title,
          description: taskData.description,
          projectId: taskData.projectId,
          status: (taskData.status as any) || (config?.defaultStatus as any) || 'todo',
          priority: taskData.priority || config?.defaultPriority || 'medium',
          assigneeId: taskData.assigneeId,
          dueDate: taskData.dueDate,
          estimatedHours: taskData.estimatedHours,
        };

        // 5. 調用 TasksService 建立任務
        const createdTask = await tasksService.create(
          createTaskDto,
          context.userId,
          context.tenantId,
        );

        return formatSuccessResponse(createdTask);
      } catch (error: any) {
        if (error instanceof ToolExecutionError) {
          throw error;
        }

        // 根據錯誤類型提供更友善的錯誤訊息
        let errorMessage = `建立任務時發生錯誤: ${error.message}`;
        
        if (error.message.includes('permission') || error.message.includes('權限')) {
          errorMessage = `權限錯誤：${error.message}`;
        } else if (error.message.includes('validation') || error.message.includes('驗證')) {
          errorMessage = `資料驗證錯誤：${error.message}`;
        } else if (error.message.includes('not found') || error.message.includes('找不到')) {
          errorMessage = `資源不存在：${error.message}`;
        }

        throw new ToolExecutionError('create_task', errorMessage, {
          originalError: error,
          input,
        });
      }
    },
  };
}

async function parseInput(input: CreateTaskInput): Promise<Partial<CreateTaskDto>> {
  const { input: inputString, title, description, projectId, priority, assigneeId, dueDate, estimatedHours } = input;
  
  // 如果有直接提供的欄位，優先使用
  if (title || projectId) {
    return {
      title: title || inputString,
      description,
      projectId,
      priority,
      assigneeId,
      dueDate,
      estimatedHours,
    };
  }

  const trimmedInput = inputString.trim();

  // 檢查是否為 JSON 格式
  if (trimmedInput.startsWith('{') && trimmedInput.endsWith('}')) {
    try {
      return JSON.parse(trimmedInput);
    } catch (error) {
      // 如果 JSON 解析失敗，繼續嘗試自然語言解析
    }
  }

  // 自然語言解析
  return parseNaturalLanguage(trimmedInput);
}

function parseNaturalLanguage(input: string): Partial<CreateTaskDto> {
  const result: Partial<CreateTaskDto> = {};

  // 提取任務標題（通常在"建立"、"建立"等關鍵字之後）
  const titleMatches = [
    /(?:建立|建立|新增)(?:一個|個)?(.+?)(?:任務|的任務)/i,
    /(?:任務|task)[:：]?\s*(.+?)(?:，|,|。|$)/i,
    /^(.+?)(?:，|,|的任務)/i,
  ];

  for (const regex of titleMatches) {
    const match = input.match(regex);
    if (match && match[1]) {
      result.title = match[1].trim();
      break;
    }
  }

  // 如果沒有找到標題，使用整個輸入作為標題
  if (!result.title) {
    result.title = input.substring(0, 100); // 限制長度
  }

  // 提取專案資訊
  const projectMatches = [
    /(?:專案|項目|project)[:：]?\s*([a-zA-Z0-9\u4e00-\u9fa5]+)/i,
    /(?:為|在)(?:專案|項目)?\s*([a-zA-Z0-9\u4e00-\u9fa5]+)/i,
  ];

  for (const regex of projectMatches) {
    const match = input.match(regex);
    if (match && match[1]) {
      // 這裡可能需要根據專案名稱查詢專案 ID
      // 暫時先保存專案名稱，後續可以擴展為 ID 查詢
      result.description = (result.description || '') + ` [專案: ${match[1]}]`;
      break;
    }
  }

  // 提取優先級
  const priorityMap: Record<string, CreateTaskDto['priority']> = {
    低: 'low',
    中: 'medium',
    高: 'high',
    緊急: 'urgent',
    低優先級: 'low',
    中優先級: 'medium',
    高優先級: 'high',
    緊急優先級: 'urgent',
    low: 'low',
    medium: 'medium',
    high: 'high',
    urgent: 'urgent',
  };

  for (const [keyword, priority] of Object.entries(priorityMap)) {
    if (input.toLowerCase().includes(keyword.toLowerCase())) {
      result.priority = priority;
      break;
    }
  }

  // 提取指派對象
  const assigneeMatches = [
    /(?:指派給|分配給|負責人[:：]?)\s*([a-zA-Z0-9\u4e00-\u9fa5]+)/i,
    /(?:assignee|assigned to)[:：]?\s*([a-zA-Z0-9\u4e00-\u9fa5]+)/i,
  ];

  for (const regex of assigneeMatches) {
    const match = input.match(regex);
    if (match && match[1]) {
      // 這裡可能需要根據用戶名稱查詢用戶 ID
      result.description = (result.description || '') + ` [指派給: ${match[1]}]`;
      break;
    }
  }

  // 提取預估工時
  const hoursMatches = [
    /(?:預估|需要|工時)[:：]?\s*(\d+)\s*(?:小時|hours?|h)/i,
    /(\d+)\s*(?:小時|hours?|h)(?:的工作|工時)/i,
  ];

  for (const regex of hoursMatches) {
    const match = input.match(regex);
    if (match && match[1]) {
      result.estimatedHours = parseInt(match[1], 10);
      break;
    }
  }

  // 提取截止日期
  const dateMatches = [
    /(?:截止日期|due date|deadline)[:：]?\s*(\d{4}-\d{1,2}-\d{1,2})/i,
    /(?:在|於|到)\s*(\d{4}-\d{1,2}-\d{1,2})\s*(?:前|之前|完成)/i,
  ];

  for (const regex of dateMatches) {
    const match = input.match(regex);
    if (match && match[1]) {
      result.dueDate = match[1];
      break;
    }
  }

  return result;
}

function formatSuccessResponse(task: any): string {
  const sections = [
    `✅ 任務建立成功！`,
    ``,
    `=== 任務詳細資訊 ===`,
    `任務標題: ${task.title}`,
    `任務 ID: ${task.id}`,
    `狀態: ${translateStatus(task.status)}`,
    `優先級: ${translatePriority(task.priority)}`,
  ];

  if (task.description) {
    sections.push(`描述: ${task.description}`);
  }

  if (task.projectId) {
    sections.push(`專案 ID: ${task.projectId}`);
  }

  if (task.assigneeId) {
    sections.push(`指派給: ${task.assignee?.name || task.assigneeId}`);
  }

  if (task.dueDate) {
    sections.push(`截止日期: ${new Date(task.dueDate).toLocaleDateString('zh-TW')}`);
  }

  if (task.estimatedHours) {
    sections.push(`預估工時: ${task.estimatedHours} 小時`);
  }

  if (task.createdAt) {
    sections.push(`建立時間: ${new Date(task.createdAt).toLocaleString('zh-TW')}`);
  }

  sections.push(``, `任務已成功建立並準備開始執行。`);

  return sections.join('\n');
}

function translateStatus(status: string): string {
  const statusMap: Record<string, string> = {
    todo: '待辦',
    in_progress: '進行中',
    review: '審核中',
    done: '完成',
    cancelled: '已取消',
    on_hold: '暫停',
  };
  return statusMap[status] || status;
}

function translatePriority(priority: string): string {
  const priorityMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '緊急',
  };
  return priorityMap[priority] || priority;
}

/**
 * 任務建立工具Factory類別
 */
export class CreateTaskToolsFactory {
  static createCreateTaskTool(
    tasksService: TasksService,
    context: ToolExecutionContext,
    config?: CreateTaskConfig,
  ) {
    return createCreateTaskTool(tasksService, context, config);
  }

  static createLangChainTools(
    tasksService: TasksService,
    context: ToolExecutionContext,
    config?: CreateTaskConfig,
  ) {
    return [createCreateTaskTool(tasksService, context, config)];
  }
} 