# Task ID: 1
# Title: Initialize Backend Project and Core Dependencies
# Status: done
# Dependencies: None
# Priority: high
# Description: Set up the NestJS backend project structure, install and configure Prisma ORM with PostgreSQL, and integrate core AI libraries (LangChain.js, LlamaIndex.js).
# Details:
Initialize NestJS project: `nest new backend`. Install dependencies: `@prisma/client`, `prisma`, `langchain`, `@langchain/openai`, `@langchain/community`, `llamaindex`, `pg`. Setup Prisma: `npx prisma init --datasource-provider postgresql`. Configure `DATABASE_URL` in `.env`. Create `prisma.service.ts`. Ensure LangChain.js and LlamaIndex.js can be imported.

# Test Strategy:
Verify NestJS app starts. Prisma connects to PostgreSQL and runs migrations. LangChain/LlamaIndex imports work.
