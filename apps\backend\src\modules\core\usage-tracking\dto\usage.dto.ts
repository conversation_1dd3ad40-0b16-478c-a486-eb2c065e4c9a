import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsBoolean, IsDate, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

export class AiUsageQueryDto {
  @ApiPropertyOptional({ description: '開始日期' })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  startDate?: Date;

  @ApiPropertyOptional({ description: '結束日期' })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  endDate?: Date;

  @ApiPropertyOptional({ description: '租戶 ID' })
  @IsUUID()
  @IsOptional()
  tenant_id?: string;

  @ApiPropertyOptional({ description: '使用者 ID' })
  @IsUUID()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional({ description: 'Agent ID' })
  @IsUUID()
  @IsOptional()
  agentId?: string;

  @ApiPropertyOptional({ description: '功能鍵名' })
  @IsString()
  @IsOptional()
  featureKey?: string;

  @ApiPropertyOptional({ description: 'API Key ID' })
  @IsUUID()
  @IsOptional()
  apiKeyId?: string;

  @ApiPropertyOptional({ description: 'Provider' })
  @IsString()
  @IsOptional()
  provider?: string;

  @ApiPropertyOptional({ description: '模型名稱' })
  @IsString()
  @IsOptional()
  modelName?: string;
}

export class AiUsageStatisticsDto {
  @ApiProperty({ description: '總呼叫次數' })
  totalCalls: number;

  @ApiProperty({ description: '總輸入 Token 數' })
  totalInputTokens: number;

  @ApiProperty({ description: '總輸出 Token 數' })
  totalOutputTokens: number;

  @ApiProperty({ description: '總花費' })
  totalCost: number;

  @ApiProperty({ description: '成功次數' })
  successfulCalls: number;

  @ApiProperty({ description: '失敗次數' })
  failedCalls: number;

  @ApiProperty({ description: '平均回應時間 (毫秒)' })
  averageResponseTime: number;
}

export class AiUsageByDateDto extends AiUsageStatisticsDto {
  @ApiProperty({ description: '日期' })
  date: Date;
}

export class AiUsageByProviderDto extends AiUsageStatisticsDto {
  @ApiProperty({ description: 'Provider' })
  provider: string;
}

export class AiUsageByModelDto extends AiUsageStatisticsDto {
  @ApiProperty({ description: 'Model 名稱' })
  modelName: string;
}

export class AiUsageByFeatureDto extends AiUsageStatisticsDto {
  @ApiProperty({ description: '功能鍵名' })
  featureKey: string;
}

export class AiUsageByAgentDto extends AiUsageStatisticsDto {
  @ApiProperty({ description: 'Agent ID' })
  agentId: string;

  @ApiProperty({ description: 'Agent 名稱' })
  agentName: string;
}

export class DetailedAiUsageStatisticsDto {
  @ApiProperty({ description: '總體統計' })
  overall: AiUsageStatisticsDto;

  @ApiProperty({ description: '依日期統計', type: [AiUsageByDateDto] })
  byDate: AiUsageByDateDto[];

  @ApiProperty({ description: '依 Provider 統計', type: [AiUsageByProviderDto] })
  byProvider: AiUsageByProviderDto[];

  @ApiProperty({ description: '依 Model 統計', type: [AiUsageByModelDto] })
  byModel: AiUsageByModelDto[];

  @ApiProperty({ description: '依功能統計', type: [AiUsageByFeatureDto] })
  byFeature: AiUsageByFeatureDto[];

  @ApiProperty({ description: '依 Agent 統計', type: [AiUsageByAgentDto] })
  byAgent: AiUsageByAgentDto[];
}
