// 系統日誌服務，負責寫入 system_logs 資料表
import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PrismaService } from '../../modules/core/prisma/prisma.service';
import * as crypto from 'crypto';

@Injectable()
export class SystemLogService {
  private readonly logger = new Logger(SystemLogService.name);

  constructor(private readonly prisma: PrismaService) {}

  async logError(data: {
    level: string;
    message: string;
    stack?: string;
    path?: string;
    method?: string;
    userId?: string;
    ip?: string;
    details?: any;
  }) {
    await this.prisma.system_logs.create({
      data: {
        id: crypto.randomUUID(),
        ...data,
      },
    });
  }

  async logAudit(data: {
    message: string;
    user_id?: string;
    ip?: string;
    tenant_id?: string;
    path?: string;
    method?: string;
    target_resource?: string;
    target_resource_id?: string;
    action?: string;
    status?: string;
    error_message?: string;
    details?: any;
  }) {
    await this.prisma.system_logs.create({
      data: {
        id: crypto.randomUUID(),
        level: 'AUDIT',
        ...data,
      },
    });
  }

  async findLogs(filters: {
    level?: string;
    userId?: string;
    action?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    searchQuery?: string;
    limit?: number;
    offset?: number;
  }) {
    const where: any = {};

    // 建構查詢條件
    if (filters.level && filters.level.trim()) {
      where.level = filters.level.trim();
    }

    if (filters.userId && filters.userId.trim()) {
      where.userId = {
        contains: filters.userId.trim(),
        mode: 'insensitive', // 不區分大小寫
      };
    }

    if (filters.action && filters.action.trim()) {
      where.action = {
        contains: filters.action.trim(),
        mode: 'insensitive', // 不區分大小寫
      };
    }

    if (filters.status && filters.status.trim()) {
      where.status = filters.status.trim();
    }

    if (filters.searchQuery && filters.searchQuery.trim()) {
      where.message = {
        contains: filters.searchQuery.trim(),
        mode: 'insensitive', // 不區分大小寫
      };
    }

    // 處理日期範圍
    if (filters.startDate && filters.startDate.trim()) {
      const startDate = new Date(filters.startDate.trim());
      startDate.setHours(0, 0, 0, 0); // 設為當天開始

      where.created_at = {
        ...where.created_at,
        gte: startDate,
      };
    }

    if (filters.endDate && filters.endDate.trim()) {
      const endDate = new Date(filters.endDate.trim());
      endDate.setHours(23, 59, 59, 999); // 設為當天結束

      where.created_at = {
        ...where.created_at,
        lte: endDate,
      };
    }

    console.log('SystemLogService findLogs where條件:', JSON.stringify(where, null, 2));

    const logs = await this.prisma.system_logs.findMany({
      where,
      orderBy: { created_at: 'desc' },
      take: filters.limit,
      skip: filters.offset,
    });

    return logs;
  }

  async countLogs(filters: {
    level?: string;
    userId?: string;
    action?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    searchQuery?: string;
  }) {
    const where: any = {};

    // 建構查詢條件（與 findLogs 相同邏輯）
    if (filters.level && filters.level.trim()) {
      where.level = filters.level.trim();
    }

    if (filters.userId && filters.userId.trim()) {
      where.userId = {
        contains: filters.userId.trim(),
        mode: 'insensitive',
      };
    }

    if (filters.action && filters.action.trim()) {
      where.action = {
        contains: filters.action.trim(),
        mode: 'insensitive',
      };
    }

    if (filters.status && filters.status.trim()) {
      where.status = filters.status.trim();
    }

    if (filters.searchQuery && filters.searchQuery.trim()) {
      where.message = {
        contains: filters.searchQuery.trim(),
        mode: 'insensitive',
      };
    }

    // 處理日期範圍（與 findLogs 相同邏輯）
    if (filters.startDate && filters.startDate.trim()) {
      const startDate = new Date(filters.startDate.trim());
      startDate.setHours(0, 0, 0, 0);

      where.created_at = {
        ...where.created_at,
        gte: startDate,
      };
    }

    if (filters.endDate && filters.endDate.trim()) {
      const endDate = new Date(filters.endDate.trim());
      endDate.setHours(23, 59, 59, 999);

      where.created_at = {
        ...where.created_at,
        lte: endDate,
      };
    }

    return await this.prisma.system_logs.count({ where });
  }

  /**
   * 事件監聽器：處理來自 LoggingInterceptor 的系統日誌事件
   */
  @OnEvent('audit.system.log')
  async handleSystemLogEvent(payload: {
    message: string;
    user_id?: string;
    ip?: string;
    tenant_id?: string;
    path?: string;
    method?: string;
    target_resource?: string;
    target_resource_id?: string;
    action?: string;
    status?: string;
    error_message?: string;
    details?: any;
  }) {
    try {
      await this.logAudit(payload);
    } catch (error) {
      this.logger.error('Failed to process system log event', {
        error: error.message,
        payload: {
          ...payload,
          details: payload.details ? '[TRUNCATED]' : undefined,
        },
      });
    }
  }
}
