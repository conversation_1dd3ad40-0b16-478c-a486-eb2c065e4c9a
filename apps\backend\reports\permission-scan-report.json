{"timestamp": "2025-06-11T15:29:03.956Z", "type": "scan-only", "scanResult": {"permissions": [{"action": "access", "subject": "Workspace", "description": "從前端檔案 role-check.ts 掃描", "isSystemDefined": false, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/utils/role-check.ts", "lineNumber": 101, "name": "存取工作區", "zone": "workspace", "scope": "WORKSPACE", "category": "workspace_settings"}, {"action": "create", "subject": "Permission", "description": "允許建立新的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 137, "name": "建立權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Permission", "description": "允許刪除權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 183, "name": "刪除權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "Permission", "description": "完整管理權限的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 198, "name": "管理權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Permission", "description": "允許查看系統權限定義和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 73, "name": "查看權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Permission", "description": "允許修改現有的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 159, "name": "修改權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Role", "description": "允許建立新的系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 161, "name": "建立角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Role", "description": "允許刪除系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 227, "name": "刪除角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Role", "description": "允許查看系統角色定義和權限分配", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 64, "name": "查看角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Role", "description": "允許修改角色的權限分配和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 249, "name": "修改角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "SystemUser", "description": "完整管理系統層級用戶的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 116, "name": "管理系統用戶", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "SystemUser", "description": "允許查看系統用戶列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 128, "name": "查看系統用戶", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "Tenant", "description": "完整管理租戶的建立、修改、刪除等所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/utils/role-check.ts", "lineNumber": 87, "name": "管理租戶", "zone": "admin", "category": "tenant_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Workspace", "description": "允許為租戶建立新的工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 283, "name": "建立工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "delete", "subject": "Workspace", "description": "允許刪除工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 126, "name": "刪除工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "read", "subject": "Workspace", "description": "允許查看工作區列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 241, "name": "查看工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "update", "subject": "Workspace", "description": "允許修改工作區的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 223, "name": "修改工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "access", "subject": "AdminPanel", "description": "允許登入和存取系統管理後台介面", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/utils/role-check.ts", "lineNumber": 76, "name": "存取管理後台", "zone": "admin", "category": "system_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Dashboard", "description": "允許查看系統管理儀表板的統計資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 179, "name": "查看管理儀表板", "zone": "admin", "category": "system_management", "scope": "SYSTEM"}, {"action": "create", "subject": "SystemUser", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 25, "name": "建立SystemUser", "zone": "admin", "scope": "SYSTEM", "category": "system_management"}, {"action": "delete", "subject": "SystemUser", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 88, "name": "刪除SystemUser", "zone": "admin", "scope": "SYSTEM", "category": "system_management"}, {"action": "update", "subject": "SystemUser", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 105, "name": "修改SystemUser", "zone": "admin", "scope": "SYSTEM", "category": "system_management"}, {"action": "create", "subject": "User", "description": "允許邀請新使用者加入工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 43, "name": "建立使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "delete", "subject": "User", "description": "允許從工作區移除使用者", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 102, "name": "刪除使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "User", "description": "允許查看工作區內的使用者列表", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 73, "name": "查看使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "update", "subject": "User", "description": "允許修改工作區使用者的資料和權限", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 86, "name": "修改使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "LineMessageLog", "description": "允許查看LINE機器人的訊息交互日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/message-log.controller.ts", "lineNumber": 28, "name": "查看LINE訊息日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "read", "subject": "SystemLog", "description": "允許查看系統操作和稽核日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-logs/system-logs.controller.ts", "lineNumber": 47, "name": "查看系統日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Comment", "description": "允許在專案、任務和檔案上新增評論", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 28, "name": "新增評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "Comment", "description": "允許刪除評論（自己的或管理權限）", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 96, "name": "刪除評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "Comment", "description": "允許查看專案、任務和檔案的評論", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 66, "name": "查看評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "update", "subject": "Comment", "description": "允許修改自己的評論內容", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 77, "name": "修改評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "CommentReaction", "description": "允許對評論新增反應和表情符號", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 113, "name": "新增評論反應", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "CommentReaction", "description": "允許移除自己的評論反應", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 132, "name": "移除評論反應", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "FilePermission", "description": "允許為檔案設定使用者存取權限", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 203, "name": "設定檔案權限", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "FileShare", "description": "允許建立檔案分享連結", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 259, "name": "建立檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "FileShare", "description": "允許刪除檔案分享連結", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 285, "name": "刪除檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "FileShare", "description": "允許查看檔案的分享連結和分享設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 275, "name": "查看檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "SharedFile", "description": "允許上傳新檔案到工作區進行分享", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 46, "name": "上傳共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "SharedFile", "description": "允許刪除工作區內的共享檔案", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 114, "name": "刪除共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "SharedFile", "description": "允許查看工作區內的共享檔案列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 88, "name": "查看共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "update", "subject": "SharedFile", "description": "允許修改共享檔案的資訊和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 98, "name": "修改共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "AiModel", "description": "允許添加新的 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", "lineNumber": 54, "name": "建立 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "AiModel", "description": "允許刪除 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", "lineNumber": 75, "name": "刪除 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "AiModel", "description": "完整管理 AI 模型的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", "lineNumber": 106, "name": "管理 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "read", "subject": "AiModel", "description": "允許查看 AI 模型列表和詳細配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", "lineNumber": 45, "name": "查看 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "update", "subject": "AiModel", "description": "允許修改 AI 模型的配置和參數", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", "lineNumber": 84, "name": "修改 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "create", "subject": "LineBot", "description": "允許建立新的 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 125, "name": "建立 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "delete", "subject": "LineBot", "description": "允許刪除 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 179, "name": "刪除 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "read", "subject": "LineBot", "description": "允許查看 LINE 機器人配置和狀態", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 242, "name": "查看 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "update", "subject": "LineBot", "description": "允許修改 LINE 機器人的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 203, "name": "修改 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}], "stats": {"totalFiles": 830, "scannedFiles": 829, "filesWithPermissions": 13, "totalPermissions": 176}, "errors": []}, "summary": {"total": 51, "byScope": {"SYSTEM": 24, "TENANT": 8, "WORKSPACE": 19}, "byCategory": {"system_management": 5, "user_management": 11, "log_management": 2, "tenant_management": 5, "collaboration": 14, "member_management": 4, "ai_management": 9, "workspace_settings": 1}, "bySubject": {"Dashboard": 1, "Permission": 5, "Role": 4, "SystemLog": 1, "SystemUser": 5, "Workspace": 5, "Comment": 4, "CommentReaction": 2, "SharedFile": 4, "FilePermission": 1, "FileShare": 3, "User": 4, "LineBot": 4, "LineMessageLog": 1, "AiModel": 5, "AdminPanel": 1, "Tenant": 1}}, "permissions": [{"action": "access", "subject": "Workspace", "description": "從前端檔案 role-check.ts 掃描", "isSystemDefined": false, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/utils/role-check.ts", "lineNumber": 101, "name": "存取工作區", "zone": "workspace", "scope": "WORKSPACE", "category": "workspace_settings"}, {"action": "create", "subject": "Permission", "description": "允許建立新的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 137, "name": "建立權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Permission", "description": "允許刪除權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 183, "name": "刪除權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "Permission", "description": "完整管理權限的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 198, "name": "管理權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Permission", "description": "允許查看系統權限定義和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 73, "name": "查看權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Permission", "description": "允許修改現有的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 159, "name": "修改權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Role", "description": "允許建立新的系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 161, "name": "建立角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Role", "description": "允許刪除系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 227, "name": "刪除角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Role", "description": "允許查看系統角色定義和權限分配", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 64, "name": "查看角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Role", "description": "允許修改角色的權限分配和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 249, "name": "修改角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "SystemUser", "description": "完整管理系統層級用戶的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 116, "name": "管理系統用戶", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "SystemUser", "description": "允許查看系統用戶列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 128, "name": "查看系統用戶", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "Tenant", "description": "完整管理租戶的建立、修改、刪除等所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/utils/role-check.ts", "lineNumber": 87, "name": "管理租戶", "zone": "admin", "category": "tenant_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Workspace", "description": "允許為租戶建立新的工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 283, "name": "建立工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "delete", "subject": "Workspace", "description": "允許刪除工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 126, "name": "刪除工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "read", "subject": "Workspace", "description": "允許查看工作區列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 241, "name": "查看工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "update", "subject": "Workspace", "description": "允許修改工作區的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 223, "name": "修改工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "access", "subject": "AdminPanel", "description": "允許登入和存取系統管理後台介面", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/utils/role-check.ts", "lineNumber": 76, "name": "存取管理後台", "zone": "admin", "category": "system_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Dashboard", "description": "允許查看系統管理儀表板的統計資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 179, "name": "查看管理儀表板", "zone": "admin", "category": "system_management", "scope": "SYSTEM"}, {"action": "create", "subject": "SystemUser", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 25, "name": "建立SystemUser", "zone": "admin", "scope": "SYSTEM", "category": "system_management"}, {"action": "delete", "subject": "SystemUser", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 88, "name": "刪除SystemUser", "zone": "admin", "scope": "SYSTEM", "category": "system_management"}, {"action": "update", "subject": "SystemUser", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", "lineNumber": 105, "name": "修改SystemUser", "zone": "admin", "scope": "SYSTEM", "category": "system_management"}, {"action": "create", "subject": "User", "description": "允許邀請新使用者加入工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 43, "name": "建立使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "delete", "subject": "User", "description": "允許從工作區移除使用者", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 102, "name": "刪除使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "User", "description": "允許查看工作區內的使用者列表", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 73, "name": "查看使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "update", "subject": "User", "description": "允許修改工作區使用者的資料和權限", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 86, "name": "修改使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "LineMessageLog", "description": "允許查看LINE機器人的訊息交互日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/message-log.controller.ts", "lineNumber": 28, "name": "查看LINE訊息日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "read", "subject": "SystemLog", "description": "允許查看系統操作和稽核日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-logs/system-logs.controller.ts", "lineNumber": 47, "name": "查看系統日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Comment", "description": "允許在專案、任務和檔案上新增評論", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 28, "name": "新增評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "Comment", "description": "允許刪除評論（自己的或管理權限）", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 96, "name": "刪除評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "Comment", "description": "允許查看專案、任務和檔案的評論", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 66, "name": "查看評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "update", "subject": "Comment", "description": "允許修改自己的評論內容", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 77, "name": "修改評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "CommentReaction", "description": "允許對評論新增反應和表情符號", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 113, "name": "新增評論反應", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "CommentReaction", "description": "允許移除自己的評論反應", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 132, "name": "移除評論反應", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "FilePermission", "description": "允許為檔案設定使用者存取權限", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 203, "name": "設定檔案權限", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "FileShare", "description": "允許建立檔案分享連結", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 259, "name": "建立檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "FileShare", "description": "允許刪除檔案分享連結", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 285, "name": "刪除檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "FileShare", "description": "允許查看檔案的分享連結和分享設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 275, "name": "查看檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "SharedFile", "description": "允許上傳新檔案到工作區進行分享", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 46, "name": "上傳共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "SharedFile", "description": "允許刪除工作區內的共享檔案", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 114, "name": "刪除共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "SharedFile", "description": "允許查看工作區內的共享檔案列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 88, "name": "查看共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "update", "subject": "SharedFile", "description": "允許修改共享檔案的資訊和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 98, "name": "修改共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "AiModel", "description": "允許添加新的 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", "lineNumber": 54, "name": "建立 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "AiModel", "description": "允許刪除 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", "lineNumber": 75, "name": "刪除 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "AiModel", "description": "完整管理 AI 模型的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", "lineNumber": 106, "name": "管理 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "read", "subject": "AiModel", "description": "允許查看 AI 模型列表和詳細配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", "lineNumber": 45, "name": "查看 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "update", "subject": "AiModel", "description": "允許修改 AI 模型的配置和參數", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", "lineNumber": 84, "name": "修改 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "create", "subject": "LineBot", "description": "允許建立新的 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 125, "name": "建立 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "delete", "subject": "LineBot", "description": "允許刪除 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 179, "name": "刪除 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "read", "subject": "LineBot", "description": "允許查看 LINE 機器人配置和狀態", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 242, "name": "查看 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "update", "subject": "LineBot", "description": "允許修改 LINE 機器人的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 203, "name": "修改 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}]}