import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import { randomUUID } from 'crypto';
import * as dotenv from 'dotenv';

// 載入環境變數
dotenv.config();

const prisma = new PrismaClient();

async function main() {
  const tenant_id = "00000000-0000-0000-0000-000000000000"; // 系統租戶 ID
  const cuid = () => randomUUID();

  console.log("🚀 開始執行 seed 腳本...");

  console.log("清除現有資料...");

  // 依照外鍵約束，以正確順序清除資料
  await prisma.ai_agent_tools.deleteMany({});
  await prisma.ai_feature_configs.deleteMany({});
  await prisma.ai_agents.deleteMany({});
  await prisma.ai_models.deleteMany({});
  await prisma.ai_keys.deleteMany({});
  await prisma.ai_tools.deleteMany({});
  
  await prisma.system_user_roles.deleteMany({});
  await prisma.tenant_user_roles.deleteMany({});
  await prisma.role_permissions.deleteMany({});
  await prisma.permissions.deleteMany({});
  await prisma.permission_categories.deleteMany({});
  await prisma.roles.deleteMany({});
  
  await prisma.system_users.deleteMany({});
  await prisma.tenants.deleteMany({});

  // 1. 建立租戶
  console.log("建立租戶...");
  const systemTenant = await prisma.tenants.create({
    data: {
      id: tenant_id,
      name: "System Tenant",
      status: "ACTIVE",
    },
  });

  // 2. 建立角色
  console.log("建立角色...");
  const superAdminRole = await prisma.roles.create({
    data: {
      id: cuid(),
      name: "SuperAdmin",
      display_name: "Super Admin",
      description: "擁有所有權限的超級管理員",
      scope: "SYSTEM",
      is_system: true,
    },
  });

  // 3. 建立系統管理員
  console.log("建立系統管理員...");
  const systemAdmin = await prisma.system_users.create({
    data: {
      id: cuid(),
      name: "System Admin",
      email: "<EMAIL>",
      password: bcrypt.hashSync("Admin@123", 10),
    },
  });

  await prisma.system_user_roles.create({
    data: {
      id: cuid(),
      system_user_id: systemAdmin.id,
      role_id: superAdminRole.id,
    },
  });

  // 4. 建立權限分類
  console.log("建立權限分類...");
  const permissionCategory = await prisma.permission_categories.create({
    data: {
      id: cuid(),
      name: "系統管理",
      description: "系統級別的管理權限",
    },
  });

  // 5. 建立權限
  console.log("建立權限...");
  const superAdminPermission = await prisma.permissions.create({
    data: {
      id: cuid(),
      subject: "all",
      action: "manage",
      scope: "SYSTEM",
      category_id: permissionCategory.id,
      description: 'Allow all actions on all subjects'
    },
  });

  // 6. 分配權限給角色
  console.log("分配權限給角色...");
  await prisma.role_permissions.create({
    data: {
      id: cuid(),
      role_id: superAdminRole.id,
      permission_id: superAdminPermission.id,
    },
  });

  // 7. 建立 AI 工具
  console.log("建立 AI 工具...");

  const fileReaderTool = await prisma.ai_tools.create({
    data: {
      id: cuid(),
      key: "file_reader",
      name: "File Reader",
      description: "Reads the content of a file.",
      input_schema: {
        type: "object",
        properties: {
          file_path: { type: "string", description: "The path to the file." },
        },
        required: ["file_path"],
      },
      scope: "SYSTEM",
      is_enabled: true,
    },
  });

  const webSearchTool = await prisma.ai_tools.create({
    data: {
      id: cuid(),
      key: "web_search",
      name: "Web Search",
      description: "Performs a web search.",
      input_schema: {
        type: "object",
        properties: {
          query: { type: "string", description: "The search query." },
        },
        required: ["query"],
      },
      scope: "SYSTEM",
      is_enabled: true,
    },
  });

  // 8. 建立 AI 相關種子數據
  console.log("建立 AI 相關種子數據...");

  const defaultKey = await prisma.ai_keys.create({
    data: {
      id: cuid(),
      name: 'Default System Key',
      api_key: 'placeholder-key-do-not-use',
      provider: 'openai',
      is_enabled: true,
    },
  });

  const defaultModel = await prisma.ai_models.create({
    data: {
      id: cuid(),
      provider: 'openai',
      model_name: 'gpt-4-turbo',
      display_name: 'GPT-4 Turbo',
      is_enabled: true,
      price_last_updated_at: new Date(),
    },
  });

  const defaultAgent = await prisma.ai_agents.create({
    data: {
      id: cuid(),
      name: "Default Assistant",
      description: "A default assistant agent.",
      scope: "SYSTEM",
      provider_type: "OPENAI",
      model_id: defaultModel.id,
      key_id: defaultKey.id,
      is_template: false,
      created_by: systemAdmin.id,
      scene: 'default',
      tenant_id: systemTenant.id,
    },
  });

  // 9. 關聯 AI 工具與 AI 代理
  console.log("關聯 AI 工具與 AI 代理...");

  await prisma.ai_agent_tools.createMany({
    data: [
      {
        ai_agent_id: defaultAgent.id,
        ai_tool_id: fileReaderTool.id,
      },
      {
        ai_agent_id: defaultAgent.id,
        ai_tool_id: webSearchTool.id,
      },
    ],
  });

  console.log("✅ Seed 腳本執行成功！");
}

main()
  .then(async () => {
    //
  })
  .catch(async (e) => {
    console.error("❌ Seed 腳本執行失敗：", e);
    process.exit(1);
  });
