import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { CreateLineBotDto } from '../dto/create-line-bot.dto';
import { UpdateLineBotDto } from '../dto/update-line-bot.dto';
import { line_bots, LineBotScope, Prisma, SystemUserRole, TenantUserRole } from '@prisma/client';

type LineBot = line_bots;
type UserRole = SystemUserRole | TenantUserRole;
import {
  Client,
  WebhookEvent,
  TextMessage,
  MessageAPIResponseBase,
  TemplateMessage,
  FlexMessage,
  FlexContainer,
  FlexBubble,
  MessageEvent,
  Message,
} from '@line/bot-sdk';
import { ConfigService } from '@nestjs/config';
import { EncryptionService } from '@/modules/core/encryption/encryption.service';
import { GroupVerificationService } from './group-verification.service';
import { MessageLogService } from './message-log.service';
import { Role as RoleEnum } from '@/common/enums/role.enum';
import { LineUsersService } from './line-users.service';

interface UserContext {
  role: UserRole;
  tenant_id?: string | null;
}

@Injectable()
export class LineBotService {
  private readonly logger = new Logger(LineBotService.name);
  private readonly WEBHOOK_PATH_PATTERN = '/api/admin/line-bots/callback';

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
    private readonly groupVerificationService: GroupVerificationService,
    private readonly messageLogService: MessageLogService,
    private readonly lineUsersService: LineUsersService,
  ) {}

  private generateFullWebhookUrl(path: string): string {
    const apiBaseUrl = this.configService.get<string>('API_BASE_URL', 'http://localhost:4000');
    return `${apiBaseUrl}${path}`;
  }

  private generateWebhookPath(botId: string): string {
    return `${this.WEBHOOK_PATH_PATTERN}/${botId}`;
  }

  private transformBotResponse(bot: LineBot | null): LineBot | null {
    if (!bot || !bot.webhook_url) {
      return bot;
    }
    return {
      ...bot,
      webhook_url: this.generateFullWebhookUrl(bot.webhook_url),
    };
  }

  private transformBotListResponse(bots: LineBot[]): LineBot[] {
    return bots
      .map((bot) => this.transformBotResponse(bot))
      .filter((bot): bot is LineBot => bot !== null);
  }

  async create(createLineBotDto: CreateLineBotDto, userContext: UserContext): Promise<LineBot> {
    const {
      bot_secret,
      bot_token,
      token_update_reminder_period_days,
      tenant_id: dtoTenantId,
      scope: dtoScope,
      ...restData
    } = createLineBotDto;

    // Initialize with a placeholder tenant relation that will be set properly below
    const data: Prisma.line_botsCreateInput = {
      id: `linebot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: restData.name,
      description: restData.description,
      is_enabled: restData.is_enabled !== undefined ? restData.is_enabled : true,
      webhook_url: restData.webhook_url,
      token_update_reminder_period_days: token_update_reminder_period_days,
      scope: LineBotScope.SYSTEM,
      tenants: { connect: { id: 'placeholder' } }, // Will be overridden below
    };

    if (userContext.role === TenantUserRole.TENANT_ADMIN) {
      if (!userContext.tenant_id) {
        throw new ForbiddenException('Tenant admin must be associated with a tenant.');
      }
      data.scope = LineBotScope.TENANT;
      data.tenants = { connect: { id: userContext.tenant_id } };
    } else if (userContext.role === SystemUserRole.SYSTEM_ADMIN) {
      const targetScope = dtoScope || LineBotScope.SYSTEM;
      data.scope = targetScope;
      if (targetScope === LineBotScope.TENANT) {
        if (!dtoTenantId) {
          throw new BadRequestException(
            'tenant_id is required when creating a TENANT scope bot as SYSTEM_ADMIN.',
          );
        }
        const tenantExists = await this.prisma.tenants.findUnique({
          where: { id: dtoTenantId },
        });
        if (!tenantExists) {
          throw new NotFoundException(`Tenant with ID ${dtoTenantId} not found.`);
        }
        data.tenants = { connect: { id: dtoTenantId } };
      } else {
        // For SYSTEM scope, we need to create a system tenant or use a default one
        // This is a temporary solution - ideally the schema should allow null tenant_id for SYSTEM scope
        const systemTenant = await this.findOrCreateSystemTenant();
        data.tenants = { connect: { id: systemTenant.id } };
      }
    } else if (userContext.role === SystemUserRole.SUPER_ADMIN) {
      const targetScope = dtoScope || LineBotScope.SYSTEM;
      data.scope = targetScope;
      if (targetScope === LineBotScope.TENANT) {
        if (!dtoTenantId) {
          throw new BadRequestException('tenant_id is required for TENANT scope bot.');
        }
        const tenantExists = await this.prisma.tenants.findUnique({
          where: { id: dtoTenantId },
        });
        if (!tenantExists) {
          throw new NotFoundException(`Tenant with ID ${dtoTenantId} not found.`);
        }
        data.tenants = { connect: { id: dtoTenantId } };
      } else {
        // For SYSTEM scope, we need to create a system tenant or use a default one
        const systemTenant = await this.findOrCreateSystemTenant();
        data.tenants = { connect: { id: systemTenant.id } };
      }
    } else {
      throw new ForbiddenException(`User with role ${userContext.role} cannot create LineBots.`);
    }

    if (bot_secret) {
      data.bot_secret = this.encryptionService.encrypt(bot_secret);
    }
    if (bot_token) {
      data.bot_token = this.encryptionService.encrypt(bot_token);
      data.token_last_updated_at = new Date();
    }

    const lineBot = await this.prisma.line_bots.create({ data });
    this.logger.log(
      `LineBot created: ${lineBot.id} with scope ${lineBot.scope} for tenant: ${lineBot.tenant_id || 'SYSTEM'}`,
    );
    return this.transformBotResponse(lineBot) as LineBot;
  }

  async update(
    id: string,
    updateLineBotDto: UpdateLineBotDto,
    userContext: UserContext,
  ): Promise<LineBot> {
    const existingBot = await this.prisma.line_bots.findUnique({
      where: { id },
    });
    if (!existingBot) {
      throw new NotFoundException(`LineBot ${id} not found.`);
    }

    if (userContext.role === TenantUserRole.TENANT_ADMIN) {
      if (existingBot.tenant_id !== userContext.tenant_id) {
        throw new ForbiddenException("Tenant admin can only update their own tenant's LineBots.");
      }
      if (updateLineBotDto.scope && updateLineBotDto.scope !== LineBotScope.TENANT) {
        throw new ForbiddenException('Tenant admins cannot change the scope of a LineBot.');
      }
      if (updateLineBotDto.tenant_id && updateLineBotDto.tenant_id !== userContext.tenant_id) {
        throw new ForbiddenException('Tenant admins cannot change the tenant_id of a LineBot.');
      }
      delete updateLineBotDto.scope;
      delete updateLineBotDto.tenant_id;
    }

    const data: Prisma.line_botsUpdateInput = {};
    let has_changes = false;

    const {
      name,
      description,
      scope,
      tenant_id: dto_tenant_id,
      webhook_url,
      is_enabled,
      bot_secret,
      bot_token,
      token_update_reminder_period_days,
    } = updateLineBotDto;

    if (name !== undefined && name !== existingBot.name) {
      data.name = name;
      has_changes = true;
    }
    if (description !== undefined && description !== existingBot.description) {
      data.description = description;
      has_changes = true;
    }

    if (userContext.role === 'SYSTEM_ADMIN' || userContext.role === 'SUPER_ADMIN') {
      if (scope !== undefined && scope !== existingBot.scope) {
        data.scope = scope;
        if (scope === LineBotScope.TENANT) {
          if (!dto_tenant_id)
            throw new BadRequestException('tenant_id is required when changing scope to TENANT.');
          const tenantExists = await this.prisma.tenants.findUnique({
            where: { id: dto_tenant_id },
          });
          if (!tenantExists)
            throw new NotFoundException(`Tenant with ID ${dto_tenant_id} not found.`);
          data.tenants = { connect: { id: dto_tenant_id } };
        } else if (scope === LineBotScope.SYSTEM) {
          // For SYSTEM scope, we need to provide a default tenant or handle this differently
          // Since tenant_id is required in the schema, we cannot disconnect it
          // This might need to be handled at the schema level or business logic level
          throw new BadRequestException(
            'Cannot change TENANT scope bot to SYSTEM scope - tenant_id is required',
          );
        }
        has_changes = true;
      } else if (
        dto_tenant_id !== undefined &&
        existingBot.scope === LineBotScope.TENANT &&
        dto_tenant_id !== existingBot.tenant_id
      ) {
        const tenantExists = await this.prisma.tenants.findUnique({
          where: { id: dto_tenant_id },
        });
        if (!tenantExists)
          throw new NotFoundException(`Tenant with ID ${dto_tenant_id} not found.`);
        data.tenants = { connect: { id: dto_tenant_id } };
        has_changes = true;
      }
    }

    if (webhook_url !== undefined && webhook_url !== existingBot.webhook_url) {
      data.webhook_url = webhook_url;
      has_changes = true;
    }
    if (is_enabled !== undefined && is_enabled !== existingBot.is_enabled) {
      data.is_enabled = is_enabled;
      has_changes = true;
    }

    if (bot_secret) {
      const encryptedNewSecret = this.encryptionService.encrypt(bot_secret);
      if (existingBot.bot_secret !== encryptedNewSecret) {
        data.bot_secret = encryptedNewSecret;
        has_changes = true;
      }
    }

    if (updateLineBotDto.hasOwnProperty('bot_token')) {
      if (bot_token === null) {
        if (existingBot.bot_token !== null) {
          data.bot_token = null;
          data.token_last_updated_at = null;
          has_changes = true;
        }
      } else if (bot_token) {
        const encryptedNewToken = this.encryptionService.encrypt(bot_token);
        if (existingBot.bot_token !== encryptedNewToken) {
          data.bot_token = encryptedNewToken;
          data.token_last_updated_at = new Date();
          has_changes = true;
        }
      }
    }

    if (updateLineBotDto.hasOwnProperty('token_update_reminder_period_days')) {
      if (token_update_reminder_period_days === null) {
        if (existingBot.token_update_reminder_period_days !== null) {
          data.token_update_reminder_period_days = null;
          has_changes = true;
        }
      } else if (
        token_update_reminder_period_days !== existingBot.token_update_reminder_period_days
      ) {
        data.token_update_reminder_period_days = token_update_reminder_period_days;
        has_changes = true;
      }
    }

    if (!has_changes) {
      this.logger.log(`LineBot update for ID: ${id} called with no new data to update.`);
      return this.transformBotResponse(existingBot) as LineBot;
    }

    const updatedLineBot = await this.prisma.line_bots.update({
      where: { id },
      data,
    });
    this.logger.log(`LineBot updated: ${updatedLineBot.id}`);
    return this.transformBotResponse(updatedLineBot) as LineBot;
  }

  async delete(id: string, userContext: UserContext): Promise<LineBot> {
    const existingBot = await this.prisma.line_bots.findUnique({
      where: { id },
    });
    if (!existingBot) {
      throw new NotFoundException(`LineBot ${id} not found.`);
    }
    if (userContext.role === 'TENANT_ADMIN' && existingBot.tenant_id !== userContext.tenant_id) {
      throw new ForbiddenException('Tenant admin cannot delete bots of other tenants.');
    }

    const deletedBot = await this.prisma.line_bots.delete({ where: { id } });
    this.logger.log(`LineBot deleted: ${deletedBot.id}`);
    return this.transformBotResponse(deletedBot) as LineBot;
  }

  async findAll(
    params: { scope?: LineBotScope; tenant_id?: string },
    userContext: UserContext,
  ): Promise<LineBot[]> {
    const where: Prisma.line_botsWhereInput = {};

    if (userContext.role === 'TENANT_ADMIN') {
      if (!userContext.tenant_id) {
        this.logger.warn('TENANT_ADMIN has no tenant_id in context for findAll.');
        throw new ForbiddenException('Tenant context is required for tenant admin.');
      }
      where.tenant_id = userContext.tenant_id;
      where.scope = LineBotScope.TENANT;
    } else if (userContext.role === 'SYSTEM_ADMIN' || userContext.role === 'SUPER_ADMIN') {
      if (params.scope) {
        where.scope = params.scope;
      }
      if (params.tenant_id) {
        if (params.scope === LineBotScope.TENANT || !params.scope) {
          where.tenant_id = params.tenant_id;
        } else if (params.scope === LineBotScope.SYSTEM && params.tenant_id) {
          this.logger.warn(
            'Filtering SYSTEM scope bots by tenant_id is not logical and will yield no results if tenant_id is provided.',
          );
          // Since tenant_id is required in schema, we use a filter that will return no results
          where.tenant_id = 'SYSTEM_SCOPE_NO_TENANT';
        }
      }
    } else {
      this.logger.warn(
        `User with role ${userContext.role} attempted to findAll LineBots without permission.`,
      );
      return [];
    }

    const bots = await this.prisma.line_bots.findMany({ where });
    return this.transformBotListResponse(bots);
  }

  async findOne(id: string): Promise<LineBot | null> {
    const bot = await this.prisma.line_bots.findUnique({ where: { id } });
    return this.transformBotResponse(bot);
  }

  async findBotsNeedingTokenReminder(
    daysThreshold: number = 7,
    userContext: UserContext,
  ): Promise<Partial<LineBot>[]> {
    const where: Prisma.line_botsWhereInput = {
      is_enabled: true,
      bot_token: { not: null },
      token_last_updated_at: { not: null },
      token_update_reminder_period_days: { not: null },
    };

    if (userContext.role === TenantUserRole.TENANT_ADMIN) {
      if (!userContext.tenant_id) {
        this.logger.warn('TENANT_ADMIN has no tenant_id for findBotsNeedingTokenReminder');
        return [];
      }
      where.tenant_id = userContext.tenant_id;
      where.scope = LineBotScope.TENANT;
    } else if (
      userContext.role === SystemUserRole.SYSTEM_ADMIN ||
      userContext.role === SystemUserRole.SUPER_ADMIN
    ) {
      // System admins can see all bots
    } else {
      this.logger.warn(
        `User with role ${userContext.role} attempted to findBotsNeedingTokenReminder.`,
      );
      return [];
    }

    const bots = await this.prisma.line_bots.findMany({
      where,
      select: {
        id: true,
        name: true,
        scope: true,
        tenant_id: true,
        token_last_updated_at: true,
        token_update_reminder_period_days: true,
      },
    });

    const today = new Date();
    const reminderBots = bots.filter((bot) => {
      if (bot.token_last_updated_at && bot.token_update_reminder_period_days) {
        const reminderDate = new Date(bot.token_last_updated_at);
        reminderDate.setDate(
          reminderDate.getDate() + bot.token_update_reminder_period_days - daysThreshold,
        );
        return reminderDate <= today;
      }
      return false;
    });

    return reminderBots;
  }

  async getDecryptedChannelSecret(botId: string): Promise<string | null> {
    const bot = await this.prisma.line_bots.findUnique({
      where: { id: botId },
      select: { bot_secret: true },
    });
    if (!bot || !bot.bot_secret) return null;
    return this.encryptionService.decrypt(bot.bot_secret);
  }

  async getDecryptedBotToken(botId: string): Promise<string | null> {
    const bot = await this.prisma.line_bots.findUnique({
      where: { id: botId },
      select: { bot_token: true },
    });
    if (!bot || !bot.bot_token) return null;
    return this.encryptionService.decrypt(bot.bot_token);
  }

  async handleWebhookEvents(botInstance: LineBot, events: WebhookEvent[]): Promise<void> {
    const decryptedToken = botInstance.bot_token
      ? this.encryptionService.decrypt(botInstance.bot_token)
      : null;

    if (!decryptedToken) {
      this.logger.error(
        `Bot ${botInstance.id} has no decrypted token. Cannot process webhook events.`,
      );
      return;
    }
    const client = new Client({ channelAccessToken: decryptedToken });

    for (const event of events) {
      try {
        await this.messageLogService.logIncomingEvent(botInstance, event);
        await this.processEvent(client, event, botInstance);
      } catch (error) {
        this.logger.error(
          `Error processing event type ${event.type} for bot ${botInstance.id}: ${error.message}`,
          error.stack,
        );
      }
    }
  }

  private async processEvent(
    client: Client,
    event: WebhookEvent,
    bot: LineBot,
  ): Promise<MessageAPIResponseBase | void> {
    this.logger.debug(`Processing event type: ${event.type} for bot: ${bot.id}`);

    const userId = event.source.userId;
    if (!userId) {
      this.logger.warn('Event has no userId, skipping processing.');
      return;
    }

    try {
      if (event.type === 'message') {
        return await this.handleMessageEvent(client, event, bot);
      }
      if (event.type === 'follow') {
        await this.lineUsersService.handleFollowEvent(userId, () => client.getProfile(userId));
        this.logger.log(`Handled follow event for user ${userId}`);
      }
      if (event.type === 'unfollow') {
        await this.lineUsersService.handleUnfollowEvent(userId);
        this.logger.log(`Handled unfollow event for user ${userId}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing event for bot ${bot.id} and user ${userId}: ${error.message}`,
        error.stack,
      );
    }
  }

  private async handleMessageEvent(
    client: Client,
    event: MessageEvent,
    bot: LineBot,
  ): Promise<MessageAPIResponseBase | void> {
    const message = event.message;
    this.logger.log(
      `Handling message event: Type ${message.type}, BotID: ${bot.id}, UserID: ${event.source.userId}, GroupID: ${event.source.type === 'group' ? event.source.groupId : 'N/A'}`,
    );

    if (message.type === 'text') {
      const getProfile = () => client.getProfile(event.source.userId!);
      await this.messageLogService.recordMessageEvent(bot, event, 'INCOMING', getProfile);

      const replyText = 'Hello! You said: ' + message.text;
      const replyMessage: TextMessage = { type: 'text', text: replyText };
      const response = await client.replyMessage(event.replyToken, replyMessage);

      const outboundEvent = {
        ...event,
        message: {
          ...replyMessage,
          id: `reply_${Date.now()}`, // Generate a temporary ID since response doesn't contain message IDs
        },
        timestamp: Date.now(),
      };
      await this.messageLogService.recordMessageEvent(bot, outboundEvent, 'OUTGOING', getProfile);

      return response;
    }
    this.logger.log(`Received non-text message type: ${message.type}`);
    return client.replyMessage(event.replyToken, {
      type: 'text',
      text: `Received a ${message.type} message. I can currently only process text. Bot: ${bot.name}`,
    });
  }

  private createFunctionListMessage(): TemplateMessage {
    return {
      type: 'template',
      altText: 'functions',
      template: {
        type: 'buttons',
        title: 'Functions',
        text: 'Select a function',
        actions: [],
      },
    };
  }

  private createQuickReplyMessage(): TextMessage {
    return { type: 'text', text: 'Quick reply', quickReply: { items: [] } };
  }

  private createWelcomeFlexMessage(): FlexMessage {
    const bubble: FlexBubble = {
      type: 'bubble',
      body: {
        type: 'box',
        layout: 'vertical',
        contents: [{ type: 'text', text: 'Welcome!' }],
      },
    };
    return { type: 'flex', altText: 'Welcome', contents: bubble };
  }

  async pushMessage(
    botId: string,
    to: string,
    messages: Message[],
  ): Promise<MessageAPIResponseBase> {
    const bot = await this.prisma.line_bots.findUnique({
      where: { id: botId },
    });
    if (!bot || !bot.is_enabled) {
      throw new NotFoundException(`LineBot ${botId} not found or is disabled.`);
    }

    const decryptedToken = bot.bot_token ? this.encryptionService.decrypt(bot.bot_token) : null;

    if (!decryptedToken) {
      throw new BadRequestException(`LineBot ${botId} does not have a configured access token.`);
    }

    const client = new Client({ channelAccessToken: decryptedToken });

    try {
      const response = await client.pushMessage(to, messages);
      this.logger.log(
        `Message pushed to ${to} via Bot ${botId}. Response: ${JSON.stringify(response)}`,
      );

      let workspaceContextId: string | undefined | null = null;
      if (bot.scope === LineBotScope.TENANT && bot.tenant_id) {
        if (to.startsWith('C') || to.startsWith('R')) {
          const verification = await this.prisma.line_group_verifications.findFirst({
            where: { bot_id: botId, group_id: to, is_verified: true },
            select: { workspace_id: true },
          });
          workspaceContextId = verification?.workspace_id;
        }
      }
      await this.messageLogService.logOutgoingMessages(bot, to, messages, response);

      return response;
    } catch (error: any) {
      this.logger.error(
        `Failed to push message to ${to} via Bot ${botId}: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(`Failed to push message: ${error.message}`);
    }
  }

  /**
   * Find or create a system tenant for SYSTEM scope bots
   * This is a workaround for the schema requirement that tenant_id is not nullable
   */
  private async findOrCreateSystemTenant() {
    const SYSTEM_TENANT_ID = 'system-tenant';

    let systemTenant = await this.prisma.tenants.findUnique({
      where: { id: SYSTEM_TENANT_ID },
    });

    if (!systemTenant) {
      // Check if there's a system plan first
      let systemPlan = await this.prisma.plans.findFirst({
        where: { name: 'System Plan' },
      });

      if (!systemPlan) {
        // Create a basic system plan if it doesn't exist
        systemPlan = await this.prisma.plans.create({
          data: {
            id: 'system-plan',
            name: 'System Plan',
            description: 'Internal system plan for SYSTEM scope bots',
            price: 0,
            billing_cycle: 'monthly',
            features: {},
            max_users: 999999,
            max_projects: 999999,
            max_storage: 999999,
            monthly_ai_credits_limit: 999999,
          },
        });
        this.logger.log('Created system plan for SYSTEM scope bots');
      }

      // Create a system tenant if it doesn't exist
      systemTenant = await this.prisma.tenants.create({
        data: {
          id: SYSTEM_TENANT_ID,
          name: 'System Tenant',
          domain: 'system.internal',
          status: 'active',
          plan_id: systemPlan.id,
        },
      });
      this.logger.log('Created system tenant for SYSTEM scope bots');
    }

    return systemTenant;
  }

  private async sendJoinVerificationMessageInternal(
    client: Client,
    replyToken: string,
    groupId: string,
  ): Promise<void> {
    const verificationUrl = `https://YOUR_FRONTEND_URL/verify-group?groupId=${groupId}`;
    const message: TextMessage = {
      type: 'text',
      text: `歡迎使用本服務！請管理員點擊以下連結完成群組驗證，以啟用完整功能： ${verificationUrl} (群組ID: ${groupId})`,
    };
    try {
      await client.replyMessage(replyToken, message);
      this.logger.log(`Sent verification guidance to group ${groupId}`);
    } catch (error) {
      this.logger.error(`Failed to send verification guidance to group ${groupId}: ${error}`);
    }
  }
}
