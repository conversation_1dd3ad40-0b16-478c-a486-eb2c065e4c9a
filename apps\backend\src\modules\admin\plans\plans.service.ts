import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { CreatePlanDto } from './dto/create-plan.dto';
import { UpdatePlanDto } from './dto/update-plan.dto';
import { Plan, Prisma } from '../../../types/prisma';
import { randomUUID } from 'crypto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class PlansService {
  private readonly logger = new Logger(PlansService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createPlanDto: CreatePlanDto): Promise<Plan> {
    try {
      const { features, isEnterprise, limits, ...rest } = createPlanDto;

      // 使用 limits 更新相關欄位（如果提供）
      const maxUsers = limits?.users || rest.maxUsers || 5;
      const maxProjects = limits?.projects || rest.maxProjects || 3;
      const maxStorage = limits?.storage || rest.maxStorage || 1;

      // 轉換 features 為正確的格式
      const featuresJson =
        features?.map((feature) => ({
          id: feature.id,
          name: feature.name,
          description: feature.description || '',
          included: feature.included,
        })) || [];

      // 構建插入查詢
      const insertQuery = `
        INSERT INTO "plans" (
          "id",
          "name",
          "description",
          "price",
          "billingCycle",
          "features",
          "maxUsers",
          "maxProjects",
          "maxStorage",
          "isPopular",
          "createdAt",
          "updatedAt"
        ) VALUES (
          gen_random_uuid(),
          $1,
          $2,
          $3,
          $4,
          $5::jsonb,
          $6,
          $7,
          $8,
          $9,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        )
        RETURNING *;
      `;

      const params = [
        rest.name,
        rest.description,
        rest.price,
        rest.billingCycle || 'monthly',
        JSON.stringify(featuresJson),
        maxUsers,
        maxProjects,
        maxStorage,
        rest.isPopular || false,
      ];

      this.logger.debug(`Insert Query: ${insertQuery}`);
      this.logger.debug(`Parameters: ${JSON.stringify(params)}`);

      const result = await this.prisma.$queryRawUnsafe(insertQuery, ...params);
      const newPlan = (result as Plan[])[0];

      if (!newPlan) {
        throw new Error('Failed to create plan');
      }

      return newPlan;
    } catch (error) {
      this.logger.error('Failed to create plan:', error);
      throw error;
    }
  }

  async findAll() {
    try {
      const plans = await this.prisma.$queryRawUnsafe(`
        SELECT * FROM plans ORDER BY price ASC;
      `);

      return plans;
    } catch (error) {
      this.logger.error('Failed to fetch plans:', error);
      throw error;
    }
  }

  async findOne(id: string) {
    try {
      const plans = await this.prisma.$queryRawUnsafe(
        `
        SELECT * FROM plans WHERE id = $1;
      `,
        id,
      );

      this.logger.debug(`查詢結果: ${JSON.stringify(plans)}`);

      const plan = (plans as Plan[])[0];
      if (!plan) {
        throw new NotFoundException(`Plan with ID ${id} not found`);
      }

      return plan;
    } catch (error) {
      this.logger.error(`Failed to fetch plan ${id}:`, error);
      throw error;
    }
  }

  async update(id: string, updatePlanDto: Partial<CreatePlanDto>) {
    try {
      // 首先檢查記錄是否存在
      const existingPlan = await this.findOne(id);
      if (!existingPlan) {
        throw new NotFoundException(`Plan with ID ${id} not found`);
      }

      const { features, billingCycle, maxUsers, maxProjects, maxStorage, isPopular, ...rest } =
        updatePlanDto;

      // 構建更新欄位
      const updateFields: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      // 處理基本欄位
      if (rest.name !== undefined) {
        updateFields.push(`"name" = $${paramIndex}`);
        params.push(rest.name);
        paramIndex++;
      }

      if (rest.description !== undefined) {
        updateFields.push(`"description" = $${paramIndex}`);
        params.push(rest.description);
        paramIndex++;
      }

      if (rest.price !== undefined) {
        updateFields.push(`"price" = $${paramIndex}`);
        params.push(rest.price);
        paramIndex++;
      }

      // 處理計費週期
      if (billingCycle !== undefined) {
        if (billingCycle !== 'monthly' && billingCycle !== 'yearly') {
          throw new Error('Invalid billing cycle value');
        }
        updateFields.push(`"billingCycle" = $${paramIndex}`);
        params.push(billingCycle);
        paramIndex++;
      }

      // 處理數值欄位
      if (maxUsers !== undefined) {
        updateFields.push(`"maxUsers" = $${paramIndex}`);
        params.push(maxUsers);
        paramIndex++;
      }

      if (maxProjects !== undefined) {
        updateFields.push(`"maxProjects" = $${paramIndex}`);
        params.push(maxProjects);
        paramIndex++;
      }

      if (maxStorage !== undefined) {
        updateFields.push(`"maxStorage" = $${paramIndex}`);
        params.push(maxStorage);
        paramIndex++;
      }

      // 處理布林值
      if (isPopular !== undefined) {
        updateFields.push(`"isPopular" = $${paramIndex}`);
        params.push(isPopular);
        paramIndex++;
      }

      // 處理功能列表
      if (features !== undefined) {
        const featuresJson = features.map((feature) => ({
          id: feature.id,
          name: feature.name,
          description: feature.description,
          included: feature.included,
        }));
        updateFields.push(`"features" = $${paramIndex}::jsonb`);
        params.push(JSON.stringify(featuresJson));
        paramIndex++;
      }

      // 如果沒有要更新的欄位，直接返回現有計劃
      if (updateFields.length === 0) {
        return existingPlan;
      }

      // 新增 WHERE 條件的參數
      params.push(id);

      // 構建並執行更新查詢
      const updateQuery = `
        UPDATE plans 
        SET ${updateFields.join(', ')},
            "updatedAt" = CURRENT_TIMESTAMP
        WHERE id = $${paramIndex}
        RETURNING *;
      `;

      this.logger.debug(`Update Query: ${updateQuery}`);
      this.logger.debug(`Parameters: ${JSON.stringify(params)}`);

      const result = await this.prisma.$queryRawUnsafe(updateQuery, ...params);
      const updatedPlan = (result as Plan[])[0];

      if (!updatedPlan) {
        throw new NotFoundException(`Failed to update plan with ID ${id}`);
      }

      return updatedPlan;
    } catch (error) {
      this.logger.error(`Failed to update plan with ID ${id}:`, error);
      this.logger.error('Error details:', error);
      throw error;
    }
  }

  async remove(id: string) {
    try {
      // 首先檢查記錄是否存在
      const existingPlan = await this.findOne(id);
      if (!existingPlan) {
        throw new NotFoundException(`Plan with ID ${id} not found`);
      }

      await this.prisma.$executeRawUnsafe(
        `
        DELETE FROM plans WHERE id = $1;
      `,
        id,
      );

      return existingPlan;
    } catch (error) {
      this.logger.error(`Failed to delete plan ${id}:`, error);
      throw error;
    }
  }

  async batchDelete(ids: string[]) {
    try {
      const idsString = ids.map((id) => `'${id}'`).join(',');

      const result = await this.prisma.$executeRawUnsafe(`
        DELETE FROM plans WHERE id IN (${idsString});
      `);

      return { count: result };
    } catch (error) {
      this.logger.error('Failed to batch delete plans:', error);
      throw error;
    }
  }
}
