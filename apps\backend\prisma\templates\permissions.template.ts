import { randomUUID } from "crypto";

// 權限類型定義
export interface Permission {
  id?: string;
  action: string;
  subject: string;
  description: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// 權限動作類型
export type PermissionAction =
  | "view"
  | "create"
  | "update"
  | "delete"
  | "manage"
  | "softDelete"
  | "hardDelete"
  | "restore"
  | "reset"
  | "test"
  | "clear"
  | "export"
  | "impersonate";

// 權限主體類型
export type PermissionSubject =
  | "AdminDashboard"
  | "Tenant"
  | "TenantDetail"
  | "TenantProfile"
  | "TenantStatus"
  | "TenantPlan"
  | "TenantResourceLimit"
  | "SystemUser"
  | "SystemUserDetail"
  | "SystemUserProfile"
  | "SystemUserRole"
  | "SystemUserStatus"
  | "SystemUserPassword"
  | "AllTenantUser"
  | "SpecificTenantUserList"
  | "SpecificTenantUserDetail"
  | "SpecificTenantUserStatus"
  | "User"
  | "SystemSetting"
  | "GeneralSetting"
  | "MailSetting"
  | "StorageSetting"
  | "AuthSetting"
  | "IdentityProvider"
  | "FeatureFlag"
  | "SystemLog"
  | "ApiKey"
  | "Role"
  | "Permission"
  | "RoleDefinition"
  | "RolePermission"
  | "Plan"
  | "PlanDetail"
  | "PlanPrice"
  | "PlanStatus"
  | "Subscription"
  | "Order"
  | "BillingData";

// 管理後台權限定義
export const ADMIN_PERMISSIONS: Permission[] = [
  // 主控台
  {
    action: "view",
    subject: "AdminDashboard",
    description: "查看管理後台主控台",
  },

  // 租戶管理
  { action: "view", subject: "Tenant", description: "查看租戶列表" },
  {
    action: "view",
    subject: "TenantDetail",
    description: "查看特定租戶詳細資訊",
  },
  { action: "create", subject: "Tenant", description: "新增租戶" },
  {
    action: "update",
    subject: "TenantProfile",
    description: "修改租戶基本資料",
  },
  { action: "update", subject: "TenantStatus", description: "修改租戶狀態" },
  { action: "update", subject: "TenantPlan", description: "修改租戶訂閱方案" },
  {
    action: "manage",
    subject: "TenantResourceLimit",
    description: "管理租戶資源限制",
  },
  { action: "softDelete", subject: "Tenant", description: "冷刪除租戶" },
  { action: "restore", subject: "Tenant", description: "恢復冷刪除的租戶" },
  { action: "hardDelete", subject: "Tenant", description: "永久刪除租戶" },

  // 系統使用者
  { action: "view", subject: "SystemUser", description: "查看系統使用者列表" },
  {
    action: "view",
    subject: "SystemUserDetail",
    description: "查看系統使用者詳細資料",
  },
  { action: "create", subject: "SystemUser", description: "新增系統使用者" },
  {
    action: "update",
    subject: "SystemUserProfile",
    description: "修改系統使用者基本資料",
  },
  {
    action: "update",
    subject: "SystemUserRole",
    description: "修改系統使用者角色",
  },
  {
    action: "update",
    subject: "SystemUserStatus",
    description: "修改系統使用者狀態",
  },
  {
    action: "reset",
    subject: "SystemUserPassword",
    description: "重設系統使用者密碼",
  },
  {
    action: "softDelete",
    subject: "SystemUser",
    description: "冷刪除系統使用者",
  },
  {
    action: "restore",
    subject: "SystemUser",
    description: "恢復冷刪除的系統使用者",
  },
  {
    action: "hardDelete",
    subject: "SystemUser",
    description: "永久刪除系統使用者",
  },

  // 租戶使用者
  {
    action: "view",
    subject: "AllTenantUser",
    description: "查看所有租戶的使用者列表",
  },
  {
    action: "view",
    subject: "SpecificTenantUserList",
    description: "查看特定租戶的使用者列表",
  },
  {
    action: "view",
    subject: "SpecificTenantUserDetail",
    description: "查看特定租戶使用者詳細資料",
  },
  {
    action: "update",
    subject: "SpecificTenantUserStatus",
    description: "修改特定租戶使用者狀態",
  },
  { action: "impersonate", subject: "User", description: "模擬使用者登入" },

  // 系統設定
  { action: "view", subject: "SystemSetting", description: "查看系統設定概覽" },
  { action: "view", subject: "GeneralSetting", description: "查看通用設定" },
  { action: "update", subject: "GeneralSetting", description: "修改通用設定" },
  { action: "view", subject: "MailSetting", description: "查看郵件伺服器設定" },
  {
    action: "update",
    subject: "MailSetting",
    description: "修改郵件伺服器設定",
  },
  { action: "test", subject: "MailSetting", description: "測試郵件發送功能" },
  {
    action: "view",
    subject: "StorageSetting",
    description: "查看檔案儲存設定",
  },
  {
    action: "update",
    subject: "StorageSetting",
    description: "修改檔案儲存設定",
  },
  { action: "view", subject: "AuthSetting", description: "查看認證相關設定" },
  { action: "update", subject: "AuthSetting", description: "修改認證相關設定" },
  {
    action: "manage",
    subject: "IdentityProvider",
    description: "管理第三方登入提供者",
  },
  {
    action: "view",
    subject: "FeatureFlag",
    description: "查看功能開關列表及狀態",
  },
  {
    action: "manage",
    subject: "FeatureFlag",
    description: "啟用或停用特定功能開關",
  },
  { action: "view", subject: "SystemLog", description: "查看系統運行日誌" },
  { action: "clear", subject: "SystemLog", description: "清除系統日誌" },
  { action: "view", subject: "ApiKey", description: "查看系統層級 API 金鑰" },
  { action: "manage", subject: "ApiKey", description: "管理系統層級 API 金鑰" },

  // 權限管理
  { action: "view", subject: "Role", description: "查看角色列表" },
  {
    action: "view",
    subject: "Permission",
    description: "查看所有可用權限列表",
  },
  { action: "create", subject: "Role", description: "新增角色" },
  {
    action: "update",
    subject: "RoleDefinition",
    description: "修改角色名稱或描述",
  },
  {
    action: "update",
    subject: "RolePermission",
    description: "修改角色權限分配",
  },
  { action: "softDelete", subject: "Role", description: "冷刪除角色" },
  { action: "restore", subject: "Role", description: "恢復冷刪除的角色" },
  { action: "hardDelete", subject: "Role", description: "永久刪除角色" },

  // 訂閱與方案
  { action: "view", subject: "Plan", description: "查看方案列表" },
  { action: "create", subject: "Plan", description: "新增收費方案" },
  { action: "update", subject: "PlanDetail", description: "修改方案細節" },
  { action: "update", subject: "PlanPrice", description: "修改方案價格" },
  { action: "update", subject: "PlanStatus", description: "修改方案狀態" },
  { action: "softDelete", subject: "Plan", description: "冷刪除方案" },
  { action: "restore", subject: "Plan", description: "恢復冷刪除的方案" },
  { action: "hardDelete", subject: "Plan", description: "永久刪除方案" },
  { action: "view", subject: "Subscription", description: "查看所有訂閱紀錄" },
  { action: "view", subject: "Order", description: "查看訂單/付款紀錄" },
  { action: "manage", subject: "Subscription", description: "管理使用者訂閱" },
  { action: "delete", subject: "Subscription", description: "取消使用者訂閱" },
  { action: "export", subject: "BillingData", description: "匯出帳務資料" },
];

// 權限工具函數
export function generatePermissionId(): string {
  return randomUUID();
}

export function createPermissionKey(action: string, subject: string): string {
  return `${action}:${subject}`;
}

export function getPermissionByKey(key: string): Permission | undefined {
  const [action, subject] = key.split(":");
  return ADMIN_PERMISSIONS.find(
    (p) => p.action === action && p.subject === subject
  );
}

export function getAllPermissionKeys(): string[] {
  return ADMIN_PERMISSIONS.map((p) => createPermissionKey(p.action, p.subject));
}
