<script setup lang="ts">
import { computed } from "vue";
import { useRouter } from 'vue-router';
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableCaption,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Plus,
  Edit,
  Trash2,
  <PERSON>rkles,
  Search,
  X,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Settings,
  Zap,
  Bot,
  Users,
  Info,
  Brain,
  AlertTriangle,
  TestTube,
  Pencil,
  BrainCircuit,
} from "lucide-vue-next";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Import types from central files
import {
  AiBotScope,
  type AiModel as CoreAiModel,
  type AiKey as CoreAiKey,
  type AiBotWithRelations,
} from "@/types/models/ai.model";
import { useBotsManagementTab } from "@/composables/admin/ai/bots/useBotsManagementTab";

const props = defineProps<{
  aiBots: AiBotWithRelations[] | undefined;
  aiModels: CoreAiModel[] | undefined;
  aiKeys: CoreAiKey[] | undefined;
}>();

const emit = defineEmits<{
  (e: "delete-agent", botId: string): void;
  (e: "test-agent", botId: string): void;
}>();

const router = useRouter();

// 使用 composable
const {
  searchTerm,
  scopeFilter,
  statusFilter,
  sortField,
  sortDirection,
  createFilteredAndSortedBots,
  toggleSort,
  resetFilters,
  getBotStats,
  formatBotForDisplay,
} = useBotsManagementTab();

// 將 props 轉換為 computed refs 以確保響應性
const aiBots = computed(() => props.aiBots);
const aiModels = computed(() => props.aiModels);
const aiKeys = computed(() => props.aiKeys);

// 篩選和排序後的機器人列表
const filteredAndSortedBots = createFilteredAndSortedBots(aiBots, aiModels, aiKeys);

// 格式化後的機器人列表，包含顯示資訊
const displayBots = computed(() => {
  return filteredAndSortedBots.value.map((bot) =>
    formatBotForDisplay(bot, aiModels.value, aiKeys.value)
  );
});

// 機器人統計 - 添加系統統計
const botStats = computed(() => {
  const stats = getBotStats(aiBots);
  return {
    ...stats.value,
    system: stats.value.byScope['SYSTEM'] || 0,
    workspace: stats.value.byScope['WORKSPACE'] || 0
  };
});

// 獲取排序圖標
const getSortIcon = (field: keyof AiBotWithRelations) => {
  if (sortField.value !== field) return null;
  return sortDirection.value === "asc" ? "asc" : "desc";
};

// 路由導航函數已移除，改用 emit 事件處理

// 獲取執行類型顯示
const getExecutionTypeDisplay = (bot: AiBotWithRelations) => {
  // 根據後端的執行類型邏輯來判斷
  // 這裡可以根據具體的業務邏輯來實現
  return (bot as any).execution_type || '工具型';
};

// 獲取執行類型標籤顏色
const getExecutionTypeVariant = (executionType: string) => {
  switch (executionType) {
    case '工具型':
      return 'default';
    case '圖譜型':
      return 'secondary';
    default:
      return 'outline';
  }
};

// 清空搜尋
const clearSearch = () => {
  searchTerm.value = "";
};

// 格式化最後更新時間
const formatUpdatedAt = (updatedAt: string) => {
  return new Date(updatedAt).toLocaleDateString('zh-TW');
};

// Make AiBotScope available in the template for the :disabled condition
const BotScopeEnum = AiBotScope;
</script>

<template>
  <div class="space-y-6">
    <!-- 操作按鈕區域 -->
    <div class="flex justify-end">
      <Button
        variant="outline"
        size="sm"
        @click="router.push('/ai-studio/new')"
      >
        <Plus class="h-4 w-4 mr-2" />
        新增助理
      </Button>
    </div>

    <!-- 統計卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- 總助理數 -->
      <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
        <div class="p-4">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-blue-100 dark:bg-blue-800 rounded-md">
              <Bot class="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">{{ botStats.total }}</p>
              <p class="text-xs text-zinc-500 dark:text-zinc-400">總助理數</p>
            </div>
          </div>
        </div>
      </Card>
      
      <!-- 已啟用 -->
      <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
        <div class="p-4">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-green-100 dark:bg-green-800 rounded-md">
              <Zap class="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">{{ botStats.enabled }}</p>
              <p class="text-xs text-zinc-500 dark:text-zinc-400">已啟用</p>
            </div>
          </div>
        </div>
      </Card>
      
      <!-- 系統級 -->
      <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
        <div class="p-4">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-purple-100 dark:bg-purple-800 rounded-md">
              <Settings class="w-4 h-4 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">{{ botStats.system }}</p>
              <p class="text-xs text-zinc-500 dark:text-zinc-400">系統級</p>
            </div>
          </div>
        </div>
      </Card>
      
      <!-- 工作區級 -->
      <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
        <div class="p-4">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-orange-100 dark:bg-orange-800 rounded-md">
              <Users class="w-4 h-4 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <p class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">{{ botStats.workspace }}</p>
              <p class="text-xs text-zinc-500 dark:text-zinc-400">工作區級</p>
            </div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 搜尋和篩選區域 -->
    <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
      <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 搜尋欄 -->
          <div class="md:col-span-2">
            <Label class="text-sm font-medium mb-2 block">搜尋助理</Label>
            <div class="relative">
              <Search class="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input 
                v-model="searchTerm"
                placeholder="搜尋助理名稱、描述..." 
                class="pl-8 h-10 text-sm"
              />
              <button
                v-if="searchTerm"
                @click="clearSearch"
                class="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                <X class="h-4 w-4" />
              </button>
            </div>
          </div>

          <!-- 範疇篩選 -->
          <div>
            <Label class="text-sm font-medium mb-2 block">範疇</Label>
            <Select v-model="scopeFilter">
              <SelectTrigger class="h-10">
                <SelectValue placeholder="選擇範疇" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部範疇</SelectItem>
                <SelectItem value="SYSTEM">系統級</SelectItem>
                <SelectItem value="WORKSPACE">工作區級</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 狀態篩選 -->
          <div>
            <Label class="text-sm font-medium mb-2 block">狀態</Label>
            <Select v-model="statusFilter">
              <SelectTrigger class="h-10">
                <SelectValue placeholder="選擇狀態" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部狀態</SelectItem>
                <SelectItem value="true">已啟用</SelectItem>
                <SelectItem value="false">已停用</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 快速重置按鈕 -->
        <div class="mt-4 flex justify-end">
          <Button
            @click="resetFilters"
            variant="outline"
            size="sm"
            v-if="searchTerm || scopeFilter !== 'all' || statusFilter !== 'all'"
          >
            重置篩選
          </Button>
        </div>
      </div>
    </Card>

    <!-- 助理列表 -->
    <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
      <div class="p-0">
        <!-- 空狀態 -->
        <div v-if="!aiBots || aiBots.length === 0" class="flex flex-col items-center justify-center py-16">
          <div class="flex flex-col items-center space-y-6 text-center max-w-lg">
            <!-- 視覺圖示 -->
            <div class="relative">
              <div class="p-6 bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 rounded-2xl">
                <Bot class="w-12 h-12 text-purple-600 dark:text-purple-400" />
              </div>
              <div class="absolute -top-1 -right-1 p-1 bg-blue-100 dark:bg-blue-900/50 rounded-full">
                <Sparkles class="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            
            <!-- 文字內容 -->
            <div class="space-y-2">
              <h3 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100">尚未創建 AI 助理</h3>
              <p class="text-sm text-zinc-600 dark:text-zinc-400 leading-relaxed">
                AI 助理是您的智能夥伴，可以幫助處理對話、回答問題、創建內容等任務。<br />
                每個助理都可以針對特定場景進行客製化設定。
              </p>
            </div>

            <!-- 功能特色 -->
            <div class="flex items-center space-x-4 py-3 px-4 bg-zinc-50 dark:bg-zinc-800 rounded-lg">
              <div class="flex items-center space-x-1">
                <BrainCircuit class="w-4 h-4 text-blue-600" />
                <span class="text-xs font-medium text-zinc-700 dark:text-zinc-300">智能對話</span>
              </div>
              <div class="flex items-center space-x-1">
                <Pencil class="w-4 h-4 text-green-600" />
                <span class="text-xs font-medium text-zinc-700 dark:text-zinc-300">內容創作</span>
              </div>
              <div class="flex items-center space-x-1">
                <Sparkles class="w-4 h-4 text-purple-600" />
                <span class="text-xs font-medium text-zinc-700 dark:text-zinc-300">個性化設定</span>
              </div>
            </div>
            
            <!-- 操作按鈕 -->
            <Button 
              @click="router.push('/ai-studio/new')" 
              class="mt-4 px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white"
              size="sm"
            >
              <Plus class="w-4 h-4 mr-2" />
              創建第一個 AI 助理
            </Button>
          </div>
        </div>

        <!-- 篩選結果為空 -->
        <div v-else-if="filteredAndSortedBots.length === 0" class="text-center py-8">
          <div class="flex flex-col items-center space-y-4">
            <div class="p-4 bg-zinc-100 dark:bg-zinc-700 rounded-lg">
              <Search class="w-8 h-8 text-zinc-400" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">找不到符合條件的助理</h3>
              <p class="text-sm text-zinc-500 dark:text-zinc-400 mt-1">請調整搜尋條件或建立新的助理</p>
            </div>
          </div>
        </div>

        <!-- 助理表格 -->
        <div v-else class="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow class="hover:bg-transparent border-b border-zinc-200 dark:border-zinc-700">
                <TableHead>
                  <button
                    @click="toggleSort('name')"
                    class="flex items-center space-x-1 font-medium text-left hover:text-zinc-900 dark:hover:text-zinc-100"
                  >
                    <span>助理名稱</span>
                    <ArrowUpDown v-if="!getSortIcon('name')" class="w-4 h-4 opacity-50" />
                    <ArrowUp v-else-if="getSortIcon('name') === 'asc'" class="w-4 h-4" />
                    <ArrowDown v-else class="w-4 h-4" />
                  </button>
                </TableHead>
                <TableHead>描述</TableHead>
                <TableHead>範疇</TableHead>
                <TableHead>模型</TableHead>
                <TableHead>狀態</TableHead>
                <TableHead>
                  <button
                    @click="toggleSort('updated_at')"
                    class="flex items-center space-x-1 font-medium text-left hover:text-zinc-900 dark:hover:text-zinc-100"
                  >
                    <span>最後更新</span>
                    <ArrowUpDown v-if="!getSortIcon('updated_at')" class="w-4 h-4 opacity-50" />
                    <ArrowUp v-else-if="getSortIcon('updated_at') === 'asc'" class="w-4 h-4" />
                    <ArrowDown v-else class="w-4 h-4" />
                  </button>
                </TableHead>
                <TableHead class="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow
                v-for="bot in displayBots"
                :key="bot.id"
                class="hover:bg-zinc-50 dark:hover:bg-zinc-800/50 border-b border-zinc-100 dark:border-zinc-800 transition-colors duration-200"
              >
                <TableCell class="font-medium">
                  <div class="flex items-center space-x-2">
                    <div class="p-1.5 bg-blue-100 dark:bg-blue-800 rounded-sm">
                      <Bot class="w-3 h-3 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <div class="font-medium text-sm">{{ bot.name }}</div>
                      <div v-if="bot.scene" class="text-xs text-zinc-500 dark:text-zinc-400">{{ bot.scene }}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div class="max-w-xs">
                    <p class="text-sm text-zinc-600 dark:text-zinc-400 line-clamp-2">
                      {{ bot.description || '無描述' }}
                    </p>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    :variant="bot.scope === 'SYSTEM' ? 'default' : 'secondary'"
                    class="text-xs"
                  >
                    {{ bot.scope === 'SYSTEM' ? '系統級' : '工作區級' }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div class="flex items-center space-x-1">
                    <Brain class="w-3 h-3 text-zinc-400" />
                    <span class="text-sm text-zinc-600 dark:text-zinc-400">
                      {{ bot.modelName || '未設定' }}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div class="flex items-center space-x-1.5">
                    <div :class="[
                      'w-2 h-2 rounded-full',
                      bot.is_enabled ? 'bg-green-500' : 'bg-zinc-400'
                    ]"></div>
                    <span class="text-xs font-medium" :class="[
                      bot.is_enabled ? 'text-green-600 dark:text-green-400' : 'text-zinc-500 dark:text-zinc-400'
                    ]">
                      {{ bot.is_enabled ? '已啟用' : '已停用' }}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <span class="text-sm text-zinc-500 dark:text-zinc-400">
                    {{ formatUpdatedAt(bot.updated_at) }}
                  </span>
                </TableCell>
                <TableCell class="text-right">
                  <div class="flex items-center justify-end space-x-1">
                    <!-- 測試按鈕 -->
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            @click="$emit('test-agent', bot.id)"
                            size="sm"
                            variant="outline"
                            class="h-8 w-8 p-0 hover:bg-green-50 hover:border-green-300 hover:text-green-700"
                          >
                            <TestTube class="w-3 h-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>測試助理</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    
                    <!-- 編輯按鈕 -->
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            @click="router.push(`/ai-studio/${bot.id}`)"
                            size="sm"
                            variant="outline"
                            class="h-8 w-8 p-0 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700"
                          >
                            <Edit class="w-3 h-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>編輯助理</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    
                    <!-- 刪除按鈕 -->
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="outline"
                                class="h-8 w-8 p-0 hover:bg-red-50 hover:border-red-300 hover:text-red-700"
                                :disabled="bot.scope === BotScopeEnum.SYSTEM"
                              >
                                <Trash2 class="w-3 h-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              {{ bot.scope === BotScopeEnum.SYSTEM ? '系統助理無法刪除' : '刪除助理' }}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </AlertDialogTrigger>
                      <AlertDialogContent class="max-w-md">
                        <AlertDialogHeader>
                          <div class="flex items-center space-x-3">
                            <div class="p-2 bg-red-100 dark:bg-red-900 rounded-full">
                              <AlertTriangle class="w-5 h-5 text-red-600 dark:text-red-400" />
                            </div>
                            <div>
                              <AlertDialogTitle class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
                                確認刪除助理
                              </AlertDialogTitle>
                            </div>
                          </div>
                          <AlertDialogDescription class="text-sm text-zinc-600 dark:text-zinc-400 mt-3">
                            您即將刪除 AI 助理：
                            <span class="font-semibold text-zinc-900 dark:text-zinc-100">「{{ bot.name }}」</span>
                            <br /><br />
                            此操作無法復原，助理的所有設定和對話歷史將會永久刪除。
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter class="gap-2 pt-4">
                          <AlertDialogCancel class="px-4">取消</AlertDialogCancel>
                          <AlertDialogAction
                            @click="$emit('delete-agent', bot.id)"
                            class="bg-red-500 hover:bg-red-600 text-white px-4"
                          >
                            <Trash2 class="w-4 h-4 mr-2" />
                            確認刪除
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
            <TableCaption v-if="filteredAndSortedBots.length > 0" class="text-sm text-zinc-500 dark:text-zinc-400">
              顯示 {{ filteredAndSortedBots.length }} 個助理
            </TableCaption>
          </Table>
        </div>
      </div>
    </Card>
  </div>
</template>
