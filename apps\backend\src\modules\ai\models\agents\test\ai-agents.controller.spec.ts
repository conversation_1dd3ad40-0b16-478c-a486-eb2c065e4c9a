import { Test, TestingModule } from '@nestjs/testing';
import { AiAgentsController } from '../ai-agents.controller';
import { AiAgentsService } from '../ai-agents.service';
import { PrismaService } from '../../../../core/prisma/prisma.service';
import { CaslAbilityFactory } from '../../../../../casl/ability/casl-ability.factory';
import { PoliciesGuard } from '../../../../../casl/guards/permission.guard';
import { PermissionCheckerService } from '../../../../../casl/services/permission-checker.service';
import { Reflector } from '@nestjs/core';
import { JwtAuthGuard } from '../../../../core/auth/guards/auth.guard';
import { CreateAgentDto, UpdateAgentDto, ExecuteAgentDto } from '../dto/agent.dto';
import { AiAgentScope, AiAgentProviderType, AiAgentResponseFormat } from '@prisma/client';

// Mock PrismaService
const mockPrismaService = {
  ai_agents: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  ai_models: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
  },
  ai_keys: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
  },
};

describe('AiAgentsController', () => {
  let controller: AiAgentsController;
  let service: AiAgentsService;
  let prismaService: PrismaService;
  let caslAbilityFactory: CaslAbilityFactory;
  let permissionCheckerService: PermissionCheckerService;

  const mockUser = {
    id: 'user-123',
    sub: 'user-123',
    email: '<EMAIL>',
    user_type: 'system',
    tenant_id: null,
    role: 'SUPER_ADMIN',
  };

  const mockAiAgent = {
    id: 'agent-123',
    name: 'Test Agent',
    description: 'Test Description',
    scope: AiAgentScope.SYSTEM,
    provider_type: AiAgentProviderType.OPENAI,
    model_id: 'model-123',
    key_id: 'key-123',
    provider_config_override: null,
    system_prompt: 'Test prompt',
    temperature: 0.7,
    max_tokens: 1000,
    response_format: AiAgentResponseFormat.TEXT,
    is_enabled: true,
    is_template: false,
    scene: 'test',
    tenant_id: null,
    workspace_id: null,
    created_at: new Date(),
    updated_at: new Date(),
    created_by: 'user-123',
    updated_by: null,
  };

  const mockExecuteResponse = {
    response: 'Test AI response',
    usage: {
      input_tokens: 10,
      output_tokens: 20,
      total_tokens: 30,
    },
    model: 'gpt-4',
    provider: 'openai',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AiAgentsController],
      providers: [
        {
          provide: AiAgentsService,
          useValue: {
            findAll: jest.fn().mockResolvedValue([mockAiAgent]),
            findOne: jest.fn().mockResolvedValue(mockAiAgent),
            create: jest.fn().mockResolvedValue(mockAiAgent),
            update: jest.fn().mockResolvedValue(mockAiAgent),
            delete: jest.fn().mockResolvedValue(mockAiAgent),
            execute: jest.fn().mockResolvedValue(mockExecuteResponse),
            testAgent: jest.fn().mockResolvedValue(mockExecuteResponse),
            optimizePrompt: jest.fn().mockResolvedValue({ optimizedPrompt: 'Optimized prompt' }),
            updateStatus: jest.fn().mockResolvedValue(mockAiAgent),
          },
        },
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: CaslAbilityFactory,
          useValue: {
            createForUser: jest.fn().mockResolvedValue({
              can: jest.fn().mockReturnValue(true),
              rules: [],
            }),
          },
        },
        {
          provide: PermissionCheckerService,
          useValue: {
            checkPermission: jest.fn().mockResolvedValue({ granted: true }),
            canManage: jest.fn().mockResolvedValue(true),
            canRead: jest.fn().mockResolvedValue(true),
            canCreate: jest.fn().mockResolvedValue(true),
            canUpdate: jest.fn().mockResolvedValue(true),
            canDelete: jest.fn().mockResolvedValue(true),
          },
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn().mockReturnValue([]),
            getAllAndOverride: jest.fn().mockReturnValue([]),
            getAllAndMerge: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: PoliciesGuard,
          useValue: {
            canActivate: jest.fn().mockResolvedValue(true),
          },
        },
        {
          provide: JwtAuthGuard,
          useValue: {
            canActivate: jest.fn().mockResolvedValue(true),
          },
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .overrideGuard(PoliciesGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    controller = module.get<AiAgentsController>(AiAgentsController);
    service = module.get<AiAgentsService>(AiAgentsService);
    prismaService = module.get<PrismaService>(PrismaService);
    caslAbilityFactory = module.get<CaslAbilityFactory>(CaslAbilityFactory);
    permissionCheckerService = module.get<PermissionCheckerService>(PermissionCheckerService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
    expect(prismaService).toBeDefined();
    expect(caslAbilityFactory).toBeDefined();
    expect(permissionCheckerService).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of AI agents', async () => {
      const result = await controller.findAll();
      expect(result).toEqual([mockAiAgent]);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single AI agent', async () => {
      const result = await controller.findOne('agent-123');
      expect(result).toEqual(mockAiAgent);
      expect(service.findOne).toHaveBeenCalledWith('agent-123');
    });
  });

  describe('create', () => {
    it('should create a new AI agent', async () => {
      const createDto: CreateAgentDto = {
        name: 'Test Agent',
        description: 'Test Description',
        scope: AiAgentScope.SYSTEM,
        provider_type: AiAgentProviderType.OPENAI,
        model_id: 'model-123',
        key_id: 'key-123',
        system_prompt: 'Test prompt',
        temperature: 0.7,
        max_tokens: 1000,
        response_format: AiAgentResponseFormat.TEXT,
        is_enabled: true,
        is_template: false,
        scene: 'test',
        tenant_id: 'tenant-123',
        workspace_id: 'workspace-123',
      };

      const result = await controller.create(createDto, mockUser);
      expect(result).toEqual(mockAiAgent);
      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.id);
    });
  });

  describe('update', () => {
    it('should update an existing AI agent', async () => {
      const updateDto: UpdateAgentDto = {
        name: 'Updated Agent',
        description: 'Updated Description',
      };

      const result = await controller.update('agent-123', updateDto, mockUser);
      expect(result).toEqual(mockAiAgent);
      expect(service.update).toHaveBeenCalledWith('agent-123', updateDto, mockUser.id);
    });
  });

  describe('remove', () => {
    it('should delete an AI agent', async () => {
      const result = await controller.remove('agent-123');
      expect(result).toEqual(mockAiAgent);
      expect(service.delete).toHaveBeenCalledWith('agent-123');
    });
  });

  describe('execute', () => {
    it('should execute an AI agent', async () => {
      const executeDto: ExecuteAgentDto = {
        messages: [{ role: 'user', content: 'Test input' }],
        temperature: 0.7,
      };

      const result = await controller.execute('agent-123', executeDto);
      expect(result).toEqual(mockExecuteResponse);
      expect(service.execute).toHaveBeenCalledWith('agent-123', executeDto);
    });
  });
});
