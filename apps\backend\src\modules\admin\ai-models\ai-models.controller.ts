import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { AiModelsService } from './ai-models.service';
import { CreateAiModelDto } from './dto/create-ai-model.dto';
import { UpdateAiModelDto } from './dto/update-ai-model.dto';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';

@Controller('ai-models')
@ApiTags('AI Models')
@ApiBearerAuth()
export class AiModelsController {
  private readonly logger = new Logger(AiModelsController.name);

  constructor(private readonly aiModelsService: AiModelsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all AI models' })
  @ApiResponse({ status: 200, description: 'List of AI models' })
  @ApiQuery({ name: 'tenantId', required: false, description: '租戶 ID (留空顯示系統級)' })
  findAll(@Query('tenantId') tenantId?: string) {
    this.logger.log('Fetching all AI models');
    return this.aiModelsService.findAll(tenantId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific AI model' })
  @ApiResponse({ status: 200, description: 'AI model details' })
  findOne(@Param('id') id: string) {
    this.logger.log(`Fetching AI model with ID: ${id}`);
    return this.aiModelsService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new AI model' })
  @ApiResponse({ status: 201, description: 'AI model created successfully' })
  create(@Body() createAiModelDto: CreateAiModelDto) {
    this.logger.log('Creating a new AI model');
    return this.aiModelsService.create(createAiModelDto);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an AI model' })
  @ApiResponse({ status: 200, description: 'AI model updated successfully' })
  update(@Param('id') id: string, @Body() updateAiModelDto: UpdateAiModelDto) {
    this.logger.log(`Updating AI model with ID: ${id}`);
    return this.aiModelsService.update(id, updateAiModelDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an AI model' })
  @ApiResponse({ status: 204, description: 'AI model deleted successfully' })
  remove(@Param('id') id: string) {
    this.logger.log(`Deleting AI model with ID: ${id}`);
    return this.aiModelsService.remove(id);
  }
}
