# Task ID: 30
# Title: 建立預定義 LangGraph 圖譜範本系統
# Status: pending
# Dependencies: 19, 29
# Priority: medium
# Description: 設計和實作預定義的 LangGraph 圖譜範本架構，支援複雜的多步驟 Agent 工作流程，如專案進度分析師等協作型 Agent。
# Details:
建立 LangGraph 圖譜管理系統:
1. **圖譜定義架構**: 
   - 在 apps/backend/src/modules/agent/graphs/ 目錄建立圖譜定義
   - 每個圖譜包含節點定義、邊條件、狀態管理
2. **範本圖譜實作**: 
   - ProjectProgressAnalystGraph: 專案進度分析師圖譜
   - 包含數據收集、依賴分析、瓶頸識別、解決方案建議等節點
3. **圖譜註冊系統**: 
   - GraphRegistry 服務管理所有可用圖譜
   - 支援圖譜的動態載入和執行
4. **狀態管理**: 
   - 定義共享狀態結構
   - 節點間的狀態傳遞和更新機制
5. **與工具整合**: 圖譜節點可調用現有的 LangChain Tools
6. **前端選擇介面**: 讓管理員在搭建器中選擇可用的圖譜範本

# Test Strategy:
建立範例圖譜並測試執行流程。驗證狀態在節點間正確傳遞。測試條件分支和循環邏輯。確認工具調用在圖譜環境中正常運作。測試錯誤處理和中斷恢復機制。
