import { Test, TestingModule } from '@nestjs/testing';
import { SchedulerRegistry } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LogArchivingService } from './log-archiving.service';
import { PrismaService } from '../../modules/core/prisma/prisma.service';
import { SettingsService } from '../../modules/admin/settings/settings.service';
import { StorageFactory } from '../../modules/core/storage/storage.factory';
import { DEFAULT_ARCHIVING_SETTINGS } from '../../modules/admin/settings/interfaces/archiving-settings.interface';

describe('LogArchivingService', () => {
  let service: LogArchivingService;
  let settingsService: jest.Mocked<SettingsService>;
  let schedulerRegistry: jest.Mocked<SchedulerRegistry>;
  let eventEmitter: jest.Mocked<EventEmitter2>;

  beforeEach(async () => {
    const mockPrismaService = {
      system_logs: {
        findMany: jest.fn(),
        deleteMany: jest.fn(),
      },
      archived_audit_logs: {
        createMany: jest.fn(),
        count: jest.fn(),
        findFirst: jest.fn(),
        deleteMany: jest.fn(),
        groupBy: jest.fn(),
      },
    };

    const mockSettingsService = {
      getArchivingSettings: jest.fn(),
      updateArchivingStatus: jest.fn(),
    };

    const mockStorageFactory = {
      createArchivingStorageService: jest.fn(),
    };

    const mockSchedulerRegistry = {
      addTimeout: jest.fn(),
      deleteTimeout: jest.fn(),
      doesExist: jest.fn(),
    };

    const mockEventEmitter = {
      emit: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LogArchivingService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: SettingsService, useValue: mockSettingsService },
        { provide: StorageFactory, useValue: mockStorageFactory },
        { provide: SchedulerRegistry, useValue: mockSchedulerRegistry },
        { provide: EventEmitter2, useValue: mockEventEmitter },
      ],
    }).compile();

    service = module.get<LogArchivingService>(LogArchivingService);
    settingsService = module.get(SettingsService);
    schedulerRegistry = module.get(SchedulerRegistry);
    eventEmitter = module.get(EventEmitter2);
  });

  describe('動態排程功能', () => {
    it('應該在模組初始化時設定排程', async () => {
      // Arrange
      settingsService.getArchivingSettings.mockResolvedValue({
        ...DEFAULT_ARCHIVING_SETTINGS,
        enabled: true,
        schedule: '0 2 * * *', // 每天凌晨2點
      });
      schedulerRegistry.doesExist.mockReturnValue(false);

      // Act
      await service.onModuleInit();

      // Assert
      expect(settingsService.getArchivingSettings).toHaveBeenCalled();
      expect(schedulerRegistry.addTimeout).toHaveBeenCalled();
    });

    it('應該在歸檔功能關閉時清理排程', async () => {
      // Arrange
      settingsService.getArchivingSettings.mockResolvedValue({
        ...DEFAULT_ARCHIVING_SETTINGS,
        enabled: false,
      });
      schedulerRegistry.doesExist.mockReturnValue(true);

      // Act
      await service.setupDynamicSchedule();

      // Assert
      expect(schedulerRegistry.deleteTimeout).toHaveBeenCalled();
    });

    it('應該響應歸檔設定更新事件', async () => {
      // Arrange
      const newSettings = {
        ...DEFAULT_ARCHIVING_SETTINGS,
        enabled: true,
        schedule: '0 3 * * *', // 每天凌晨3點
      };
      settingsService.getArchivingSettings.mockResolvedValue(newSettings);

      // Act
      await service.handleArchivingSettingsUpdated({
        settings: newSettings,
        userId: 'test-user',
        timestamp: new Date(),
      });

      // Assert
      expect(settingsService.getArchivingSettings).toHaveBeenCalled();
    });

    it('應該正確計算下次執行時間', () => {
      // Arrange & Act
      const nextTime = service['getNextExecutionTime']('0 2 * * *');

      // Assert
      expect(nextTime).toBeInstanceOf(Date);
      expect(nextTime!.getHours()).toBe(2);
      expect(nextTime!.getMinutes()).toBe(0);
    });
  });

  describe('歸檔執行功能', () => {
    it('應該在歸檔功能關閉時跳過執行', async () => {
      // Arrange
      settingsService.getArchivingSettings.mockResolvedValue({
        ...DEFAULT_ARCHIVING_SETTINGS,
        enabled: false,
      });

      // Act
      await service.handleScheduledArchiving();

      // Assert
      expect(settingsService.updateArchivingStatus).not.toHaveBeenCalled();
    });
  });
});
