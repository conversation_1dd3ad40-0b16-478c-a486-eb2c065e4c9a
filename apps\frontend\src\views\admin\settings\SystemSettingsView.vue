<script setup lang="ts">
import type { ViewProps } from '@/types/components/view.type';
import SettingsSidebar from '@/components/admin/settings/SettingsSidebar.vue';
import SettingsContent from '@/components/admin/settings/SettingsContent.vue';
import { Drawer, DrawerContent, DrawerTrigger, DrawerClose } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Menu, X, Settings } from 'lucide-vue-next';
import { ref, onMounted } from 'vue';
import { SidebarProvider } from '@/components/ui/sidebar';

defineOptions({
  name: 'SystemSettingsView',
});

withDefaults(defineProps<ViewProps>(), {
  title: '系統設定',
  description: '管理系統設定與偏好',
});

const isMobile = ref(false);
const drawerOpen = ref(false);

onMounted(() => {
  // 使用媒體查詢判斷是否是移動設備
  const mediaQuery = window.matchMedia('(max-width: 768px)');
  isMobile.value = mediaQuery.matches;

  // 設置監聽器以處理視窗大小變化
  const handleResize = (e: MediaQueryListEvent) => {
    isMobile.value = e.matches;
    if (!e.matches) {
      drawerOpen.value = false;
    }
  };

  mediaQuery.addEventListener('change', handleResize);

  // 在組件卸載時移除監聽器
  return () => {
    mediaQuery.removeEventListener('change', handleResize);
  };
});
</script>

<template>
  <div class="w-full">
    <div class="h-full flex flex-col">
      <!-- 移動端視圖 -->
      <div v-if="isMobile" class="md:hidden flex flex-col h-full">
        <div class="flex p-4 items-center border-b space-x-3 flex-shrink-0">
          <div class="p-2 bg-primary/10 rounded-md">
            <Settings class="w-4 h-4 text-primary" />
          </div>
          <div>
            <h1 class="text-xl font-semibold text-gray-900 dark:text-zinc-100">系統設定</h1>
            <p class="text-sm text-gray-600 dark:text-zinc-400 mt-0.5">管理系統設定與偏好</p>
          </div>
          <div class="ml-auto">
            <Drawer v-model:open="drawerOpen">
              <DrawerTrigger asChild>
                <Button variant="outline" size="icon">
                  <Menu class="h-4 w-4" />
                </Button>
              </DrawerTrigger>
              <DrawerContent class="p-0 rounded-t-3xl max-h-[90vh] h-[90vh] flex flex-col">
                <div class="p-4 flex items-center justify-between border-b">
                  <h2 class="text-lg font-semibold">系統設定選單</h2>
                  <DrawerClose asChild>
                    <Button variant="ghost" size="icon">
                      <X class="h-4 w-4" />
                    </Button>
                  </DrawerClose>
                </div>
                <div class="flex-grow overflow-y-auto">
                  <SettingsSidebar class="w-full border-r-0" />
                </div>
              </DrawerContent>
            </Drawer>
          </div>
        </div>
        <div class="flex-1 px-2 py-2.5 overflow-y-auto">
          <SettingsContent />
        </div>
      </div>

      <!-- 桌面端視圖 -->
      <div v-else class="hidden md:flex w-full">
        <!-- 側邊欄 -->
        <div class="fixed top-16 h-[calc(100vh-4rem)] w-48 border-r bg-secondary/20 z-10">
          <SettingsSidebar />
        </div>
        <!-- 內容區域 -->
        <div class="ml-48 flex-1 min-h-[calc(100vh-4rem)] overflow-y-auto">
          <div class="py-3 px-3 sm:px-4 md:px-5 w-full">
            <SettingsContent />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
