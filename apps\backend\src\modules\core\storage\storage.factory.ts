import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IStorageService, StorageConfig } from './interfaces/storage.interface';
import { LocalStorageService } from './implementations/local-storage.service';
import { S3StorageService } from './implementations/s3-storage.service';
import { GcsStorageService } from './implementations/gcs-storage.service';

/**
 * 儲存工廠類別
 * 負責根據配置建立適當的儲存服務實例
 */
@Injectable()
export class StorageFactory {
  private readonly logger = new Logger(StorageFactory.name);
  private readonly storageInstances = new Map<string, IStorageService>();

  constructor(private configService: ConfigService) {}

  /**
   * 建立儲存服務實例
   * @param config 儲存配置
   * @returns 儲存服務實例
   */
  createStorageService(config: StorageConfig): IStorageService {
    const cacheKey = this.generateCacheKey(config);

    // 檢查是否已有緩存的實例
    if (this.storageInstances.has(cacheKey)) {
      return this.storageInstances.get(cacheKey)!;
    }

    let storageService: IStorageService;

    switch (config.provider) {
      case 'local':
        storageService = new LocalStorageService(config);
        break;

      case 's3':
        storageService = new S3StorageService(config);
        break;

      case 'azure':
        // TODO: 實作 Azure Blob Storage
        throw new Error('Azure Blob Storage not implemented yet');

      case 'gcs':
        storageService = new GcsStorageService(config);
        break;

      default:
        throw new Error(`Unsupported storage provider: ${config.provider}`);
    }

    // 緩存實例
    this.storageInstances.set(cacheKey, storageService);
    this.logger.log(`Created storage service for provider: ${config.provider}`);

    return storageService;
  }

  /**
   * 根據設定建立預設儲存服務
   * @returns 預設儲存服務實例
   */
  createDefaultStorageService(): IStorageService {
    const config = this.getDefaultStorageConfig();
    return this.createStorageService(config);
  }

  /**
   * 為歸檔建立儲存服務
   * @param archivingConfig 歸檔配置
   * @returns 歸檔用儲存服務實例
   */
  createArchivingStorageService(archivingConfig: {
    storageProvider: 'local' | 's3' | 'azure' | 'gcs';
    storagePath: string;
    [key: string]: any;
  }): IStorageService {
    const config: StorageConfig = {
      provider: archivingConfig.storageProvider,
      basePath: archivingConfig.storagePath,
      ...this.getProviderCredentials(archivingConfig.storageProvider),
    };

    return this.createStorageService(config);
  }

  /**
   * 測試儲存服務連接
   * @param config 儲存配置
   * @returns 測試結果
   */
  async testStorageConnection(config: StorageConfig): Promise<boolean> {
    try {
      const storageService = this.createStorageService(config);
      return await storageService.testConnection();
    } catch (error) {
      this.logger.error('Storage connection test failed', error);
      return false;
    }
  }

  /**
   * 清除緩存的儲存服務實例
   * @param provider 儲存提供商（可選，不提供則清除所有）
   */
  clearCache(provider?: string): void {
    if (provider) {
      const keysToDelete = Array.from(this.storageInstances.keys()).filter((key) =>
        key.includes(provider),
      );

      keysToDelete.forEach((key) => {
        this.storageInstances.delete(key);
      });

      this.logger.log(`Cleared cache for provider: ${provider}`);
    } else {
      this.storageInstances.clear();
      this.logger.log('Cleared all storage service cache');
    }
  }

  /**
   * 獲取支援的儲存提供商清單
   * @returns 支援的提供商清單
   */
  getSupportedProviders(): string[] {
    return ['local', 's3', 'azure', 'gcs'];
  }

  /**
   * 檢查提供商是否支援
   * @param provider 儲存提供商
   * @returns 是否支援
   */
  isProviderSupported(provider: string): boolean {
    return this.getSupportedProviders().includes(provider);
  }

  /**
   * 生成緩存鍵
   * @param config 儲存配置
   * @returns 緩存鍵
   */
  private generateCacheKey(config: StorageConfig): string {
    const keyParts = [
      config.provider,
      config.region || 'default',
      config.bucket || config.basePath || 'default',
    ];

    return keyParts.join(':');
  }

  /**
   * 獲取預設儲存配置
   * @returns 預設儲存配置
   */
  private getDefaultStorageConfig(): StorageConfig {
    const provider = this.configService.get<string>('STORAGE_PROVIDER', 'local') as
      | 'local'
      | 's3'
      | 'azure'
      | 'gcs';

    const config: StorageConfig = {
      provider,
      ...this.getProviderCredentials(provider),
    };

    // 根據提供商設定特定配置
    switch (provider) {
      case 'local':
        config.basePath = this.configService.get<string>('UPLOAD_DIR', 'uploads');
        break;

      case 's3':
        config.bucket = this.configService.get<string>('AWS_S3_BUCKET');
        config.region = this.configService.get<string>('AWS_REGION', 'us-east-1');
        config.endpoint = this.configService.get<string>('AWS_S3_ENDPOINT');
        break;

      case 'azure':
        // TODO: Azure 配置
        break;

      case 'gcs':
        config.bucket = this.configService.get<string>('GCS_BUCKET');
        config.region = this.configService.get<string>('GCS_PROJECT_ID'); // GCS 使用 project ID
        break;
    }

    return config;
  }

  /**
   * 獲取提供商認證資訊
   * @param provider 儲存提供商
   * @returns 認證資訊
   */
  private getProviderCredentials(provider: string): Partial<StorageConfig> {
    switch (provider) {
      case 's3':
        return {
          accessKey: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
          secretKey: this.configService.get<string>('AWS_SECRET_ACCESS_KEY'),
        };

      case 'azure':
        // TODO: Azure 認證
        return {};

      case 'gcs':
        return {
          accessKey: this.configService.get<string>('GCS_KEY_FILE'), // 服務帳號金鑰檔案路徑
          secretKey: this.configService.get<string>('GCS_CREDENTIALS'), // JSON 格式的服務帳號金鑰
        };

      default:
        return {};
    }
  }
}
