import { Tool } from 'langchain/tools';
import { JwtUser } from '@/types/jwt-user.type';
import { AppAbility } from '@/types/models/casl.model';

/**
 * 工具執行上下文
 */
export interface ToolExecutionContext {
  /** 租戶 ID */
  tenantId: string;
  /** 用戶 ID */
  userId: string;
  /** 工作空間 ID（可選） */
  workspaceId?: string;
  /** 專案根目錄 */
  workspaceRoot: string;
  /** 用戶權限 */
  ability: AppAbility;
  /** 完整的用戶資訊 */
  user: JwtUser;
  /** 輸入參數 */
  input?: any;
}

/**
 * 工具執行結果
 */
export interface ToolExecutionResult {
  /** 執行是否成功 */
  success: boolean;
  /** 執行結果數據 */
  data?: any;
  /** 錯誤訊息（當 success 為 false 時） */
  error?: string;
  /** 執行元數據 */
  metadata?: {
    executedAt?: Date;
    tenantId?: string;
    userId?: string;
    operation?: string;
    [key: string]: any;
  };
}

/**
 * 工具配置參數
 */
export interface ToolConfig {
  /** 工具名稱 */
  name: string;
  /** 工具顯示名稱 */
  displayName?: string;
  /** 工具描述 */
  description?: string;
  /** 工具分類 */
  category?: string;
  /** 工具標籤 */
  tags?: string[];
  /** 輸入參數 Schema */
  inputSchema?: any;
  /** 輸出參數 Schema */
  outputSchema?: any;
  /** 工具版本 */
  version?: string;
  /** 工具作者 */
  author?: string;
  /** 是否啟用 */
  isEnabled?: boolean;
  /** 必要權限 */
  requiredPermissions?: string[];
  /** 工具特定配置 */
  config?: Record<string, any>;
  /** 工具 ID（向後兼容） */
  id?: string;
  /** 工具標識符（向後兼容） */
  key?: string;
}

/**
 * 工具實作介面
 */
export interface ToolImplementation {
  /** 工具標識符 */
  readonly key: string;
  /** 工具名稱 */
  readonly name: string;
  /** 工具描述 */
  readonly description: string;
  /** 建立 LangChain Tool 實例 */
  createTool(context: ToolExecutionContext, config: ToolConfig): Promise<Tool>;
  /** 驗證工具配置 */
  validateConfig?(config: ToolConfig): Promise<boolean>;
  /** 檢查工具權限 */
  checkPermissions?(context: ToolExecutionContext): Promise<boolean>;
}

/**
 * 工具註冊表介面
 */
export interface ToolRegistry {
  /** 註冊工具實作 */
  register(implementation: ToolImplementation): void;
  /** 取消註冊工具 */
  unregister(key: string): void;
  /** 取得工具實作 */
  get(key: string): ToolImplementation | undefined;
  /** 取得所有已註冊的工具 */
  getAll(): Map<string, ToolImplementation>;
  /** 檢查工具是否已註冊 */
  has(key: string): boolean;
}

/**
 * 工具執行器介面
 */
export interface ToolExecutor {
  /** 載入並建立工具實例 */
  loadTool(toolConfig: ToolConfig, context: ToolExecutionContext): Promise<Tool>;
  /** 載入多個工具 */
  loadTools(toolConfigs: ToolConfig[], context: ToolExecutionContext): Promise<Tool[]>;
  /** 驗證工具配置 */
  validateToolConfig(toolConfig: ToolConfig): Promise<boolean>;
  /** 檢查工具執行權限 */
  checkToolPermissions(toolKey: string, context: ToolExecutionContext): Promise<boolean>;
}
