import { Prisma } from '@prisma/client';

/**
 * 實際從資料庫中獲取的 Permission 的結構
 */
export interface DbPermission {
  id: string;
  action: string;
  subject: string; // This will be a string like 'User', 'Role', 'Project', etc.
  conditions: Prisma.JsonValue | null;
  fields?: string[];
  description: string | null;
  deprecated: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * 系統用戶角色關聯查詢結果類型
 */
export interface SystemUserRoleWithRole {
  id: string;
  system_user_id: string;
  role_id: string;
  created_at: Date;
  updated_at: Date;
  role: {
    id: string;
    name: string;
    display_name: string;
    description: string | null;
    scope: string;
    is_system: boolean;
    tenant_id: string | null;
    created_at: Date;
    updated_at: Date;
  };
}

/**
 * 租戶用戶角色關聯查詢結果類型
 */
export interface TenantUserRoleWithRole {
  id: string;
  tenant_user_id: string;
  role_id: string;
  created_at: Date;
  updated_at: Date;
  role: {
    id: string;
    name: string;
    display_name: string;
    description: string | null;
    scope: string;
    is_system: boolean;
    tenant_id: string | null;
    created_at: Date;
    updated_at: Date;
  };
}

/**
 * 使用者類型枚舉
 */
export enum UserType {
  SYSTEM = 'system',
  TENANT = 'tenant',
}

/**
 * 建立用戶能力的參數類型
 */
export interface CreateUserAbilityParams {
  user_id: string; // user_id from JWT
  user_type: 'system' | 'tenant'; // user_type from JWT
  tenant_id?: string | null; // Primary tenant_id from JWT, if available and relevant for global context
  // Any other relevant user context from JWT can be added here
}

// 常量定義
export const SUPER_ADMIN_ROLE_NAME = 'SUPER_ADMIN';
