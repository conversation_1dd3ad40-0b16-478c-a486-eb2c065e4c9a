---
mode: "agent"
description: "協助產生新的 Instructions (Prompt) 檔案"
---

# 新 Instructions (Prompt) 檔案產生器

我需要為一個特定的開發任務建立一個新的 Instructions (Prompt) 檔案 (`.prompt.md`)。

**新 Prompt 檔案的目標與用途**:
`${input:promptPurpose:請詳細描述這個新 Prompt 檔案要解決的問題或自動化的任務}`

**新 Prompt 檔案的建議檔名 (不含 .prompt.md)**:
`${input:promptFileName:例如：generate-nestjs-crud-endpoints}`

**新 Prompt 檔案的模式 (mode)**:
`${input:promptMode:agent|edit|ask}`

**新 Prompt 檔案可能需要的工具 (tools)** (若有多個請用逗號分隔，例如 `codebase, terminal`):
`${input:promptTools:例如：codebase}`

**新 Prompt 檔案的內容大綱或關鍵指令**:
`${input:promptContentOutline:請條列出希望 Prompt 包含的主要問題、指令或變數}`

請根據以上資訊，為我產生一個新的 `.prompt.md` 檔案的完整內容模板。
模板應包含 YAML Front Matter (mode, tools, description) 和 Markdown 主體。
在 Markdown 主體中，請適當使用 `${input:variableName:placeholder}` 來提示使用者輸入必要的資訊。
