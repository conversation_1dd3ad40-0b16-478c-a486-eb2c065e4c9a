import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { RAGSecurityService } from './rag-security.service';
import { EmbeddingService, EmbeddingOptions } from '../llm/services/embedding.service';
import { DocumentProcessorService } from './services/document-processor.service';
import { AiKeysService } from '../models/configuration/keys/ai-keys.service';
import { Document } from 'langchain/document';
import { AiAgentProviderType } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';

interface FileUploadedEvent {
  filePath: string;
  tenantId: string;
  fileId: string;
  workspaceId?: string;
  fileName: string;
  mimeType?: string;
}

@Injectable()
export class RAGIngestionService {
  private readonly logger = new Logger(RAGIngestionService.name);

  // 預設嵌入配置
  private readonly defaultEmbeddingOptions: Omit<EmbeddingOptions, 'keyId'> = {
    providerType: AiAgentProviderType.OPENAI,
    model: 'text-embedding-3-small',
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly ragSecurityService: RAGSecurityService,
    private readonly embeddingService: EmbeddingService,
    private readonly documentProcessor: DocumentProcessorService,
    private readonly aiKeysService: AiKeysService,
  ) {}

  @OnEvent('file.uploaded')
  async handleFileUpload(event: FileUploadedEvent) {
    this.logger.log(`Processing file upload event: ${JSON.stringify(event)}`);

    try {
      await this.processAndIndexFile(event);
      this.logger.log(`Successfully indexed file: ${event.fileName}`);
    } catch (error) {
      this.logger.error(`Failed to index file: ${event.fileName}`, error);
      throw error;
    }
  }

  private async processAndIndexFile(event: FileUploadedEvent) {
    const { filePath, tenantId, fileId, workspaceId, fileName, mimeType } = event;

    // 安全驗證
    await this.ragSecurityService.validateFileUpload(filePath, tenantId, fileId, workspaceId);

    // 檢查文件是否存在
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    // 使用文件處理器處理文件
    const documents = await this.loadDocuments(filePath, fileName, mimeType);

    // 為每個文檔建立向量並存儲
    for (let i = 0; i < documents.length; i++) {
      const document = documents[i];

      // 添加元數據
      document.metadata = {
        ...document.metadata,
        tenant_id: tenantId,
        file_id: fileId,
        workspace_id: workspaceId,
        file_name: fileName,
        mime_type: mimeType,
        chunk_index: i,
        created_at: new Date().toISOString(),
      };

      // 使用真正的嵌入服務生成向量
      const embeddingResult = await this.generateEmbeddingWithDefaultKey(document.pageContent);
      const embedding = embeddingResult.embedding;

      // 存儲到資料庫
      await this.storeVectorDocument(document, embedding, tenantId, fileId, workspaceId);
    }
  }

  private async loadDocuments(
    filePath: string,
    fileName: string,
    mimeType?: string,
  ): Promise<Document[]> {
    try {
      // 使用文件處理器處理各種類型的文件
      const processedDocuments = await this.documentProcessor.processDocument(
        filePath,
        fileName,
        mimeType,
      );

      // processDocument 已經返回 Document[] 陣列，直接返回
      return processedDocuments;
    } catch (error) {
      this.logger.error(`Failed to load document: ${fileName}`, error);
      throw error;
    }
  }

  private async storeVectorDocument(
    document: Document,
    embedding: number[],
    tenantId: string,
    fileId: string,
    workspaceId?: string,
  ) {
    const documentId = uuidv4();

    try {
      // 存儲主文檔
      await this.prisma.vector_documents.create({
        data: {
          id: documentId,
          tenant_id: tenantId,
          file_id: fileId,
          workspace_id: workspaceId,
          content: document.pageContent,
          metadata: document.metadata,
          embedding: embedding, // JSON 格式存儲
        },
      });

      // 如果文檔很大，可以分塊存儲
      const chunks = this.splitTextIntoChunks(document.pageContent, 1000);

      for (let i = 0; i < chunks.length; i++) {
        const chunkEmbeddingResult = await this.generateEmbeddingWithDefaultKey(chunks[i]);
        const chunkEmbedding = chunkEmbeddingResult.embedding;

        await this.prisma.vector_chunks.create({
          data: {
            id: uuidv4(),
            document_id: documentId,
            tenant_id: tenantId,
            chunk_index: i,
            content: chunks[i],
            metadata: {
              ...document.metadata,
              chunk_size: chunks[i].length,
              total_chunks: chunks.length,
            },
            embedding: chunkEmbedding,
          },
        });
      }

      this.logger.log(`Stored document ${documentId} with ${chunks.length} chunks`);
    } catch (error) {
      this.logger.error(`Failed to store vector document: ${documentId}`, error);
      throw error;
    }
  }

  private splitTextIntoChunks(text: string, chunkSize: number = 1000): string[] {
    // 如果文本小於塊大小，直接返回
    if (text.length <= chunkSize) {
      return [text];
    }

    const chunks: string[] = [];
    const sentences = text.split(/([.!?]+)/).filter((s) => s.trim().length > 0);

    let currentChunk = '';

    for (let i = 0; i < sentences.length; i += 2) {
      const sentence = sentences[i];
      const punctuation = sentences[i + 1] || '';
      const fullSentence = sentence + punctuation;

      if (currentChunk.length + fullSentence.length > chunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = fullSentence;
      } else {
        currentChunk += (currentChunk.length > 0 ? ' ' : '') + fullSentence;
      }
    }

    if (currentChunk.trim().length > 0) {
      chunks.push(currentChunk.trim());
    }

    return chunks.length > 0 ? chunks : [text];
  }

  async searchSimilarDocuments(
    query: string,
    tenantId: string,
    workspaceId?: string,
    limit: number = 10,
    similarityThreshold: number = 0.0,
  ) {
    // 安全驗證
    this.ragSecurityService.validateSearchQuery(query, tenantId);

    try {
      const queryEmbeddingResult = await this.generateEmbeddingWithDefaultKey(query);
      const queryEmbedding = queryEmbeddingResult.embedding;

      // 這裡需要實現向量相似度搜索
      // 由於我們使用 JSON 存儲，需要自定義相似度計算
      const documents = await this.prisma.vector_documents.findMany({
        where: {
          tenant_id: tenantId,
          workspace_id: workspaceId,
        },
        take: limit * 2, // 取更多資料用於過濾
      });

      // 計算相似度並排序
      const results = documents.map((doc) => ({
        ...doc,
        similarity: this.calculateCosineSimilarity(queryEmbedding, doc.embedding as number[]),
      }));

      return results
        .filter((result) => result.similarity >= similarityThreshold) // 應用相似度閾值過濾
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);
    } catch (error) {
      this.logger.error('Failed to search similar documents', error);
      throw error;
    }
  }

  /**
   * 批量生成嵌入
   */
  async generateBatchEmbeddings(texts: string[], keyId: string): Promise<number[][]> {
    try {
      const options: EmbeddingOptions = {
        ...this.defaultEmbeddingOptions,
        keyId,
      };

      const result = await this.embeddingService.generateBatchEmbeddings(texts, options);
      return result.embeddings;
    } catch (error) {
      this.logger.error('Failed to generate batch embeddings', error);
      throw error;
    }
  }

  /**
   * 重新索引文件（用於更新嵌入模型時）
   */
  async reindexDocument(documentId: string, tenantId: string): Promise<void> {
    try {
      const document = await this.prisma.vector_documents.findFirst({
        where: {
          id: documentId,
          tenant_id: tenantId,
        },
      });

      if (!document) {
        throw new Error(`Document not found: ${documentId}`);
      }

      // 重新生成嵌入
      const newEmbeddingResult = await this.generateEmbeddingWithDefaultKey(document.content);
      const newEmbedding = newEmbeddingResult.embedding;

      // 更新文檔嵌入
      await this.prisma.vector_documents.update({
        where: { id: documentId },
        data: { embedding: newEmbedding },
      });

      // 重新生成塊嵌入
      const chunks = await this.prisma.vector_chunks.findMany({
        where: { document_id: documentId },
      });

      for (const chunk of chunks) {
        const chunkEmbeddingResult = await this.generateEmbeddingWithDefaultKey(chunk.content);
        const chunkEmbedding = chunkEmbeddingResult.embedding;

        await this.prisma.vector_chunks.update({
          where: { id: chunk.id },
          data: { embedding: chunkEmbedding },
        });
      }

      this.logger.log(`Successfully reindexed document: ${documentId}`);
    } catch (error) {
      this.logger.error(`Failed to reindex document: ${documentId}`, error);
      throw error;
    }
  }

  /**
   * 使用適當的金鑰生成嵌入
   * 實作完整的金鑰管理邏輯
   */
  private async generateEmbeddingWithDefaultKey(text: string) {
    try {
      // 從資料庫獲取可用的 OpenAI 金鑰
      const availableKey = await this.findAvailableOpenAIKey();

      if (!availableKey) {
        this.logger.error('No available OpenAI key found for embedding generation');
        throw new Error('No available API key for embedding generation');
      }

      const options: EmbeddingOptions = {
        ...this.defaultEmbeddingOptions,
        keyId: availableKey.id,
      };

      return await this.embeddingService.generateEmbedding(text, options);
    } catch (error) {
      this.logger.error('Failed to generate embedding with available key:', error);
      throw error;
    }
  }

  /**
   * 獲取用於批量操作的金鑰ID
   */
  async getEmbeddingKeyId(): Promise<string> {
    const availableKey = await this.findAvailableOpenAIKey();

    if (!availableKey) {
      throw new Error('No available API key for embedding generation');
    }

    return availableKey.id;
  }

  /**
   * 查找可用的 OpenAI 金鑰
   */
  private async findAvailableOpenAIKey() {
    try {
      // 查找所有啟用的 OpenAI 金鑰
      const keys = await this.prisma.ai_keys.findMany({
        where: {
          provider: 'openai',
          is_enabled: true,
        },
        orderBy: {
          created_at: 'asc', // 優先使用較早建立的金鑰
        },
      });

      if (keys.length === 0) {
        return null;
      }

      // 返回第一個可用的金鑰
      return keys[0];
    } catch (error) {
      this.logger.error('Failed to find available OpenAI key:', error);
      return null;
    }
  }

  private calculateCosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;

    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
    const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
    const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));

    return dotProduct / (magnitudeA * magnitudeB);
  }
}
