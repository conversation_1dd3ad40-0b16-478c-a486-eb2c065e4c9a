import * as fs from "fs";
import * as path from "path";
import { ScanResult, SyncResult, PermissionSyncReport, PermissionDefinition } from "./types";
import { PERMISSION_CATEGORY_LABELS } from "../../src/common/constants/i18n.constants";

/**
 * 權限報告產生器
 * 負責產生同步報告和統計資訊
 */
export class PermissionReporter {
  private reportDir: string;

  constructor(reportDir?: string) {
    this.reportDir = reportDir || path.join(__dirname, "../../reports");
  }

  /**
   * 產生完整的權限同步報告
   */
  async generateReport(
    scanResult: ScanResult,
    syncResult?: SyncResult,
    mode: 'dry-run' | 'sync' = 'sync'
  ): Promise<PermissionSyncReport> {
    console.log("\n📊 產生權限報告...");

    const report: PermissionSyncReport = {
      timestamp: new Date().toISOString(),
      version: this.getVersion(),
      mode,
      scanResult,
      syncResult,
      summary: this.generateSummary(scanResult.permissions),
      permissions: this.sortPermissions(scanResult.permissions)
    };

    // 寫入報告檔案
    await this.writeReportFile(report);

    // 輸出控制台摘要
    this.printConsoleSummary(report);

    console.log(`  📄 報告已產生: ${this.getReportPath()}`);
    
    return report;
  }

  /**
   * 產生掃描報告
   */
  async generateScanReport(scanResult: ScanResult): Promise<void> {
    console.log("\n📊 產生掃描報告...");

    const report = {
      timestamp: new Date().toISOString(),
      type: 'scan-only',
      scanResult,
      summary: this.generateSummary(scanResult.permissions),
      permissions: this.sortPermissions(scanResult.permissions)
    };

    // 寫入掃描報告檔案
    const scanReportPath = path.join(this.reportDir, "permission-scan-report.json");
    await this.ensureReportDir();
    fs.writeFileSync(scanReportPath, JSON.stringify(report, null, 2));

    // 輸出掃描摘要
    this.printScanSummary(scanResult);

    console.log(`  📄 掃描報告已產生: ${scanReportPath}`);
  }

  /**
   * 產生統計摘要
   */
  private generateSummary(permissions: PermissionDefinition[]) {
    const summary = {
      total: permissions.length,
      byScope: {} as Record<string, number>,
      byCategory: {} as Record<string, number>,
      bySubject: {} as Record<string, number>
    };

    // 統計分析
    for (const perm of permissions) {
      // 按範圍統計
      const scope = perm.scope || "GLOBAL";
      summary.byScope[scope] = (summary.byScope[scope] || 0) + 1;

      // 按分類統計
      const category = perm.category || "other";
      summary.byCategory[category] = (summary.byCategory[category] || 0) + 1;

      // 按主體統計
      summary.bySubject[perm.subject] = (summary.bySubject[perm.subject] || 0) + 1;
    }

    return summary;
  }

  /**
   * 排序權限列表
   */
  private sortPermissions(permissions: PermissionDefinition[]): PermissionDefinition[] {
    return permissions.sort((a, b) => {
      // 先按分類反向排序
      const categoryA = a.category || "other";
      const categoryB = b.category || "other";
      if (categoryA !== categoryB) {
        return categoryB.localeCompare(categoryA);
      }

      // 再按主體排序
      if (a.subject !== b.subject) {
        return a.subject.localeCompare(b.subject);
      }

      // 最後按動作排序
      return a.action.localeCompare(b.action);
    });
  }

  /**
   * 寫入報告檔案
   */
  private async writeReportFile(report: PermissionSyncReport): Promise<void> {
    await this.ensureReportDir();
    
    const reportPath = this.getReportPath();
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    // 同時產生簡化版本的報告（用於快速查看）
    const simplifiedReport = {
      timestamp: report.timestamp,
      mode: report.mode,
      stats: {
        scan: report.scanResult.stats,
        sync: report.syncResult ? {
          created: report.syncResult.created,
          updated: report.syncResult.updated,
          deprecated: report.syncResult.deprecated,
          errors: report.syncResult.errors
        } : undefined
      },
      summary: report.summary,
      errors: [
        ...report.scanResult.errors,
        ...(report.syncResult?.details.errorMessages || [])
      ]
    };

    const simplifiedPath = path.join(this.reportDir, "permission-sync-summary.json");
    fs.writeFileSync(simplifiedPath, JSON.stringify(simplifiedReport, null, 2));
  }

  /**
   * 輸出控制台摘要
   */
  private printConsoleSummary(report: PermissionSyncReport): void {
    console.log("\n📈 權限摘要:");

    // 掃描統計
    console.log("  掃描統計:");
    console.log(`    總檔案數: ${report.scanResult.stats.totalFiles}`);
    console.log(`    已掃描: ${report.scanResult.stats.scannedFiles}`);
    console.log(`    含權限: ${report.scanResult.stats.filesWithPermissions}`);
    console.log(`    總權限: ${report.scanResult.stats.totalPermissions}`);

    // 同步統計
    if (report.syncResult) {
      console.log("  同步統計:");
      console.log(`    新增: ${report.syncResult.created}`);
      console.log(`    更新: ${report.syncResult.updated}`);
      console.log(`    廢棄: ${report.syncResult.deprecated}`);
      console.log(`    錯誤: ${report.syncResult.errors}`);
    }

    // 按範圍統計
    console.log("  按範圍統計:");
    const scopeLabels = {
      SYSTEM: "系統",
      TENANT: "租戶",
      WORKSPACE: "工作區",
      GLOBAL: "全域"
    };

    for (const [scope, count] of Object.entries(report.summary.byScope)) {
      const label = scopeLabels[scope] || scope;
      console.log(`    ${label}: ${count}`);
    }

    // 按分類統計
    console.log("  按分類統計:");
    const categoryNameMap = PERMISSION_CATEGORY_LABELS;

    for (const [category, count] of Object.entries(report.summary.byCategory)) {
      const label = categoryNameMap[category] || category;
      console.log(`    ${label}: ${count}`);
    }

    // 最常見的主體
    console.log("  最常見的主體:");
    const topSubjects = Object.entries(report.summary.bySubject)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);

    for (const [subject, count] of topSubjects) {
      console.log(`    ${subject}: ${count}`);
    }

    // 錯誤報告
    if (
      report.scanResult.errors.length > 0 ||
      (report.syncResult?.errors || 0) > 0
    ) {
      console.log("\n⚠️  錯誤報告:");

      if (report.scanResult.errors.length > 0) {
        console.log("  掃描錯誤:");
        report.scanResult.errors.forEach((error) => {
          console.log(`    • ${error}`);
        });
      }

      if (
        report.syncResult?.details?.errorMessages &&
        report.syncResult.details.errorMessages.length > 0
      ) {
        console.log("  同步錯誤:");
        report.syncResult.details.errorMessages.forEach((error) => {
          console.log(`    • ${error}`);
        });
      }
    }

  }

  /**
   * 輸出掃描摘要
   */
  private printScanSummary(scanResult: ScanResult): void {
    console.log("\n📈 掃描摘要:");
    console.log(`  總檔案數: ${scanResult.stats.totalFiles}`);
    console.log(`  已掃描檔案: ${scanResult.stats.scannedFiles}`);
    console.log(`  含權限檔案: ${scanResult.stats.filesWithPermissions}`);
    console.log(`  發現權限: ${scanResult.permissions.length}`);

    if (scanResult.errors.length > 0) {
      console.log("\n⚠️  掃描錯誤:");
      scanResult.errors.forEach(error => {
        console.log(`    • ${error}`);
      });
    }
  }

  /**
   * 產生 Markdown 格式報告
   */
  async generateMarkdownReport(report: PermissionSyncReport): Promise<string> {
    const markdownPath = path.join(this.reportDir, "permission-sync-report.md");
    await this.ensureReportDir();

    const lines: string[] = [];
    lines.push(`# 權限同步報告\n`);
    lines.push(`- **產生時間**: ${report.timestamp}`);
    lines.push(`- **版本**: ${report.version}`);
    lines.push(`- **模式**: ${report.mode}\n`);

    lines.push(`## 統計摘要`);
    lines.push(`- 總權限數: ${report.summary.total}`);
    lines.push(`- 按範圍:`);
    Object.entries(report.summary.byScope).forEach(([scope, count]) => {
      lines.push(`  - ${scope}: ${count}`);
    });
    lines.push(`- 按分類:`);
    Object.entries(report.summary.byCategory).forEach(([category, count]) => {
      lines.push(`  - ${category}: ${count}`);
    });

    lines.push(`\n## 權限列表`);
    report.permissions.forEach(perm => {
      // 使用正確的反引號標記輸出權限主體與動作
      lines.push(`- \`${perm.subject}\` \`${perm.action}\` [${perm.scope}/${perm.category}]`);
    });

    const markdown = lines.join('\n');
    fs.writeFileSync(markdownPath, markdown);
    return markdownPath;
  }

  /**
   * 產生權限變更差異報告
   */
  generateDiffReport(syncResult: SyncResult): string {
    let diff = "";

    if (syncResult.details.createdPermissions.length > 0) {
      diff += "## 新增權限\n\n";
      syncResult.details.createdPermissions.forEach(permission => {
        diff += `+ ${permission}\n`;
      });
      diff += "\n";
    }

    if (syncResult.details.updatedPermissions.length > 0) {
      diff += "## 更新權限\n\n";
      syncResult.details.updatedPermissions.forEach(permission => {
        diff += `~ ${permission}\n`;
      });
      diff += "\n";
    }

    if (syncResult.details.deprecatedPermissions.length > 0) {
      diff += "## 廢棄權限\n\n";
      syncResult.details.deprecatedPermissions.forEach(permission => {
        diff += `- ${permission}\n`;
      });
      diff += "\n";
    }

    return diff;
  }

  /**
   * 確保報告目錄存在
   */
  private async ensureReportDir(): Promise<void> {
    if (!fs.existsSync(this.reportDir)) {
      fs.mkdirSync(this.reportDir, { recursive: true });
    }
  }

  /**
   * 取得報告檔案路徑
   */
  private getReportPath(): string {
    return path.join(this.reportDir, "permission-sync-report.json");
  }

  /**
   * 取得版本資訊
   */
  private getVersion(): string {
    try {
      const packageJsonPath = path.join(__dirname, "../../../package.json");
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
        return packageJson.version || "unknown";
      }
    } catch (error) {
      // 忽略錯誤
    }
    return "unknown";
  }
}
