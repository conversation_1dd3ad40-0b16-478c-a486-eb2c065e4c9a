import { PermissionSyncer } from "./syncer";
import { PrismaClient } from "@prisma/client";

describe("PermissionSyncer", () => {
  it("空權限定義應回傳零統計值", async () => {
    // 模擬 PrismaClient，包含所有被呼叫的方法
    const mockPrisma = {
      $transaction: jest.fn().mockImplementation(async (fn) => {
        // 使用同一個 mock prisma 執行事務
        return await fn(mockPrisma);
      }),
      system_logs: {
        create: jest.fn().mockResolvedValue({ id: "test-log-id" }),
      },
      permission_categories: {
        findUnique: jest.fn().mockResolvedValue(null),
        upsert: jest.fn().mockResolvedValue({
          id: "test-category-id",
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
        findMany: jest.fn().mockResolvedValue([]),
        update: jest.fn().mockResolvedValue({}),
      },
      permissions: {
        findFirst: jest.fn().mockResolvedValue(null),
        findMany: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: "test-permission-id" }),
        update: jest.fn().mockResolvedValue({}),
      },
    } as unknown as PrismaClient;

    const syncer = new PermissionSyncer(mockPrisma);
    const result = await syncer.sync([], { dryRun: false, force: false });

    expect(result.created).toBe(0);
    expect(result.updated).toBe(0);
    expect(result.deprecated).toBe(0);
    expect(result.errors).toBe(0);
    expect(result.details.createdPermissions).toHaveLength(0);
    expect(result.details.updatedPermissions).toHaveLength(0);
    expect(result.details.deprecatedPermissions).toHaveLength(0);
  });
});
