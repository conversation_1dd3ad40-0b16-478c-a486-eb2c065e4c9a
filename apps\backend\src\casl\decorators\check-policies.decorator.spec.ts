import { Test, TestingModule } from '@nestjs/testing';
import { Reflector } from '@nestjs/core';
import { Controller, Get } from '@nestjs/common';
import { ExecutionContext } from '@nestjs/common';
import {
  CheckPolicies,
  RequirePermissions,
  RequirePermission,
  RequireRead,
  RequireCreate,
  RequireUpdate,
  RequireDelete,
  RequireManage,
  RequireAccess,
  CHECK_POLICIES_KEY,
  REQUIRE_PERMISSIONS_KEY,
  PermissionRequirement,
} from './check-policies.decorator';
import { Actions, Subjects } from '@horizai/permissions';
import { AppAbility } from '../../types/models/casl.model';
import { EnhancedPermissionGuard } from '../guards/enhanced-permission.guard';
import { CaslAbilityFactory } from '../ability/casl-ability.factory';
import {
  PermissionCheckerService,
  BatchPermissionCheckResult,
} from '../services/permission-checker.service';

// 測試用控制器
@Controller('test')
class TestController {
  @Get('basic-check')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.USER))
  basicCheck() {
    return 'basic check';
  }

  @Get('multiple-checks')
  @CheckPolicies(
    (ability: AppAbility) => ability.can(Actions.READ, Subjects.USER),
    (ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.USER),
  )
  multipleChecks() {
    return 'multiple checks';
  }

  @Get('require-permissions')
  @RequirePermissions([
    { action: Actions.READ, subject: Subjects.USER },
    { action: Actions.UPDATE, subject: Subjects.USER, conditions: { id: '{{user_id}}' } },
  ])
  requirePermissions() {
    return 'require permissions';
  }

  @Get('require-permission')
  @RequirePermission(Actions.READ, Subjects.USER)
  requirePermission() {
    return 'require permission';
  }

  @Get('require-read')
  @RequireRead(Subjects.USER)
  requireRead() {
    return 'require read';
  }

  @Get('require-create')
  @RequireCreate(Subjects.USER)
  requireCreate() {
    return 'require create';
  }

  @Get('require-update')
  @RequireUpdate(Subjects.USER)
  requireUpdate() {
    return 'require update';
  }

  @Get('require-delete')
  @RequireDelete(Subjects.USER)
  requireDelete() {
    return 'require delete';
  }

  @Get('require-manage')
  @RequireManage(Subjects.USER)
  requireManage() {
    return 'require manage';
  }

  @Get('require-access')
  @RequireAccess(Subjects.ADMIN_PANEL)
  requireAccess() {
    return 'require access';
  }

  @Get('require-with-conditions')
  @RequireRead(Subjects.USER, { tenant_id: '{{tenant_id}}' })
  requireWithConditions() {
    return 'require with conditions';
  }
}

describe('CheckPolicies Decorators', () => {
  let reflector: Reflector;
  let testController: TestController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TestController],
    }).compile();

    reflector = module.get<Reflector>(Reflector);
    testController = module.get<TestController>(TestController);
  });

  describe('@CheckPolicies', () => {
    it('should set metadata for basic check', () => {
      const metadata = reflector.get(CHECK_POLICIES_KEY, TestController.prototype.basicCheck);

      expect(metadata).toBeDefined();
      expect(metadata).toHaveLength(1);
      expect(typeof metadata[0]).toBe('function');
    });

    it('should set metadata for multiple checks', () => {
      const metadata = reflector.get(CHECK_POLICIES_KEY, TestController.prototype.multipleChecks);

      expect(metadata).toBeDefined();
      expect(metadata).toHaveLength(2);
      expect(typeof metadata[0]).toBe('function');
      expect(typeof metadata[1]).toBe('function');
    });

    it('should return undefined for methods without @CheckPolicies', () => {
      const metadata = reflector.get(
        CHECK_POLICIES_KEY,
        TestController.prototype.requirePermissions,
      );

      expect(metadata).toBeUndefined();
    });
  });

  describe('@RequirePermissions', () => {
    it('should set metadata for require permissions', () => {
      const metadata = reflector.get(
        REQUIRE_PERMISSIONS_KEY,
        TestController.prototype.requirePermissions,
      );

      expect(metadata).toBeDefined();
      expect(metadata).toHaveLength(2);

      const [firstRequirement, secondRequirement] = metadata as PermissionRequirement[];
      expect(firstRequirement.action).toBe(Actions.READ);
      expect(firstRequirement.subject).toBe(Subjects.USER);
      expect(firstRequirement.conditions).toBeUndefined();

      expect(secondRequirement.action).toBe(Actions.UPDATE);
      expect(secondRequirement.subject).toBe(Subjects.USER);
      expect(secondRequirement.conditions).toEqual({ id: '{{user_id}}' });
    });
  });

  describe('@RequirePermission', () => {
    it('should set metadata for single permission', () => {
      const metadata = reflector.get(
        REQUIRE_PERMISSIONS_KEY,
        TestController.prototype.requirePermission,
      );

      expect(metadata).toBeDefined();
      expect(metadata).toHaveLength(1);

      const [requirement] = metadata as PermissionRequirement[];
      expect(requirement.action).toBe(Actions.READ);
      expect(requirement.subject).toBe(Subjects.USER);
    });
  });

  describe('Convenience Decorators', () => {
    it('@RequireRead should set read permission', () => {
      const metadata = reflector.get(REQUIRE_PERMISSIONS_KEY, TestController.prototype.requireRead);

      expect(metadata).toBeDefined();
      expect(metadata).toHaveLength(1);

      const [requirement] = metadata as PermissionRequirement[];
      expect(requirement.action).toBe(Actions.READ);
      expect(requirement.subject).toBe(Subjects.USER);
    });

    it('@RequireCreate should set create permission', () => {
      const metadata = reflector.get(
        REQUIRE_PERMISSIONS_KEY,
        TestController.prototype.requireCreate,
      );

      expect(metadata).toBeDefined();
      const [requirement] = metadata as PermissionRequirement[];
      expect(requirement.action).toBe(Actions.CREATE);
      expect(requirement.subject).toBe(Subjects.USER);
    });

    it('@RequireUpdate should set update permission', () => {
      const metadata = reflector.get(
        REQUIRE_PERMISSIONS_KEY,
        TestController.prototype.requireUpdate,
      );

      expect(metadata).toBeDefined();
      const [requirement] = metadata as PermissionRequirement[];
      expect(requirement.action).toBe(Actions.UPDATE);
      expect(requirement.subject).toBe(Subjects.USER);
    });

    it('@RequireDelete should set delete permission', () => {
      const metadata = reflector.get(
        REQUIRE_PERMISSIONS_KEY,
        TestController.prototype.requireDelete,
      );

      expect(metadata).toBeDefined();
      const [requirement] = metadata as PermissionRequirement[];
      expect(requirement.action).toBe(Actions.DELETE);
      expect(requirement.subject).toBe(Subjects.USER);
    });

    it('@RequireManage should set manage permission', () => {
      const metadata = reflector.get(
        REQUIRE_PERMISSIONS_KEY,
        TestController.prototype.requireManage,
      );

      expect(metadata).toBeDefined();
      const [requirement] = metadata as PermissionRequirement[];
      expect(requirement.action).toBe(Actions.MANAGE);
      expect(requirement.subject).toBe(Subjects.USER);
    });

    it('@RequireAccess should set access permission', () => {
      const metadata = reflector.get(
        REQUIRE_PERMISSIONS_KEY,
        TestController.prototype.requireAccess,
      );

      expect(metadata).toBeDefined();
      const [requirement] = metadata as PermissionRequirement[];
      expect(requirement.action).toBe(Actions.ACCESS);
      expect(requirement.subject).toBe(Subjects.ADMIN_PANEL);
    });

    it('@RequireRead with conditions should set read permission with conditions', () => {
      const metadata = reflector.get(
        REQUIRE_PERMISSIONS_KEY,
        TestController.prototype.requireWithConditions,
      );

      expect(metadata).toBeDefined();
      const [requirement] = metadata as PermissionRequirement[];
      expect(requirement.action).toBe(Actions.READ);
      expect(requirement.subject).toBe(Subjects.USER);
      expect(requirement.conditions).toEqual({ tenant_id: '{{tenant_id}}' });
    });
  });

  describe('Metadata Keys', () => {
    it('should have correct CHECK_POLICIES_KEY', () => {
      expect(CHECK_POLICIES_KEY).toBe('check_policy');
    });

    it('should have correct REQUIRE_PERMISSIONS_KEY', () => {
      expect(REQUIRE_PERMISSIONS_KEY).toBe('require_permissions');
    });
  });
});

describe('EnhancedPermissionGuard', () => {
  let guard: EnhancedPermissionGuard;
  let reflector: Reflector;
  let caslAbilityFactory: CaslAbilityFactory;

  const mockExecutionContext = {
    getHandler: jest.fn(),
    getClass: jest.fn(),
    switchToHttp: jest.fn().mockReturnValue({
      getRequest: jest.fn().mockReturnValue({
        user: {
          sub: 'user123',
          user_type: 'tenant',
          tenant_id: 'tenant123',
          workspace_id: 'workspace123',
          email: '<EMAIL>',
        },
      }),
    }),
  } as unknown as ExecutionContext;

  const mockAbility = {
    can: jest.fn().mockReturnValue(true),
    cannot: jest.fn(),
    rules: [],
  } as unknown as AppAbility;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EnhancedPermissionGuard,
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: CaslAbilityFactory,
          useValue: {
            createForUser: jest.fn().mockResolvedValue(mockAbility),
          },
        },
      ],
    }).compile();

    guard = module.get<EnhancedPermissionGuard>(EnhancedPermissionGuard);
    reflector = module.get<Reflector>(Reflector);
    caslAbilityFactory = module.get<CaslAbilityFactory>(CaslAbilityFactory);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('canActivate', () => {
    it('should allow access when no decorators are present', async () => {
      jest.spyOn(reflector, 'get').mockReturnValue(undefined);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it('should handle @CheckPolicies decorator correctly', async () => {
      const mockPolicyHandler = jest.fn().mockReturnValue(true);
      jest
        .spyOn(reflector, 'get')
        .mockReturnValueOnce([mockPolicyHandler]) // CHECK_POLICIES_KEY
        .mockReturnValueOnce(undefined); // REQUIRE_PERMISSIONS_KEY

      const result = await guard.canActivate(mockExecutionContext);

      expect(caslAbilityFactory.createForUser).toHaveBeenCalledWith({
        user_id: 'user123',
        user_type: 'tenant',
        tenant_id: 'tenant123',
      });
      expect(mockPolicyHandler).toHaveBeenCalledWith(mockAbility);
      expect(result).toBe(true);
    });

    it('should handle @RequirePermissions decorator correctly', async () => {
      const mockPermissions: PermissionRequirement[] = [
        { action: Actions.READ, subject: Subjects.USER },
        { action: Actions.UPDATE, subject: Subjects.USER, conditions: { id: '{{user_id}}' } },
      ];

      jest
        .spyOn(reflector, 'get')
        .mockReturnValueOnce(undefined) // CHECK_POLICIES_KEY
        .mockReturnValueOnce(mockPermissions); // REQUIRE_PERMISSIONS_KEY

      const result = await guard.canActivate(mockExecutionContext);

      expect(mockAbility.can).toHaveBeenCalledWith(Actions.READ, Subjects.USER);
      expect(mockAbility.can).toHaveBeenCalledWith(
        Actions.UPDATE,
        expect.objectContaining({
          __type: Subjects.USER,
          id: 'user123',
        }),
      );
      expect(result).toBe(true);
    });

    it('should handle both decorators when present (prioritize @CheckPolicies)', async () => {
      const mockPolicyHandler = jest.fn().mockReturnValue(true);
      const mockPermissions: PermissionRequirement[] = [
        { action: Actions.READ, subject: Subjects.USER },
      ];

      jest
        .spyOn(reflector, 'get')
        .mockReturnValueOnce([mockPolicyHandler]) // CHECK_POLICIES_KEY
        .mockReturnValueOnce(mockPermissions); // REQUIRE_PERMISSIONS_KEY

      const result = await guard.canActivate(mockExecutionContext);

      expect(mockPolicyHandler).toHaveBeenCalledWith(mockAbility);
      expect(mockAbility.can).toHaveBeenCalledWith(Actions.READ, Subjects.USER);
      expect(result).toBe(true);
    });

    it('should deny access when @CheckPolicies returns false', async () => {
      const mockPolicyHandler = jest.fn().mockReturnValue(false);
      jest
        .spyOn(reflector, 'get')
        .mockReturnValueOnce([mockPolicyHandler])
        .mockReturnValueOnce(undefined);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(false);
    });

    it('should deny access when @RequirePermissions check fails', async () => {
      const mockPermissions: PermissionRequirement[] = [
        { action: Actions.READ, subject: Subjects.USER },
      ];

      jest
        .spyOn(reflector, 'get')
        .mockReturnValueOnce(undefined)
        .mockReturnValueOnce(mockPermissions);

      // Mock ability.can to return false
      jest.spyOn(mockAbility, 'can').mockReturnValue(false);

      const result = await guard.canActivate(mockExecutionContext);

      expect(mockAbility.can).toHaveBeenCalledWith(Actions.READ, Subjects.USER);
      expect(result).toBe(false);
    });

    it('should handle multiple policy handlers with AND logic', async () => {
      const mockPolicyHandler1 = jest.fn().mockReturnValue(true);
      const mockPolicyHandler2 = jest.fn().mockReturnValue(false);

      jest
        .spyOn(reflector, 'get')
        .mockReturnValueOnce([mockPolicyHandler1, mockPolicyHandler2])
        .mockReturnValueOnce(undefined);

      const result = await guard.canActivate(mockExecutionContext);

      expect(mockPolicyHandler1).toHaveBeenCalledWith(mockAbility);
      expect(mockPolicyHandler2).toHaveBeenCalledWith(mockAbility);
      expect(result).toBe(false); // 任一失敗則拒絕
    });

    it('should handle errors gracefully', async () => {
      jest
        .spyOn(reflector, 'get')
        .mockReturnValueOnce(undefined)
        .mockReturnValueOnce([{ action: Actions.READ, subject: Subjects.USER }]);

      // Mock createForUser to throw an error
      jest
        .spyOn(caslAbilityFactory, 'createForUser')
        .mockRejectedValue(new Error('Ability creation failed'));

      try {
        await guard.canActivate(mockExecutionContext);
        fail('Expected ForbiddenException to be thrown');
      } catch (error) {
        expect(error.message).toBe('Failed to create permissions for user.');
      }
    });
  });
});
