import { AiAgentProviderType } from '@prisma/client';

/**
 * Represents a single message in a conversation with an AI model.
 * The content can be a simple string or a structured array for complex inputs like images.
 */
export interface AiMessage {
  role: 'system' | 'user' | 'assistant';
  content:
    | string
    | Array<{ type: 'text'; text: string } | { type: 'image_url'; image_url: { url: string } }>;
}

/**
 * Represents the structured response from an AI model.
 */
export interface AiResponse {
  content: string | null;
  usage: {
    inputTokens: number;
    outputTokens: number;
  };
}

/**
 * Defines the options for executing a request with the LlmService.
 */
export interface LlmExecuteOptions {
  providerType: AiAgentProviderType;
  model: string;
  keyId: string; // The ID of the AiKey to use for this execution.
  temperature?: number;
  maxTokens?: number;
  responseFormat?: 'text' | 'json_object';
  apiUrl?: string; // Optional: For OpenAI-compatible providers
  systemPrompt?: string; // Optional: System prompt to override the one in messages
}

/**
 * Defines the options for retrieving available models from a provider.
 */
export interface LlmGetModelsOptions {
  provider: 'openai' | 'openai-compatible' | 'anthropic' | 'google-gemini';
  apiKey: string;
  apiUrl?: string;
}

/**
 * High-level LLM Service interface that abstracts provider complexity.
 * This service is the single point of contact for all external AI model interactions.
 */
export interface ILlmService {
  /**
   * Executes a message-based completion request to the specified AI provider.
   * @param messages The array of messages for the conversation.
   * @param options The execution options, including provider, model, and key.
   * @returns A promise that resolves to an AiResponse object.
   */
  execute(messages: AiMessage[], options: LlmExecuteOptions): Promise<AiResponse>;

  /**
   * Get available models for a specific provider.
   * @param options The options for fetching models, including provider and credentials.
   * @returns A promise that resolves to a list of model ID strings.
   */
  getAvailableModels(options: LlmGetModelsOptions): Promise<string[]>;
}
