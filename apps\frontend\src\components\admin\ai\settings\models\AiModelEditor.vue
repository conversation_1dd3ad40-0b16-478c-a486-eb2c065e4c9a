<!-- AiModelEditor.vue -->
<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";
import { Info, X } from "lucide-vue-next";
import { Badge } from "@/components/ui/badge";

// 供應商清單
const MODEL_PROVIDERS = [
  { value: "openai", label: "OpenAI" },
  { value: "claude", label: "Anthropic (Claude)" },
  { value: "google-gemini", label: "Google Gemini" },
  { value: "openai-compatible", label: "OpenAI Compatible" },
];

// 將常數曝露出去給父元件使用
defineExpose({
  MODEL_PROVIDERS,
});

const props = defineProps<{
  modelData: {
    id?: string;
    provider?: string;
    model_name?: string;
    display_name?: string;
    is_enabled?: boolean;
    input_price_per_1k_tokens?: string | number;
    output_price_per_1k_tokens?: string | number;
    currency?: string;
    context_window_tokens?: string | number | null;
    notes?: string | null;
  };
  isEditing?: boolean;
  availableProviders?: { value: string; label: string }[];
}>();

const emit = defineEmits<{
  (e: "save", data: any): void;
  (e: "close"): void;
}>();

// 本地狀態
const localModelData = ref({
  id: props.modelData?.id || "",
  provider: props.modelData?.provider || "openai",
  model_name: props.modelData?.model_name || "",
  display_name: props.modelData?.display_name || "",
  is_enabled:
    props.modelData?.is_enabled !== undefined
      ? props.modelData.is_enabled
      : true,
  input_price_per_1k_tokens: props.modelData?.input_price_per_1k_tokens || "0",
  output_price_per_1k_tokens:
    props.modelData?.output_price_per_1k_tokens || "0",
  currency: props.modelData?.currency || "USD",
  context_window_tokens:
    props.modelData?.context_window_tokens !== undefined &&
    props.modelData.context_window_tokens !== null
      ? props.modelData.context_window_tokens.toString()
      : "",
  notes: props.modelData?.notes || "",
});

// 同步 props 的變化
watch(
  () => props.modelData,
  (newData) => {
    if (newData) {
      localModelData.value = {
        id: newData.id || localModelData.value.id,
        provider: newData.provider || localModelData.value.provider,
        model_name: newData.model_name || localModelData.value.model_name,
        display_name: newData.display_name || localModelData.value.display_name,
        is_enabled:
          newData.is_enabled !== undefined
            ? newData.is_enabled
            : localModelData.value.is_enabled,
        input_price_per_1k_tokens:
          newData.input_price_per_1k_tokens !== undefined
            ? newData.input_price_per_1k_tokens.toString()
            : localModelData.value.input_price_per_1k_tokens,
        output_price_per_1k_tokens:
          newData.output_price_per_1k_tokens !== undefined
            ? newData.output_price_per_1k_tokens.toString()
            : localModelData.value.output_price_per_1k_tokens,
        currency: newData.currency || localModelData.value.currency,
        context_window_tokens:
          newData.context_window_tokens !== undefined &&
          newData.context_window_tokens !== null
            ? newData.context_window_tokens.toString()
            : localModelData.value.context_window_tokens,
        notes:
          newData.notes !== undefined && newData.notes !== null
            ? newData.notes
            : localModelData.value.notes,
      };
    }
  },
  { deep: true, immediate: true }
);

// 驗證相關狀態
const errors = ref<Record<string, string>>({});

// 驗證表單
const validateForm = () => {
  const newErrors: Record<string, string> = {};

  if (!localModelData.value.model_name.trim()) {
    newErrors.model_name = "模型 ID 不能為空";
  }

  if (!localModelData.value.display_name.trim()) {
    newErrors.display_name = "顯示名稱不能為空";
  }

  // 驗證價格是數字
  const inputPrice = parseFloat(
    String(localModelData.value.input_price_per_1k_tokens)
  );
  if (isNaN(inputPrice) || inputPrice < 0) {
    newErrors.input_price_per_1k_tokens = "輸入價格必須是非負數";
  }

  const outputPrice = parseFloat(
    String(localModelData.value.output_price_per_1k_tokens)
  );
  if (isNaN(outputPrice) || outputPrice < 0) {
    newErrors.output_price_per_1k_tokens = "輸出價格必須是非負數";
  }

  // 驗證上下文視窗 tokens 是數字
  if (localModelData.value.context_window_tokens) {
    const contextTokens = parseFloat(
      String(localModelData.value.context_window_tokens)
    );
    if (isNaN(contextTokens) || contextTokens <= 0) {
      newErrors.context_window_tokens = "上下文視窗必須是正數";
    }
  }

  errors.value = newErrors;
  return Object.keys(newErrors).length === 0;
};

// 保存模型
const handleSave = () => {
  if (!validateForm()) {
    return;
  }

  // 格式化數據
  const formattedData = {
    ...localModelData.value,
    input_price_per_1k_tokens: parseFloat(
      String(localModelData.value.input_price_per_1k_tokens)
    ),
    output_price_per_1k_tokens: parseFloat(
      String(localModelData.value.output_price_per_1k_tokens)
    ),
    context_window_tokens: localModelData.value.context_window_tokens
      ? parseFloat(String(localModelData.value.context_window_tokens))
      : null,
  };

  emit("save", formattedData);
};

// 取消編輯
const handleClose = () => {
  emit("close");
};

// 計算使用的供應商列表
const providerOptions = computed(() => {
  return props.availableProviders || MODEL_PROVIDERS;
});

// 貨幣選項
const CURRENCIES = [
  { value: "USD", label: "USD ($)" },
  { value: "EUR", label: "EUR (€)" },
  { value: "GBP", label: "GBP (£)" },
  { value: "JPY", label: "JPY (¥)" },
  { value: "CNY", label: "CNY (¥)" },
  { value: "TWD", label: "TWD (NT$)" },
];
</script>

<template>
  <div class="space-y-6">
    <!-- 名稱和狀態 -->
    <div class="grid grid-cols-2 gap-6">
      <!-- 供應商 -->
      <div class="space-y-2">
        <div class="flex items-center gap-1.5">
          <Label class="text-sm font-medium">供應商</Label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-5 w-5 rounded-full p-0"
                >
                  <Info class="h-3.5 w-3.5 text-muted-foreground" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p class="max-w-xs">選擇 AI 模型的提供商</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Select v-model="localModelData.provider" :disabled="props.isEditing">
          <SelectTrigger class="w-full">
            <SelectValue placeholder="選擇供應商" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="p in providerOptions"
              :key="p.value"
              :value="p.value"
              >{{ p.label }}</SelectItem
            >
          </SelectContent>
        </Select>
      </div>

      <!-- 啟用狀態 -->
      <div class="space-y-2 flex items-center">
        <div class="flex items-center space-x-2">
          <Switch id="model-enabled" v-model="localModelData.is_enabled" />
          <Label for="model-enabled">啟用此模型</Label>
        </div>
      </div>

      <!-- 模型 ID -->
      <div class="space-y-2 col-span-2">
        <div class="flex items-center gap-1.5">
          <Label class="text-sm font-medium">模型 ID</Label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-5 w-5 rounded-full p-0"
                >
                  <Info class="h-3.5 w-3.5 text-muted-foreground" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p class="max-w-xs">模型在 API 中的識別符號，例如 "gpt-4"</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Input
          v-model="localModelData.model_name"
          placeholder="例如：gpt-4, claude-3-opus-20240229"
          :class="{ 'border-destructive': errors.model_name }"
          :disabled="props.isEditing"
        />
        <p v-if="errors.model_name" class="text-xs text-destructive mt-1">
          {{ errors.model_name }}
        </p>
      </div>

      <!-- 顯示名稱 -->
      <div class="space-y-2 col-span-2">
        <div class="flex items-center gap-1.5">
          <Label class="text-sm font-medium">顯示名稱</Label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-5 w-5 rounded-full p-0"
                >
                  <Info class="h-3.5 w-3.5 text-muted-foreground" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p class="max-w-xs">在 UI 中顯示的模型名稱</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Input
          v-model="localModelData.display_name"
          placeholder="例如：GPT-4、Claude 3 Opus"
          :class="{ 'border-destructive': errors.display_name }"
        />
        <p v-if="errors.display_name" class="text-xs text-destructive mt-1">
          {{ errors.display_name }}
        </p>
      </div>
    </div>

    <!-- 價格設定 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-sm font-medium">價格設定</h3>
        <Badge variant="outline" class="text-xs">
          <span class="text-primary">價格可自動同步</span>
        </Badge>
      </div>
      <div class="grid grid-cols-2 gap-6">
        <!-- 輸入價格 -->
        <div class="space-y-2">
          <div class="flex items-center gap-1.5">
            <Label class="text-sm font-medium">輸入價格(每千 tokens)</Label>
            <Badge variant="outline" class="text-xs py-0 px-1">自動</Badge>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-5 w-5 rounded-full p-0"
                  >
                    <Info class="h-3.5 w-3.5 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p class="max-w-xs">
                    每千 tokens
                    的輸入(提示詞)價格，可透過「自動同步價格」功能更新
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Input
            v-model="localModelData.input_price_per_1k_tokens"
            type="number"
            step="0.0001"
            :class="{ 'border-destructive': errors.input_price_per_1k_tokens }"
          />
          <p
            v-if="errors.input_price_per_1k_tokens"
            class="text-xs text-destructive mt-1"
          >
            {{ errors.input_price_per_1k_tokens }}
          </p>
        </div>

        <!-- 輸出價格 -->
        <div class="space-y-2">
          <div class="flex items-center gap-1.5">
            <Label class="text-sm font-medium">輸出價格(每千 tokens)</Label>
            <Badge variant="outline" class="text-xs py-0 px-1">自動</Badge>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-5 w-5 rounded-full p-0"
                  >
                    <Info class="h-3.5 w-3.5 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p class="max-w-xs">
                    每千 tokens 的輸出(回應)價格，可透過「自動同步價格」功能更新
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Input
            v-model="localModelData.output_price_per_1k_tokens"
            type="number"
            step="0.0001"
            :class="{ 'border-destructive': errors.output_price_per_1k_tokens }"
          />
          <p
            v-if="errors.output_price_per_1k_tokens"
            class="text-xs text-destructive mt-1"
          >
            {{ errors.output_price_per_1k_tokens }}
          </p>
        </div>

        <!-- 貨幣 -->
        <div class="space-y-2">
          <Label class="text-sm font-medium">貨幣</Label>
          <Select v-model="localModelData.currency">
            <SelectTrigger class="w-full">
              <SelectValue placeholder="選擇貨幣" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="currency in CURRENCIES"
                :key="currency.value"
                :value="currency.value"
                >{{ currency.label }}</SelectItem
              >
            </SelectContent>
          </Select>
        </div>

        <!-- 上下文視窗大小 -->
        <div class="space-y-2">
          <div class="flex items-center gap-1.5">
            <Label class="text-sm font-medium">上下文視窗 (tokens)</Label>
            <Badge variant="outline" class="text-xs py-0 px-1">自動</Badge>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-5 w-5 rounded-full p-0"
                  >
                    <Info class="h-3.5 w-3.5 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p class="max-w-xs">
                    模型支持的最大上下文視窗大小 (以 tokens
                    為單位)，可透過「自動同步價格」功能更新
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Input
            v-model="localModelData.context_window_tokens"
            type="number"
            placeholder="例如：8192, 16384, 32768"
            :class="{ 'border-destructive': errors.context_window_tokens }"
          />
          <p
            v-if="errors.context_window_tokens"
            class="text-xs text-destructive mt-1"
          >
            {{ errors.context_window_tokens }}
          </p>
        </div>
      </div>
    </div>

    <!-- 備註 -->
    <div class="space-y-2">
      <div class="flex items-center gap-1.5">
        <Label class="text-sm font-medium">備註</Label>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                class="h-5 w-5 rounded-full p-0"
              >
                <Info class="h-3.5 w-3.5 text-muted-foreground" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p class="max-w-xs">此模型的額外資訊或說明</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Textarea
        v-model="localModelData.notes"
        placeholder="此模型的特點、使用建議等..."
        rows="3"
      />
    </div>

    <!-- 按鈕 -->
    <div class="flex justify-end gap-3 pt-4">
      <Button variant="outline" @click="handleClose"> 取消 </Button>
      <Button variant="default" @click="handleSave">
        {{ props.isEditing ? "儲存變更" : "新增模型" }}
      </Button>
    </div>
  </div>
</template>
