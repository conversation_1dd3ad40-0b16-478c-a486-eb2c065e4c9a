import { Injectable, Logger } from '@nestjs/common';
import { AiErrorHandlerService } from './ai-error-handler.service';
import { AiMonitoringService } from './ai-monitoring.service';
import { LlmService } from '../../../llm/services/llm.service';
import {
  AiMessage,
  AiResponse,
  LlmExecuteOptions,
} from '../../../llm/interfaces/llm-service.interface';
import { AiAgentProviderType } from '@prisma/client';

/**
 * AI 整合服務 - 提供帶有錯誤處理和監控的統一 AI 操作接口
 */
@Injectable()
export class AiIntegrationService {
  private readonly logger = new Logger(AiIntegrationService.name);

  constructor(
    private readonly llmService: LlmService,
    private readonly errorHandler: AiErrorHandlerService,
    private readonly monitoring: AiMonitoringService,
  ) {}

  /**
   * 執行 AI 操作（帶有完整的錯誤處理和監控）
   */
  async execute(messages: AiMessage[], options: LlmExecuteOptions): Promise<AiResponse> {
    const { providerType, model, keyId } = options;
    const provider = providerType.toString();

    // 開始監控
    const operation = this.monitoring.startOperation(provider, model, 'execute');

    try {
      // 使用錯誤處理服務執行操作
      const result = await this.errorHandler.executeWithErrorHandling(
        async () => {
          return await this.llmService.execute(messages, options);
        },
        provider,
        model,
      );

      // 完成監控（成功）
      await operation.finish(
        true,
        undefined,
        result.usage
          ? {
              input: result.usage.inputTokens,
              output: result.usage.outputTokens,
            }
          : undefined,
      );

      return result;
    } catch (error) {
      // 完成監控（失敗）
      await operation.finish(false, error as Error);
      throw error;
    }
  }

  /**
   * 執行帶有後備策略的 AI 操作
   */
  async executeWithFallback(
    messages: AiMessage[],
    primaryOptions: LlmExecuteOptions,
    fallbackOptions: LlmExecuteOptions,
  ): Promise<AiResponse> {
    const primaryProvider = primaryOptions.providerType.toString();
    const fallbackProvider = fallbackOptions.providerType.toString();

    return await this.errorHandler.executeWithFallback(
      async () => {
        return await this.execute(messages, primaryOptions);
      },
      async () => {
        this.logger.warn(`使用備用方案: ${fallbackProvider}/${fallbackOptions.model}`);
        return await this.execute(messages, fallbackOptions);
      },
      primaryProvider,
      primaryOptions.model,
      {
        enabled: true,
        degradedService: true,
        fallbackProvider,
        fallbackModel: fallbackOptions.model,
      },
    );
  }

  /**
   * 批量執行 AI 操作
   */
  async executeBatch(
    requests: Array<{
      messages: AiMessage[];
      options: LlmExecuteOptions;
    }>,
    batchOptions?: {
      maxConcurrency?: number;
      failFast?: boolean;
    },
  ): Promise<Array<{ success: boolean; data?: AiResponse; error?: Error }>> {
    const operations = requests.map(
      ({ messages, options }) =>
        () =>
          this.execute(messages, options),
    );

    const provider = requests[0]?.options.providerType.toString() || 'unknown';
    const model = requests[0]?.options.model;

    return await this.errorHandler.executeBatch(operations, provider, model, batchOptions);
  }

  /**
   * 獲取可用模型（帶有錯誤處理）
   */
  async getAvailableModels(
    provider: 'openai' | 'openai-compatible' | 'anthropic' | 'google-gemini',
    apiKey: string,
    apiUrl?: string,
  ): Promise<string[]> {
    const operation = this.monitoring.startOperation(provider, undefined, 'getModels');

    try {
      const result = await this.errorHandler.executeWithErrorHandling(async () => {
        return await this.llmService.getAvailableModels({
          provider,
          apiKey,
          apiUrl,
        });
      }, provider);

      await operation.finish(true);
      return result;
    } catch (error) {
      await operation.finish(false, error as Error);
      throw error;
    }
  }

  /**
   * 獲取健康狀態
   */
  async getHealthStatus(): Promise<{
    overall: any;
    services: any[];
    errorHandler: any;
    monitoring: any;
  }> {
    const monitoringHealth = await this.monitoring.getHealthStatus();
    const errorHandlerHealth = this.errorHandler.getHealthReport();

    return {
      overall: monitoringHealth.overall,
      services: monitoringHealth.services,
      errorHandler: errorHandlerHealth,
      monitoring: {
        activeAlerts: monitoringHealth.activeAlerts,
        circuitBreakers: monitoringHealth.circuitBreakers,
        benchmarks: monitoringHealth.benchmarks,
      },
    };
  }

  /**
   * 手動重置斷路器
   */
  resetCircuitBreaker(provider: string, model?: string): boolean {
    return this.errorHandler.resetCircuitBreaker(provider, model);
  }

  /**
   * 確認警報
   */
  acknowledgeAlert(alertId: string): boolean {
    return this.monitoring.acknowledgeAlert(alertId);
  }

  /**
   * 解決警報
   */
  resolveAlert(alertId: string): boolean {
    return this.monitoring.resolveAlert(alertId);
  }

  /**
   * 獲取詳細報告
   */
  async getDetailedReport(timeWindowMinutes = 60): Promise<any> {
    return await this.monitoring.getDetailedReport(timeWindowMinutes);
  }

  /**
   * 導出指標數據
   */
  exportMetrics(timeWindowMinutes = 60): any[] {
    return this.monitoring.exportMetrics(timeWindowMinutes);
  }

  /**
   * 設定並發限制
   */
  setConcurrencyLimit(provider: string, limit: number): void {
    this.errorHandler.setConcurrencyLimit(provider, limit);
  }

  /**
   * 獲取斷路器狀態
   */
  getCircuitBreakerStatus(provider: string, model?: string): any {
    return this.errorHandler.getCircuitBreakerStatus(provider, model);
  }

  /**
   * 清理資源
   */
  async cleanup(): Promise<void> {
    await this.errorHandler.cleanup();
    this.logger.log('AI 整合服務已清理');
  }
}
