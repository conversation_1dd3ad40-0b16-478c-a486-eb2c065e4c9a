// 遷移模組主要入口點
export { MigrationManager } from "./migration-manager";

// 便利函式
import { MigrationManager } from "./migration-manager";

/**
 * 快速執行用戶資料表分離遷移
 */
export async function migrateUsersSeparation(
  options: { dryRun?: boolean; force?: boolean } = {}
) {
  const manager = new MigrationManager({
    dryRun: options.dryRun || false,
    force: options.force || false,
    backupBeforeMigration: true,
    validateAfterMigration: true,
  });

  await manager.executeMigration(["users-separation"]);
}

/**
 * 快速執行權限同步
 */
export async function syncPermissions(
  options: { dryRun?: boolean; force?: boolean } = {}
) {
  const manager = new MigrationManager({
    dryRun: options.dryRun || false,
    force: options.force || false,
    backupBeforeMigration: false,
    validateAfterMigration: true,
  });

  await manager.executeMigration(["permissions-sync"]);
}

/**
 * 執行完整遷移（Schema + 權限同步）
 * 注意：用戶分離在初始化時已完成，無需再次執行
 */
export async function fullMigration(
  options: {
    dryRun?: boolean;
    force?: boolean;
    environment?: "development" | "staging" | "production";
    skipBackup?: boolean;
    includeUsersSeparation?: boolean; // 允許手動包含用戶分離（如果需要）
  } = {}
) {
  const manager = new MigrationManager({
    dryRun: options.dryRun || false,
    force: options.force || false,
    environment: options.environment || "development",
    backupBeforeMigration: options.skipBackup
      ? false
      : options.environment !== "development",
    validateAfterMigration: true,
  });

  const steps = ["schema-update", "permissions-sync"];

  // 只有在明確要求時才包含用戶分離步驟
  if (options.includeUsersSeparation) {
    steps.splice(1, 0, "users-separation"); // 在 schema-update 之後插入
  }

  await manager.executeMigration(steps);
}

/**
 * 檢查資料庫狀態
 */
export async function checkDatabaseStatus() {
  const manager = new MigrationManager();
  await manager.getStatus();
}

/**
 * 列出所有可用的遷移步驟
 */
export async function listMigrationSteps() {
  const manager = new MigrationManager();
  await manager.listMigrationSteps();
}
