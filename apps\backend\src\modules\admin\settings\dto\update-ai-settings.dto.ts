import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsNumber, MinLength, Matches, IsOptional } from 'class-validator';

export class UpdateAISettingsDto {
  @ApiProperty({
    description: 'Provider 金鑰設定',
    required: true,
    type: Object,
    additionalProperties: true,
  })
  providerKeys: Record<string, any>;

  @ApiProperty({ description: '每月使用額度' })
  @IsNumber()
  monthlyQuota: number;

  @ApiProperty({ description: '是否啟用 AI 功能' })
  @IsBoolean()
  enabled: boolean;
}
