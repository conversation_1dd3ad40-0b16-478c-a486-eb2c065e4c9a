<template>
  <div class="space-y-6">
    <!-- 同步按鈕 -->
    <div class="flex justify-end">
      <Button
        variant="outline"
        size="sm"
        @click="handleSyncFeatureDefinitions"
        :disabled="isSyncing"
      >
        <RefreshCw :class="{ 'animate-spin': isSyncing }" class="h-4 w-4 mr-2" />
        {{ isSyncing ? '同步中...' : '同步功能定義' }}
      </Button>
    </div>

    <!-- 功能列表 -->
    <div class="space-y-4">
      <div
        v-for="featureDef in props.systemFeatureDefinitions"
        :key="featureDef.id"
        class="group"
      >
        <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm hover:shadow-md transition-shadow duration-200">
          <!-- 功能標題區域 -->
          <div class="p-4 border-b border-gray-200 dark:border-zinc-700">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div :class="[
                  'p-2 rounded-md',
                  isFeatureEnabled(featureDef) 
                    ? 'bg-blue-100 dark:bg-blue-800' 
                    : 'bg-zinc-100 dark:bg-zinc-700'
                ]">
                  <Sparkles :class="[
                    'w-4 h-4',
                    isFeatureEnabled(featureDef) 
                      ? 'text-blue-600 dark:text-blue-400' 
                      : 'text-zinc-400'
                  ]" />
                </div>
                <div>
                  <div class="flex items-center space-x-2">
                    <h4 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
                      {{ featureDef.name }}
                    </h4>
                    <Badge variant="outline" class="font-mono text-xs">
                      {{ featureDef.key }}
                    </Badge>
                  </div>
                  <p class="text-sm text-zinc-600 dark:text-zinc-400 mt-0.5">
                    {{ featureDef.description || "無描述" }}
                  </p>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <div class="flex flex-col items-end space-y-2">
                  <Switch
                    :id="`feature-${featureDef.id}`"
                    :model-value="isFeatureEnabled(featureDef)"
                    @update:model-value="(val: boolean) => handleFeatureToggle(featureDef, val)"
                  />
                  <div class="flex items-center space-x-1.5 text-xs">
                    <div :class="[
                      'w-2 h-2 rounded-full',
                      isFeatureEnabled(featureDef) ? 'bg-green-500' : 'bg-zinc-400'
                    ]"></div>
                    <span class="text-zinc-500 dark:text-zinc-400">
                      {{ isFeatureEnabled(featureDef) ? "已啟用" : "已停用" }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 助理配置區域 -->
          <div v-if="isFeatureEnabled(featureDef)" class="p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <Bot class="h-4 w-4 text-blue-500" />
                <span class="font-medium text-sm text-zinc-900 dark:text-zinc-100">關聯 AI 助理</span>
              </div>
              
              <div class="flex items-center space-x-2">
                <!-- 如果有專用助理，顯示助理資訊 -->
                <div v-if="props.getEffectiveBotForFeatureProp?.(featureDef)">
                  <Badge variant="secondary" class="flex items-center space-x-1.5 text-xs">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>{{ props.getEffectiveBotForFeatureProp(featureDef).name }}</span>
                  </Badge>
                </div>
                
                <!-- 如果沒有助理，顯示創建按鈕 -->
                <div v-else class="flex items-center space-x-2">
                  <Badge variant="outline" class="text-amber-600 border-amber-300 text-xs">
                    <AlertTriangle class="w-3 h-3 mr-1" />
                    未配置助理
                  </Badge>
                  <Button
                    size="sm"
                    variant="outline"
                    @click="onRequestNewBot(featureDef)"
                    class="text-xs h-8"
                  >
                    <Plus class="w-3 h-3 mr-1" />
                    創建助理
                  </Button>
                </div>
              </div>
            </div>
            
            <!-- 助理詳細資訊 -->
            <div v-if="props.getEffectiveBotForFeatureProp?.(featureDef)" class="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800">
              <div class="flex items-center justify-between">
                <div class="space-y-0.5">
                  <p class="text-sm font-medium text-blue-900 dark:text-blue-100">
                    {{ props.getEffectiveBotForFeatureProp(featureDef).name }}
                  </p>
                  <p class="text-xs text-blue-700 dark:text-blue-300">
                    {{ props.getEffectiveBotForFeatureProp(featureDef).description || '無描述' }}
                  </p>
                </div>
                <Badge variant="outline" class="text-blue-600 border-blue-300 text-xs">
                  {{ props.getEffectiveBotForFeatureProp(featureDef).scope === 'SYSTEM' ? '系統級' : '工作區級' }}
                </Badge>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 空狀態 -->
    <div v-if="!props.systemFeatureDefinitions || props.systemFeatureDefinitions.length === 0" class="text-center py-8">
      <div class="flex flex-col items-center space-y-4">
        <div class="p-4 bg-zinc-100 dark:bg-zinc-700 rounded-lg">
          <Puzzle class="w-8 h-8 text-zinc-400" />
        </div>
        <div>
          <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">尚無功能定義</h3>
          <p class="text-sm text-zinc-500 dark:text-zinc-400 mt-1">點擊「同步功能定義」按鈕來載入系統功能</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { 
  Sparkles, RefreshCw, Bot, Plus, AlertTriangle, Puzzle
} from "lucide-vue-next";

// 修改：直接使用 useAiFeatureConfig 中整合的 useFeatureConfigTabLogic
import { useFeatureConfigTabLogic } from "@/composables/admin/ai/features/useAiFeatureConfig"; 
import { AI_FEATURES } from "@/constants/ai-features.constants";
import { useNotification } from "@/composables/shared/useNotification";

// Import types from central files
import type {
  AiFeatureConfigUpdateDto,
  AiBotUpdateDto,
} from "@/types/dto/ai.dto";
import {
  AiBotScope,
  type AiBot as CoreAiBot,
  type AiFeatureConfig as CoreAiFeatureConfig,
} from "@/types/models/ai.model";

// Define prop types using imported or local types
export interface Props {
  systemFeatureDefinitions: any[] | null | undefined; // Keep any for now as it comes from parent
  aiBots: any[] | undefined; // Keep any for now as it comes from parent
  updateFeatureConfig: (
    featureKey: string,
    data: AiFeatureConfigUpdateDto
  ) => Promise<CoreAiFeatureConfig | null>;
  fetchSystemFeatureDefinitions: () => Promise<void>;
  getBotById: (botId: string) => any | undefined;
  getEffectiveBotForFeatureProp: (featureDefinition: any) => any | undefined;
  updateAiBot: (
    botId: string,
    data: AiBotUpdateDto
  ) => Promise<CoreAiBot | null>;
  fetchAiBots: () => Promise<void>;
  activeTab: string;
  syncFeatureDefinitions: (features: Array<{
    key: string;
    name: string;
    description?: string;
    systemBot?: boolean;
  }>) => Promise<{
    created: number;
    updated: number;
    skipped: number;
    errors: string[];
  }>;
}

const props = defineProps<Props>();

// 監控 props.aiBots 的變化
watch(() => props.aiBots, (newBots, oldBots) => {
  // Optional: Log for debugging if needed in development
}, { deep: true });

const emit = defineEmits<{
  (
    e: "request-new-bot",
    payload: { scope: AiBotScope; scene: string; name: string }
  ): void;
  (e: "update:activeTab", value: string): void;
}>();

// 修改：直接使用從 useAiFeatureConfig 導入的 useFeatureConfigTabLogic
const { 
  handleFeatureToggle, 
  onRequestNewBot 
} = useFeatureConfigTabLogic({
  aiBots: computed(() => props.aiBots),
  aiModels: [], // Assuming aiModels and aiKeys are not directly needed by this specific logic now
  aiKeys: [],   // or should be passed if they are
  systemFeatureDefinitions: computed(() => props.systemFeatureDefinitions || []),
  fetchAiBots: props.fetchAiBots,
  fetchSystemFeatureDefinitions: props.fetchSystemFeatureDefinitions,
  updateAiBot: props.updateAiBot,
  updateFeatureConfig: props.updateFeatureConfig, // Pass the prop from AISettings
});

// 檢查功能是否啟用
const isFeatureEnabled = (featureDef: any): boolean => {
  if (!featureDef) return false;
  const isEnabled = !!featureDef?.config?.is_enabled;
  return isEnabled;
}

// 同步功能定義的狀態管理
const notification = useNotification();
const isSyncing = ref(false);

// 同步功能定義到資料庫
const handleSyncFeatureDefinitions = async () => {
  if (isSyncing.value) return;
  
  isSyncing.value = true;
  try {
    const result = await props.syncFeatureDefinitions(AI_FEATURES);
    
    notification.toast.success("同步完成", `建立: ${result.created} 個，更新: ${result.updated} 個${result.errors.length > 0 ? `，錯誤: ${result.errors.length} 個` : ''}`);

    // 如果有錯誤，在控制台顯示詳細信息
    if (result.errors.length > 0) {
      console.error("同步功能定義時發生錯誤:", result.errors);
      notification.toast.warning("同步完成但有錯誤", `請檢查控制台了解詳細錯誤信息`);
    }
  } catch (error) {
    console.error("同步功能定義失敗:", error);
    notification.toast.error("同步失敗", "無法同步功能定義到資料庫，請檢查網路連線或聯絡管理員");
  } finally {
    isSyncing.value = false;
  }
};
</script> 