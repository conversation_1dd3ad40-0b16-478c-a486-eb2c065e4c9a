import {
  Controller,
  Post,
  Body,
  Get,
  Put,
  Delete,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpCode,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AgentsService } from '../../core/agents/agents.service';
import {
  CreateAgentDto,
  UpdateAgentDto,
  TestAgentDto,
  ExecuteAgentDto,
  AssignToolsToAgentDto,
  AgentToolResponseDto,
} from '../../core/agents/dto/agent.dto';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { CurrentUser } from '../../core/auth/decorators/current-user.decorator';
import { AiAgentScope } from '@prisma/client';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions } from '@horizai/permissions';
import { JwtUser } from '../../../types/jwt-user.type';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';

@ApiTags('workspace/agents')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, PoliciesGuard)
@Controller('workspace/agents')
export class WorkspaceAgentsController {
  private readonly logger = new Logger(WorkspaceAgentsController.name);

  constructor(private readonly agentsService: AgentsService) {}

  @Get()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '租戶管理員讀取工作區 AI Agent' })
  findAll(
    @Query('scope') scope?: AiAgentScope,
    @CurrentUser() user?: JwtUser,
  ) {
    // 只返回當前租戶的 Agents
    return this.agentsService.findAll(user?.tenant_id || undefined, undefined, scope);
  }

  @Get(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '租戶管理員根據 ID 讀取工作區 AI Agent' })
  async findOne(@Param('id') id: string, @CurrentUser() user: JwtUser) {
    const agent = await this.agentsService.findOne(id);
    
    // 確保 Agent 屬於當前租戶
    if (agent.tenant_id !== user.tenant_id) {
      throw new Error('無權限存取此 Agent');
    }
    
    return agent;
  }

  @Post()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, 'ai_agents'))
  @ApiOperation({ summary: '租戶管理員建立新的工作區 AI Agent' })
  async create(@Body(ValidationPipe) createAgentDto: CreateAgentDto, @CurrentUser() user: JwtUser) {
    // 確保租戶管理員只能創建租戶級 Agent
    const workspaceAgentDto = {
      ...createAgentDto,
      scope: (createAgentDto.scope === 'SYSTEM' ? 'TENANT' : createAgentDto.scope) as AiAgentScope,
      tenant_id: user.tenant_id || '',
    };
    return this.agentsService.create(workspaceAgentDto, user.id);
  }

  @Put(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '租戶管理員更新工作區 AI Agent' })
  async update(
    @Param('id') id: string,
    @Body(ValidationPipe) updateAgentDto: UpdateAgentDto,
    @CurrentUser() user: JwtUser,
  ) {
    // 先確認 Agent 屬於當前租戶
    const agent = await this.agentsService.findOne(id);
    if (agent.tenant_id !== user.tenant_id) {
      throw new Error('無權限修改此 Agent');
    }
    
    return this.agentsService.update(id, updateAgentDto, user.id);
  }

  @Delete(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, 'ai_agents'))
  @ApiOperation({ summary: '租戶管理員刪除工作區 AI Agent' })
  async remove(@Param('id') id: string, @CurrentUser() user: JwtUser) {
    // 先確認 Agent 屬於當前租戶
    const agent = await this.agentsService.findOne(id);
    if (agent.tenant_id !== user.tenant_id) {
      throw new Error('無權限刪除此 Agent');
    }
    
    return this.agentsService.delete(id);
  }

  @Post('test')
  @ApiOperation({ summary: '租戶管理員測試工作區 Agent 設定' })
  async testAgent(@Body() dto: TestAgentDto, @CurrentUser() user: JwtUser) {
    // 先確認 Agent 屬於當前租戶
    const agent = await this.agentsService.findOne(dto.agent_id);
    if (agent.tenant_id !== user.tenant_id) {
      throw new Error('無權限測試此 Agent');
    }
    
    return await this.agentsService.testAgent(
      dto.agent_id,
      dto.message,
      dto.prompt,
      dto.temperature,
    );
  }

  @Put(':id/status')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '租戶管理員更新工作區 Agent 啟用狀態' })
  async updateStatus(
    @Param('id') id: string, 
    @Body('isEnabled') isEnabled: boolean,
    @CurrentUser() user: JwtUser
  ) {
    // 先確認 Agent 屬於當前租戶
    const agent = await this.agentsService.findOne(id);
    if (agent.tenant_id !== user.tenant_id) {
      throw new Error('無權限修改此 Agent 狀態');
    }
    
    return this.agentsService.updateStatus(id, isEnabled);
  }

  @Get(':id/tools')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '租戶管理員取得工作區 Agent 的工具列表' })
  @ApiResponse({
    status: 200,
    description: '成功取得 Agent 的工具列表',
    type: [AgentToolResponseDto],
  })
  @ApiResponse({ status: 404, description: '找不到指定的 Agent' })
  async getAgentTools(@Param('id') id: string, @CurrentUser() user: JwtUser): Promise<AgentToolResponseDto[]> {
    // 先確認 Agent 屬於當前租戶
    const agent = await this.agentsService.findOne(id);
    if (agent.tenant_id !== user.tenant_id) {
      throw new Error('無權限存取此 Agent 工具');
    }
    
    return this.agentsService.getAgentTools(id);
  }

  @Put(':id/tools')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '租戶管理員為工作區 Agent 指派工具' })
  @ApiResponse({ status: 200, description: '成功指派工具給 Agent' })
  @ApiResponse({ status: 400, description: '請求參數錯誤' })
  @ApiResponse({ status: 404, description: '找不到指定的 Agent 或工具' })
  async assignToolsToAgent(
    @Param('id') id: string,
    @Body() assignToolsDto: AssignToolsToAgentDto,
    @CurrentUser() user: JwtUser,
  ): Promise<{ message: string; assignedCount: number }> {
    // 先確認 Agent 屬於當前租戶
    const agent = await this.agentsService.findOne(id);
    if (agent.tenant_id !== user.tenant_id) {
      throw new Error('無權限修改此 Agent 工具');
    }
    
    await this.agentsService.assignToolsToAgent(id, assignToolsDto.toolIds);
    return {
      message: '成功指派工具給 Agent',
      assignedCount: assignToolsDto.toolIds.length,
    };
  }

  @Delete(':id/tools/:toolId')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '租戶管理員移除工作區 Agent 的特定工具' })
  @ApiResponse({ status: 204, description: '成功移除工具' })
  @ApiResponse({ status: 404, description: '找不到指定的 Agent 或工具關聯' })
  @HttpCode(204)
  async removeToolFromAgent(
    @Param('id') id: string,
    @Param('toolId') toolId: string,
    @CurrentUser() user: JwtUser,
  ): Promise<void> {
    // 先確認 Agent 屬於當前租戶
    const agent = await this.agentsService.findOne(id);
    if (agent.tenant_id !== user.tenant_id) {
      throw new Error('無權限修改此 Agent 工具');
    }
    
    await this.agentsService.removeToolFromAgent(id, toolId);
  }

  @Get('tools/available')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '租戶管理員取得工作區可用工具' })
  async getAvailableTools(@CurrentUser() user: JwtUser) {
    return this.agentsService.getAvailableTools(user.tenant_id || '', user);
  }

  @Post(':id/execute')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.EXECUTE, 'ai_agents'))
  @HttpCode(200)
  @ApiOperation({ summary: '租戶管理員執行工作區 Agent' })
  async execute(
    @Param('id') id: string, 
    @Body() body: ExecuteAgentDto,
    @CurrentUser() user: JwtUser
  ) {
    try {
      // 先確認 Agent 屬於當前租戶
      const agent = await this.agentsService.findOne(id);
      if (agent.tenant_id !== user.tenant_id) {
        throw new Error('無權限執行此 Agent');
      }
      
      const response = await this.agentsService.execute(id, body);
      return response;
    } catch (error) {
      this.logger.error(`執行 Agent ${id} 失敗:`, error);
      throw error;
    }
  }
} 