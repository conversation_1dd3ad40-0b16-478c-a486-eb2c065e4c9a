/**
 * 系統角色列舉 - 包含系統級和租戶級角色
 */
export enum Role {
  // 系統級角色
  SUPER_ADMIN = 'SUPER_ADMIN',
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  SYSTEM_MODERATOR = 'SYSTEM_MODERATOR',

  // 租戶級角色
  TENANT_ADMIN = 'TENANT_ADMIN',
  TENANT_MANAGER = 'TENANT_MANAGER',
  TENANT_USER = 'TENANT_USER',
  TENANT_VIEWER = 'TENANT_VIEWER',
}

// 區分系統級別和租戶級別角色
export const SYSTEM_ROLES = [Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.SYSTEM_MODERATOR];
export const TENANT_ROLES = [
  Role.TENANT_ADMIN,
  Role.TENANT_MANAGER,
  Role.TENANT_USER,
  Role.TENANT_VIEWER,
];

// 為了向後兼容，保留 UserRole 作為 Role 的別名
export import UserRole = Role;

export const ROLE_VALUES = Object.values(Role);
