# Task ID: 18
# Title: Develop AI Usage Tracking and Tenant Quota System
# Status: pending
# Dependencies: 2, 6, 12
# Priority: medium
# Description: Create `AiUsage` model to log details of each Agent/LLM execution (tokens, cost). Implement `TenantAiQuota` to manage and enforce usage limits for tenants.
# Details:
Prisma schema for `AiUsageLog`: `id`, `tenantId`, `aiModelId`, `promptTokens Int`, `completionTokens Int`, `totalTokens Int`, `cost Float`, `executedAt DateTime`. Prisma schema for `TenantAiQuota`: `id`, `tenantId @unique`, `monthlyQuota Float`, `usedQuota Float`, `resetDate DateTime`. `AgentRunnerService` or LLM abstraction logs usage. Implement quota check before AI tasks.

# Test Strategy:
Verify `AiUsageLog` entries created. Test quota enforcement (block calls if exceeded). Test quota reset logic.
