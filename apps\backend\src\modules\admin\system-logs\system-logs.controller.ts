import {
  Controller,
  Get,
  Query,
  UseGuards,
  Req,
  Res,
  Post,
  Body,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/core/auth/guards/auth.guard';
import { PoliciesGuard } from '@/casl/guards/permission.guard';
import { CheckPolicies } from '@/casl/decorators/check-policies.decorator';
import { Actions, Subjects } from '@horizai/permissions';
import { SystemLogsService } from './system-logs.service';
import { Response } from 'express';
import { AppAbility } from '@/types/models/casl.model';
import { SystemLogQueryDto, SystemLogReportDto, SystemLogDto } from './dto/system-log.dto';
import { JwtUser } from '@/types/jwt-user.type';
import * as ExcelJS from 'exceljs';

@ApiTags('admin/system-logs')
@Controller('admin/system-logs')
@UseGuards(JwtAuthGuard, PoliciesGuard)
@ApiBearerAuth()
export class SystemLogsController {
  constructor(private readonly systemLogsService: SystemLogsService) {}

  @Get()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.SYSTEM_LOG))
  @ApiOperation({ summary: '查詢系統日誌列表' })
  @ApiResponse({
    status: 200,
    description: '成功取得日誌列表',
  })
  async getLogs(@Query() queryDto: SystemLogQueryDto, @Req() req: any) {
    const user: JwtUser = req.user;
    const result = await this.systemLogsService.getLogs(queryDto, user.tenant_id || undefined);

    return {
      data: result.logs,
      meta: result.meta,
    };
  }

  @Get('stats')
  @CheckPolicies((ability) => ability.can(Actions.READ, Subjects.SYSTEM_LOG))
  @ApiOperation({ summary: '獲取稽核統計' })
  async getStats(@Req() req: any) {
    const user: JwtUser = req.user;
    const stats = await this.systemLogsService.getAuditStats(user.tenant_id || undefined);

    return {
      data: stats,
    };
  }

  @Get('actions')
  @CheckPolicies((ability) => ability.can(Actions.READ, Subjects.SYSTEM_LOG))
  @ApiOperation({ summary: '獲取可用的稽核操作類型' })
  async getActions(@Req() req: any) {
    const user: JwtUser = req.user;
    const actions = await this.systemLogsService.getAvailableActions(user.tenant_id || undefined);

    return {
      data: actions,
    };
  }

  @Post('reports/generate')
  @CheckPolicies((ability) => ability.can(Actions.READ, Subjects.SYSTEM_LOG))
  @ApiOperation({ summary: '生成稽核報告' })
  async generateReport(@Body() reportDto: SystemLogReportDto, @Req() req: any, @Res() res: Response) {
    const user: JwtUser = req.user;

    try {
      const logs = await this.systemLogsService.getLogsForReport(reportDto, user.tenant_id || undefined);
      const filename = `audit-report-${new Date().toISOString().split('T')[0]}`;

      switch (reportDto.format) {
        case 'json':
          res.setHeader('Content-Type', 'application/json');
          res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
          return res.status(HttpStatus.OK).json(logs);

        case 'csv':
          const csvContent = this.systemLogsService.generateCSV(logs);
          res.setHeader('Content-Type', 'text/csv');
          res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
          return res.status(HttpStatus.OK).send(csvContent);

        case 'xlsx':
          const excelBuffer = await this.systemLogsService.generateExcel(logs);
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          res.setHeader('Content-Disposition', `attachment; filename="${filename}.xlsx"`);
          return res.status(HttpStatus.OK).send(excelBuffer);

        default:
          return res.status(HttpStatus.BAD_REQUEST).json({
            message: 'Unsupported format',
          });
      }
    } catch (error) {
      console.error('Generate report error:', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to generate report',
      });
    }
  }
}
