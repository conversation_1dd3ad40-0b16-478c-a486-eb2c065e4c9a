import { IsString, IsOptional, <PERSON><PERSON>rray, IsNotEmpty } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreatePermissionDto {
  @ApiProperty({
    description: '操作行為 (action)，如 create/read/update/delete/manage',
  })
  @IsString()
  @IsNotEmpty()
  action: string;

  @ApiProperty({ description: '資源主體 (subject)，如 User/Role/Project' })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiPropertyOptional({ description: '條件 (conditions)，JSON 格式' })
  @IsOptional()
  conditions?: any;

  @ApiProperty({
    description: '欄位 (fields)，限制可讀/寫的欄位列表',
    type: [String],
  })
  @IsArray()
  fields: string[];

  @ApiPropertyOptional({ description: '描述 (description)' })
  @IsString()
  @IsOptional()
  description?: string;
}

export class UpdatePermissionDto {
  @ApiPropertyOptional({ description: '操作行為 (action)' })
  @IsString()
  @IsOptional()
  action?: string;

  @ApiPropertyOptional({ description: '資源主體 (subject)' })
  @IsString()
  @IsOptional()
  subject?: string;

  @ApiPropertyOptional({ description: '條件 (conditions)，JSON 格式' })
  @IsOptional()
  conditions?: any;

  @ApiPropertyOptional({
    description: '欄位 (fields)，限制可讀/寫的欄位列表',
    type: [String],
  })
  @IsArray()
  @IsOptional()
  fields?: string[];

  @ApiPropertyOptional({ description: '描述 (description)' })
  @IsString()
  @IsOptional()
  description?: string;
}

export class PermissionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty({ description: '操作行為' })
  action: string;

  @ApiProperty({ description: '資源主體' })
  subject: string;

  @ApiPropertyOptional({ description: '條件', type: Object, additionalProperties: true })
  @IsOptional()
  conditions?: any;

  @ApiProperty({ description: '欄位列表', type: [String] })
  fields: string[];

  @ApiPropertyOptional({ description: '描述' })
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '建立時間' })
  created_at: Date;

  @ApiProperty({ description: '更新時間' })
  updated_at: Date;
}
