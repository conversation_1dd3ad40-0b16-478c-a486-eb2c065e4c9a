# HorizAI SaaS 資料庫遷移工具

這是一個統一的資料庫遷移管理工具，整合了所有資料庫相關的遷移操作，包括用戶資料表分離、權限同步、Schema 更新等。

## 🚀 快速開始

### 檢查資料庫狀態

```bash
pnpm migration:status
```

### 列出所有可用的遷移步驟

```bash
pnpm migration:list
```

### 執行完整遷移（推薦）

```bash
pnpm migration:full
```

## 📋 可用命令

### 基本命令

| 命令                    | 說明                   |
| ----------------------- | ---------------------- |
| `pnpm migration:status` | 檢查資料庫狀態         |
| `pnpm migration:list`   | 列出所有可用的遷移步驟 |
| `pnpm migration:health` | 執行資料庫健康檢查     |

### 遷移命令

| 命令                         | 說明                                         |
| ---------------------------- | -------------------------------------------- |
| `pnpm migration:full`        | 執行完整遷移（Schema + 用戶分離 + 權限同步） |
| `pnpm migration:users`       | 僅執行用戶資料表分離                         |
| `pnpm migration:permissions` | 僅執行權限同步                               |

### 進階命令

```bash
# 自訂遷移步驟
pnpm migration:migrate --steps "users-separation,permissions-sync"

# 乾跑模式（不實際執行）
pnpm migration:migrate --dry-run

# 指定環境
pnpm migration:migrate --env production

# 強制執行
pnpm migration:migrate --force

# 跳過備份
pnpm migration:migrate --no-backup

# 跳過驗證
pnpm migration:migrate --no-validate
```

## 🔧 遷移步驟說明

### 1. schema-update

- **說明**: 應用 Prisma schema 變更
- **執行**: `npx prisma migrate deploy`
- **時間**: 約 1-10 分鐘（取決於變更複雜度）

### 2. users-separation

- **說明**: 將舊的 `users` 資料表分離為 `system_users` 和 `tenant_users`
- **執行**: 自動遷移現有用戶資料
- **時間**: 約 1-5 分鐘（取決於用戶數量）

### 3. permissions-sync

- **說明**: 同步權限定義到資料庫
- **執行**: 運行權限同步腳本
- **時間**: 約 30 秒 - 2 分鐘

## 🛡️ 安全特性

### 自動備份

- 預設在遷移前自動建立資料庫備份
- 備份檔案存放在 `apps/backend/backups/` 目錄
- 可使用 `--no-backup` 跳過備份

### 驗證機制

- 遷移完成後自動驗證結果
- 檢查資料表存在性和資料完整性
- 可使用 `--no-validate` 跳過驗證

### 乾跑模式

- 使用 `--dry-run` 可以預覽遷移步驟而不實際執行
- 適合在正式環境執行前進行測試

## 📊 環境配置

工具支援多環境配置：

### Development（開發環境）

- 備份保留 7 天
- 詳細日誌輸出
- 寬鬆驗證模式

### Staging（測試環境）

- 備份保留 14 天
- 壓縮備份檔案
- 嚴格驗證模式
- 日誌記錄到檔案

### Production（正式環境）

- 備份保留 30 天
- 壓縮備份檔案
- 最嚴格驗證模式
- 僅記錄警告和錯誤

## 🔍 故障排除

### 常見問題

#### 1. 資料庫連線失敗

```bash
# 檢查環境變數
echo $DATABASE_URL

# 測試連線
pnpm migration:health
```

#### 2. 權限不足

```bash
# 確保資料庫使用者有足夠權限
# 需要 CREATE, ALTER, DROP, SELECT, INSERT, UPDATE, DELETE 權限
```

#### 3. 遷移卡住

```bash
# 檢查資料庫鎖定
# 可能需要重啟資料庫服務或終止長時間運行的查詢
```

#### 4. 備份失敗

```bash
# 檢查 pg_dump 是否可用
pg_dump --version

# 檢查磁碟空間
df -h
```

### 回滾策略

如果遷移失敗，可以使用以下策略：

1. **使用備份還原**

   ```bash
   # 找到最新的備份檔案
   ls -la apps/backend/backups/

   # 還原備份（請謹慎操作）
   psql $DATABASE_URL < apps/backend/backups/backup-YYYY-MM-DD.sql
   ```

2. **手動回滾特定步驟**
   ```bash
   # 目前回滾功能正在開發中
   pnpm migration rollback --step users-separation
   ```

## 📝 開發指南

### 新增遷移步驟

1. 在 `migration-manager.ts` 中註冊新步驟：

   ```typescript
   this.migrationSteps.set("new-step", {
     id: "new-step",
     name: "新遷移步驟",
     description: "步驟描述",
     execute: this.executeNewStep.bind(this),
     validate: this.validateNewStep.bind(this),
     rollback: this.rollbackNewStep.bind(this),
   });
   ```

2. 實作對應的方法：

   ```typescript
   private async executeNewStep(): Promise<void> {
     // 實作邏輯
   }

   private async validateNewStep(): Promise<boolean> {
     // 驗證邏輯
     return true;
   }

   private async rollbackNewStep(): Promise<void> {
     // 回滾邏輯
   }
   ```

3. 在 `config.ts` 中新增步驟配置：
   ```typescript
   'new-step': {
     dependencies: ['other-step'], // 依賴的步驟
     timeout: 300000, // 超時時間
     retries: 1 // 重試次數
   }
   ```

### 測試

```bash
# 在開發環境測試
pnpm migration:migrate --dry-run --env development

# 在測試環境驗證
pnpm migration:migrate --env staging
```

## 📚 相關文件

- [Prisma 遷移文件](https://www.prisma.io/docs/concepts/components/prisma-migrate)
- [PostgreSQL 備份與還原](https://www.postgresql.org/docs/current/backup.html)
- [HorizAI 資料庫架構指南](../../README.md)

## 🤝 貢獻

如果您發現問題或有改進建議，請：

1. 建立 Issue 描述問題
2. 提交 Pull Request 包含修復
3. 更新相關文件

## 📄 授權

本工具遵循 HorizAI SaaS 專案的授權條款。
