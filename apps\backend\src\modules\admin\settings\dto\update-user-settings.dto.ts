import { IsString, IsBoolean, IsEnum, IsIn } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Role } from '../../../../common/enums/role.enum';

export class UpdateUserSettingsDto {
  @ApiProperty({ description: '是否允許公開註冊' })
  @IsBoolean()
  allowPublicSignup!: boolean;

  @ApiProperty({
    description: '預設使用者角色',
    enum: Role,
    enumName: 'Role',
  })
  @IsEnum(Role)
  defaultUserRole!: Role;

  @ApiProperty({ description: '是否要求 Email 驗證' })
  @IsBoolean()
  requireEmailVerification!: boolean;

  @ApiProperty({ description: '預設使用者狀態' })
  @IsString()
  @IsIn(['active', 'inactive', 'pending'])
  defaultUserStatus!: 'active' | 'inactive' | 'pending';

  @ApiProperty({ description: '是否允許同時登入' })
  @IsBoolean()
  allowMultipleLogin!: boolean;

  @ApiProperty({ description: '預設方案 ID' })
  @IsString()
  defaultPlan!: string;
}
