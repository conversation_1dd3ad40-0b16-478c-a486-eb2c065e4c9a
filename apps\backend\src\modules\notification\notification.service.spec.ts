import { Test, TestingModule } from '@nestjs/testing';
import { NotificationService } from './notification.service';
import { PrismaService } from '../core/prisma/prisma.service';
import { MessageCenterService } from '../workspace/message-center/services/message-center.service';
import { BadRequestException } from '@nestjs/common';
import { ConversationType } from '@prisma/client';

describe('NotificationService', () => {
  let service: NotificationService;
  let prisma: PrismaService;
  let messageCenter: MessageCenterService;

  const mockPrismaService = {
    message_conversations: {
      findFirst: jest.fn(),
      create: jest.fn(),
    },
  };

  const mockMessageCenterService = {
    sendMessage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: MessageCenterService, useValue: mockMessageCenterService },
      ],
    }).compile();

    service = module.get<NotificationService>(NotificationService);
    prisma = module.get<PrismaService>(PrismaService);
    messageCenter = module.get<MessageCenterService>(MessageCenterService);

    // Reset mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendMessage (internal, user)', () => {
    it('should find an existing direct conversation and send a message', async () => {
      const dto = {
        channel: 'internal' as const,
        recipient_type: 'user' as const,
        recipient_id: 'user-2',
        message: 'Hello user!',
      };
      const tenantId = 'tenant-1';
      const senderId = 'user-1';

      const mockConversation = {
        id: 'convo-1',
        type: ConversationType.DIRECT,
        participant_ids: [senderId, dto.recipient_id],
      };

      // Mock findFirst to return the existing conversation
      (prisma.message_conversations.findFirst as jest.Mock).mockResolvedValue(mockConversation);

      await service.sendMessage(dto, tenantId, senderId);

      expect(prisma.message_conversations.findFirst).toHaveBeenCalledWith({
        where: {
          tenant_id: tenantId,
          type: 'DIRECT',
          AND: [
            { participant_ids: { array_contains: senderId } },
            { participant_ids: { array_contains: dto.recipient_id } },
          ],
        },
      });
      expect(prisma.message_conversations.create).not.toHaveBeenCalled();
      expect(messageCenter.sendMessage).toHaveBeenCalledWith(
        {
          conversationId: mockConversation.id,
          content: dto.message,
          contentType: 'TEXT',
        },
        tenantId,
        senderId,
        'AGENT',
        'Agent',
      );
    });

    it('should create a new direct conversation if none exists', async () => {
      const dto = {
        channel: 'internal' as const,
        recipient_type: 'user' as const,
        recipient_id: 'user-2',
        message: 'Hello new user!',
      };
      const tenantId = 'tenant-1';
      const senderId = 'user-1';
      const newConversationId = 'convo-new';

      // Mock findFirst to return null
      (prisma.message_conversations.findFirst as jest.Mock).mockResolvedValue(null);
      // Mock create to return a new conversation
      (prisma.message_conversations.create as jest.Mock).mockResolvedValue({
        id: newConversationId,
      });

      await service.sendMessage(dto, tenantId, senderId);

      expect(prisma.message_conversations.findFirst).toHaveBeenCalledTimes(1);
      expect(prisma.message_conversations.create).toHaveBeenCalledWith({
        data: {
          tenant_id: tenantId,
          type: 'DIRECT',
          participant_ids: [senderId, dto.recipient_id].sort(),
          created_by: senderId,
          title: `Conversation between ${senderId} and ${dto.recipient_id}`,
        },
      });
      expect(messageCenter.sendMessage).toHaveBeenCalledWith(
        expect.objectContaining({ conversationId: newConversationId }),
        tenantId,
        senderId,
        'AGENT',
        'Agent',
      );
    });
  });

  describe('sendMessage (internal, group)', () => {
    it('should find an existing group conversation and send a message', async () => {
      const dto = {
        channel: 'internal' as const,
        recipient_type: 'group' as const,
        recipient_id: 'project-1',
        message: 'Hello group!',
      };
      const tenantId = 'tenant-1';
      const senderId = 'user-1';

      const mockConversation = { id: 'group-convo-1' };

      // Mock findFirst to return the existing group conversation
      (prisma.message_conversations.findFirst as jest.Mock).mockResolvedValue(mockConversation);

      await service.sendMessage(dto, tenantId, senderId);

      expect(prisma.message_conversations.findFirst).toHaveBeenCalledWith({
        where: {
          tenant_id: tenantId,
          type: 'GROUP',
          title: `group_chat_${dto.recipient_id}`,
        },
      });
      expect(prisma.message_conversations.create).not.toHaveBeenCalled();
      expect(messageCenter.sendMessage).toHaveBeenCalledWith(
        expect.objectContaining({ conversationId: mockConversation.id }),
        tenantId,
        senderId,
        'AGENT',
        'Agent',
      );
    });
  });
});
