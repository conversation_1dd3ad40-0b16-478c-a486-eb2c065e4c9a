-- 先移除相關的外鍵約束
ALTER TABLE "ai_features" DROP CONSTRAINT IF EXISTS "ai_features_botId_fkey";

-- 建立臨時表
CREATE TABLE "ai_bots_temp" AS 
SELECT 
    id,
    name,
    description,
    scene,
    "createdAt",
    "updatedAt",
    model,
    prompt,
    temperature,
    "apiKey",
    "apiUrl",
    provider,
    "createdBy",
    "isEnabled",
    "isTemplate",
    CASE 
        WHEN scope = 'ADMIN' THEN 'SYSTEM'
        WHEN scope = 'WORKSPACE' THEN 'WORKSPACE'
        WHEN scope = 'BACKEND' THEN 'TENANT_TEMPLATE'
        ELSE 'WORKSPACE'
    END as scope,
    "tenantId",
    "updatedBy",
    "responseFormat"
FROM "ai_bots";

-- 刪除原表
DROP TABLE "ai_bots";

-- 刪除舊的 enum
DROP TYPE IF EXISTS "AiBotScope";

-- 建立新的 enum
CREATE TYPE "AiBotScope" AS ENUM ('SYSTEM', 'WORKSPACE', 'TENANT_TEMPLATE');

-- 重建表
CREATE TABLE "ai_bots" (
    id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    scene TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    model TEXT NOT NULL DEFAULT 'gpt-4.1-2025-04-14',
    prompt TEXT,
    temperature DOUBLE PRECISION NOT NULL DEFAULT 0.7,
    "apiKey" TEXT,
    "apiUrl" TEXT,
    provider TEXT NOT NULL DEFAULT 'openai',
    "createdBy" TEXT NOT NULL,
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "isTemplate" BOOLEAN NOT NULL DEFAULT false,
    scope "AiBotScope" NOT NULL DEFAULT 'WORKSPACE',
    "tenantId" TEXT,
    "updatedBy" TEXT,
    "responseFormat" "ResponseFormat" DEFAULT 'text',
    CONSTRAINT "ai_bots_pkey" PRIMARY KEY (id)
);

-- 恢復資料（使用型別轉換）
INSERT INTO "ai_bots" (
    id,
    name,
    description,
    scene,
    "createdAt",
    "updatedAt",
    model,
    prompt,
    temperature,
    "apiKey",
    "apiUrl",
    provider,
    "createdBy",
    "isEnabled",
    "isTemplate",
    scope,
    "tenantId",
    "updatedBy",
    "responseFormat"
)
SELECT 
    id,
    name,
    description,
    scene,
    "createdAt",
    "updatedAt",
    model,
    prompt,
    temperature,
    "apiKey",
    "apiUrl",
    provider,
    "createdBy",
    "isEnabled",
    "isTemplate",
    scope::"AiBotScope",
    "tenantId",
    "updatedBy",
    "responseFormat"::"ResponseFormat"
FROM "ai_bots_temp";

-- 刪除臨時表
DROP TABLE "ai_bots_temp";

-- 重建索引
CREATE INDEX "ai_bots_scope_idx" ON "ai_bots"("scope");
CREATE INDEX "ai_bots_tenantId_idx" ON "ai_bots"("tenantId");
CREATE INDEX "ai_bots_scope_tenantId_idx" ON "ai_bots"("scope", "tenantId");

-- 重建外鍵關係
ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_tenantId_fkey" 
    FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_createdBy_fkey" 
    FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_updatedBy_fkey" 
    FOREIGN KEY ("updatedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- 重建 ai_features 的外鍵關係
ALTER TABLE "ai_features" ADD CONSTRAINT "ai_features_botId_fkey" 
    FOREIGN KEY ("botId") REFERENCES "ai_bots"("id") ON DELETE SET NULL ON UPDATE CASCADE; 