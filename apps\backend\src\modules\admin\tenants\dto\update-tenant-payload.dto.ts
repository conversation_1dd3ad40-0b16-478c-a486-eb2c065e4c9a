import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEmail,
  IsNumber,
  IsEnum,
  IsDateString,
  IsArray,
  IsNotEmpty,
  ValidateIf,
  IsObject,
} from 'class-validator';

export class UpdateTenantPayloadDto {
  @ApiProperty({ description: '租戶名稱', required: false })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({ description: '租戶網域', required: false })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  domain?: string;

  @ApiProperty({
    description: '租戶狀態',
    enum: ['active', 'inactive', 'pending'],
    required: false,
  })
  @IsString()
  @IsOptional()
  @IsEnum(['active', 'inactive', 'pending'])
  status?: string;

  @ApiProperty({ description: '管理員名稱', required: false })
  @IsString()
  @IsOptional()
  admin_name?: string;

  @ApiProperty({ description: '管理員信箱', required: false })
  @IsEmail({}, { message: '請輸入有效的電子郵件地址' })
  @IsOptional()
  admin_email?: string;

  @ApiProperty({
    description: '公司規模',
    enum: ['1-10', '11-50', '51-200', '201-500', '500+'],
    required: false,
  })
  @IsString()
  @IsOptional()
  @IsEnum(['1-10', '11-50', '51-200', '201-500', '500+'])
  company_size?: string;

  @ApiProperty({ description: '產業類型', required: false })
  @IsString()
  @IsOptional()
  industry?: string;

  @ApiProperty({ description: '方案 ID', required: false })
  @IsString()
  @IsOptional()
  plan_id?: string;

  @ApiProperty({ description: '最大使用者數', minimum: 1, maximum: 1000, required: false })
  @IsNumber({}, { message: '最大使用者數必須是數字' })
  @IsOptional()
  max_users?: number;

  @ApiProperty({ description: '最大專案數', minimum: 1, maximum: 1000, required: false })
  @IsNumber({}, { message: '最大專案數必須是數字' })
  @IsOptional()
  max_projects?: number;

  @ApiProperty({ description: '最大儲存空間 (GB)', minimum: 1, maximum: 10000, required: false })
  @IsNumber({}, { message: '最大儲存空間必須是數字' })
  @IsOptional()
  maxStorage?: number;

  @ApiProperty({ description: '帳單週期', enum: ['monthly', 'yearly'], required: false })
  @IsString()
  @IsOptional()
  @IsEnum(['monthly', 'yearly'])
  billingCycle?: string;

  @ApiProperty({
    description: '下次帳單日期',
    required: false,
    format: 'date-time',
    type: 'string',
    nullable: true,
  })
  @ValidateIf((o) => o.next_billing_date !== null && o.next_billing_date !== '')
  @IsDateString({ strict: false }, { message: '下次帳單日期格式無效 (應為 YYYY-MM-DD)' })
  @IsOptional()
  nextBillingDate?: string | null;

  @ApiProperty({ description: '付款狀態', enum: ['paid', 'unpaid', 'overdue'], required: false })
  @IsString()
  @IsOptional()
  @IsEnum(['paid', 'unpaid', 'overdue'])
  paymentStatus?: string;

  @ApiProperty({ description: '部門列表', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true, message: '每個部門名稱都必須是字串' })
  departments?: string[];

  @ApiProperty({ description: '自定義屬性', required: false })
  @IsObject()
  @IsOptional()
  customProperties?: Record<string, any>;
}
