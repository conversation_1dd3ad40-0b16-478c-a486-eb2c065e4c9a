-- CreateTable
CREATE TABLE "ai_features" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT,
    "feature" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT false,
    "keyId" TEXT,
    "botId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_features_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ai_features_feature_idx" ON "ai_features"("feature");

-- CreateIndex
CREATE INDEX "ai_features_keyId_idx" ON "ai_features"("keyId");

-- CreateIndex
CREATE INDEX "ai_features_botId_idx" ON "ai_features"("botId");

-- CreateIndex
CREATE UNIQUE INDEX "ai_features_tenantId_feature_key" ON "ai_features"("tenantId", "feature");

-- AddForeignKey
ALTER TABLE "ai_features" ADD CONSTRAINT "ai_features_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_features" ADD CONSTRAINT "ai_features_keyId_fkey" FOREIGN KEY ("keyId") REFERENCES "ai_keys"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_features" ADD CONSTRAINT "ai_features_botId_fkey" FOREIGN KEY ("botId") REFERENCES "ai_bots"("id") ON DELETE SET NULL ON UPDATE CASCADE;
