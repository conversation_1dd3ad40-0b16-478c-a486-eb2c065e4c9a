/*
  Warnings:

  - The values [OWNE<PERSON>,<PERSON>MI<PERSON>,MEMBER,VIEWER] on the enum `TenantUserRole` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "TenantUserRole_new" AS ENUM ('TENANT_ADMIN', 'TENANT_MANAGER', 'TENANT_USER', 'TENANT_VIEWER');
ALTER TABLE "tenant_users" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "tenant_users" ALTER COLUMN "role" TYPE "TenantUserRole_new" USING ("role"::text::"TenantUserRole_new");
ALTER TYPE "TenantUserRole" RENAME TO "TenantUserRole_old";
ALTER TYPE "TenantUserRole_new" RENAME TO "TenantUserRole";
DROP TYPE "TenantUserRole_old";
ALTER TABLE "tenant_users" ALTER COLUMN "role" SET DEFAULT 'TENANT_USER';
COMMIT;

-- AlterTable
ALTER TABLE "tenant_users" ALTER COLUMN "role" SET DEFAULT 'TENANT_USER';
