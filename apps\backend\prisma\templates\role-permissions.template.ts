import { createPermissionKey } from './permissions.template';

// 角色類型定義
export type RoleName =
  | 'SUPER_ADMIN'
  | 'SYSTEM_ADMIN'
  | 'TENANT_ADMIN'
  | 'TENANT_USER';

// 角色權限映射類型
export type RolePermissionMapping = {
  [key in RoleName]: 'ALL' | string[];
};

// 角色權限映射定義
export const ROLE_PERMISSIONS: RolePermissionMapping = {
  // 超級管理員擁有所有權限
  SUPER_ADMIN: 'ALL',

  // 系統管理員權限
  SYSTEM_ADMIN: [
    // 主控台
    createPermissionKey('view', 'AdminDashboard'),

    // 租戶管理
    createPermissionKey('view', 'Tenant'),
    createPermissionKey('view', 'TenantDetail'),
    createPermissionKey('create', 'Tenant'),
    createPermissionKey('update', 'TenantProfile'),
    createPermissionKey('update', 'TenantStatus'),
    createPermissionKey('update', 'TenantPlan'),
    createPermissionKey('manage', 'TenantResourceLimit'),
    createPermissionKey('softDelete', 'Tenant'),
    createPermissionKey('restore', 'Tenant'),

    // 系統使用者
    createPermissionKey('view', 'SystemUser'),
    createPermissionKey('view', 'SystemUserDetail'),
    createPermissionKey('create', 'SystemUser'),
    createPermissionKey('update', 'SystemUserProfile'),
    createPermissionKey('update', 'SystemUserRole'),
    createPermissionKey('update', 'SystemUserStatus'),
    createPermissionKey('reset', 'SystemUserPassword'),
    createPermissionKey('softDelete', 'SystemUser'),
    createPermissionKey('restore', 'SystemUser'),

    // 租戶使用者
    createPermissionKey('view', 'AllTenantUser'),
    createPermissionKey('view', 'SpecificTenantUserList'),
    createPermissionKey('view', 'SpecificTenantUserDetail'),
    createPermissionKey('update', 'SpecificTenantUserStatus'),

    // 系統設定
    createPermissionKey('view', 'SystemSetting'),
    createPermissionKey('view', 'GeneralSetting'),
    createPermissionKey('update', 'GeneralSetting'),
    createPermissionKey('view', 'MailSetting'),
    createPermissionKey('update', 'MailSetting'),
    createPermissionKey('test', 'MailSetting'),
    createPermissionKey('view', 'StorageSetting'),
    createPermissionKey('update', 'StorageSetting'),
    createPermissionKey('view', 'AuthSetting'),
    createPermissionKey('update', 'AuthSetting'),
    createPermissionKey('manage', 'IdentityProvider'),
    createPermissionKey('view', 'FeatureFlag'),
    createPermissionKey('manage', 'FeatureFlag'),
    createPermissionKey('view', 'SystemLog'),

    // 訂閱與方案
    createPermissionKey('view', 'Plan'),
    createPermissionKey('view', 'Subscription'),
    createPermissionKey('view', 'Order'),
    createPermissionKey('manage', 'Subscription'),
    createPermissionKey('export', 'BillingData')
  ],

  // 租戶管理員權限
  TENANT_ADMIN: [
    // 租戶使用者管理
    createPermissionKey('view', 'SpecificTenantUserList'),
    createPermissionKey('view', 'SpecificTenantUserDetail'),
    createPermissionKey('update', 'SpecificTenantUserStatus'),

    // 訂閱查看
    createPermissionKey('view', 'Subscription'),
    createPermissionKey('view', 'Order'),

    // 基本設定查看
    createPermissionKey('view', 'GeneralSetting'),
    createPermissionKey('view', 'StorageSetting'),

    // 協作功能管理權限
    createPermissionKey('manage', 'SharedFile'),
    createPermissionKey('manage', 'FilePermission'),
    createPermissionKey('manage', 'FileShare'),
    createPermissionKey('manage', 'Comment'),
    createPermissionKey('manage', 'CommentReaction'),
    createPermissionKey('manage', 'Notification'),
    createPermissionKey('manage', 'CollaborationSession')
  ],

  // 租戶使用者權限
  TENANT_USER: [
    // 基本查看權限
    createPermissionKey('view', 'GeneralSetting'),

    // 協作功能基本權限
    createPermissionKey('read', 'SharedFile'),
    createPermissionKey('create', 'SharedFile'),
    createPermissionKey('update', 'SharedFile'),
    createPermissionKey('share', 'SharedFile'),
    createPermissionKey('read', 'FilePermission'),
    createPermissionKey('create', 'FileShare'),
    createPermissionKey('read', 'FileShare'),
    createPermissionKey('update', 'FileShare'),
    createPermissionKey('delete', 'FileShare'),
    createPermissionKey('read', 'Comment'),
    createPermissionKey('create', 'Comment'),
    createPermissionKey('update', 'Comment'),
    createPermissionKey('delete', 'Comment'),
    createPermissionKey('read', 'CommentReaction'),
    createPermissionKey('create', 'CommentReaction'),
    createPermissionKey('delete', 'CommentReaction'),
    createPermissionKey('read', 'Notification'),
    createPermissionKey('create', 'Notification'),
    createPermissionKey('update', 'Notification'),
    createPermissionKey('delete', 'Notification'),
    createPermissionKey('read', 'CollaborationSession'),
    createPermissionKey('create', 'CollaborationSession'),
    createPermissionKey('update', 'CollaborationSession')
  ]
};

// 角色工具函數
export function getRolePermissions(roleName: RoleName): string[] {
  const perms = ROLE_PERMISSIONS[roleName];
  return perms === 'ALL' ? ['manage:all'] : perms;
}

export function hasPermission(roleName: RoleName, permissionKey: string): boolean {
  const perms = ROLE_PERMISSIONS[roleName];
  if (perms === 'ALL') return true;
  return perms.includes(permissionKey);
} 