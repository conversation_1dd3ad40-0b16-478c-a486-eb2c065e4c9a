import { SetMetadata } from '@nestjs/common';
import { PolicyHandler } from '../interfaces/policy-handler.interface';
import { Actions, Subjects } from '../../types/models/casl.model';

// 重新導出 PolicyHandler 以供其他模組使用
export { PolicyHandler } from '../interfaces/policy-handler.interface';

/**
 * 原始 PolicyHandler 的裝飾器元數據鍵
 */
export const CHECK_POLICIES_KEY = 'check_policy';

/**
 * 原始 PolicyHandler 裝飾器
 * 提供最大靈活性，允許自訂 PolicyHandler 函數
 *
 * @param handlers PolicyHandler 陣列
 */
export const CheckPolicies = (...handlers: PolicyHandler[]) =>
  SetMetadata(CHECK_POLICIES_KEY, handlers);

/**
 * 權限要求裝飾器的元數據鍵
 */
export const REQUIRE_PERMISSIONS_KEY = 'require_permissions';

/**
 * 權限要求介面
 */
export interface PermissionRequirement {
  action: Actions;
  subject: Subjects;
  conditions?: Record<string, any>;
  fields?: string[];
}

/**
 * 要求特定權限的裝飾器
 * 提供比 @CheckPolicies 更簡潔的 API
 *
 * @param requirements 權限要求陣列
 *
 * @example
 * ```typescript
 * @RequirePermissions([
 *   { action: 'read', subject: 'User' },
 *   { action: 'update', subject: 'User', conditions: { id: '{{user_id}}' } }
 * ])
 * async updateUser() { ... }
 * ```
 */
export const RequirePermissions = (requirements: PermissionRequirement[]) =>
  SetMetadata(REQUIRE_PERMISSIONS_KEY, requirements);

/**
 * 要求單一權限的便利裝飾器
 *
 * @param action 操作動作
 * @param subject 資源主體
 * @param conditions 可選的條件
 * @param fields 可選的欄位限制
 *
 * @example
 * ```typescript
 * @RequirePermission('read', 'User')
 * async getUsers() { ... }
 *
 * @RequirePermission('update', 'User', { id: '{{user_id}}' })
 * async updateUser() { ... }
 * ```
 */
export const RequirePermission = (
  action: Actions,
  subject: Subjects,
  conditions?: Record<string, any>,
  fields?: string[],
) => RequirePermissions([{ action, subject, conditions, fields }]);

/**
 * 要求管理權限的裝飾器
 * 等同於 @RequirePermission('manage', subject)
 *
 * @param subject 資源主體
 *
 * @example
 * ```typescript
 * @RequireManage('User')
 * async deleteUser() { ... }
 * ```
 */
export const RequireManage = (subject: Subjects) => RequirePermission('manage', subject);

/**
 * 要求讀取權限的裝飾器
 * 等同於 @RequirePermission('read', subject)
 *
 * @param subject 資源主體
 * @param conditions 可選的條件
 *
 * @example
 * ```typescript
 * @RequireRead('User')
 * async getUsers() { ... }
 * ```
 */
export const RequireRead = (subject: Subjects, conditions?: Record<string, any>) =>
  RequirePermission('read', subject, conditions);

/**
 * 要求建立權限的裝飾器
 * 等同於 @RequirePermission('create', subject)
 *
 * @param subject 資源主體
 * @param conditions 可選的條件
 *
 * @example
 * ```typescript
 * @RequireCreate('User')
 * async createUser() { ... }
 * ```
 */
export const RequireCreate = (subject: Subjects, conditions?: Record<string, any>) =>
  RequirePermission('create', subject, conditions);

/**
 * 要求更新權限的裝飾器
 * 等同於 @RequirePermission('update', subject)
 *
 * @param subject 資源主體
 * @param conditions 可選的條件
 *
 * @example
 * ```typescript
 * @RequireUpdate('User')
 * async updateUser() { ... }
 * ```
 */
export const RequireUpdate = (subject: Subjects, conditions?: Record<string, any>) =>
  RequirePermission('update', subject, conditions);

/**
 * 要求刪除權限的裝飾器
 * 等同於 @RequirePermission('delete', subject)
 *
 * @param subject 資源主體
 * @param conditions 可選的條件
 *
 * @example
 * ```typescript
 * @RequireDelete('User')
 * async deleteUser() { ... }
 * ```
 */
export const RequireDelete = (subject: Subjects, conditions?: Record<string, any>) =>
  RequirePermission('delete', subject, conditions);

/**
 * 要求訪問權限的裝飾器
 * 等同於 @RequirePermission('access', subject)
 *
 * @param subject 資源主體
 * @param conditions 可選的條件
 *
 * @example
 * ```typescript
 * @RequireAccess('AdminPanel')
 * async getAdminData() { ... }
 * ```
 */
export const RequireAccess = (subject: Subjects, conditions?: Record<string, any>) =>
  RequirePermission('access', subject, conditions);
