import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUrl } from 'class-validator';

export class CreateKeyDto {
  @ApiProperty({ description: 'Key 名稱' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'AI Provider' })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @ApiProperty({ description: 'API 金鑰' })
  @IsString()
  @IsNotEmpty()
  apiKey: string;

  @ApiPropertyOptional({ description: 'API 服務 URL (僅 OpenAI Compatible Provider 需要)' })
  @IsUrl({ require_tld: false })
  @IsOptional()
  apiUrl?: string;

  @ApiPropertyOptional({ description: '是否啟用' })
  @IsBoolean()
  @IsOptional()
  isEnabled?: boolean;

  @ApiPropertyOptional({ description: '租戶 ID (留空表示系統級)' })
  @IsString()
  @IsOptional()
  tenantId?: string;
}

export class UpdateKeyDto {
  @ApiPropertyOptional({ description: 'Key 名稱' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: 'API 金鑰' })
  @IsString()
  @IsOptional()
  apiKey?: string;

  @ApiPropertyOptional({ description: 'API 服務 URL (僅 OpenAI Compatible Provider 需要)' })
  @IsUrl({ require_tld: false })
  @IsOptional()
  apiUrl?: string;

  @ApiPropertyOptional({ description: '是否啟用' })
  @IsBoolean()
  @IsOptional()
  isEnabled?: boolean;

  @ApiPropertyOptional({ description: '租戶 ID (留空表示系統級)' })
  @IsString()
  @IsOptional()
  tenantId?: string;
}

export class TestKeyDto {
  @ApiProperty({ description: 'AI Provider' })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @ApiProperty({ description: 'API 金鑰' })
  @IsString()
  @IsNotEmpty()
  apiKey: string;

  @ApiPropertyOptional({ description: 'API 服務 URL (僅 OpenAI Compatible Provider 需要)' })
  @IsUrl({ require_tld: false })
  @IsOptional()
  apiUrl?: string;
}

export class KeyResponseDto {
  @ApiProperty({ description: 'Key ID' })
  id: string;

  @ApiProperty({ description: 'Key 名稱' })
  name: string;

  @ApiProperty({ description: 'AI Provider' })
  provider: string;

  @ApiPropertyOptional({ description: 'API 服務 URL' })
  api_url?: string;

  @ApiProperty({ description: '是否啟用' })
  is_enabled: boolean;

  @ApiPropertyOptional({ description: '租戶 ID' })
  tenant_id?: string;

  @ApiProperty({ description: '最後測試時間' })
  last_test?: Date;

  @ApiProperty({ description: '建立時間' })
  created_at: Date;

  @ApiProperty({ description: '更新時間' })
  updated_at: Date;
}
