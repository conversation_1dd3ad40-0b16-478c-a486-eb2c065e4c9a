/*
  Warnings:

  - Changed the type of `value` on the `settings` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- DropForeignKey
ALTER TABLE "settings" DROP CONSTRAINT "settings_createdBy_fkey";

-- DropForeignKey
ALTER TABLE "settings" DROP CONSTRAINT "settings_updatedBy_fkey";

-- DropIndex
DROP INDEX "settings_name_type_key";

-- AlterTable
ALTER TABLE "settings" DROP COLUMN "value",
ADD COLUMN     "value" JSONB NOT NULL;

-- CreateIndex
CREATE INDEX "settings_createdBy_idx" ON "settings"("createdBy");

-- CreateIndex
CREATE INDEX "settings_updatedBy_idx" ON "settings"("updatedBy");
