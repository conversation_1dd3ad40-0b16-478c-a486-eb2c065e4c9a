import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { RoleHierarchyInfo } from '../types/role.types';
import { RoleScope } from '@prisma/client';

/**
 * 角色層級管理服務
 * 負責管理角色的層級關係和權限繼承
 */
@Injectable()
export class RoleHierarchyService {
  private readonly logger = new Logger(RoleHierarchyService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 獲取角色層級資訊
   */
  async getRoleHierarchy(roleId: string): Promise<RoleHierarchyInfo> {
    const role = await this.prisma.roles.findUnique({
      where: { id: roleId },
      include: {
        roles: true, // parent
        other_roles: true, // children
      },
    });

    if (!role) {
      throw new NotFoundException(`Role with ID ${roleId} not found`);
    }

    return {
      role_id: role.id,
      role_name: role.name,
      parent_role_id: role.parent_role_id ?? null,
      child_role_ids: role.other_roles.map((r) => r.id),
      level: 0, // Placeholder, actual level calculation might be needed
      direct_permissions: [], // Placeholder
      inherited_permissions: [], // Placeholder
    };
  }

  /**
   * 設置角色的父角色
   */
  async setParentRole(roleId: string, parentRoleId: string | null): Promise<void> {
    const [role, parentRole] = await Promise.all([
      this.prisma.roles.findUnique({ where: { id: roleId } }),
      parentRoleId ? this.prisma.roles.findUnique({ where: { id: parentRoleId } }) : null,
    ]);

    if (!role) throw new NotFoundException(`Role with ID ${roleId} not found`);
    if (parentRoleId && !parentRole)
      throw new NotFoundException(`Parent role with ID ${parentRoleId} not found`);

    if (role.scope !== RoleScope.SYSTEM && parentRole && parentRole.scope !== RoleScope.SYSTEM) {
      if (role.tenant_id !== parentRole.tenant_id) {
        throw new BadRequestException('角色和父角色必須屬於同一租戶');
      }
    }

    await this.prisma.roles.update({
      where: { id: roleId },
      data: { parent_role_id: parentRoleId },
    });

    this.logger.log(`Successfully set parent role for ${roleId}: ${parentRoleId}`);
  }

  /**
   * 獲取角色的所有子角色（遞歸）
   */
  async getAllChildRoles(roleId: string): Promise<string[]> {
    const childRoles: string[] = [];
    const visited = new Set<string>();

    const collectChildren = async (currentRoleId: string) => {
      if (visited.has(currentRoleId)) {
        return; // 避免無限循環
      }
      visited.add(currentRoleId);

      const directChildren = await this.prisma.roles.findMany({
        where: { parent_role_id: currentRoleId },
        select: { id: true },
      });

      for (const child of directChildren) {
        childRoles.push(child.id);
        await collectChildren(child.id);
      }
    };

    await collectChildren(roleId);
    return childRoles;
  }

  /**
   * 獲取角色的所有父角色（遞歸）
   */
  async getAllParentRoles(roleId: string): Promise<string[]> {
    const parentRoles: string[] = [];
    const visited = new Set<string>();

    const collectParents = async (currentRoleId: string) => {
      if (visited.has(currentRoleId)) {
        return; // 避免無限循環
      }
      visited.add(currentRoleId);

      const role = await this.prisma.roles.findUnique({
        where: { id: currentRoleId },
        select: { parent_role_id: true },
      });

      if (role?.parent_role_id) {
        parentRoles.push(role.parent_role_id);
        await collectParents(role.parent_role_id);
      }
    };

    await collectParents(roleId);
    return parentRoles;
  }

  /**
   * 獲取繼承的權限
   */
  async getInheritedPermissions(roleId: string): Promise<string[]> {
    const parentRoles = await this.getAllParentRoles(roleId);

    if (parentRoles.length === 0) {
      return [];
    }

    const inheritedPermissions = await this.prisma.role_permissions.findMany({
      where: {
        role_id: { in: parentRoles },
      },
      select: { permission_id: true },
    });

    return [...new Set(inheritedPermissions.map((rp) => rp.permission_id))];
  }

  /**
   * 獲取角色的有效權限（直接權限 + 繼承權限）
   */
  async getEffectivePermissions(roleId: string): Promise<string[]> {
    const [directPermissions, inheritedPermissions] = await Promise.all([
      this.getDirectPermissions(roleId),
      this.getInheritedPermissions(roleId),
    ]);

    return [...new Set([...directPermissions, ...inheritedPermissions])];
  }

  /**
   * 驗證角色層級結構
   */
  async validateHierarchy(): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 檢查循環引用
    const cycles = await this.detectCycles();
    if (cycles.length > 0) {
      errors.push(`檢測到循環引用: ${cycles.join(', ')}`);
    }

    // 檢查孤立角色
    const orphanedRoles = await this.findOrphanedRoles();
    if (orphanedRoles.length > 0) {
      warnings.push(`發現孤立角色: ${orphanedRoles.join(', ')}`);
    }

    // 檢查範圍不兼容
    const scopeConflicts = await this.findScopeConflicts();
    if (scopeConflicts.length > 0) {
      errors.push(`範圍衝突: ${scopeConflicts.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 計算角色層級深度
   */
  private async calculateRoleLevel(roleId: string): Promise<number> {
    const parentRoles = await this.getAllParentRoles(roleId);
    return parentRoles.length;
  }

  /**
   * 檢查是否會造成循環引用
   */
  private async wouldCreateCycle(roleId: string, parentRoleId: string): Promise<boolean> {
    const childRoles = await this.getAllChildRoles(roleId);
    return childRoles.includes(parentRoleId);
  }

  /**
   * 檢查範圍兼容性
   */
  private areScopesCompatible(childScope: RoleScope, parentScope: RoleScope): boolean {
    // 系統角色只能繼承自系統角色
    if (childScope === RoleScope.SYSTEM) {
      return parentScope === RoleScope.SYSTEM;
    }

    // 租戶角色可以繼承自系統角色或租戶角色
    if (childScope === RoleScope.TENANT) {
      return parentScope === RoleScope.SYSTEM || parentScope === RoleScope.TENANT;
    }

    // 工作區角色可以繼承自任何角色
    if (childScope === RoleScope.WORKSPACE) {
      return true;
    }

    return false;
  }

  /**
   * 獲取直接權限
   */
  private async getDirectPermissions(roleId: string): Promise<string[]> {
    const rolePermissions = await this.prisma.role_permissions.findMany({
      where: { role_id: roleId },
      select: { permission_id: true },
    });
    return rolePermissions.map((rp) => rp.permission_id);
  }

  /**
   * 檢測循環引用
   */
  private async detectCycles(): Promise<string[]> {
    const cycles: string[] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const roles = await this.prisma.roles.findMany({
      select: { id: true, parent_role_id: true },
    });

    const hasCycle = (roleId: string): boolean => {
      if (recursionStack.has(roleId)) {
        cycles.push(roleId);
        return true;
      }

      if (visited.has(roleId)) {
        return false;
      }

      visited.add(roleId);
      recursionStack.add(roleId);

      const role = roles.find((r) => r.id === roleId);
      if (role?.parent_role_id && hasCycle(role.parent_role_id)) {
        return true;
      }

      recursionStack.delete(roleId);
      return false;
    };

    for (const role of roles) {
      if (!visited.has(role.id)) {
        hasCycle(role.id);
      }
    }

    return cycles;
  }

  /**
   * 查找孤立角色
   */
  private async findOrphanedRoles(): Promise<string[]> {
    const rolesWithoutParentOrChildren = await this.prisma.roles.findMany({
      where: {
        AND: [
          { parent_role_id: null },
          {
            other_roles: {
              none: {},
            },
          },
        ],
      },
      select: { id: true },
    });

    return rolesWithoutParentOrChildren.map((r) => r.id);
  }

  /**
   * 查找範圍衝突
   */
  private async findScopeConflicts(): Promise<string[]> {
    const conflicts: string[] = [];

    const rolesWithParents = await this.prisma.roles.findMany({
      where: { parent_role_id: { not: null } },
      include: { roles: true },
    });

    for (const role of rolesWithParents) {
      if (role.roles && !this.areScopesCompatible(role.scope, role.roles.scope)) {
        conflicts.push(`${role.id} (${role.scope}) -> ${role.roles.id} (${role.roles.scope})`);
      }
    }

    return conflicts;
  }
}
