{"timestamp": "2025-06-21T09:30:45.591Z", "version": "unknown", "mode": "sync", "scanResult": {"permissions": [{"action": "access", "subject": "Workspace", "description": "從前端檔案 permission.guard.ts 掃描", "isSystemDefined": false, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/router/guards/permission.guard.ts", "lineNumber": 116, "name": "存取工作區", "zone": "workspace", "scope": "WORKSPACE", "category": "workspace_settings"}, {"action": "create", "subject": "Permission", "description": "允許建立新的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 119, "name": "建立權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Permission", "description": "允許刪除權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 172, "name": "刪除權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "Permission", "description": "完整管理權限的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 220, "name": "管理權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Permission", "description": "允許查看系統權限定義和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/roles.controller.ts", "lineNumber": 67, "name": "查看權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Permission", "description": "允許修改現有的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 143, "name": "修改權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Role", "description": "允許建立新的系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/roles.controller.ts", "lineNumber": 154, "name": "建立角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Role", "description": "允許刪除系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/roles.controller.ts", "lineNumber": 208, "name": "刪除角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Role", "description": "允許查看系統角色定義和權限分配", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/roles.controller.ts", "lineNumber": 545, "name": "查看角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Role", "description": "允許修改角色的權限分配和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/roles.controller.ts", "lineNumber": 232, "name": "修改角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "SystemUser", "description": "允許查看系統用戶列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/casl/ability/casl-ability.factory.spec.ts", "lineNumber": 102, "name": "查看系統用戶", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "TenantUser", "description": "完整管理租戶內用戶的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/casl/ability/casl-ability.factory.spec.ts", "lineNumber": 148, "name": "管理租戶用戶", "zone": "admin", "category": "user_management", "scope": "TENANT"}, {"action": "manage", "subject": "Tenant", "description": "完整管理租戶的建立、修改、刪除等所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/router/guards/permission.guard.ts", "lineNumber": 104, "name": "管理租戶", "zone": "admin", "category": "tenant_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Tenant", "description": "允許查看租戶列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/casl/ability/casl-ability.factory.spec.ts", "lineNumber": 104, "name": "查看租戶", "zone": "admin", "category": "tenant_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Workspace", "description": "允許為租戶建立新的工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 245, "name": "建立工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "delete", "subject": "Workspace", "description": "允許刪除工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 108, "name": "刪除工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "read", "subject": "Workspace", "description": "允許查看工作區列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 206, "name": "查看工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "update", "subject": "Workspace", "description": "允許修改工作區的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 190, "name": "修改工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "access", "subject": "AdminPanel", "description": "允許登入和存取系統管理後台介面", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/router/guards/permission.guard.ts", "lineNumber": 84, "name": "存取管理後台", "zone": "admin", "category": "system_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Dashboard", "description": "允許查看系統管理儀表板的統計資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 185, "name": "查看管理儀表板", "zone": "admin", "category": "system_management", "scope": "SYSTEM"}, {"action": "read", "subject": "DashboardActiveUsers", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 123, "name": "查看DashboardActiveUsers", "zone": "admin", "scope": "GLOBAL", "category": "system_management"}, {"action": "read", "subject": "DashboardActivity", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 186, "name": "查看DashboardActivity", "zone": "admin", "scope": "GLOBAL", "category": "system_management"}, {"action": "read", "subject": "DashboardRecentOrders", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 88, "name": "查看DashboardRecentOrders", "zone": "admin", "scope": "GLOBAL", "category": "system_management"}, {"action": "read", "subject": "DashboardRecentTenants", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 59, "name": "查看DashboardRecentTenants", "zone": "admin", "scope": "TENANT", "category": "system_management"}, {"action": "read", "subject": "DashboardRevenue", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 148, "name": "查看DashboardRevenue", "zone": "admin", "scope": "GLOBAL", "category": "system_management"}, {"action": "read", "subject": "DashboardStats", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 33, "name": "查看DashboardStats", "zone": "admin", "scope": "GLOBAL", "category": "system_management"}, {"action": "delete", "subject": "SystemUser", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/casl/ability/casl-ability.factory.spec.ts", "lineNumber": 103, "name": "刪除SystemUser", "zone": "admin", "scope": "SYSTEM", "category": "system_management"}, {"action": "create", "subject": "Plan", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/plans/plans.controller.ts", "lineNumber": 23, "name": "建立訂閱方案", "zone": "admin", "scope": "GLOBAL", "category": "subscription_management"}, {"action": "delete", "subject": "Plan", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/plans/plans.controller.ts", "lineNumber": 63, "name": "刪除訂閱方案", "zone": "admin", "scope": "GLOBAL", "category": "subscription_management"}, {"action": "read", "subject": "Plan", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/plans/plans.controller.ts", "lineNumber": 43, "name": "查看訂閱方案", "zone": "admin", "scope": "GLOBAL", "category": "subscription_management"}, {"action": "update", "subject": "Plan", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/plans/plans.controller.ts", "lineNumber": 53, "name": "修改訂閱方案", "zone": "admin", "scope": "GLOBAL", "category": "subscription_management"}, {"action": "Action.MANAGE", "subject": "Role", "description": "從 can() 方法呼叫掃描 (字串字面量)", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/role-assignment.controller.ts", "lineNumber": 389, "name": "Action.MANAGE角色", "zone": "admin", "scope": "GLOBAL", "category": "role_management"}, {"action": "Action.READ", "subject": "Role", "description": "從 can() 方法呼叫掃描 (字串字面量)", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/role-assignment.controller.ts", "lineNumber": 374, "name": "Action.READ角色", "zone": "admin", "scope": "GLOBAL", "category": "role_management"}, {"action": "Actions.MANAGE", "subject": "all", "description": "從 can() 方法呼叫掃描 (字串字面量)", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 184, "name": "Actions.MANAGEall", "zone": "admin", "scope": "GLOBAL", "category": "other"}, {"action": "action", "subject": "subjectType", "description": "從 can() 方法呼叫掃描 (字串字面量)", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/casl/utils/permission.utils.ts", "lineNumber": 48, "name": "actionsubjectType", "zone": "admin", "scope": "GLOBAL", "category": "other"}, {"action": "create", "subject": "User", "description": "允許邀請新使用者加入工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 36, "name": "建立使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "delete", "subject": "User", "description": "允許從工作區移除使用者", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 81, "name": "刪除使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "User", "description": "允許查看工作區內的使用者列表", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 59, "name": "查看使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "update", "subject": "User", "description": "允許修改工作區使用者的資料和權限", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 67, "name": "修改使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "LineMessageLog", "description": "允許查看LINE機器人的訊息交互日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/message-log.controller.ts", "lineNumber": 27, "name": "查看LINE訊息日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "read", "subject": "SystemLog", "description": "允許查看系統操作和稽核日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-logs/system-logs.controller.ts", "lineNumber": 36, "name": "查看系統日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Comment", "description": "允許在專案、任務和檔案上新增評論", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 28, "name": "新增評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "Comment", "description": "允許刪除評論（自己的或管理權限）", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 90, "name": "刪除評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "Comment", "description": "允許查看專案、任務和檔案的評論", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 63, "name": "查看評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "update", "subject": "Comment", "description": "允許修改自己的評論內容", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 71, "name": "修改評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "CommentReaction", "description": "允許對評論新增反應和表情符號", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 98, "name": "新增評論反應", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "CommentReaction", "description": "允許移除自己的評論反應", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 117, "name": "移除評論反應", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "FilePermission", "description": "允許為檔案設定使用者存取權限", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 187, "name": "設定檔案權限", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "FileShare", "description": "允許建立檔案分享連結", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 228, "name": "建立檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "FileShare", "description": "允許刪除檔案分享連結", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 249, "name": "刪除檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "FileShare", "description": "允許查看檔案的分享連結和分享設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 239, "name": "查看檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "SharedFile", "description": "允許上傳新檔案到工作區進行分享", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 46, "name": "上傳共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "SharedFile", "description": "允許刪除工作區內的共享檔案", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 104, "name": "刪除共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "SharedFile", "description": "允許查看工作區內的共享檔案列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 83, "name": "查看共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "update", "subject": "SharedFile", "description": "允許修改共享檔案的資訊和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 93, "name": "修改共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "ai_bots", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/bots/ai-bots.controller.ts", "lineNumber": 66, "name": "建立ai_bots", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "delete", "subject": "ai_bots", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/bots/ai-bots.controller.ts", "lineNumber": 84, "name": "刪除ai_bots", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "read", "subject": "ai_bots", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/bots/ai-bots.controller.ts", "lineNumber": 161, "name": "查看ai_bots", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "update", "subject": "ai_bots", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/bots/ai-bots.controller.ts", "lineNumber": 187, "name": "修改ai_bots", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "create", "subject": "ai_models", "description": "允許添加新的 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/models/ai-models.controller.ts", "lineNumber": 45, "name": "建立 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "ai_models", "description": "允許刪除 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/models/ai-models.controller.ts", "lineNumber": 59, "name": "刪除 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "ai_models", "description": "完整管理 AI 模型的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/models/ai-models.controller.ts", "lineNumber": 83, "name": "管理 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "read", "subject": "ai_models", "description": "允許查看 AI 模型列表和詳細配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/models/ai-models.controller.ts", "lineNumber": 38, "name": "查看 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "update", "subject": "ai_models", "description": "允許修改 AI 模型的配置和參數", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/models/ai-models.controller.ts", "lineNumber": 66, "name": "修改 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "create", "subject": "ai_tools", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/tools/ai-tools.controller.ts", "lineNumber": 69, "name": "建立ai_tools", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "delete", "subject": "ai_tools", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/tools/ai-tools.controller.ts", "lineNumber": 97, "name": "刪除ai_tools", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "read", "subject": "ai_tools", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/tools/ai-tools.controller.ts", "lineNumber": 60, "name": "查看ai_tools", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "update", "subject": "ai_tools", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/tools/ai-tools.controller.ts", "lineNumber": 82, "name": "修改ai_tools", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "create", "subject": "LineBot", "description": "允許建立新的 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 120, "name": "建立 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "delete", "subject": "LineBot", "description": "允許刪除 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 160, "name": "刪除 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "read", "subject": "LineBot", "description": "允許查看 LINE 機器人配置和狀態", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 219, "name": "查看 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "update", "subject": "LineBot", "description": "允許修改 LINE 機器人的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 182, "name": "修改 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "execute", "subject": "ai_bots", "description": "允許使用和執行 AI 助理功能", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/bots/ai-bots.controller.ts", "lineNumber": 139, "name": "執行 AI 助理", "zone": "workspace", "category": "ai_features", "scope": "WORKSPACE"}], "stats": {"totalFiles": 876, "scannedFiles": 875, "filesWithPermissions": 21, "totalPermissions": 264}, "errors": []}, "syncResult": {"created": 0, "updated": 0, "deprecated": 0, "errors": 0, "details": {"createdPermissions": [], "updatedPermissions": [], "deprecatedPermissions": [], "errorMessages": []}}, "summary": {"total": 73, "byScope": {"GLOBAL": 21, "SYSTEM": 22, "TENANT": 10, "WORKSPACE": 20}, "byCategory": {"other": 2, "user_management": 11, "system_management": 9, "tenant_management": 6, "member_management": 4, "log_management": 2, "ai_management": 17, "subscription_management": 4, "collaboration": 14, "role_management": 2, "ai_features": 1, "workspace_settings": 1}, "bySubject": {"all": 1, "SystemUser": 2, "Tenant": 2, "TenantUser": 1, "User": 4, "subjectType": 1, "SystemLog": 1, "ai_bots": 5, "Dashboard": 1, "DashboardStats": 1, "DashboardRecentTenants": 1, "DashboardRecentOrders": 1, "DashboardActiveUsers": 1, "DashboardRevenue": 1, "DashboardActivity": 1, "Permission": 5, "Plan": 4, "Workspace": 5, "Comment": 4, "CommentReaction": 2, "SharedFile": 4, "FilePermission": 1, "FileShare": 3, "LineBot": 4, "LineMessageLog": 1, "Role": 6, "ai_models": 5, "ai_tools": 4, "AdminPanel": 1}}, "permissions": [{"action": "access", "subject": "Workspace", "description": "從前端檔案 permission.guard.ts 掃描", "isSystemDefined": false, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/router/guards/permission.guard.ts", "lineNumber": 116, "name": "存取工作區", "zone": "workspace", "scope": "WORKSPACE", "category": "workspace_settings"}, {"action": "create", "subject": "Permission", "description": "允許建立新的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 119, "name": "建立權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Permission", "description": "允許刪除權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 172, "name": "刪除權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "Permission", "description": "完整管理權限的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 220, "name": "管理權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Permission", "description": "允許查看系統權限定義和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/roles.controller.ts", "lineNumber": 67, "name": "查看權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Permission", "description": "允許修改現有的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 143, "name": "修改權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Role", "description": "允許建立新的系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/roles.controller.ts", "lineNumber": 154, "name": "建立角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Role", "description": "允許刪除系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/roles.controller.ts", "lineNumber": 208, "name": "刪除角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Role", "description": "允許查看系統角色定義和權限分配", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/roles.controller.ts", "lineNumber": 545, "name": "查看角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Role", "description": "允許修改角色的權限分配和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/roles.controller.ts", "lineNumber": 232, "name": "修改角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "SystemUser", "description": "允許查看系統用戶列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/casl/ability/casl-ability.factory.spec.ts", "lineNumber": 102, "name": "查看系統用戶", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "TenantUser", "description": "完整管理租戶內用戶的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/casl/ability/casl-ability.factory.spec.ts", "lineNumber": 148, "name": "管理租戶用戶", "zone": "admin", "category": "user_management", "scope": "TENANT"}, {"action": "manage", "subject": "Tenant", "description": "完整管理租戶的建立、修改、刪除等所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/router/guards/permission.guard.ts", "lineNumber": 104, "name": "管理租戶", "zone": "admin", "category": "tenant_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Tenant", "description": "允許查看租戶列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/casl/ability/casl-ability.factory.spec.ts", "lineNumber": 104, "name": "查看租戶", "zone": "admin", "category": "tenant_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Workspace", "description": "允許為租戶建立新的工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 245, "name": "建立工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "delete", "subject": "Workspace", "description": "允許刪除工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 108, "name": "刪除工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "read", "subject": "Workspace", "description": "允許查看工作區列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 206, "name": "查看工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "update", "subject": "Workspace", "description": "允許修改工作區的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 190, "name": "修改工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "access", "subject": "AdminPanel", "description": "允許登入和存取系統管理後台介面", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/router/guards/permission.guard.ts", "lineNumber": 84, "name": "存取管理後台", "zone": "admin", "category": "system_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Dashboard", "description": "允許查看系統管理儀表板的統計資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 185, "name": "查看管理儀表板", "zone": "admin", "category": "system_management", "scope": "SYSTEM"}, {"action": "read", "subject": "DashboardActiveUsers", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 123, "name": "查看DashboardActiveUsers", "zone": "admin", "scope": "GLOBAL", "category": "system_management"}, {"action": "read", "subject": "DashboardActivity", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 186, "name": "查看DashboardActivity", "zone": "admin", "scope": "GLOBAL", "category": "system_management"}, {"action": "read", "subject": "DashboardRecentOrders", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 88, "name": "查看DashboardRecentOrders", "zone": "admin", "scope": "GLOBAL", "category": "system_management"}, {"action": "read", "subject": "DashboardRecentTenants", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 59, "name": "查看DashboardRecentTenants", "zone": "admin", "scope": "TENANT", "category": "system_management"}, {"action": "read", "subject": "DashboardRevenue", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 148, "name": "查看DashboardRevenue", "zone": "admin", "scope": "GLOBAL", "category": "system_management"}, {"action": "read", "subject": "DashboardStats", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 33, "name": "查看DashboardStats", "zone": "admin", "scope": "GLOBAL", "category": "system_management"}, {"action": "delete", "subject": "SystemUser", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/casl/ability/casl-ability.factory.spec.ts", "lineNumber": 103, "name": "刪除SystemUser", "zone": "admin", "scope": "SYSTEM", "category": "system_management"}, {"action": "create", "subject": "Plan", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/plans/plans.controller.ts", "lineNumber": 23, "name": "建立訂閱方案", "zone": "admin", "scope": "GLOBAL", "category": "subscription_management"}, {"action": "delete", "subject": "Plan", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/plans/plans.controller.ts", "lineNumber": 63, "name": "刪除訂閱方案", "zone": "admin", "scope": "GLOBAL", "category": "subscription_management"}, {"action": "read", "subject": "Plan", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/plans/plans.controller.ts", "lineNumber": 43, "name": "查看訂閱方案", "zone": "admin", "scope": "GLOBAL", "category": "subscription_management"}, {"action": "update", "subject": "Plan", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/plans/plans.controller.ts", "lineNumber": 53, "name": "修改訂閱方案", "zone": "admin", "scope": "GLOBAL", "category": "subscription_management"}, {"action": "Action.MANAGE", "subject": "Role", "description": "從 can() 方法呼叫掃描 (字串字面量)", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/role-assignment.controller.ts", "lineNumber": 389, "name": "Action.MANAGE角色", "zone": "admin", "scope": "GLOBAL", "category": "role_management"}, {"action": "Action.READ", "subject": "Role", "description": "從 can() 方法呼叫掃描 (字串字面量)", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/controllers/role-assignment.controller.ts", "lineNumber": 374, "name": "Action.READ角色", "zone": "admin", "scope": "GLOBAL", "category": "role_management"}, {"action": "Actions.MANAGE", "subject": "all", "description": "從 can() 方法呼叫掃描 (字串字面量)", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 184, "name": "Actions.MANAGEall", "zone": "admin", "scope": "GLOBAL", "category": "other"}, {"action": "action", "subject": "subjectType", "description": "從 can() 方法呼叫掃描 (字串字面量)", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/casl/utils/permission.utils.ts", "lineNumber": 48, "name": "actionsubjectType", "zone": "admin", "scope": "GLOBAL", "category": "other"}, {"action": "create", "subject": "User", "description": "允許邀請新使用者加入工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 36, "name": "建立使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "delete", "subject": "User", "description": "允許從工作區移除使用者", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 81, "name": "刪除使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "User", "description": "允許查看工作區內的使用者列表", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 59, "name": "查看使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "update", "subject": "User", "description": "允許修改工作區使用者的資料和權限", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 67, "name": "修改使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "LineMessageLog", "description": "允許查看LINE機器人的訊息交互日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/message-log.controller.ts", "lineNumber": 27, "name": "查看LINE訊息日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "read", "subject": "SystemLog", "description": "允許查看系統操作和稽核日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-logs/system-logs.controller.ts", "lineNumber": 36, "name": "查看系統日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Comment", "description": "允許在專案、任務和檔案上新增評論", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 28, "name": "新增評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "Comment", "description": "允許刪除評論（自己的或管理權限）", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 90, "name": "刪除評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "Comment", "description": "允許查看專案、任務和檔案的評論", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 63, "name": "查看評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "update", "subject": "Comment", "description": "允許修改自己的評論內容", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 71, "name": "修改評論", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "CommentReaction", "description": "允許對評論新增反應和表情符號", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 98, "name": "新增評論反應", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "CommentReaction", "description": "允許移除自己的評論反應", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", "lineNumber": 117, "name": "移除評論反應", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "FilePermission", "description": "允許為檔案設定使用者存取權限", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 187, "name": "設定檔案權限", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "FileShare", "description": "允許建立檔案分享連結", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 228, "name": "建立檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "FileShare", "description": "允許刪除檔案分享連結", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 249, "name": "刪除檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "FileShare", "description": "允許查看檔案的分享連結和分享設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 239, "name": "查看檔案分享", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "SharedFile", "description": "允許上傳新檔案到工作區進行分享", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 46, "name": "上傳共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "delete", "subject": "SharedFile", "description": "允許刪除工作區內的共享檔案", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 104, "name": "刪除共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "read", "subject": "SharedFile", "description": "允許查看工作區內的共享檔案列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 83, "name": "查看共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "update", "subject": "SharedFile", "description": "允許修改共享檔案的資訊和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", "lineNumber": 93, "name": "修改共享檔案", "zone": "workspace", "category": "collaboration", "scope": "WORKSPACE"}, {"action": "create", "subject": "ai_bots", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/bots/ai-bots.controller.ts", "lineNumber": 66, "name": "建立ai_bots", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "delete", "subject": "ai_bots", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/bots/ai-bots.controller.ts", "lineNumber": 84, "name": "刪除ai_bots", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "read", "subject": "ai_bots", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/bots/ai-bots.controller.ts", "lineNumber": 161, "name": "查看ai_bots", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "update", "subject": "ai_bots", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/bots/ai-bots.controller.ts", "lineNumber": 187, "name": "修改ai_bots", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "create", "subject": "ai_models", "description": "允許添加新的 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/models/ai-models.controller.ts", "lineNumber": 45, "name": "建立 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "ai_models", "description": "允許刪除 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/models/ai-models.controller.ts", "lineNumber": 59, "name": "刪除 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "ai_models", "description": "完整管理 AI 模型的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/models/ai-models.controller.ts", "lineNumber": 83, "name": "管理 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "read", "subject": "ai_models", "description": "允許查看 AI 模型列表和詳細配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/models/ai-models.controller.ts", "lineNumber": 38, "name": "查看 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "update", "subject": "ai_models", "description": "允許修改 AI 模型的配置和參數", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/models/ai-models.controller.ts", "lineNumber": 66, "name": "修改 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "create", "subject": "ai_tools", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/tools/ai-tools.controller.ts", "lineNumber": 69, "name": "建立ai_tools", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "delete", "subject": "ai_tools", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/tools/ai-tools.controller.ts", "lineNumber": 97, "name": "刪除ai_tools", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "read", "subject": "ai_tools", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/tools/ai-tools.controller.ts", "lineNumber": 60, "name": "查看ai_tools", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "update", "subject": "ai_tools", "description": "從權限常數引用掃描", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/configuration/tools/ai-tools.controller.ts", "lineNumber": 82, "name": "修改ai_tools", "zone": "admin", "scope": "GLOBAL", "category": "ai_management"}, {"action": "create", "subject": "LineBot", "description": "允許建立新的 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 120, "name": "建立 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "delete", "subject": "LineBot", "description": "允許刪除 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 160, "name": "刪除 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "read", "subject": "LineBot", "description": "允許查看 LINE 機器人配置和狀態", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 219, "name": "查看 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "update", "subject": "LineBot", "description": "允許修改 LINE 機器人的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 182, "name": "修改 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "execute", "subject": "ai_bots", "description": "允許使用和執行 AI 助理功能", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/ai/models/bots/ai-bots.controller.ts", "lineNumber": 139, "name": "執行 AI 助理", "zone": "workspace", "category": "ai_features", "scope": "WORKSPACE"}]}