import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { LineBotController } from './controllers/line-bot.controller';
import { LineBotService } from './services/line-bot.service';
import { GroupVerificationController as LineGroupVerificationController } from './controllers/group-verification.controller';
import { GroupVerificationService as LineGroupVerificationService } from './services/group-verification.service';
import { LineWebhookValidationMiddleware } from './middleware/line-webhook-validation.middleware';
import { EncryptionModule } from '@/modules/core/encryption/encryption.module';
import { CaslModule } from '@/casl/casl.module';
import { MessageLogService } from './services/message-log.service';
import { CommonModule } from '@/common/common.module';
import { SettingsModule } from '../settings/settings.module';
import { LineLoginConfigController } from './controllers/line-login-config.controller';
import { LineLoginConfigService } from './services/line-login-config.service';
import { AuthModule } from '@/modules/core/auth/auth.module';
import { LineUsersService } from './services/line-users.service';

@Module({
  imports: [PrismaModule, EncryptionModule, CaslModule, SettingsModule, AuthModule, CommonModule],
  controllers: [LineBotController, LineGroupVerificationController, LineLoginConfigController],
  providers: [
    LineBotService,
    LineGroupVerificationService,
    MessageLogService,
    LineLoginConfigService,
    LineUsersService,
  ],
  exports: [
    LineBotService,
    LineGroupVerificationService,
    MessageLogService,
    LineLoginConfigService,
    LineUsersService,
  ],
})
export class LineModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LineWebhookValidationMiddleware).forRoutes({
      path: 'admin/line-bots/callback/:botId',
      method: RequestMethod.POST,
    });
  }
}
