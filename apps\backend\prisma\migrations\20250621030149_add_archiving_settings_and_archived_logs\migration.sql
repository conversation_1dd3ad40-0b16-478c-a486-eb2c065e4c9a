-- CreateTable
CREATE TABLE "archived_audit_logs" (
    "id" TEXT NOT NULL,
    "original_log_id" TEXT NOT NULL,
    "level" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "stack" TEXT,
    "path" TEXT,
    "method" TEXT,
    "user_id" TEXT,
    "ip" TEXT,
    "original_created_at" TIMESTAMP(3) NOT NULL,
    "action" TEXT,
    "details" JSONB,
    "error_message" TEXT,
    "status" TEXT,
    "target_resource" TEXT,
    "target_resource_id" TEXT,
    "tenant_id" TEXT,
    "archived_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "archive_batch_id" TEXT,
    "storage_location" TEXT,
    "archive_format" TEXT NOT NULL DEFAULT 'json',
    "compressed" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "archived_audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "archived_audit_logs_tenant_id_idx" ON "archived_audit_logs"("tenant_id");

-- CreateIndex
CREATE INDEX "archived_audit_logs_user_id_idx" ON "archived_audit_logs"("user_id");

-- CreateIndex
CREATE INDEX "archived_audit_logs_action_idx" ON "archived_audit_logs"("action");

-- CreateIndex
CREATE INDEX "archived_audit_logs_target_resource_idx" ON "archived_audit_logs"("target_resource");

-- CreateIndex
CREATE INDEX "archived_audit_logs_original_created_at_idx" ON "archived_audit_logs"("original_created_at");

-- CreateIndex
CREATE INDEX "archived_audit_logs_archived_at_idx" ON "archived_audit_logs"("archived_at");

-- CreateIndex
CREATE INDEX "archived_audit_logs_archive_batch_id_idx" ON "archived_audit_logs"("archive_batch_id");

-- CreateIndex
CREATE INDEX "archived_audit_logs_tenant_id_original_created_at_idx" ON "archived_audit_logs"("tenant_id", "original_created_at");

-- CreateIndex
CREATE INDEX "archived_audit_logs_tenant_id_archived_at_idx" ON "archived_audit_logs"("tenant_id", "archived_at");

-- CreateIndex
CREATE INDEX "system_logs_tenant_id_idx" ON "system_logs"("tenant_id");

-- CreateIndex
CREATE INDEX "system_logs_user_id_idx" ON "system_logs"("user_id");

-- CreateIndex
CREATE INDEX "system_logs_action_idx" ON "system_logs"("action");

-- CreateIndex
CREATE INDEX "system_logs_target_resource_idx" ON "system_logs"("target_resource");

-- CreateIndex
CREATE INDEX "system_logs_status_idx" ON "system_logs"("status");

-- CreateIndex
CREATE INDEX "system_logs_level_idx" ON "system_logs"("level");

-- CreateIndex
CREATE INDEX "system_logs_created_at_idx" ON "system_logs"("created_at");

-- CreateIndex
CREATE INDEX "system_logs_tenant_id_created_at_idx" ON "system_logs"("tenant_id", "created_at");

-- CreateIndex
CREATE INDEX "system_logs_tenant_id_action_idx" ON "system_logs"("tenant_id", "action");

-- CreateIndex
CREATE INDEX "system_logs_tenant_id_user_id_created_at_idx" ON "system_logs"("tenant_id", "user_id", "created_at");
