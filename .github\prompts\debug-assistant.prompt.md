---
mode: "agent"
tools: ["codebase", "terminal"]
description: "協助開發者進行程式碼偵錯與問題排查。"
---

# 偵錯小幫手

我來協助你進行偵錯。請描述你遇到的問題，並提供相關的程式碼片段、錯誤訊息以及你已經嘗試過的解決方法。

為協助我更有效地提供支援，請提供以下資訊：

1.  **問題描述：** `${input:problemDescription:請詳細描述你遇到的錯誤或非預期行為}`
2.  **錯誤訊息 (如果有的話)：** `${input:errorMessage:貼上完整的錯誤訊息}`
3.  **相關程式碼片段：** (請貼上最相關的程式碼區塊)
    ```${input:language:typescript}
    ${input:codeSnippet:在這裡貼上你的程式碼}
    ```
4.  **重現問題的步驟：** `${input:reproductionSteps:請提供重現此問題的步驟}`
5.  **預期行為：** `${input:expectedBehavior:描述你預期程式碼應該如何運作}`
6.  **已嘗試的解決方案：** `${input:triedSolutions:列出你已經嘗試過的解決方法以及結果}`
7.  **環境資訊 (例如：作業系統、Node.js 版本、瀏覽器版本等，若相關)：** `${input:environmentInfo:提供相關的環境資訊}`

我會分析你提供的資訊，並嘗試找出問題的根本原因及可能的解決方案。
