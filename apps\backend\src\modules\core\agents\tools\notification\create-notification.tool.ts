import { z } from 'zod';
import { MessageCenterService } from '@/modules/workspace/message-center/services/message-center.service';
import { NotificationType, NotificationPriority } from '@prisma/client';

// Zod schema for creating notifications
const createNotificationSchema = z.object({
  title: z.string().min(1).describe('The notification title'),
  message: z.string().min(1).describe('The notification message content'),
  type: z.nativeEnum(NotificationType).optional().default(NotificationType.INFO).describe('Notification type (INFO, WARNING, ERROR, SUCCESS)'),
  priority: z.nativeEnum(NotificationPriority).optional().default(NotificationPriority.NORMAL).describe('Notification priority (LOW, NORMAL, HIGH, URGENT)'),
  recipient_id: z.string().min(1).describe('The ID of the user who will receive the notification'),
  recipient_type: z.string().optional().default('user').describe('The type of recipient (e.g., "user", "group")'),
  entity_type: z.string().optional().describe('The type of entity this relates to (e.g., "project", "task")'),
  entity_id: z.string().optional().describe('The ID of the related entity'),
  action_url: z.string().url().optional().describe('URL for action button in the notification'),
  workspace_id: z.string().optional().describe('The workspace ID if notification is workspace-specific'),
  expires_at: z.string().optional().describe('When the notification should expire (ISO 8601 format)'),
});

/**
 * Creates a CreateNotificationTool using a simple function-based approach
 * @param messageCenterService - The message center service instance
 * @param tenantId - The tenant ID for isolation
 * @returns A simple tool object compatible with LangChain
 */
export function createNotificationTool(
  messageCenterService: MessageCenterService,
  tenantId: string,
) {
  return {
    name: 'create_notification',
    description: `Create a system notification for a user.

This tool allows the AI agent to create notifications that appear in the user's notification center.

Required fields:
- title: The notification title
- message: The notification message content
- recipient_id: The ID of the user who will receive the notification

Optional fields:
- type: Notification type (INFO, WARNING, ERROR, SUCCESS) - defaults to INFO
- priority: Notification priority (LOW, NORMAL, HIGH, URGENT) - defaults to NORMAL
- recipient_type: The type of recipient - defaults to "user"
- entity_type: The type of entity this relates to (e.g., "project", "task")
- entity_id: The ID of the related entity
- action_url: URL for action button in the notification
- workspace_id: The workspace ID if notification is workspace-specific
- expires_at: When the notification should expire (ISO 8601 format)`,
    schema: createNotificationSchema,
    call: async (input: any) => {
      try {
        const notification = await messageCenterService.createNotification(
          {
            title: input.title,
            message: input.message,
            type: input.type,
            priority: input.priority,
            recipient_id: input.recipient_id,
            recipient_type: input.recipient_type,
            entity_type: input.entity_type,
            entity_id: input.entity_id,
            action_url: input.action_url,
            workspace_id: input.workspace_id,
            expires_at: input.expires_at ? new Date(input.expires_at) : undefined,
          },
          tenantId,
        );

        return `Successfully created notification "${input.title}" for ${input.recipient_type} ${input.recipient_id}. Notification ID: ${notification.id}`;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        throw new Error(`Notification creation failed: ${errorMessage}`);
      }
    },
  };
}

// Export the schema for use in other tools or tests
export { createNotificationSchema }; 