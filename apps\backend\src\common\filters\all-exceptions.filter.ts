import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  Injectable,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { SystemLogService } from '../services/system-log.service';
import { HttpAdapterHost } from '@nestjs/core';

function structuredLog({
  level,
  context,
  action,
  message,
  user,
  meta,
}: {
  level: string;
  context: string;
  action: string;
  message: string;
  user?: any;
  meta?: any;
}) {
  const log = {
    timestamp: new Date().toISOString(),
    level,
    context,
    action,
    message,
    ...(user ? { user } : {}),
    ...(meta ? { meta } : {}),
  };
  console.log(JSON.stringify(log));
}

@Injectable()
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(
    private readonly httpAdapterHost: HttpAdapterHost,
    private readonly systemLogService: SystemLogService,
  ) {}

  async catch(exception: any, host: ArgumentsHost) {
    const { httpAdapter } = this.httpAdapterHost;

    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const user = request.user as any;

    const httpStatus =
      exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;

    const responseBody = {
      statusCode: httpStatus,
      timestamp: new Date().toISOString(),
      path: httpAdapter.getRequestUrl(ctx.getRequest()),
      message: exception.message,
      errors: exception?.response?.errors || [],
    };

    // 只有在非 404/401/403 錯誤時才記錄日誌，以減少噪音
    if (httpStatus >= 500 || (httpStatus >= 400 && ![401, 403, 404].includes(httpStatus))) {
      try {
        await this.systemLogService.logError({
          level: 'error',
          message: `[API Error] ${exception.message}`,
          stack: exception.stack,
          path: request.url,
          method: request.method,
          userId: user ? user.id : undefined,
          ip: request.ip,
          details: {
            // 包含額外的請求和使用者資訊
            headers: request.headers,
            body: request.body,
            user: user ? { id: user.id, email: user.email } : undefined,
          },
        });
      } catch (logError) {
        console.error('--- FAILED TO WRITE TO SYSTEM LOG ---');
        console.error(logError);
        console.error('--- ORIGINAL EXCEPTION ---');
        console.error(exception);
        console.error('------------------------------------');
      }
    }

    httpAdapter.reply(ctx.getResponse(), responseBody, httpStatus);
  }
}
