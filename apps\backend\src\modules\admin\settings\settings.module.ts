import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SettingsController } from './settings.controller';
import { SettingsService } from './settings.service';
import { PrismaModule } from '../../core/prisma/prisma.module';
import { CaslModule } from '../../../casl/casl.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { MailModule } from '../../core/mail/mail.module';

@Module({
  imports: [PrismaModule, CaslModule, EventEmitterModule.forRoot(), MailModule],
  controllers: [SettingsController],
  providers: [SettingsService],
  exports: [SettingsService],
})
export class SettingsModule {}
