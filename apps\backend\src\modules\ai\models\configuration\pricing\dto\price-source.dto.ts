import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUrl, IsOptional } from 'class-validator';

export class CreatePriceSourceDto {
  @ApiProperty({ description: 'AI 供應商標識 (openai, anthropic, gemini)' })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @ApiProperty({ description: '價格來源網址' })
  @IsUrl()
  @IsNotEmpty()
  url: string;
}

export class UpdatePriceSourceDto {
  @ApiPropertyOptional({ description: 'AI 供應商標識 (openai, anthropic, gemini)' })
  @IsString()
  @IsOptional()
  provider?: string;

  @ApiPropertyOptional({ description: '價格來源網址' })
  @IsUrl()
  @IsOptional()
  url?: string;
}

export class PriceSourceResponseDto {
  @ApiProperty({ description: '價格來源 ID' })
  id: string;

  @ApiProperty({ description: 'AI 供應商標識 (openai, anthropic, gemini)' })
  provider: string;

  @ApiProperty({ description: '價格來源網址' })
  url: string;

  @ApiProperty({ description: '建立時間' })
  created_at: Date;

  @ApiProperty({ description: '更新時間' })
  updated_at: Date;
}
