import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PoliciesGuard } from './permission.guard';
import { CaslAbilityFactory } from '../ability/casl-ability.factory';
import { CHECK_POLICIES_KEY } from '../decorators/check-policies.decorator';
import { PermissionCheckerService } from '../services/permission-checker.service';

describe('PoliciesGuard', () => {
  let guard: PoliciesGuard;
  let reflector: Reflector;
  let caslAbilityFactory: CaslAbilityFactory;

  const mockExecutionContext = {
    getHandler: jest.fn(),
    getClass: jest.fn(),
    switchToHttp: jest.fn().mockReturnValue({
      getRequest: jest.fn().mockReturnValue({
        user: {
          sub: 'test-user-id',
          tenant_id: 'test-tenant-id',
          user_type: 'system',
          email: '<EMAIL>',
        },
      }),
    }),
  } as unknown as ExecutionContext;

  const mockCaslAbilityFactory = {
    createForUser: jest.fn().mockResolvedValue({
      can: jest.fn().mockReturnValue(true),
      rules: [{ action: 'read', subject: 'User' }],
    }),
  };

  const mockReflector = {
    get: jest.fn(),
  };

  const mockPermissionCheckerService = {
    checkPermission: jest.fn().mockResolvedValue(true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PoliciesGuard,
        {
          provide: CaslAbilityFactory,
          useValue: mockCaslAbilityFactory,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
        {
          provide: PermissionCheckerService,
          useValue: mockPermissionCheckerService,
        },
      ],
    }).compile();

    guard = module.get<PoliciesGuard>(PoliciesGuard);
    reflector = module.get<Reflector>(Reflector);
    caslAbilityFactory = module.get<CaslAbilityFactory>(CaslAbilityFactory);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('should return true when no policies are defined', async () => {
      // Arrange
      mockReflector.get.mockReturnValue(undefined);

      // Act
      const result = await guard.canActivate(mockExecutionContext);

      // Assert
      expect(result).toBe(true);
      expect(reflector.get).toHaveBeenCalledWith(
        CHECK_POLICIES_KEY,
        mockExecutionContext.getHandler(),
      );
    });

    it('should return true when user has required permissions', async () => {
      // Arrange
      const mockPolicyHandler = jest.fn().mockReturnValue(true);
      mockReflector.get.mockReturnValue([mockPolicyHandler]);

      // Act
      const result = await guard.canActivate(mockExecutionContext);

      // Assert
      expect(result).toBe(true);
      expect(caslAbilityFactory.createForUser).toHaveBeenCalledWith({
        user_id: 'test-user-id',
        tenant_id: 'test-tenant-id',
        user_type: 'system',
      });
      expect(mockPolicyHandler).toHaveBeenCalled();
    });

    it('should return false when user lacks required permissions', async () => {
      // Arrange
      const mockPolicyHandler = jest.fn().mockReturnValue(false);
      mockReflector.get.mockReturnValue([mockPolicyHandler]);

      // Act
      const result = await guard.canActivate(mockExecutionContext);

      // Assert
      expect(result).toBe(false);
      expect(mockPolicyHandler).toHaveBeenCalled();
    });

    it('should handle multiple policy handlers correctly', async () => {
      // Arrange
      const mockPolicyHandler1 = jest.fn().mockReturnValue(true);
      const mockPolicyHandler2 = jest.fn().mockReturnValue(false);
      mockReflector.get.mockReturnValue([mockPolicyHandler1, mockPolicyHandler2]);

      // Act
      const result = await guard.canActivate(mockExecutionContext);

      // Assert
      expect(result).toBe(false); // Should be false if any handler returns false
      expect(mockPolicyHandler1).toHaveBeenCalled();
      expect(mockPolicyHandler2).toHaveBeenCalled();
    });

    it('should return false when user is missing in request', async () => {
      // Arrange
      const mockContextWithoutUser = {
        ...mockExecutionContext,
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue({}), // No user
        }),
      };
      mockReflector.get.mockReturnValue([jest.fn()]);

      // Act
      const result = await guard.canActivate(mockContextWithoutUser as ExecutionContext);

      // Assert
      expect(result).toBe(false);
    });
  });
});
