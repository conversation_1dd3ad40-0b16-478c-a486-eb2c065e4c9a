import { IsString, IsBoolean, Is<PERSON>ptional, IsISO8601, IsEmail, ValidateIf } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateGeneralSettingsDto {
  @ApiProperty({ description: '網站名稱' })
  @IsString()
  siteName: string;

  @ApiProperty({ description: '網站描述' })
  @IsString()
  siteDescription: string;

  @ApiProperty({ description: '預設語言' })
  @IsString()
  defaultLanguage: string;

  @ApiProperty({ description: '預設時區' })
  @IsString()
  timezone: string;

  @ApiProperty({ description: '支援 Email' })
  @IsEmail()
  supportEmail: string;

  @ApiProperty({ description: '維護模式開關' })
  @IsBoolean()
  maintenanceMode: boolean;

  @ApiProperty({ description: '維護開始時間', required: false })
  @IsOptional()
  @ValidateIf((o) => o.maintenanceMode === true)
  @IsISO8601()
  maintenanceStartTime: string | null;

  @ApiProperty({ description: '維護結束時間', required: false })
  @IsOptional()
  @ValidateIf((o) => o.maintenanceMode === true)
  @IsISO8601()
  maintenanceEndTime: string | null;

  @ApiProperty({ description: '維護說明訊息' })
  @IsString()
  @ValidateIf((o) => o.maintenanceMode === true)
  maintenanceMessage: string;

  @ApiProperty({ description: '允許訪問的 IP 列表' })
  @IsString()
  @ValidateIf((o) => o.maintenanceMode === true)
  maintenanceAllowedIps: string;

  @ApiProperty({ description: '是否自動發送維護通知' })
  @IsBoolean()
  @ValidateIf((o) => o.maintenanceMode === true)
  maintenanceNotification: boolean;
}
