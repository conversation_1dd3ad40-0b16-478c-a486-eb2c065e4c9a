import { IsString, IsOptional, <PERSON><PERSON><PERSON>, IsBoolean, IsInt, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { LineBotScope } from './create-line-bot.dto';

export class UpdateLineBotDto {
  @ApiProperty({ description: 'Line Bot 名稱', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Line Bot 描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Line Bot 範圍',
    enum: LineBotScope,
    required: false,
  })
  @IsOptional()
  @IsEnum(LineBotScope)
  scope?: LineBotScope;

  @ApiProperty({ description: '租戶 ID', required: false })
  @IsOptional()
  @IsString()
  tenant_id?: string;

  @ApiProperty({ description: 'Channel Secret', required: false })
  @IsOptional()
  @IsString()
  bot_secret?: string;

  @ApiProperty({ description: 'Channel Access Token', required: false })
  @IsOptional()
  @IsString()
  bot_token?: string;

  @ApiProperty({ description: 'Webhook URL', required: false })
  @IsOptional()
  @IsString()
  webhook_url?: string;

  @ApiProperty({ description: '是否啟用', required: false })
  @IsOptional()
  @IsBoolean()
  is_enabled?: boolean;

  @ApiProperty({
    description: '憑證更新提醒週期（天）',
    required: false,
    type: Number,
    example: 90,
  })
  @IsInt()
  @IsOptional()
  @Min(1)
  token_update_reminder_period_days?: number;
}
