# HorizAI Agent Tools 開發指南

> **版本**: 2.0 (新架構)  
> **最後更新**: 2024年12月  
> **狀態**: ✅ 架構遷移完成

## 📋 目錄

1. [概述](#概述)
2. [新架構概覽](#新架構概覽)
3. [開發環境設置](#開發環境設置)
4. [創建新工具](#創建新工具)
5. [測試指南](#測試指南)
6. [部署與集成](#部署與集成)
7. [最佳實踐](#最佳實踐)
8. [常見問題](#常見問題)
9. [架構遷移記錄](#架構遷移記錄)

## 🎯 概述

HorizAI Agent Tools 是一個基於 NestJS 和 LangChain 的智能代理工具系統，為 AI 代理提供豐富的功能支持。系統已完成從基於類別繼承的舊架構到基於工廠函式的新架構遷移。

### 核心特性

- 🏭 **工廠函式架構**: 靈活的工具創建和配置
- 🔒 **多租戶隔離**: 完整的租戶級別數據隔離
- 🧪 **完整測試覆蓋**: 單元測試、整合測試、功能測試
- 📦 **模組化設計**: 按功能領域組織的清晰架構
- 🔧 **依賴注入**: NestJS 原生 DI 支持
- 🚀 **高效能**: 優化的工具註冊和執行機制

## 🏗️ 新架構概覽

### 架構組件

```
tools/
├── core/                       # 核心基礎設施
│   ├── index.ts               # 統一導出
│   └── types.ts               # 共享類型定義
├── implementations/            # 基礎工具實作
│   ├── file-reader-tools.factory.ts
│   ├── web-search-tools.factory.ts
│   └── index.ts
├── notification/              # 通知相關工具
├── project/                   # 專案相關工具
├── task/                     # 任務相關工具
├── progress/                 # 進度追蹤工具
├── knowledge/                # 知識庫工具
├── tool-registration.service.ts
├── tools.module.ts
└── index.ts                  # 主要導出點
```

### 工廠函式架構

新架構採用工廠函式模式，提供以下優勢：

- **函式式編程**: 純函式，易於測試和理解
- **配置靈活性**: 支援動態配置和條件創建
- **依賴注入**: 與 NestJS DI 系統完美整合
- **型別安全**: 完整的 TypeScript 支持

## 🛠️ 開發環境設置

### 前置需求

- Node.js 18+
- NestJS CLI
- TypeScript 4.9+
- Jest (測試框架)

### 安裝依賴

```bash
# 進入後端目錄
cd apps/backend

# 安裝依賴
npm install

# 運行測試確認環境
npm run test:tools
```

### 開發工具

```bash
# 編譯檢查
npm run build

# 測試覆蓋率
npm run test:cov

# 程式碼品質檢查
npm run lint
```

## 🔨 創建新工具

### 步驟 1: 選擇工具類別

根據功能選擇適當的目錄：

- `implementations/` - 基礎功能工具 (檔案操作、網路搜尋等)
- `project/` - 專案管理相關
- `task/` - 任務處理相關
- `notification/` - 通知系統相關
- `knowledge/` - 知識庫相關
- `progress/` - 進度追蹤相關

### 步驟 2: 定義類型介面

```typescript
// types/my-tool.types.ts
export interface MyToolConfig {
  apiKey?: string;
  timeout?: number;
  retryAttempts?: number;
}

export interface MyToolInput {
  query: string;
  options?: {
    format?: 'json' | 'text';
    maxResults?: number;
  };
}

export interface MyToolOutput {
  success: boolean;
  data: any;
  metadata?: {
    processingTime: number;
    source: string;
  };
}
```

### 步驟 3: 實作工廠函式

```typescript
// my-tool.factory.ts
import { Injectable } from '@nestjs/common';
import { Tool } from 'langchain/tools';
import { MyToolConfig, MyToolInput, MyToolOutput } from './types';

/**
 * 我的工具實作
 */
class MyTool extends Tool {
  name = 'my_tool';
  description = 'This tool does something amazing for the AI agent';

  constructor(
    private readonly config: MyToolConfig,
    private readonly tenantId: string,
    private readonly dependencies: any
  ) {
    super();
  }

  async _call(input: string): Promise<string> {
    try {
      const parsedInput: MyToolInput = JSON.parse(input);
      
      // 驗證輸入
      this.validateInput(parsedInput);
      
      // 執行工具邏輯
      const result = await this.executeLogic(parsedInput);
      
      // 返回結構化結果
      return JSON.stringify(result);
    } catch (error) {
      return JSON.stringify({
        success: false,
        error: error.message,
        code: 'MY_TOOL_ERROR'
      });
    }
  }

  private validateInput(input: MyToolInput): void {
    if (!input.query || typeof input.query !== 'string') {
      throw new Error('Query is required and must be a string');
    }
  }

  private async executeLogic(input: MyToolInput): Promise<MyToolOutput> {
    // 實作具體邏輯
    const data = await this.processQuery(input.query);
    
    return {
      success: true,
      data,
      metadata: {
        processingTime: Date.now(),
        source: 'my_tool'
      }
    };
  }

  private async processQuery(query: string): Promise<any> {
    // 具體業務邏輯
    return { result: `Processed: ${query}` };
  }
}

/**
 * 工廠函式 - 創建工具實例
 */
export function createMyTool(
  config: MyToolConfig,
  tenantId: string,
  dependencies: any
): MyTool {
  return new MyTool(config, tenantId, dependencies);
}

/**
 * 工廠類別 - 用於 DI 容器
 */
@Injectable()
export class MyToolFactory {
  constructor(
    // 注入必要的服務
  ) {}

  create(tenantId: string, config?: Partial<MyToolConfig>): MyTool {
    const defaultConfig: MyToolConfig = {
      timeout: 30000,
      retryAttempts: 3,
    };

    const finalConfig = { ...defaultConfig, ...config };
    
    return createMyTool(
      finalConfig,
      tenantId,
      {
        // 傳入依賴服務
      }
    );
  }
}
```

### 步驟 4: 撰寫測試

```typescript
// my-tool.factory.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { MyToolFactory, createMyTool } from './my-tool.factory';

describe('MyToolFactory', () => {
  let factory: MyToolFactory;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [MyToolFactory],
    }).compile();

    factory = module.get<MyToolFactory>(MyToolFactory);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('create', () => {
    it('應該創建工具實例', () => {
      const tool = factory.create('tenant-123');
      
      expect(tool).toBeDefined();
      expect(tool.name).toBe('my_tool');
      expect(tool.description).toContain('amazing');
    });

    it('應該支援自訂配置', () => {
      const config = { timeout: 60000 };
      const tool = factory.create('tenant-123', config);
      
      expect(tool).toBeDefined();
      // 驗證配置是否正確套用
    });
  });

  describe('工具功能', () => {
    it('應該正確處理有效輸入', async () => {
      const tool = factory.create('tenant-123');
      const input = JSON.stringify({
        query: 'test query',
        options: { format: 'json' }
      });

      const result = await tool._call(input);
      const parsed = JSON.parse(result);

      expect(parsed.success).toBe(true);
      expect(parsed.data).toBeDefined();
    });

    it('應該處理無效輸入', async () => {
      const tool = factory.create('tenant-123');
      const invalidInput = JSON.stringify({});

      const result = await tool._call(invalidInput);
      const parsed = JSON.parse(result);

      expect(parsed.success).toBe(false);
      expect(parsed.error).toContain('Query is required');
    });
  });
});
```

### 步驟 5: 註冊工具

```typescript
// 更新 index.ts
export {
  createMyTool,
  MyToolFactory,
  type MyToolConfig,
  type MyToolInput,
  type MyToolOutput,
} from './my-tool.factory';

// 更新工具註冊表
export enum ModernToolName {
  // ... 現有工具
  MY_TOOL = 'my_tool',
}

export const MODERN_TOOL_REGISTRY = {
  // ... 現有註冊
  [ModernToolName.MY_TOOL]: MyToolFactory,
} as const;
```

## 🧪 測試指南

### 測試類型

1. **單元測試**: 測試個別工具功能
2. **整合測試**: 測試工具與服務的整合
3. **功能測試**: 端到端功能驗證

### 測試最佳實踐

```typescript
describe('工具測試範例', () => {
  // 設置測試環境
  beforeEach(async () => {
    // 初始化測試模組
  });

  // 清理資源
  afterEach(async () => {
    // 清理測試資源
  });

  // 測試工具創建
  it('應該創建工具實例', () => {
    // 測試基本創建功能
  });

  // 測試配置
  it('應該支援配置選項', () => {
    // 測試配置功能
  });

  // 測試正常流程
  it('應該正確處理正常輸入', async () => {
    // 測試正常執行路徑
  });

  // 測試錯誤處理
  it('應該優雅處理錯誤', async () => {
    // 測試錯誤處理機制
  });

  // 測試租戶隔離
  it('應該確保租戶隔離', async () => {
    // 測試多租戶功能
  });
});
```

### 運行測試

```bash
# 運行所有工具測試
npm run test:tools

# 運行特定工具測試
npm run test -- --testPathPattern=my-tool

# 生成測試覆蓋率報告
npm run test:cov
```

## 🚀 部署與集成

### 模組註冊

```typescript
// tools.module.ts
@Module({
  providers: [
    // ... 現有工廠
    MyToolFactory,
  ],
  exports: [
    // ... 現有導出
    MyToolFactory,
  ],
})
export class ToolsModule {}
```

### 服務集成

```typescript
// 在 Agent 服務中使用
@Injectable()
export class AgentService {
  constructor(
    private readonly myToolFactory: MyToolFactory,
    // ... 其他工廠
  ) {}

  async createAgentTools(tenantId: string): Promise<Tool[]> {
    return [
      this.myToolFactory.create(tenantId),
      // ... 其他工具
    ];
  }
}
```

## 📝 最佳實踐

### 1. 錯誤處理

```typescript
// ✅ 良好的錯誤處理
try {
  const result = await this.processData(input);
  return JSON.stringify({ success: true, data: result });
} catch (error) {
  console.error(`MyTool error: ${error.message}`, { tenantId, input });
  return JSON.stringify({
    success: false,
    error: 'Failed to process request',
    code: 'PROCESSING_ERROR'
  });
}
```

### 2. 輸入驗證

```typescript
// ✅ 嚴格的輸入驗證
private validateInput(input: MyToolInput): void {
  if (!input || typeof input !== 'object') {
    throw new Error('Input must be a valid object');
  }
  
  if (!input.query || typeof input.query !== 'string') {
    throw new Error('Query is required and must be a string');
  }
  
  if (input.query.length > 1000) {
    throw new Error('Query too long (max 1000 characters)');
  }
}
```

### 3. 租戶隔離

```typescript
// ✅ 確保租戶隔離
async executeLogic(input: MyToolInput): Promise<MyToolOutput> {
  // 所有資料庫查詢都要包含 tenantId
  const data = await this.dataService.findByTenant(
    this.tenantId,
    input.query
  );
  
  return { success: true, data };
}
```

### 4. 配置管理

```typescript
// ✅ 靈活的配置系統
export interface MyToolConfig {
  // 必要配置
  apiEndpoint: string;
  
  // 可選配置with defaults
  timeout?: number;
  retryAttempts?: number;
  
  // 環境特定配置
  enableDebug?: boolean;
}

const DEFAULT_CONFIG: Partial<MyToolConfig> = {
  timeout: 30000,
  retryAttempts: 3,
  enableDebug: false,
};
```

### 5. 日誌記錄

```typescript
// ✅ 適當的日誌記錄
async _call(input: string): Promise<string> {
  const startTime = Date.now();
  
  try {
    console.log(`MyTool started`, { tenantId: this.tenantId });
    
    const result = await this.executeLogic(parsedInput);
    
    console.log(`MyTool completed`, {
      tenantId: this.tenantId,
      duration: Date.now() - startTime
    });
    
    return JSON.stringify(result);
  } catch (error) {
    console.error(`MyTool failed`, {
      tenantId: this.tenantId,
      error: error.message,
      duration: Date.now() - startTime
    });
    
    throw error;
  }
}
```

## ❓ 常見問題

### Q: 如何處理異步操作？

A: 所有工具方法都是異步的，可以安全使用 async/await：

```typescript
async _call(input: string): Promise<string> {
  const data = await this.asyncOperation();
  return JSON.stringify(data);
}
```

### Q: 如何共享依賴服務？

A: 透過工廠類別的建構函數注入：

```typescript
@Injectable()
export class MyToolFactory {
  constructor(
    private readonly sharedService: SharedService,
  ) {}
  
  create(tenantId: string): MyTool {
    return new MyTool(tenantId, this.sharedService);
  }
}
```

### Q: 如何處理大量數據？

A: 實施分頁和限制機制：

```typescript
private async processLargeDataset(query: string): Promise<any> {
  const limit = 100; // 限制結果數量
  const results = await this.dataService.findWithLimit(
    this.tenantId,
    query,
    limit
  );
  
  if (results.length === limit) {
    return {
      data: results,
      hasMore: true,
      message: 'Results limited to 100 items'
    };
  }
  
  return { data: results, hasMore: false };
}
```

### Q: 如何測試依賴外部 API 的工具？

A: 使用 Mock 和測試雙重：

```typescript
const mockApiService = {
  search: jest.fn().mockResolvedValue({ results: ['test'] }),
};

beforeEach(() => {
  // 替換真實服務
  module.overrideProvider(ApiService)
    .useValue(mockApiService)
    .compile();
});
```

## 📊 架構遷移記錄

### 遷移完成狀態 ✅

- **BaseToolImplementation 移除**: 舊的基礎類別已完全移除
- **工廠函式架構**: 所有工具已遷移到新的工廠函式架構
- **測試覆蓋**: 新增 21 個測試檔案，覆蓋所有主要工具
- **文檔更新**: 完成開發指南和架構文檔

### 已遷移工具

1. ✅ **FileReaderToolsFactory** - 檔案讀取工具
2. ✅ **WebSearchToolsFactory** - 網路搜尋工具
3. ✅ **NotificationToolsFactory** - 通知系統工具
4. ✅ **ProjectInfoToolsFactory** - 專案資訊工具
5. ✅ **CreateTaskToolsFactory** - 任務創建工具
6. ✅ **ProgressUpdateToolsFactory** - 進度更新工具
7. ✅ **KnowledgeBaseToolsFactory** - 知識庫工具

### 測試覆蓋統計

- **總測試檔案**: 21 個
- **覆蓋的工廠**: 7/7 (100%)
- **測試類型**: 單元測試、整合測試、功能測試
- **品質檢查**: 全部通過

## 🔗 相關資源

- [NestJS 文檔](https://docs.nestjs.com/)
- [LangChain Tools 文檔](https://js.langchain.com/docs/modules/agents/tools/)
- [Jest 測試框架](https://jestjs.io/)
- [TypeScript 指南](https://www.typescriptlang.org/docs/)

---

**維護者**: HorizAI 開發團隊  
**更新頻率**: 隨架構變更更新  
**問題回報**: 請提交 Issue 或 PR 