<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import type { ViewProps } from '@/types/components/view.type';
import { Line<PERSON>hart, Bar<PERSON>hart } from '@/components/ui/charts';
import {
  Users,
  Building2,
  Activity,
  TrendingUp,
  Shield,
  ShieldCheck,
  Clock,
  BarChart3,
} from 'lucide-vue-next';
import { adminDashboardService } from '@/services/admin/dashboard.service';
import { useNotification } from '@/composables/shared/useNotification';
import { useAuth, useAbility } from '@horizai/auth';
import { useRouter } from 'vue-router';
import type { DashboardStats, RevenueData, ActivityData } from '@/types/models/admin.model';
import StatsCards from '@/components/admin/dashboard/StatsCards.vue';
import OrderStatsCards from '@/components/admin/dashboard/OrderStatsCards.vue';
import WorkspaceStatsCards from '@/components/admin/dashboard/WorkspaceStatsCards.vue';
import { usePlans } from '@/composables/admin/usePlans';
import PlanStats from '@/components/admin/dashboard/PlanStats.vue';

defineOptions({
  name: 'AdminDashboardView',
});

const props = withDefaults(defineProps<ViewProps>(), {
  title: '管理員儀表板',
  description: '系統概覽與重要指標',
});

const router = useRouter();
const notification = useNotification();
const auth = useAuth();
const { can } = useAbility();
const { planStats, fetchPlans } = usePlans();

// 頁面狀態
const isLoading = ref(false);
const stats = ref<DashboardStats>({
  // 使用者相關
  totalUsers: 0,
  activeUsers: 0,
  adminCount: 0,
  recentLogins: 0,

  // 租戶相關
  totalTenants: 0,
  tenantCount: 0,
  tenantGrowthRate: 0,

  // 工作區相關
  totalWorkspaces: 0,
  activeWorkspaces: 0,
  monthlyNewWorkspaces: 0,
  monthlyWorkspaceGrowth: 0,
  storageUsed: 0,
  storageLimit: 1000, // 1TB
  workspaceActivityRate: 0,
  apiCalls: 0,

  // 專案相關
  totalProjects: 0,

  // 系統相關
  systemHealth: 98,

  // 收入相關
  monthlyRevenue: 0,
  revenueGrowth: 0,

  // 訂單相關
  totalOrders: 0,
  completedOrders: 0,
  monthlyOrders: 0,
  monthlyGrowth: 0,
  totalRevenue: 0,
  averageOrderValue: 0,
  pendingOrders: 0,

  // 活動記錄
  activities: [],
});

const revenueData = ref({
  labels: ['一月', '二月', '三月', '四月', '五月', '六月'],
  datasets: [
    {
      label: '營收 (USD)',
      data: [0, 0, 0, 0, 0, 0],
      borderColor: 'rgb(99, 102, 241)',
      tension: 0.3,
    },
  ],
});

const userActivityData = ref({
  labels: ['週一', '週二', '週三', '週四', '週五', '週六', '週日'],
  datasets: [
    {
      label: '活躍使用者',
      data: [0, 0, 0, 0, 0, 0, 0],
      backgroundColor: 'rgb(99, 102, 241)',
    },
  ],
});

import { formatNumber, formatCurrency, formatStorage, formatPercent } from '@/utils/formatting';

// 讀取儀表板數據
const fetchDashboardStats = async () => {
  if (isLoading.value) return;

  try {
    isLoading.value = true;

    const [statsResult, revenueResult, activityResult] = await Promise.allSettled([
      adminDashboardService.getStats(),
      adminDashboardService.getRevenue(),
      adminDashboardService.getActivity(),
    ]);

    // stats
    if (statsResult.status === 'fulfilled' && statsResult.value) {
      stats.value = statsResult.value;
    }

    // revenue
    if (
      revenueResult.status === 'fulfilled' &&
      revenueResult.value &&
      Array.isArray(revenueResult.value.revenue)
    ) {
      revenueData.value.datasets[0].data = revenueResult.value.revenue;
      revenueData.value.labels = revenueResult.value.labels ?? [];
    }

    // activity
    if (
      activityResult.status === 'fulfilled' &&
      activityResult.value &&
      Array.isArray(activityResult.value.activity)
    ) {
      userActivityData.value.datasets[0].data = activityResult.value.activity;
      userActivityData.value.labels = activityResult.value.labels ?? [];
    }
  } catch (error: any) {
    notification.toast.error('載入失敗', '讀取儀表板數據時發生錯誤');
    handleApiError(error);
  } finally {
    isLoading.value = false;
  }
};

const handleApiError = (error: any) => {
  if (error?.response?.status === 401) {
    notification.flash.error('請重新登入，您的登入狀態已失效');
    router.push('/auth/login');
  } else if (error?.response?.status === 403) {
    notification.flash.error('權限不足，您沒有權限執行此操作');
    router.push('/');
  } else {
    notification.flash.error('操作失敗，' + (error.message || '發生未知錯誤'));
  }
};

// 定時更新數據
let updateInterval: NodeJS.Timeout | null = null;

onMounted(async () => {
  try {
    await Promise.all([fetchDashboardStats(), fetchPlans(true)]);
    updateInterval = setInterval(fetchDashboardStats, 5 * 60 * 1000);
  } catch (error) {
    handleApiError(error);
  }
});

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval);
    updateInterval = null;
  }
});
</script>

<template>
  <div class="p-4 space-y-4">
    <!-- 頁面標題 -->
    <div class="flex items-center space-x-3">
      <div class="p-2 bg-primary/10 rounded-md">
        <BarChart3 class="w-4 h-4 text-primary" />
      </div>
      <div>
        <h1 class="text-xl font-semibold text-gray-900 dark:text-zinc-100">{{ props.title }}</h1>
        <p class="text-sm text-gray-600 dark:text-zinc-400 mt-0.5">{{ props.description }}</p>
      </div>
    </div>

    <!-- 訂單統計卡片 -->
    <OrderStatsCards
      :stats="{
        totalOrders: stats.totalOrders,
        completedOrders: stats.completedOrders,
        monthlyOrders: stats.monthlyOrders,
        monthlyGrowth: stats.monthlyGrowth,
        totalRevenue: stats.totalRevenue,
        averageOrderValue: stats.averageOrderValue,
        pendingOrders: stats.pendingOrders,
      }"
    />

    <!-- 工作區統計卡片 -->
    <WorkspaceStatsCards
      :stats="{
        totalWorkspaces: stats.totalWorkspaces,
        activeWorkspaces: stats.activeWorkspaces,
        monthlyNewWorkspaces: stats.monthlyNewWorkspaces,
        monthlyGrowth: stats.monthlyWorkspaceGrowth,
        storageUsed: stats.storageUsed,
        storageLimit: stats.storageLimit,
        activityRate: stats.workspaceActivityRate,
        apiCalls: stats.apiCalls,
      }"
    />

    <!-- 統計卡片 -->
    <StatsCards
      :stats="{
        totalUsers: stats.totalUsers,
        activeUsers: stats.activeUsers,
        adminCount: stats.adminCount,
        tenantCount: stats.tenantCount,
        recentLogins: stats.recentLogins,
        systemHealth: stats.systemHealth,
        monthlyRevenue: stats.monthlyRevenue,
        revenueGrowth: stats.revenueGrowth,
        storageUsed: stats.storageUsed,
        storageLimit: stats.storageLimit,
        apiCalls: stats.apiCalls,
        tenantGrowthRate: stats.tenantGrowthRate,
      }"
    />

    <!-- 方案統計 -->
    <Suspense>
      <PlanStats v-if="planStats" :stats="planStats" />
      <template #fallback>
        <div class="animate-pulse">
          <div
            class="h-[200px] bg-gray-200 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700"
          ></div>
        </div>
      </template>
    </Suspense>

    <!-- 圖表區域 -->
    <div class="grid gap-4 md:grid-cols-2">
      <!-- 營收趨勢 -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-zinc-800">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-sm font-medium text-gray-900 dark:text-zinc-100">營收趨勢</h3>
        </div>
        <div class="p-4">
          <div class="h-[300px]">
            <LineChart :data="revenueData" />
          </div>
        </div>
      </div>

      <!-- 使用者活躍度 -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-zinc-800">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-sm font-medium text-gray-900 dark:text-zinc-100">使用者活躍度</h3>
        </div>
        <div class="p-4">
          <div class="h-[300px]">
            <BarChart :data="userActivityData" />
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活動 -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-zinc-800">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-sm font-medium text-gray-900 dark:text-zinc-100">最近活動</h3>
      </div>
      <div class="p-4">
        <div class="space-y-3">
          <div
            v-for="(activity, index) in stats.activities"
            :key="index"
            class="flex items-start space-x-3 p-3 rounded-md border border-gray-100 dark:border-zinc-700 bg-gray-50/50 dark:bg-zinc-800/50"
          >
            <div class="p-1.5 rounded-md bg-primary/10">
              <component
                :is="
                  activity.type === 'user'
                    ? Users
                    : activity.type === 'tenant'
                      ? Building2
                      : Activity
                "
                class="h-3 w-3 text-primary"
              />
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-zinc-100">
                {{ activity.message }}
              </p>
              <p class="text-xs text-gray-500 dark:text-zinc-400 mt-0.5">
                {{ activity.timestamp }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
