-- AlterTable
ALTER TABLE "line_bots" ADD COLUMN     "needs_token_update_reminder" BO<PERSON>EAN NOT NULL DEFAULT false,
ADD COLUMN     "workspace_id" TEXT,
ALTER COLUMN "bot_secret" DROP NOT NULL,
ALTER COLUMN "bot_token" DROP NOT NULL;

-- AlterTable
ALTER TABLE "permissions" ADD COLUMN     "fields" TEXT[];

-- CreateIndex
CREATE INDEX "line_bots_tenant_id_idx" ON "line_bots"("tenant_id");

-- CreateIndex
CREATE INDEX "line_bots_workspace_id_idx" ON "line_bots"("workspace_id");

-- AddForeignKey
ALTER TABLE "line_bots" ADD CONSTRAINT "line_bots_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE SET NULL ON UPDATE CASCADE;
