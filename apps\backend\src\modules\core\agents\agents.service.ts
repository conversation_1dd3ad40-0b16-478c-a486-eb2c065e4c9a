import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EncryptionService } from '../encryption/encryption.service';
import {
  CreateAgentDto,
  UpdateAgentDto,
  OptimizePromptDto,
  ExecuteAgentDto,
} from './dto/agent.dto';
import { v4 as uuidv4 } from 'uuid';
import {
  ai_agents,
  AiAgentScope,
  AiAgentResponseFormat,
  Prisma,
  ai_models,
  ai_keys,
} from '@prisma/client';
import { JwtUser } from '../../../types/jwt-user.type';
import { ConfigService } from '@nestjs/config';
import { AiModelsService } from '../../ai/models/configuration/models/ai-models.service';
import { AiKeysService } from '../../ai/models/configuration/keys/ai-keys.service';
import { LangChainLlmService } from '../../ai/llm/services/langchain-llm.service';
import { PermissionCheckerService } from '../../../casl/services/permission-checker.service';
import { CaslAbilityFactory } from '../../../casl/ability/casl-ability.factory';

// 導入拆分後的服務
import { AgentExecutorService } from './services/agent-executor.service';
import { AgentToolsService } from './services/agent-tools.service';
import { AgentStatusService } from './services/agent-status.service';
import { AgentPromptService } from './services/agent-prompt.service';

type FullAgent = ai_agents & { ai_models: ai_models; ai_keys: ai_keys };

@Injectable()
export class AgentsService {
  private readonly logger = new Logger(AgentsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly encryptionService: EncryptionService,
    private readonly aiModelsService: AiModelsService,
    private readonly aiKeysService: AiKeysService,
    private readonly langChainLlmService: LangChainLlmService,
    private readonly permissionChecker: PermissionCheckerService,
    private readonly caslAbilityFactory: CaslAbilityFactory,
    private readonly configService: ConfigService,
    // 注入拆分後的服務
    private readonly agentExecutor: AgentExecutorService,
    private readonly agentTools: AgentToolsService,
    private readonly agentStatus: AgentStatusService,
    private readonly agentPrompt: AgentPromptService,
  ) {}

  // ========== CRUD 操作 (核心資料管理) ==========

  /**
   * 查找所有 Agent
   */
  async findAll(
    tenantId?: string,
    workspaceId?: string,
    scope?: AiAgentScope,
  ): Promise<FullAgent[]> {
    const where: Prisma.ai_agentsWhereInput = {};

    if (tenantId) where.tenant_id = tenantId;
    if (workspaceId) where.workspace_id = workspaceId;
    if (scope) where.scope = scope;

    return this.prisma.ai_agents.findMany({
      where,
      include: {
        ai_models: true,
        ai_keys: true,
      },
    });
  }

  /**
   * 查找單個 Agent
   */
  async findOne(id: string): Promise<FullAgent> {
    const agent = await this.prisma.ai_agents.findUnique({
      where: { id },
      include: {
        ai_models: true,
        ai_keys: true,
      },
    });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${id} not found`);
    }

    return agent;
  }

  /**
   * 創建新 Agent
   */
  async create(createAgentDto: CreateAgentDto, userId: string): Promise<FullAgent> {
    const now = new Date();
    const agent = await this.prisma.ai_agents.create({
      data: {
        id: uuidv4(),
        name: createAgentDto.name,
        description: createAgentDto.description || '',
        system_prompt: createAgentDto.system_prompt,
        provider_type: createAgentDto.provider_type,
        temperature: createAgentDto.temperature,
        max_tokens: createAgentDto.max_tokens,
        response_format: createAgentDto.response_format || AiAgentResponseFormat.TEXT,
        scope: createAgentDto.scope,
        tenant_id: createAgentDto.tenant_id,
        workspace_id: createAgentDto.workspace_id,
        model_id: createAgentDto.model_id,
        key_id: createAgentDto.key_id,
        is_enabled: createAgentDto.is_enabled ?? true,
        created_by: userId,
        created_at: now,
        updated_at: now,
      },
      include: {
        ai_models: true,
        ai_keys: true,
      },
    });

    this.logger.log(`Created new agent: ${agent.id} (${agent.name})`);
    return agent;
  }

  /**
   * 更新 Agent
   */
  async update(id: string, updateAgentDto: UpdateAgentDto, userId: string): Promise<FullAgent> {
    const updateData: any = {
      updated_at: new Date(),
      updated_by: userId,
    };

    if (updateAgentDto.name) updateData.name = updateAgentDto.name;
    if (updateAgentDto.description !== undefined)
      updateData.description = updateAgentDto.description;
    if (updateAgentDto.system_prompt !== undefined)
      updateData.system_prompt = updateAgentDto.system_prompt;

    const agent = await this.prisma.ai_agents.update({
      where: { id },
      data: updateData,
      include: {
        ai_models: true,
        ai_keys: true,
      },
    });

    this.logger.log(`Updated agent: ${agent.id} (${agent.name})`);
    return agent;
  }

  /**
   * 刪除 Agent
   */
  async delete(id: string): Promise<FullAgent> {
    const agent = await this.prisma.ai_agents.delete({
      where: { id },
      include: { ai_models: true, ai_keys: true },
    });

    this.logger.log(`Deleted agent: ${agent.id} (${agent.name})`);
    return agent;
  }

  // ========== 執行相關方法 (委託給 AgentExecutorService) ==========

  /**
   * 執行 Agent
   */
  async execute(agentId: string, executeDto: ExecuteAgentDto) {
    return this.agentExecutor.execute(agentId, executeDto);
  }

  /**
   * 測試 Agent
   */
  async testAgent(agentId: string, message: string, prompt?: string, temperature?: number) {
    return this.agentExecutor.testAgent(agentId, message, prompt, temperature);
  }

  /**
   * 運行 Agent (完整執行流程)
   */
  async runAgent(
    userInput: string,
    user: JwtUser,
    agentConfigId?: string,
    sessionId?: string,
  ): Promise<string> {
    return this.agentExecutor.runAgent(userInput, user, agentConfigId, sessionId);
  }

  // ========== 工具管理方法 (委託給 AgentToolsService) ==========

  /**
   * 獲取 Agent 工具
   */
  async getAgentTools(agentId: string): Promise<any[]> {
    return this.agentTools.getAgentTools(agentId);
  }

  /**
   * 為 Agent 指派工具
   */
  async assignToolsToAgent(agentId: string, toolIds: string[]): Promise<void> {
    return this.agentTools.assignToolsToAgent(agentId, toolIds);
  }

  /**
   * 從 Agent 移除工具
   */
  async removeToolFromAgent(agentId: string, toolId: string): Promise<void> {
    return this.agentTools.removeToolFromAgent(agentId, toolId);
  }

  /**
   * 獲取 Agent 工具配置
   */
  async getAgentToolConfigs(agentId: string, tenantId: string) {
    return this.agentTools.getAgentToolConfigs(agentId, tenantId);
  }

  /**
   * 獲取可用工具
   */
  async getAvailableTools(tenantId: string, user?: JwtUser) {
    return this.agentTools.getAvailableTools(tenantId, user);
  }

  // ========== 狀態管理方法 (委託給 AgentStatusService) ==========

  /**
   * 更新 Agent 狀態
   */
  async updateStatus(id: string, isEnabled: boolean): Promise<FullAgent> {
    return this.agentStatus.updateAgentStatus(id, isEnabled);
  }

  /**
   * 獲取 Agent 系統狀態
   */
  async getAgentStatus() {
    return this.agentStatus.getAgentStatus();
  }

  /**
   * 獲取 Agent 詳細狀態
   */
  async getAgentDetailedStatus(agentId: string) {
    return this.agentStatus.getAgentDetailedStatus(agentId);
  }

  /**
   * 獲取健康度摘要
   */
  async getHealthSummary() {
    return this.agentStatus.getHealthSummary();
  }

  // ========== 提示詞優化方法 (委託給 AgentPromptService) ==========

  /**
   * 優化提示詞
   */
  async optimizePrompt(dto: OptimizePromptDto) {
    return this.agentPrompt.optimizePrompt(dto);
  }

  // ========== 便利方法 ==========

  /**
   * 檢查 Agent 是否屬於指定租戶
   */
  async validateAgentTenant(agentId: string, tenantId: string): Promise<boolean> {
    try {
      const agent = await this.findOne(agentId);
      return agent.tenant_id === tenantId;
    } catch (error) {
      return false;
    }
  }

  /**
   * 獲取租戶的所有啟用 Agent
   */
  async getEnabledAgentsForTenant(tenantId: string): Promise<FullAgent[]> {
    return this.findAll(tenantId, undefined, undefined).then((agents) =>
      agents.filter((agent) => agent.is_enabled),
    );
  }

  /**
   * 統計方法
   */
  async getAgentStats(tenantId?: string) {
    const where: Prisma.ai_agentsWhereInput = tenantId ? { tenant_id: tenantId } : {};

    const [total, enabled, disabled] = await Promise.all([
      this.prisma.ai_agents.count({ where }),
      this.prisma.ai_agents.count({ where: { ...where, is_enabled: true } }),
      this.prisma.ai_agents.count({ where: { ...where, is_enabled: false } }),
    ]);

    return {
      total,
      enabled,
      disabled,
      enabledPercentage: total > 0 ? Math.round((enabled / total) * 100) : 0,
    };
  }
}
