import { Injectable, NestInterceptor, Execution<PERSON>ontext, CallH<PERSON><PERSON>, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request } from 'express';
import { AuditLogService } from '../services/audit-log.service';
import { AUDIT_METADATA_KEY, AuditOptions } from '../decorators/audit.decorator';
import { JwtUser } from '../../types/jwt-user.type';

@Injectable()
export class AuditInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuditInterceptor.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly auditLogService: AuditLogService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const auditOptions = this.reflector.get<AuditOptions>(AUDIT_METADATA_KEY, context.getHandler());

    if (!auditOptions) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest<Request>();
    const user = request.user as JwtUser | undefined;
    const args = context.getArgs();
    const startTime = Date.now();

    // 提取上下文信息
    const auditContext = {
      user,
      ip: this.getClientIp(request),
      userAgent: request.get('User-Agent'),
      path: request.url,
      method: request.method,
    };

    return next.handle().pipe(
      tap(async (result) => {
        if (auditOptions.onlyOnFailure) {
          return; // 只在失敗時記錄，跳過成功情況
        }

        try {
          await this.logAuditEvent(
            auditOptions,
            args,
            result,
            auditContext,
            startTime,
            true, // success
          );
        } catch (error) {
          this.logger.error('Failed to log audit event on success', {
            error: error.message,
            action: auditOptions.action,
          });
        }
      }),
      catchError(async (error) => {
        if (auditOptions.onlyOnSuccess) {
          return throwError(() => error); // 只在成功時記錄，跳過失敗情況
        }

        try {
          await this.logAuditEvent(
            auditOptions,
            args,
            null,
            auditContext,
            startTime,
            false, // failure
            error,
          );
        } catch (auditError) {
          this.logger.error('Failed to log audit event on failure', {
            error: auditError.message,
            originalError: error.message,
            action: auditOptions.action,
          });
        }

        return throwError(() => error);
      }),
    );
  }

  private async logAuditEvent(
    options: AuditOptions,
    args: any[],
    result: any,
    context: any,
    startTime: number,
    success: boolean,
    error?: any,
  ): Promise<void> {
    const endTime = Date.now();
    const executionTime = endTime - startTime;

    // 構建稽核詳情
    const details: Record<string, any> = {
      executionTime,
      success,
      timestamp: new Date().toISOString(),
    };

    // 添加自定義上下文
    if (options.contextExtractor) {
      try {
        const customContext = options.contextExtractor(args, result);
        Object.assign(details, customContext);
      } catch (contextError) {
        this.logger.warn('Failed to extract custom context', {
          error: contextError.message,
          action: options.action,
        });
      }
    }

    // 記錄方法參數
    if (options.logArgs && args.length > 0) {
      details.arguments = this.sanitizeArguments(args, options);
    }

    // 記錄返回值
    if (options.logResult && result !== undefined && success) {
      details.result = this.sanitizeResult(result, options);
    }

    // 記錄錯誤信息
    if (!success && error) {
      details.error = {
        message: error.message,
        name: error.name,
        stack: error.stack,
      };
    }

    // 生成描述
    const description = this.generateDescription(options.description, args, result, context);

    // 提取資源ID
    const resourceId = this.extractResourceId(options, args, result);

    // 記錄系統日誌
    await this.auditLogService.log(
      {
        action: options.action,
        message: description,
        target_resource: options.resource,
        target_resource_id: resourceId,
        status: success ? 'SUCCESS' : 'FAILURE',
        details,
        error_message: error?.message,
      },
      context,
    );
  }

  private sanitizeArguments(args: any[], options: AuditOptions): any[] {
    return args.map((arg, index) => {
      // 檢查是否要排除此參數
      if (options.excludeArgs?.includes(index)) {
        return '[EXCLUDED]';
      }

      return this.sanitizeObject(arg, options.excludeProperties);
    });
  }

  private sanitizeResult(result: any, options: AuditOptions): any {
    return this.sanitizeObject(result, options.excludeProperties);
  }

  private sanitizeObject(obj: any, excludeProperties?: string[]): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.sanitizeObject(item, excludeProperties));
    }

    const sanitized = { ...obj };

    // 排除指定屬性
    if (excludeProperties) {
      for (const prop of excludeProperties) {
        if (prop in sanitized) {
          sanitized[prop] = '[EXCLUDED]';
        }
      }
    }

    return sanitized;
  }

  private generateDescription(
    template: string | undefined,
    args: any[],
    result: any,
    context: any,
  ): string {
    if (!template) {
      return '';
    }

    try {
      // 簡單的模板替換
      let description = template;

      // 替換 ${args.0.property} 形式的參數引用
      description = description.replace(/\$\{args\.(\d+)\.([^}]+)\}/g, (match, index, property) => {
        const argIndex = parseInt(index, 10);
        if (args[argIndex] && typeof args[argIndex] === 'object') {
          return args[argIndex][property] || match;
        }
        return match;
      });

      // 替換 ${args.0} 形式的參數引用
      description = description.replace(/\$\{args\.(\d+)\}/g, (match, index) => {
        const argIndex = parseInt(index, 10);
        return args[argIndex] !== undefined ? String(args[argIndex]) : match;
      });

      // 替換 ${result.property} 形式的結果引用
      description = description.replace(/\$\{result\.([^}]+)\}/g, (match, property) => {
        if (result && typeof result === 'object') {
          return result[property] || match;
        }
        return match;
      });

      // 替換 ${user.property} 形式的用戶引用
      description = description.replace(/\$\{user\.([^}]+)\}/g, (match, property) => {
        if (context.user && typeof context.user === 'object') {
          return context.user[property] || match;
        }
        return match;
      });

      return description;
    } catch (error) {
      this.logger.warn('Failed to generate description from template', {
        template,
        error: error.message,
      });
      return template;
    }
  }

  private extractResourceId(options: AuditOptions, args: any[], result: any): string | undefined {
    if (!options.resourceIdExtractor) {
      return undefined;
    }

    try {
      return options.resourceIdExtractor(args, result);
    } catch (error) {
      this.logger.warn('Failed to extract resource ID', {
        error: error.message,
        action: options.action,
      });
      return undefined;
    }
  }

  private getClientIp(request: Request): string {
    return (
      request.ip || request.connection?.remoteAddress || request.socket?.remoteAddress || 'unknown'
    );
  }
}
