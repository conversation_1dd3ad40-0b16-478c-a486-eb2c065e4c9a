import { Controller, Get, Post, Put, Body, Param, Delete, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { RolesGuard } from '../../core/auth/guards/roles.guard';
import { Roles } from '../../core/auth/decorators/roles.decorator';
import { Role } from '../../../common/enums/role.enum';
import { UpdateGeneralSettingsDto } from './dto/update-general-settings.dto';
import { UpdateSecuritySettingsDto } from './dto/update-security-settings.dto';
import { UpdateAISettingsDto } from './dto/update-ai-settings.dto';
import { UpdateLineSettingsDto } from './dto/update-line-settings.dto';
import { UpdateUserSettingsDto } from './dto/update-user-settings.dto';
import { UpdateTenantSettingsDto } from './dto/update-tenant-settings.dto';
import { UpdateEmailSettingsDto } from './dto/update-email-settings.dto';
import { UpdateStorageSettingsDto } from './dto/update-storage-settings.dto';
import { UpdateBillingSettingsDto } from './dto/update-billing-settings.dto';
import { UpdateAppearanceSettingsDto } from './dto/update-appearance-settings.dto';
import { UpdateLegalSettingsDto } from './dto/update-legal-settings.dto';
import { UpdateArchivingSettingsDto } from './dto/update-archiving-settings.dto';
import { SettingsService } from './settings.service';
import { CreateSettingDto } from './dto/create-setting.dto';
import { Request } from 'express';
import { PrismaClient, system_users as SystemUser } from '@prisma/client';
import { Audit } from '../../../common/decorators/audit.decorator';

interface RequestWithUser extends Request {
  user: SystemUser;
}

@ApiTags('admin/settings')
@Controller('admin/settings')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}

  @Post()
  @Audit({
    action: 'SETTING_CREATE',
    resource: 'system_setting',
    description: 'Created new setting: ${args.0.key}',
  })
  create(@Body() dto: CreateSettingDto, @Req() req: RequestWithUser) {
    return this.settingsService.create(dto, req.user.id);
  }

  @Get('type/:type')
  findByType(@Param('type') type: string) {
    return this.settingsService.findByType(type);
  }

  @Post('general')
  @Audit({
    action: 'SETTINGS_UPDATE_GENERAL',
    resource: 'system_setting',
    description: 'Updated general settings',
    logArgs: true,
  })
  updateGeneralSettings(@Body() data: Record<string, any>, @Req() req: RequestWithUser) {
    return this.settingsService.updateGeneralSettings(data, req.user.id);
  }

  @Post('security')
  @Audit({
    action: 'SETTINGS_UPDATE_SECURITY',
    resource: 'system_setting',
    description: 'Updated security settings',
    logArgs: true,
    excludeProperties: ['secret', 'key', 'token'], // 過濾常見的敏感字段
  })
  updateSecuritySettings(@Body() data: Record<string, any>, @Req() req: RequestWithUser) {
    return this.settingsService.updateSecuritySettings(data, req.user.id);
  }

  @Get('general')
  getGeneralSettings() {
    return this.settingsService.getGeneralSettings();
  }

  @Get('security')
  getSecuritySettings() {
    return this.settingsService.getSecuritySettings();
  }

  @Delete('id/:id')
  @Audit({
    action: 'SETTING_DELETE',
    resource: 'system_setting',
    description: 'Deleted setting with ID: ${args.0}',
    resourceIdExtractor: (args) => args[0],
  })
  remove(@Param('id') id: string) {
    return this.settingsService.delete(id);
  }

  @Get('user')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '取得使用者設定' })
  async getUserSettings() {
    return this.settingsService.getUserSettings();
  }

  @Put('user')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新使用者設定' })
  @Audit({
    action: 'SETTINGS_UPDATE_USER',
    resource: 'system_setting',
    description: 'Updated user settings',
    logArgs: true,
  })
  async updateUserSettings(@Body() dto: UpdateUserSettingsDto, @Req() req: RequestWithUser) {
    return this.settingsService.updateUserSettings(dto, req.user.id);
  }

  @Get('tenant')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '取得租戶設定' })
  async getTenantSettings() {
    return this.settingsService.getTenantSettings();
  }

  @Put('tenant')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新租戶設定' })
  @Audit({
    action: 'SETTINGS_UPDATE_TENANT',
    resource: 'system_setting',
    description: 'Updated tenant settings',
    logArgs: true,
  })
  async updateTenantSettings(@Body() dto: UpdateTenantSettingsDto, @Req() req: RequestWithUser) {
    return this.settingsService.updateTenantSettings(dto, req.user.id);
  }

  @Get('email')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '取得郵件設定' })
  async getEmailSettings() {
    return this.settingsService.getEmailSettings();
  }

  @Put('email')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新郵件設定' })
  @Audit({
    action: 'SETTINGS_UPDATE_EMAIL',
    resource: 'system_setting',
    description: 'Updated email settings',
    logArgs: true,
    excludeProperties: ['password', 'apiKey', 'secretKey'],
  })
  async updateEmailSettings(@Body() dto: UpdateEmailSettingsDto, @Req() req: RequestWithUser) {
    return this.settingsService.updateEmailSettings(dto, req.user.id);
  }

  @Post('email/test')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '測試郵件設定' })
  @Audit({
    action: 'SETTINGS_TEST_EMAIL',
    resource: 'system_setting',
    description: 'Sent test email to ${args.0.to}',
    logArgs: true,
    excludeProperties: ['password', 'apiKey', 'secretKey'],
  })
  async testEmailSettings(@Body() dto: Record<string, any>) {
    return this.settingsService.testEmailSetting(dto);
  }

  @Get('storage')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '取得儲存設定' })
  async getStorageSettings() {
    return this.settingsService.getStorageSettings();
  }

  @Put('storage')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新儲存設定' })
  @Audit({
    action: 'SETTINGS_UPDATE_STORAGE',
    resource: 'system_setting',
    description: 'Updated storage settings',
    logArgs: true,
    excludeProperties: ['accessKey', 'secretKey', 'password', 'apiKey'],
  })
  async updateStorageSettings(@Body() dto: UpdateStorageSettingsDto, @Req() req: RequestWithUser) {
    return this.settingsService.updateStorageSettings(dto, req.user.id);
  }

  @Get('billing')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '取得帳單設定' })
  async getBillingSettings() {
    return this.settingsService.getBillingSettings();
  }

  @Put('billing')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新帳單設定' })
  async updateBillingSettings(@Body() dto: UpdateBillingSettingsDto, @Req() req: RequestWithUser) {
    return this.settingsService.updateBillingSettings(dto, req.user.id);
  }

  @Get('appearance')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '取得外觀設定' })
  async getAppearanceSettings() {
    return this.settingsService.getAppearanceSettings();
  }

  @Put('appearance')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新外觀設定' })
  async updateAppearanceSettings(
    @Body() dto: UpdateAppearanceSettingsDto,
    @Req() req: RequestWithUser,
  ) {
    return this.settingsService.updateAppearanceSettings(dto, req.user.id);
  }

  @Get('legal')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '取得法律設定' })
  async getLegalSettings() {
    return this.settingsService.getLegalSettings();
  }

  @Put('legal')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新法律設定' })
  async updateLegalSettings(@Body() dto: UpdateLegalSettingsDto, @Req() req: RequestWithUser) {
    return this.settingsService.updateLegalSettings(dto, req.user.id);
  }

  @Get('archiving')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '取得歸檔設定' })
  async getArchivingSettings() {
    return this.settingsService.getArchivingSettings();
  }

  @Put('archiving')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新歸檔設定' })
  @Audit({
    action: 'SETTINGS_UPDATE_ARCHIVING',
    resource: 'system_setting',
    description: 'Updated archiving settings',
    logArgs: true,
    excludeProperties: ['storageCredentials', 'accessKey', 'secretKey'],
  })
  async updateArchivingSettings(
    @Body() dto: UpdateArchivingSettingsDto,
    @Req() req: RequestWithUser,
  ) {
    return this.settingsService.updateArchivingSettings(dto, req.user.id);
  }
}
