/*
  Warnings:

  - Made the column `tenant_id` on table `ai_bots` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tenant_id` on table `ai_usage_logs` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tenant_id` on table `line_bots` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tenant_id` on table `line_group_verifications` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tenant_id` on table `line_message_logs` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "ai_bots" ALTER COLUMN "tenant_id" SET NOT NULL;

-- AlterTable
ALTER TABLE "ai_usage_logs" ALTER COLUMN "tenant_id" SET NOT NULL;

-- AlterTable
ALTER TABLE "line_bots" ALTER COLUMN "tenant_id" SET NOT NULL;

-- AlterTable
ALTER TABLE "line_group_verifications" ALTER COLUMN "tenant_id" SET NOT NULL;

-- AlterTable
ALTER TABLE "line_message_logs" ALTER COLUMN "tenant_id" SET NOT NULL;
