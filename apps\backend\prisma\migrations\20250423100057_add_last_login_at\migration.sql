/*
  Warnings:

  - Made the column `projectId` on table `photos` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "photos" DROP CONSTRAINT "photos_projectId_fkey";

-- AlterTable
ALTER TABLE "photos" ALTER COLUMN "projectId" SET NOT NULL;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "lastLoginAt" TIMESTAMP(3);

-- AddForeignKey
ALTER TABLE "photos" ADD CONSTRAINT "photos_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "projects"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
