# 全域 Copilot 指示

**請使用繁體中文回覆**

本專案為 HorizAI SaaS 平台，採用 Monorepo 架構。主要技術棧如下：

- **前端**: Vue 3 (Composition API, `<script setup>`), TypeScript, Tailwind CSS, Vite
- **後端**: NestJS, TypeScript, Prisma
- **共用套件**: 使用 pnpm workspace 管理

**通用開發原則**:

1.  **Commit Message**: 所有程式碼提交需遵循 Conventional Commits 規範 (例如 `feat:`, `fix:`, `docs:`, `style:`, `refactor:`, `test:`, `chore:`)。
    - Scope 應指明影響範圍 (例如 `feat(frontend): ...`, `fix(backend-user): ...`)。
2.  **非同步操作**: 優先使用 `async/await` 處理。
3.  **程式碼可讀性**: 重視程式碼的清晰度和適當的註解。複雜邏輯必須有註解說明。
4.  **安全性**: 安全性是首要考量，特別是在處理使用者輸入、身份驗證、授權和資料庫操作時。
5.  **錯誤處理**: 應有明確的錯誤處理機制，並提供有意義的錯誤訊息。
6.  **文件參考**: 開發時請參考 `DevDoc/` 資料夾中的相關 PRD (產品需求文件) 和技術文件。
7.  **命名慣例**:
    - 變數、函式：camelCase
    - 類別、介面、型別別名、Vue 元件：PascalCase
    - 常數：SCREAMING_SNAKE_CASE
    - 檔案名稱：kebab-case (例如 `user-service.ts`) 或 PascalCase (例如 `UserProfile.vue`)。
8.  **模組化**: 遵循單一職責原則，保持函式和模組的簡潔性。
9.  **分段產生程式碼片段**: 如程式碼較長，請分段產生，並於每段開頭標註「（第 N 段/共 M 段）」。
