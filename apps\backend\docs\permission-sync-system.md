# 多租戶權限系統 – 產品需求文件（PRD）

> 本文件為 HorizAI SaaS 權限同步自動化系統的產品需求文件（Product Requirements Document, PRD），請依據本文件規格進行設計與開發。

## 概述

權限同步自動化系統是 HorizAI SaaS 平台的核心功能，負責自動掃描、同步和管理整個專案中的權限定義。此系統確保後端程式碼（透過控制器裝飾器和共享權限包 `@horizai/permissions`）中宣告的權限，與資料庫中的權限列表 (`Permission` 表) 嚴格一致，並為前端的權限控制（UI 元素顯隱、資訊屏蔽）提供基礎。目標是提供一個集中、可靠且自動化的權限管理解決方案，作為整體認證與權限架構的關鍵支撐。

---

## 系統架構與模組職責

### 核心元件

1. **Scanner（掃描器）** - `scripts/permission-sync/scanner.ts`

   - **主要掃描來源**:
     - 後端 Controller 中的 `@CheckPolicies()` 裝飾器引用的權限常數 (這些常數應來自 `@horizai/permissions`)。
     - 共享權限套件 `@horizai/permissions` (位於 `packages/permissions/src/constants.ts` 或類似路徑) 中定義的所有靜態權限標識符 (Action:Subject)。這是權限宣告的**主要來源**。
   - **次要/未來掃描來源**:
     - (可選) 後端 Service 或 Guard 中直接使用 CASL `ability.can()` 方法時，若其參數為靜態權限常數 (來自 `@horizai/permissions`)。
     - (未來考慮) 前端 Vue 元件、TypeScript 檔案中的權限使用（如 `$can`/`useAbility().can` 調用、路由 meta 權限），這需要更複雜的靜態分析能力。
   - **功能**:
     - 解析權限標識符，並從程式碼上下文（或配置）推斷其 `scope` (SYSTEM, TENANT, WORKSPACE) 和 `category` (模組分類)。
     - 支援增量掃描與快取（如 `.cache/permissions.hash`），提升 CI/CD 效率。

2. **Syncer（同步器）** - `scripts/permission-sync/syncer.ts`

   - **功能**:
     - 比對 `scanner` 掃描到的權限列表與資料庫 `Permission` 表中的記錄。
     - 自動在 `Permission` 表中新增程式碼中已宣告但在資料庫中不存在的權限。這些權限的 Action, Subject, Description (可選，從註解或配置提取), Category, Scope 等資訊應盡可能從掃描結果和配置中獲取。
     - 對於資料庫中存在但程式碼中已不再宣告的權限，將其標記為「已廢棄」 (例如更新 `description` 或設定 `is_deprecated` 旗標)，而非直接物理刪除，以保留稽核軌跡並支持平滑遷移。
     - 支援 `--dry-run` 預覽變更，以及 `--force` 強制覆蓋資料庫中權限的某些屬性（如 `description`, `category`, `scope` - 需謹慎）。
     - (未來可選) 同步權限分類到獨立的 `PermissionCategory` 表。

3. **Reporter（報告器）** - `scripts/permission-sync/reporter.ts`

   - **功能**:
     - 產生 JSON 和/或 Markdown 格式的同步報告，記錄本次同步的新增、更新、標記為廢棄的權限，以及任何掃描或同步過程中的錯誤/警告。
     - 報告預設存放於 `apps/backend/reports/permission-sync-report.json`，並在 Console 輸出重點摘要。
     - 報告應清晰易懂，便於開發者和管理員審閱權限變更。

4. **CLI 工具** - `scripts/permission-sync/cli.ts`
   - **功能**:
     - 提供統一的命令列介面，整合 `scan`、`sync`、`report` 子命令。
     - 支援參數如 `--dry-run`、`--force`、`--no-cache`、`--format` (報告格式)、`--output` (報告路徑)。
     - 設計目標是易於在開發環境手動執行，也便於整合到 CI/CD Pipeline、資料庫遷移腳本等自動化流程中。
     - CI/CD 流程中，若偵測到權限定義不一致（例如，`--dry-run` 顯示有待同步的變更），應能觸發警告或中斷流程。

### 集中式權限套件 (`@horizai/permissions`)

位於 `packages/permissions/` 的共享 NPM 套件，是整個權限系統的關鍵組成：

- **核心職責**:
  - **唯一真實來源 (Single Source of Truth)**: 定義系統中所有靜態的權限操作 (Action) 和作用對象 (Subject) 常數 (例如在 `constants.ts` 中)。
  - **跨專案共享**: 被後端 (`apps/backend`) 和前端 (`apps/frontend`) 同時引用。
- **一致性保障**:
  - 確保後端（`@CheckPolicies` 裝飾器, `CaslAbilityFactory` 邏輯）和前端（`$can` 指令, `useAbility().can` 方法, 路由守衛）在進行權限判斷時，引用的權限標識符字串完全相同。
  - 從根本上避免因手動輸入字串或前後端定義不一致導致的權限判斷錯誤。
- **類型安全**:
  - 提供 TypeScript 類型定義，允許在編碼階段進行類型檢查，增強程式碼的健壯性。
- **易於掃描與維護**:
  - `Scanner` 模組將此套件作為權限掃描的主要入口，集中管理權限宣告。
  - 新增或修改權限時，只需在此套件中操作，`permission-sync` 工具會自動將變更同步到資料庫。
- **版本控制**:
  - 作為一個獨立的 NPM 套件，其變更可以被明確地進行版本控制和追蹤，方便管理不同版本的權限集。

---

## 產品需求對應與跌代優化

### 1. 身份驗證（AuthN）

- 採用自有或第三方 OIDC / OAuth2 Provider（**未採用自架 Logto OSS**）。
- 支援本地帳密、Google、Azure AD、Line Login。
- MFA：Email OTP，後續支援 TOTP。

### 2. 租戶管理（Tenant / Org Service）

- CRUD：建立、停用、刪除租戶。
- 訂閱方案與用量追蹤（使用量 API）。
- 成員管理：邀請、停用、移轉租戶擁有權。
- 支援 DB-per-Tenant 與 Shared-DB + RLS，依租戶需求彈性切換。

### 3. 角色與權限（Policy Service）

- 角色類型：Platform（平台）、Org（組織/租戶），支援模板複製。
- 權限 CRUD，支援 Action / Subject / Condition（條件式授權）。
- REST API `/roles/:id/permissions` 已上線；**gRPC 介面尚未全面上線**，如確認需求，需補充 proto 定義與服務實作。
- **權限定義來源**: 所有 Action 和 Subject 的定義應集中在 `@horizai/permissions` 共享套件中，確保前後端引用一致。`Permission` 表的內容由 `permission-sync` 工具根據程式碼中的宣告自動填充和更新。

### 4. 權限定義掃描與同步（CI Pipeline）

- **掃描目標**: 主要掃描後端控制器中的 `@CheckPolicies` 裝飾器 (其引用的權限常數來自 `@horizai/permissions`) 和 `@horizai/permissions` 套件本身。
- **輸出與同步**: 掃描結果直接透過 `Syncer` 模組與資料庫 `Permission` 表進行比對和同步。
- **CI/CD 流程**: `pnpm db:sync-perms --dry-run` 作為 CI 檢查點，若有未同步的權限則發出警告或失敗。CI 應確保 `@horizai/permissions` 套件的版本與應用程式依賴一致。
- **架構**: GitHub Action → Docker Runner → 執行 `pnpm db:sync-perms --dry-run`。

### 5. 授權判斷（Ability SDK / CASL Integration）

- **後端 (`CaslAbilityFactory`)**: 從 JWT 解析 `userId`, `roles` 等，結合從資料庫讀取的角色-權限映射 (基於 `Permission` 表中由 `permission-sync` 同步的權限，這些權限的標識符源自 `@horizai/permissions`) 和用戶的直接權限，來構建 CASL `AppAbility` 實例。
- **前端 (`authStore`)**: 從後端 `/api/auth/me` 端點獲取序列化的 CASL 權限規則 (JSON 格式，這些規則同樣基於後端 `AppAbility` 的生成邏輯)，並在前端實例化 `AppAbility`。
- **權限快取**: (可選) 後端可考慮對用戶的 `AppAbility` 規則進行快取 (如 Redis) 以提升性能，特別是對於不經常變動權限的用戶。

### 6. Audit & Logging

- 所有管理操作存至 audit_logs（user_id, org_id, action, target, diff）。`permission-sync` 工具的執行（開始、結束、變更內容摘要）也應納入系統日誌。
- 支援 90 天線上查詢，180 天歸檔至 S3。
- 權限異動、授權失敗、敏感操作皆寫入 Audit Log，可導出至 SIEM。

### 7. 後台管理 UI

- 技術棧：Vue 3（Composition API、`<script setup>`）、TypeScript、Tailwind CSS、Vite；後端 NestJS + TypeScript + Prisma。
- 語系策略：**本階段僅繁體中文，i18n 權限詞條自動化已設計，未來可擴充多語**。
- **權限可視化與指派**:
  - 管理界面應清晰展示從 `Permission` 表中讀取的權限列表（此列表由 `permission-sync` 自動維護，其權限標識符源自 `@horizai/permissions`）。權限應按分類 (Category) 和作用域 (Scope) 組織，方便查找。
  - 提供直觀的角色權限指派界面，例如「左側權限樹/列表 + 右側選定角色」的佈局，允許管理員勾選或拖拽方式為角色賦予權限。
  - 操作應有即時反饋，並考慮批量操作的便利性。
  - UI/UX 設計需遵循 `UIDesignAndStyleGuide.mdc` 中的相關原則，確保易用性。

---

## 使用方式與 CLI 指令

### 基本命令

```bash
pnpm db:scan-perms --no-cache # 僅掃描，不寫入資料庫，可查看掃描了哪些權限
pnpm db:sync-perms --dry-run    # 掃描並預覽將對資料庫進行的變更，不實際執行
pnpm db:sync-perms [--force]    # 掃描並實際同步權限到資料庫 (force 用於覆蓋屬性)
pnpm db:report-perms --output=both # 產生 JSON 和 Markdown 格式的最新同步報告
```

### CLI 參數

```bash
pnpm db:scan-perms [--no-cache]
pnpm db:sync-perms [--dry-run] [--force]
pnpm db:report-perms [--format json|markdown] [--output <path>]
```

---

## 其他補充與未來擴充

- **資料庫隔離模式**：預設 Shared-DB + RLS，DB-per-Tenant 為選項，依租戶需求可切換。
- **Fine-Grained ABAC**：支援 Granular Access Control（RBAC + ABAC，CASL Conditions）。
- **Log Retention / SIEM**：權限異動、授權失敗、敏感操作皆寫入 Audit Log，預設保留 90 天線上查詢 / 180 天冷存，可導出至 SIEM。
- **CI/CD 整合**：權限定義掃描、同步、i18n 詞條自動化皆可於 CI/CD 流程自動執行。
- **i18n 策略**：本階段僅繁體中文，i18n 權限詞條自動化已設計，未來可擴充多語。
- **角色模板複製**：支援角色模板複製，便於租戶快速建立權限結構。
- **gRPC 權限查詢**：如有需求可補充 proto 定義與服務實作。
- **系統日誌與權限異動追蹤**: 未來可擴充同步日誌寫入稽核表，並整合監控告警。`permission-sync` 的執行本身應被記錄。

---

## 相關文件

- **開發說明**: `apps/backend/docs/permission-sync-dev.md`
- **核心規則**: `.cursor/rules/AuthAndAccessControlGuide.mdc` (定義整體認證與權限架構)
- **共享權限包**: `packages/permissions/` (權限常數的唯一真實來源)
- **CASL 整合**: (可補充一個更詳細的 CASL 使用指南文件)
