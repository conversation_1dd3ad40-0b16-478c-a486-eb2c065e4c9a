import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Query,
  Logger,
  BadRequestException,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/core/auth/guards/auth.guard';
import { AiKeysService } from './ai-keys.service';
import { CreateKeyDto, UpdateKeyDto } from './dto/ai-keys.dto';
import { TestKeyPlaintextDto } from './dto/test-key-plaintext.dto';

@ApiTags('admin/ai/keys')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('admin/ai/keys')
export class AiKeysController {
  private readonly logger = new Logger(AiKeysController.name);

  constructor(private readonly aiKeysService: AiKeysService) {}

  @Get()
  @ApiOperation({ summary: '取得所有 AI 金鑰' })
  @ApiQuery({ name: 'tenantId', required: false, description: '租戶 ID (留空顯示系統級)' })
  async findAll(@Query('tenantId') tenantId?: string) {
    return this.aiKeysService.findAll(tenantId);
  }

  @Get(':id')
  @ApiOperation({ summary: '取得單一 AI 金鑰' })
  async findOne(@Param('id') id: string) {
    return this.aiKeysService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: '建立 AI 金鑰' })
  async create(@Body() createKeyDto: CreateKeyDto) {
    return this.aiKeysService.create(createKeyDto);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新 AI 金鑰' })
  async update(@Param('id') id: string, @Body() updateKeyDto: UpdateKeyDto) {
    return this.aiKeysService.update(id, updateKeyDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '刪除 AI 金鑰' })
  async remove(@Param('id') id: string) {
    return this.aiKeysService.remove(id);
  }

  @Post(':id/test')
  @ApiOperation({ summary: '測試 AI 金鑰是否有效' })
  @HttpCode(200)
  async testKey(@Param('id') id: string) {
    const isValid = await this.aiKeysService.testKey(id);
    return { success: isValid };
  }

  @Post('test-plaintext')
  @HttpCode(200)
  @ApiOperation({ summary: '測試未加密的 API 金鑰' })
  async testKeyPlaintext(@Body() dto: TestKeyPlaintextDto) {
    const isValid = await (this.aiKeysService as any).testKeyPlaintext(
      dto.provider,
      dto.apiKey,
      dto.apiUrl,
    );
    return { success: isValid };
  }
}
