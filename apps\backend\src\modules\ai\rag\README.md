# RAG (Retrieval-Augmented Generation) 模組

## 概述

RAG 模組實現了文件索引和檢索增強生成功能，自動監聽文件上傳事件並將文件內容索引到向量資料庫中，以支援智能搜尋和文件問答功能。

## 架構

```
rag/
├── rag.module.ts                    # RAG 模組定義
├── rag-ingestion.service.ts         # 文件攝取和索引服務
├── rag-ingestion.service.spec.ts    # 單元測試
└── README.md                        # 本說明文件
```

## 核心功能

### 1. 文件攝取 (Document Ingestion)

- **自動觸發**: 監聽 `file.uploaded` 事件
- **支援格式**: PDF, TXT, MD 等文件類型
- **多租戶隔離**: 每個文件都標記 `tenant_id` 和 `workspace_id`
- **文本分塊**: 大文件自動分割為多個語意塊

### 2. 向量索引 (Vector Indexing)

- **嵌入生成**: 為文件內容生成向量嵌入
- **元數據儲存**: 保存文件相關的所有元數據
- **分塊索引**: 支援大文件的分塊索引和檢索

### 3. 相似度搜尋 (Similarity Search)

- **語意搜尋**: 基於向量相似度的智能搜尋
- **租戶隔離**: 搜尋結果自動限制在當前租戶範圍內
- **結果排序**: 按相似度分數排序返回結果

## 使用方式

### 1. 自動文件索引

文件上傳後會自動觸發索引過程，無需手動操作：

```typescript
// 文件上傳時自動發出事件
this.eventEmitter.emit('file.uploaded', {
  filePath: '/uploads/document.pdf',
  tenantId: 'tenant-123',
  fileId: 'file-456',
  workspaceId: 'workspace-789',
  fileName: 'document.pdf',
  mimeType: 'application/pdf',
});
```

### 2. 文件搜尋

```typescript
import { RAGIngestionService } from './rag/rag-ingestion.service';

@Injectable()
export class SearchService {
  constructor(private ragService: RAGIngestionService) {}

  async searchDocuments(query: string, tenantId: string, workspaceId?: string) {
    return await this.ragService.searchSimilarDocuments(
      query,
      tenantId,
      workspaceId,
      10 // 限制結果數量
    );
  }
}
```

### 3. 手動觸發索引

```typescript
@Injectable()
export class FileProcessingService {
  constructor(private ragService: RAGIngestionService) {}

  async reindexFile(fileEvent: FileUploadedEvent) {
    await this.ragService.handleFileUpload(fileEvent);
  }
}
```

## 資料庫結構

### vector_documents 表

```sql
CREATE TABLE vector_documents (
  id            TEXT PRIMARY KEY,
  tenant_id     TEXT NOT NULL,
  file_id       TEXT,
  workspace_id  TEXT,
  content       TEXT NOT NULL,
  metadata      JSON,
  embedding     JSON,
  created_at    TIMESTAMP DEFAULT NOW(),
  updated_at    TIMESTAMP DEFAULT NOW()
);
```

### vector_chunks 表

```sql
CREATE TABLE vector_chunks (
  id            TEXT PRIMARY KEY,
  document_id   TEXT NOT NULL,
  tenant_id     TEXT NOT NULL,
  chunk_index   INTEGER NOT NULL,
  content       TEXT NOT NULL,
  metadata      JSON,
  embedding     JSON,
  created_at    TIMESTAMP DEFAULT NOW(),
  updated_at    TIMESTAMP DEFAULT NOW()
);
```

## 設定

### 環境變數

```env
# OpenAI API Key (用於生成嵌入向量)
OPENAI_API_KEY=sk-...

# 資料庫連接
DATABASE_URL=postgresql://...
```

### 模組註冊

RAG 模組已自動註冊在 `WorkspaceAiModule` 中：

```typescript
@Module({
  imports: [
    // ... 其他模組
    RAGModule,
  ],
  exports: [RAGModule],
})
export class WorkspaceAiModule {}
```

## 擴展功能

### 1. 新增文件類型支援

在 `loadDocuments` 方法中添加新的文件類型處理：

```typescript
private async loadDocuments(filePath: string, fileName: string, mimeType?: string): Promise<Document[]> {
  const fileExtension = path.extname(fileName).toLowerCase();
  
  if (fileExtension === '.docx') {
    return await this.loadDocxDocument(filePath);
  }
  // ... 其他類型
}
```

### 2. 自定義嵌入模型

修改 `getEmbedding` 方法以使用不同的嵌入模型：

```typescript
private async getEmbedding(text: string): Promise<number[]> {
  // 使用自定義嵌入服務
  return await this.customEmbeddingService.embed(text);
}
```

### 3. 增強元數據

在文件索引時添加更多元數據：

```typescript
document.metadata = {
  ...document.metadata,
  tenant_id: tenantId,
  file_id: fileId,
  workspace_id: workspaceId,
  // 新增元數據
  author: fileInfo.author,
  tags: fileInfo.tags,
  language: detectedLanguage,
};
```

## 測試

### 運行單元測試

```bash
pnpm test rag-ingestion.service.spec.ts
```

### 測試覆蓋

- ✅ 文件上傳事件處理
- ✅ 不同文件類型加載
- ✅ 向量嵌入生成
- ✅ 資料庫存儲
- ✅ 相似度搜尋
- ✅ 文本分塊
- ✅ 餘弦相似度計算

## 性能考量

### 1. 分塊策略

- 預設分塊大小：1000 字符
- 按句子邊界分割，保持語意完整性
- 可根據文件類型調整分塊策略

### 2. 向量維度

- 使用 1536 維向量（與 OpenAI embeddings 兼容）
- 平衡精度和存儲效率

### 3. 索引優化

- 在 `tenant_id` 和 `workspace_id` 上建立索引
- 考慮對 embedding 使用專門的向量索引（未來可升級至 pgvector）

## 故障排除

### 常見問題

1. **文件無法索引**
   - 檢查文件路徑是否正確
   - 確認文件格式是否支援
   - 查看日誌中的錯誤訊息

2. **搜尋結果不準確**
   - 檢查查詢關鍵字是否適當
   - 調整相似度閾值
   - 考慮優化分塊策略

3. **效能問題**
   - 檢查資料庫索引
   - 監控嵌入生成時間
   - 考慮批次處理大量文件

### 日誌監控

關鍵日誌事件：
- 文件上傳事件接收
- 文件加載成功/失敗
- 向量生成和存儲
- 搜尋查詢執行

## 未來改進

1. **真正的 pgvector 整合**: 升級至使用 PostgreSQL pgvector 擴展
2. **多語言支援**: 添加多種語言的文件處理
3. **實時索引**: 實現增量索引和即時更新
4. **效能優化**: 批次處理和並行索引
5. **語意分塊**: 使用更智能的語意分塊算法

## 貢獻

歡迎提交 Pull Request 來改進 RAG 模組功能。請確保：

1. 新增功能有對應的單元測試
2. 更新相關文檔
3. 遵循現有的代碼風格
4. 測試通過所有現有測試用例 