---
applyTo: "apps/backend/src/**/*.ts"
---

# 後端開發指南 (NestJS & Prisma)

本指南適用於 `apps/backend/src/` 目錄下的所有 NestJS 和 Prisma 相關程式碼。

**核心原則**:

- 遵循 [通用編碼標準](./general-coding.instructions.md)。
- 構建可擴展、可維護且安全的 API。
- 充分利用 NestJS 的模組化和相依性注入特性。

## NestJS

- **模組 (Modules)**:
  - 每個功能領域應有其專屬模組 (例如 `UserModule`, `AuthModule`)。
  - 模組應明確定義 `imports`, `controllers`, `providers`, `exports`。
- **控制器 (Controllers)**:
  - 負責處理傳入請求、驗證輸入、呼叫服務並回傳回應。
  - 使用裝飾器 (例如 `@Get()`, `@Post()`, `@Body()`, `@Param()`, `@Query()`)。
  - 保持控制器輕量，主要業務邏輯應在服務中。
- **服務 (Services)**:
  - 包含核心業務邏輯。
  - 應注入到控制器或其他服務中。
  - 與資料庫的互動 (透過 PrismaService) 通常在此層進行。
- **DTO (Data Transfer Objects)**:
  - 用於定義 API 的請求和回應結構。
  - 應放置於對應模組的 `dto` 資料夾 (例如 `apps/backend/src/modules/user/dto/create-user.dto.ts`)。
  - 使用 `class-validator` 和 `class-transformer` 進行驗證和轉換。
- **相依性注入 (Dependency Injection)**:
  - 充分利用 NestJS 的 DI 機制來管理類別實例和其相依性。
- **錯誤處理**:
  - 使用 NestJS 內建的 HTTP Exception (例如 `HttpException`, `NotFoundException`, `BadRequestException`)。
  - 可建立自訂 Exception Filters 來統一處理特定錯誤。
- **Guards**: 用於身份驗證和授權，保護路由。
- **Pipes**: 用於資料轉換和驗證。
- **Interceptors**: 用於在請求/回應生命週期中加入額外邏輯 (例如，日誌記錄、回應轉換)。
- **設定管理**: 使用 `@nestjs/config` 模組管理環境變數。參考 `apps/backend/scripts/check-env.ts`。
- **Enum 管理**: 參考 `apps/backend/scripts/check-enum.ts`。

## Prisma

- **PrismaClient**:
  - 透過 `PrismaService` (`apps/backend/src/prisma/prisma.service.ts`) 注入和使用。
  - 確保 `PrismaService` 正確處理 `$on('beforeExit', ...)` 以優雅關閉。
- **查詢 (Queries)**:
  - 優先使用 Prisma Client 的類型安全查詢。
  - 注意效能，避免 N+1 問題，善用 `include` (用於載入關聯) 和 `select` (用於選擇特定欄位)。
  - 複雜查詢或原生 SQL 查詢應謹慎使用，並考慮其可維護性。
- **交易 (Transactions)**:
  - 對於需要確保原子性的多個資料庫操作，應使用 Prisma 的交易 (`$transaction`)。
- **Migrations**:
  - 資料庫結構變更必須透過 Prisma Migrate 產生和套用 migration 檔案。
  - Migration 檔案位於 `apps/backend/prisma/migrations/`。
  - 參考 `apps/backend/docs/db-migration-guide.md`。
- **Seeding**:
  - 使用 `apps/backend/prisma/seed.ts` 進行資料庫初始化填充。

## CASL (權限控制)

- 權限邏輯實作於 `apps/backend/src/casl/`。
- `AbilityFactory` (`apps/backend/src/casl/casl-ability.factory.ts`) 用於根據使用者角色和權限產生 `Ability` 實例。
- 在控制器或服務中使用 `Ability` 實例進行細粒度的權限檢查。
- 可結合 NestJS Guards (`@UseGuards(CaslGuard)`) 進行路由級別的權限控制。

## API 設計

- 遵循 RESTful API 設計原則。
- API 版本控制 (若需要)。
- 回應格式應保持一致，包含必要的狀態碼和資料。
- 敏感資料不應直接在 API 回應中傳回。

## 專案結構參考

- `app.module.ts`: 根模組。
- `main.ts`: 應用程式進入點。
- `casl/`: CASL 權限控制相關。
- `common/`: 通用模組、裝飾器、工具等。
- `modules/`: 各業務功能模組。
  - `[moduleName]/dto/`
  - `[moduleName]/entities/` (若使用 TypeORM 或類似 ORM 的 Entity)
  - `[moduleName]/[moduleName].controller.ts`
  - `[moduleName]/[moduleName].service.ts`
  - `[moduleName]/[moduleName].module.ts`
- `prisma/`: Prisma 服務和設定。
- `types/`: 後端專用的 TypeScript 型別定義。
