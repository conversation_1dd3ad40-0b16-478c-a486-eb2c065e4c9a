/*
  Warnings:

  - You are about to drop the `line_bot_settings` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "LineMessageDirection" AS ENUM ('INCOMING', 'OUTGOING');

-- DropF<PERSON>ignKey
ALTER TABLE "line_bot_settings" DROP CONSTRAINT "line_bot_settings_tenant_id_fkey";

-- DropTable
DROP TABLE "line_bot_settings";

-- CreateTable
CREATE TABLE "line_bots" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "scope" "LineBotScope" NOT NULL,
    "tenant_id" TEXT,
    "bot_secret" TEXT NOT NULL,
    "bot_token" TEXT NOT NULL,
    "token_last_updated_at" TIMESTAMP(3),
    "token_update_reminder_period_days" INTEGER,
    "webhook_url" TEXT,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "line_bots_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "line_group_verifications" (
    "id" TEXT NOT NULL,
    "bot_id" TEXT NOT NULL,
    "group_id" TEXT NOT NULL,
    "workspace_id" TEXT,
    "tenant_id" TEXT,
    "is_verified" BOOLEAN NOT NULL DEFAULT false,
    "verified_at" TIMESTAMP(3),
    "verified_by_user_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "line_group_verifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "line_message_logs" (
    "id" TEXT NOT NULL,
    "bot_id" TEXT NOT NULL,
    "group_id" TEXT,
    "line_user_id" TEXT NOT NULL,
    "message_id" TEXT NOT NULL,
    "message_type" TEXT NOT NULL,
    "message_content" JSONB,
    "direction" "LineMessageDirection" NOT NULL,
    "processed_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "workspace_id" TEXT,
    "tenant_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "line_message_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "line_group_verifications_tenant_id_idx" ON "line_group_verifications"("tenant_id");

-- CreateIndex
CREATE INDEX "line_group_verifications_workspace_id_idx" ON "line_group_verifications"("workspace_id");

-- CreateIndex
CREATE UNIQUE INDEX "line_group_verifications_bot_id_group_id_key" ON "line_group_verifications"("bot_id", "group_id");

-- CreateIndex
CREATE INDEX "line_message_logs_line_user_id_idx" ON "line_message_logs"("line_user_id");

-- CreateIndex
CREATE INDEX "line_message_logs_group_id_idx" ON "line_message_logs"("group_id");

-- CreateIndex
CREATE INDEX "line_message_logs_tenant_id_idx" ON "line_message_logs"("tenant_id");

-- CreateIndex
CREATE INDEX "line_message_logs_workspace_id_idx" ON "line_message_logs"("workspace_id");

-- CreateIndex
CREATE INDEX "line_message_logs_message_id_idx" ON "line_message_logs"("message_id");

-- AddForeignKey
ALTER TABLE "line_bots" ADD CONSTRAINT "line_bots_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_group_verifications" ADD CONSTRAINT "line_group_verifications_bot_id_fkey" FOREIGN KEY ("bot_id") REFERENCES "line_bots"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_group_verifications" ADD CONSTRAINT "line_group_verifications_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_group_verifications" ADD CONSTRAINT "line_group_verifications_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_group_verifications" ADD CONSTRAINT "line_group_verifications_verified_by_user_id_fkey" FOREIGN KEY ("verified_by_user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_message_logs" ADD CONSTRAINT "line_message_logs_bot_id_fkey" FOREIGN KEY ("bot_id") REFERENCES "line_bots"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_message_logs" ADD CONSTRAINT "line_message_logs_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_message_logs" ADD CONSTRAINT "line_message_logs_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
