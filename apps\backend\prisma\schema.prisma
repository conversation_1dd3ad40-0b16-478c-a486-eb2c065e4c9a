generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model ai_agents {
  id                       String                @id
  name                     String
  description              String?
  scene                    String?
  temperature              Float?                @default(0.7)
  scope                    AiAgentScope
  provider_type            AiAgentProviderType
  model_id                 String
  key_id                   String
  provider_config_override Json?
  system_prompt            String?
  max_tokens               Int?
  response_format          AiAgentResponseFormat @default(TEXT)
  execution_type           AiAgentExecutionType  @default(SINGLE_CALL)
  is_enabled               Boolean               @default(true)
  is_template              Boolean               @default(false)
  tenant_id                String
  workspace_id             String?
  created_at               DateTime              @default(now())
  updated_at               DateTime              @updatedAt
  created_by               String
  updated_by               String?
  ai_keys                  ai_keys               @relation(fields: [key_id], references: [id])
  ai_models                ai_models             @relation(fields: [model_id], references: [id])
  tenants                  tenants               @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces               workspaces?           @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  ai_feature_configs       ai_feature_configs[]
  ai_usage_logs            ai_usage_logs[]
  ai_agent_tools           ai_agent_tools[]

  @@index([scope, tenant_id, workspace_id])
}

model ai_feature_configs {
  id          String    @id
  feature_key String    @unique
  is_enabled  Boolean   @default(false)
  agent_id    String?
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  ai_agents   ai_agents? @relation(fields: [agent_id], references: [id])
}

model ai_global_settings {
  id                          String   @id
  is_ai_globally_enabled      Boolean  @default(true)
  global_monthly_quota_tokens BigInt?
  global_monthly_quota_calls  Int?
  created_at                  DateTime @default(now())
  updated_at                  DateTime @updatedAt
}

model ai_keys {
  id            String          @id
  provider      String
  name          String
  api_key       String
  api_url       String?
  is_enabled    Boolean         @default(true)
  tenant_id     String?
  created_at    DateTime        @default(now())
  updated_at    DateTime        @updatedAt
  last_test     DateTime?
  tenants       tenants?        @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  ai_agents     ai_agents[]
  ai_models     ai_models[]
  ai_usage_logs ai_usage_logs[]

  @@index([tenant_id])
}

model ai_models {
  id                         String    @id
  provider                   String
  model_name                 String
  display_name               String
  is_enabled                 Boolean   @default(true)
  input_price_per_1k_tokens  Decimal   @default(0)
  output_price_per_1k_tokens Decimal   @default(0)
  currency                   String    @default("USD")
  price_last_updated_at      DateTime
  context_window_tokens      Int?
  notes                      String?
  tenant_id                  String?
  ai_key_id                  String?
  config                     Json?
  created_at                 DateTime  @default(now())
  updated_at                 DateTime  @updatedAt
  tenants                    tenants?  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  ai_keys                    ai_keys?  @relation(fields: [ai_key_id], references: [id], onDelete: SetNull)
  ai_agents                  ai_agents[]

  @@unique([provider, model_name])
  @@index([tenant_id])
  @@index([ai_key_id])
}

model ai_price_sources {
  id         String   @id
  provider   String
  url        String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
}

model ai_usage_logs {
  id                 String    @id
  user_id            String?
  tenant_id          String
  agent_id           String
  feature_key        String?
  api_key_id         String
  provider           String
  model_name         String
  input_tokens       Int
  output_tokens      Int
  call_count         Int       @default(1)
  estimated_cost     Decimal
  request_timestamp  DateTime  @default(now())
  response_timestamp DateTime?
  is_success         Boolean
  error_message      String?
  created_at         DateTime  @default(now())
  ai_keys            ai_keys   @relation(fields: [api_key_id], references: [id])
  ai_agents          ai_agents @relation(fields: [agent_id], references: [id])

  @@index([api_key_id])
  @@index([agent_id])
  @@index([feature_key])
  @@index([request_timestamp])
  @@index([tenant_id])
  @@index([user_id])
}

model ai_tools {
  id           String        @id @default(cuid())
  key          String        @unique // 程式化標識符，例如 "file_reader", "code_generator"
  name         String        // 顯示名稱，例如 "檔案讀取工具"
  description  String?       // 工具描述
  input_schema Json?         // 輸入參數的 JSON Schema
  scope        AiToolScope   // 工具範疇：SYSTEM, TENANT, WORKSPACE
  is_enabled   Boolean       @default(true)
  tenant_id    String?       // 租戶 ID（若為租戶級工具）
  workspace_id String?       // 工作空間 ID（若為工作空間級工具）
  created_at   DateTime      @default(now())
  updated_at   DateTime      @updatedAt
  created_by   String?       // 建立者 ID
  updated_by   String?       // 更新者 ID
  
  // Relations
  tenants      tenants?      @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces   workspaces?   @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  ai_agent_tools ai_agent_tools[]

  @@index([scope, tenant_id, workspace_id])
  @@index([is_enabled])
  @@index([key])
}

model ai_agent_tools {
  id          String   @id @default(cuid())
  ai_agent_id String   // 關聯到 AiAgent
  ai_tool_id  String   // 關聯到 AiTool
  is_enabled  Boolean  @default(true) // 是否啟用此工具
  config      Json?    // 工具特定配置
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  
  // Relations
  ai_agent ai_agents @relation(fields: [ai_agent_id], references: [id], onDelete: Cascade)
  ai_tool  ai_tools  @relation(fields: [ai_tool_id], references: [id], onDelete: Cascade)

  @@unique([ai_agent_id, ai_tool_id]) // 確保每個 Agent 只能有一個相同工具的關聯
  @@index([ai_agent_id])
  @@index([ai_tool_id])
  @@index([is_enabled])
}

model ai_photo_analysis_results {
  id                  String   @id
  photo_url           String
  project_id          String
  tenant_id           String
  user_id             String
  analysis_type       String
  context             String?
  confidence          Decimal  @default(0.8)
  description         String
  progress_percentage Int?
  quality_score       Int?
  safety_issues       String[]
  equipment_detected  String[]
  recommendations     String[]
  ai_model_used       String?
  processing_time_ms  Int?
  created_at          DateTime @default(now())
  updated_at          DateTime @updatedAt
  projects            projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
  tenants             tenants  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@index([project_id])
  @@index([tenant_id])
  @@index([user_id])
  @@index([analysis_type])
  @@index([created_at])
}

model albums {
  id           String   @id
  name         String
  description  String?
  photos_count Int      @default(0)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
  user_id      String
  tenant_id    String
  tenants      tenants  @relation(fields: [tenant_id], references: [id])
  photos       photos[]

  @@index([tenant_id])
  @@index([user_id])
}

model line_auth_states {
  id         String   @id
  state      String   @unique
  user_id    String
  expires_at DateTime
  created_at DateTime @default(now())

  @@index([state])
  @@index([user_id])
}

model line_bots {
  id                                String                     @id
  name                              String
  description                       String?
  scope                             LineBotScope
  tenant_id                         String
  bot_secret                        String?
  bot_token                         String?
  token_last_updated_at             DateTime?
  token_update_reminder_period_days Int?
  webhook_url                       String?
  is_enabled                        Boolean                    @default(true)
  created_at                        DateTime                   @default(now())
  updated_at                        DateTime                   @updatedAt
  needs_token_update_reminder       Boolean                    @default(false)
  workspace_id                      String?
  tenants                           tenants                    @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces                        workspaces?                @relation(fields: [workspace_id], references: [id])
  line_group_verifications          line_group_verifications[]
  line_message_logs                 line_message_logs[]

  @@index([tenant_id])
  @@index([workspace_id])
}

model line_group_verifications {
  id                  String      @id
  bot_id              String
  group_id            String
  workspace_id        String?
  tenant_id           String
  is_verified         Boolean     @default(false)
  verified_at         DateTime?
  verified_by_user_id String?
  created_at          DateTime    @default(now())
  updated_at          DateTime    @updatedAt
  line_bot            line_bots   @relation(fields: [bot_id], references: [id], onDelete: Cascade)
  tenant              tenants     @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspace           workspaces? @relation(fields: [workspace_id], references: [id])

  @@unique([bot_id, group_id])
}

model line_message_logs {
  id              String               @id
  bot_id          String
  group_id        String?
  line_user_id    String
  message_id      String
  message_type    String
  message_content Json?
  direction       LineMessageDirection
  processed_at    DateTime             @default(now())
  workspace_id    String?
  tenant_id       String
  created_at      DateTime             @default(now())
  updated_at      DateTime             @updatedAt

  line_bot  line_bots   @relation(fields: [bot_id], references: [id], onDelete: Cascade)
  line_user line_users  @relation(fields: [line_user_id], references: [id], onDelete: Cascade)
  tenant    tenants     @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspace workspaces? @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@index([bot_id])
  @@index([group_id])
  @@index([line_user_id])
  @@index([tenant_id])
  @@index([workspace_id])
}

model login_logs {
  id          String   @id
  user_id     String?
  ip_address  String?
  user_agent  String?
  login_at    DateTime @default(now())
  success     Boolean
  fail_reason String?
}

model order_histories {
  id          String   @id
  type        String
  status      String
  description String
  by          String?
  order_id    String
  created_at  DateTime @default(now())
  orders      orders   @relation(fields: [order_id], references: [id])
}

model orders {
  id                    String            @id
  tenant_id             String
  plan_id               String
  tenant_name           String
  plan_name             String
  amount                Float
  period                Int
  number_of_subscribers Int
  start_date            DateTime
  end_date              DateTime
  status                String            @default("PENDING")
  remarks               String?
  billing_cycle         String            @default("monthly")
  created_at            DateTime          @default(now())
  updated_at            DateTime          @updatedAt
  order_histories       order_histories[]
  plans                 plans             @relation(fields: [plan_id], references: [id])
  tenants               tenants           @relation(fields: [tenant_id], references: [id])
  payments              payments?
}

model payments {
  id             String   @id
  method         String
  status         String
  transaction_id String?
  amount         Float?
  currency       String?  @default("TWD")
  order_id       String   @unique
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt
  orders         orders   @relation(fields: [order_id], references: [id])
}

model permission_categories {
  id          String        @id
  name        String        @unique
  description String?
  icon        String?
  sort_order  Int           @default(0)
  is_active   Boolean       @default(true)
  created_at  DateTime      @default(now())
  updated_at  DateTime      @updatedAt
  permissions permissions[]
}

model permissions {
  id                    String                 @id
  action                String
  subject               String
  conditions            Json?
  description           String?
  created_at            DateTime               @default(now())
  updated_at            DateTime               @updatedAt
  deprecated            Boolean                @default(false)
  fields                String[]
  category_id           String?
  is_system_defined     Boolean                @default(false)
  scope                 PermissionScope?
  name                  String?
  zone                  String?
  permission_categories permission_categories? @relation(fields: [category_id], references: [id])
  role_permissions      role_permissions[]

  @@unique([action, subject])
}

model photos {
  id          String        @id
  title       String
  description String?
  url         String
  category    PhotoCategory
  metadata    Json?
  created_at  DateTime      @default(now())
  updated_at  DateTime      @updatedAt
  album_id    String
  user_id     String
  tenant_id   String
  project_id  String
  albums      albums        @relation(fields: [album_id], references: [id])
  projects    projects      @relation(fields: [project_id], references: [id])
  tenants     tenants       @relation(fields: [tenant_id], references: [id])

  @@index([album_id])
  @@index([project_id])
  @@index([tenant_id])
  @@index([user_id])
}

model plans {
  id                       String          @id
  name                     String          @unique
  description              String
  price                    Float
  billing_cycle            String          @default("monthly")
  features                 Json
  max_users                Int
  max_projects             Int
  max_storage              Int
  is_popular               Boolean         @default(false)
  created_at               DateTime        @default(now())
  updated_at               DateTime        @updatedAt
  monthly_ai_credits_limit Decimal?        @default(0)
  orders                   orders[]
  subscriptions            subscriptions[]
  tenants                  tenants[]
}

model projects {
  id                String    @id @default(cuid())
  name              String
  description       String?
  status            String    @default("planning")
  start_date        DateTime?
  end_date          DateTime?
  budget            Float?
  priority          String    @default("medium")
  tenant_id         String
  user_id           String
  workspace_id      String? // 工作區關聯
  parent_project_id String? // 支援專案階層結構
  level             Int       @default(0) // 專案層級，0為根專案
  path              String? // 專案路徑，用於快速查詢階層
  created_at        DateTime  @default(now())
  updated_at        DateTime  @updatedAt

  // 關聯關係
  photos                    photos[]
  tasks                     tasks[]
  progress_entries          progress_entries[]
  project_milestones        project_milestones[]
  progress_reports          progress_reports[]
  ai_photo_analysis_results ai_photo_analysis_results[]
  tenants                   tenants                     @relation(fields: [tenant_id], references: [id])
  workspaces                workspaces?                 @relation(fields: [workspace_id], references: [id], onDelete: SetNull)

  // 階層關係
  parent_project projects?  @relation("ProjectHierarchy", fields: [parent_project_id], references: [id], onDelete: Cascade)
  sub_projects   projects[] @relation("ProjectHierarchy")

  @@index([tenant_id])
  @@index([user_id])
  @@index([workspace_id])
  @@index([parent_project_id])
  @@index([level])
  @@index([path])
}

model tasks {
  id               String             @id @default(cuid())
  title            String
  description      String?
  status           String             @default("todo")
  priority         String             @default("medium")
  due_date         DateTime?
  start_date       DateTime?
  estimated_hours  Float?
  actual_hours     Float?
  project_id       String
  assignee_id      String?
  created_by_id    String
  tenant_id        String
  created_at       DateTime           @default(now())
  updated_at       DateTime           @updatedAt
  project          projects           @relation(fields: [project_id], references: [id], onDelete: Cascade)
  tenants          tenants            @relation(fields: [tenant_id], references: [id])
  progress_entries progress_entries[]

  @@index([project_id])
  @@index([assignee_id])
  @@index([tenant_id])
  @@index([status])
}

model progress_entries {
  id             String       @id @default(cuid())
  title          String
  description    String?
  progress_type  ProgressType @default(TASK_UPDATE)
  progress_value Float? // 進度百分比 (0-100)
  status         String? // 狀態更新
  notes          String? // 備註
  photo_urls     String[] // 相關照片 URLs
  metadata       Json? // 額外的元數據

  // 關聯
  project_id String?
  task_id    String?
  user_id    String // 記錄者
  tenant_id  String

  // 時間戳
  recorded_at DateTime @default(now()) // 實際發生時間
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // 關聯關係
  project projects? @relation(fields: [project_id], references: [id], onDelete: Cascade)
  task    tasks?    @relation(fields: [task_id], references: [id], onDelete: Cascade)
  tenants tenants   @relation(fields: [tenant_id], references: [id])

  @@index([project_id])
  @@index([task_id])
  @@index([tenant_id])
  @@index([user_id])
  @@index([recorded_at])
}

model project_milestones {
  id           String          @id @default(cuid())
  title        String
  description  String?
  target_date  DateTime
  completed_at DateTime?
  status       MilestoneStatus @default(PENDING)
  priority     String          @default("medium")

  // 關聯
  project_id    String
  tenant_id     String
  created_by_id String

  // 時間戳
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // 關聯關係
  project projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
  tenants tenants  @relation(fields: [tenant_id], references: [id])

  @@index([project_id])
  @@index([tenant_id])
  @@index([target_date])
}

model progress_reports {
  id          String     @id @default(cuid())
  title       String
  report_type ReportType @default(WEEKLY)
  period      String // 報告期間 (如 "2024-W01")

  // 統計數據
  total_tasks       Int   @default(0)
  completed_tasks   Int   @default(0)
  in_progress_tasks Int   @default(0)
  overdue_tasks     Int   @default(0)
  completion_rate   Float @default(0) // 完成率

  // 預測數據 (為 AI 分析預留)
  predicted_completion_date DateTime?
  risk_level                String? // 風險等級
  recommendations           Json? // AI 建議

  // 關聯
  project_id   String? // 可以是項目報告或整體報告
  tenant_id    String
  generated_by String // 生成者

  // 時間戳
  report_date DateTime @default(now())
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // 關聯關係
  project projects? @relation(fields: [project_id], references: [id], onDelete: Cascade)
  tenants tenants   @relation(fields: [tenant_id], references: [id])

  @@unique([project_id, report_type, period])
  @@index([project_id])
  @@index([tenant_id])
  @@index([report_date])
}

model refresh_tokens {
  id             String    @id @default(cuid())
  token          String    @unique
  system_user_id String? // 系統用戶 ID
  tenant_user_id String? // 租戶用戶 ID
  user_type      String // "system" | "tenant"
  is_valid       Boolean   @default(true) @map("is_valid")
  device_info    String?   @map("device_info")
  expires_at     DateTime
  revoked_at     DateTime? @map("revoked_at")
  created_at     DateTime  @default(now())

  // 關聯
  system_users system_users? @relation(fields: [system_user_id], references: [id], onDelete: Cascade)
  tenant_users tenant_users? @relation(fields: [tenant_user_id], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

model password_reset_tokens {
  id             String    @id @default(cuid())
  email          String
  token          String    @unique
  user_type      String // "system" | "tenant"
  system_user_id String?
  tenant_user_id String?
  expires_at     DateTime
  used_at        DateTime?
  created_at     DateTime  @default(now())

  // 關聯 (條件性外鍵 - 根據 user_type 決定使用哪個)
  system_user system_users? @relation("SystemUserPasswordResets", fields: [system_user_id], references: [id], onDelete: Cascade)
  tenant_user tenant_users? @relation("TenantUserPasswordResets", fields: [tenant_user_id], references: [id], onDelete: Cascade)

  @@index([email])
  @@index([token])
  @@index([system_user_id])
  @@index([tenant_user_id])
  @@map("password_reset_tokens")
}

model role_permissions {
  id            String      @id
  role_id       String
  permission_id String
  created_at    DateTime    @default(now())
  updated_at    DateTime    @updatedAt
  permissions   permissions @relation(fields: [permission_id], references: [id], onDelete: Cascade)
  roles         roles       @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([role_id, permission_id])
}

model roles {
  id                 String               @id
  name               String               @unique
  display_name       String
  description        String?
  is_system          Boolean              @default(false)
  tenant_id          String?
  created_at         DateTime             @default(now())
  updated_at         DateTime             @updatedAt
  parent_role_id     String?
  scope              RoleScope            @default(SYSTEM)
  role_permissions   role_permissions[]
  roles              roles?               @relation("rolesToroles", fields: [parent_role_id], references: [id])
  other_roles        roles[]              @relation("rolesToroles")
  tenants            tenants?             @relation(fields: [tenant_id], references: [id])
  tenant_invitations tenant_invitations[]
  system_user_roles  system_user_roles[]  @relation("SystemUserRoles")
  tenant_user_roles  tenant_user_roles[]  @relation("TenantUserRoles")
}

model settings {
  id         String   @id
  type       String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  created_by String?
  name       String
  updated_by String?
  value      Json

  @@unique([name, type])
  @@index([created_by])
  @@index([type])
  @@index([updated_by])
}

model subscriptions {
  id                    String    @id
  tenant_id             String?
  plan_id               String?
  tenant_name           String
  plan_name             String
  amount                Float
  period                Int
  number_of_subscribers Int
  start_date            DateTime
  end_date              DateTime?
  status                String    @default("PENDING")
  remarks               String?
  payment_method        String?
  transaction_id        String?
  created_at            DateTime  @default(now())
  updated_at            DateTime  @updatedAt
  plans                 plans?    @relation(fields: [plan_id], references: [id])
  tenants               tenants?  @relation(fields: [tenant_id], references: [id])

  @@index([plan_id])
  @@index([tenant_id])
}

model system_logs {
  id                 String   @id
  level              String
  message            String
  stack              String?
  path               String?
  method             String?
  user_id            String?
  ip                 String?
  created_at         DateTime @default(now())
  action             String?
  details            Json?
  error_message      String?
  status             String?
  target_resource    String?
  target_resource_id String?
  tenant_id          String?

  // 索引優化 - 基於系統日誌查詢模式
  @@index([tenant_id])                              // 租戶隔離
  @@index([user_id])                                // 用戶查詢
  @@index([action])                                 // 操作類型查詢
  @@index([target_resource])                        // 資源類型查詢
  @@index([status])                                 // 狀態查詢
  @@index([level])                                  // 日誌級別過濾
  @@index([created_at])                             // 時間排序和範圍查詢
  @@index([tenant_id, created_at])                  // 複合索引：租戶 + 時間
  @@index([tenant_id, action])                      // 複合索引：租戶 + 操作
  @@index([tenant_id, user_id, created_at])         // 複合索引：租戶 + 用戶 + 時間
}

model archived_audit_logs {
  id                 String   @id
  original_log_id    String   // 原始日誌記錄的 ID
  level              String
  message            String
  stack              String?
  path               String?
  method             String?
  user_id            String?
  ip                 String?
  original_created_at DateTime // 原始建立時間
  action             String?
  details            Json?
  error_message      String?
  status             String?
  target_resource    String?
  target_resource_id String?
  tenant_id          String?
  
  // 歸檔相關欄位
  archived_at        DateTime @default(now()) // 歸檔時間
  archive_batch_id   String?  // 歸檔批次 ID
  storage_location   String?  // 儲存位置 (檔案路徑或雲端位置)
  archive_format     String   @default("json") // 歸檔格式
  compressed         Boolean  @default(false) // 是否壓縮
  
  // 索引優化 - 基於歸檔查詢模式
  @@index([tenant_id])                              // 租戶隔離
  @@index([user_id])                                // 用戶查詢
  @@index([action])                                 // 操作類型查詢
  @@index([target_resource])                        // 資源類型查詢
  @@index([original_created_at])                    // 原始建立時間查詢
  @@index([archived_at])                            // 歸檔時間查詢
  @@index([archive_batch_id])                       // 批次查詢
  @@index([tenant_id, original_created_at])         // 複合索引：租戶 + 原始時間
  @@index([tenant_id, archived_at])                 // 複合索引：租戶 + 歸檔時間
}

model tenant_credit_purchases {
  id           String   @id
  tenant_id    String
  amount       Decimal
  price_paid   Decimal
  currency     String
  payment_id   String?
  purchased_at DateTime @default(now())
  notes        String?
  tenants      tenants  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@index([tenant_id])
}

model tenant_invitations {
  id             String    @id
  email          String
  tenant_id      String
  role_id        String
  status         String    @default("pending")
  token          String    @unique
  created_at     DateTime  @default(now())
  expires_at     DateTime
  created_by_id  String?
  accepted_by_id String?
  accepted_at    DateTime?
  roles          roles     @relation(fields: [role_id], references: [id])
  tenants        tenants   @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@unique([email, tenant_id, status])
  @@index([email])
  @@index([role_id])
  @@index([tenant_id])
  @@index([token])
}

model tenants {
  id                           String                         @id
  name                         String                         @unique
  display_name                 String?
  domain                       String?                        @unique
  departments                  String[]                       @default([])
  is_default                   Boolean                        @default(false)
  created_at                   DateTime                       @default(now())
  updated_at                   DateTime                       @updatedAt
  status                       String                         @default("pending")
  admin_email                  String?
  admin_name                   String?
  company_size                 String?                        @default("1-10")
  industry                     String?
  max_projects                 Int?                           @default(10)
  max_storage                  Int?                           @default(10)
  max_users                    Int?                           @default(5)
  next_billing_date            DateTime?
  payment_status               String?                        @default("unpaid")
  plan_id                      String?
  contact_email                String?
  contact_name                 String?
  billing_cycle                String?                        @default("monthly")
  current_ai_credits           Decimal?                       @default(0)
  settings                     Json?
  ai_keys                      ai_keys[]
  ai_models                    ai_models[]
  ai_agents                    ai_agents[]
  ai_tools                     ai_tools[]
  ai_photo_analysis_results    ai_photo_analysis_results[]
  albums                       albums[]
  line_bots                    line_bots[]
  line_group_verifications     line_group_verifications[]
  line_message_logs            line_message_logs[]
  orders                       orders[]
  photos                       photos[]
  projects                     projects[]
  roles                        roles[]
  tasks                        tasks[]
  progress_entries             progress_entries[]
  project_milestones           project_milestones[]
  progress_reports             progress_reports[]
  subscriptions                subscriptions[]
  tenant_credit_purchases      tenant_credit_purchases[]
  tenant_invitations           tenant_invitations[]
  tenant_lifecycle_events      tenant_lifecycle_events[]
  workspace_templates          workspace_templates[]
  comments                     comments[]
  comment_reactions            comment_reactions[]
  comment_mentions             comment_mentions[]
  shared_files                 shared_files[]
  file_permissions             file_permissions[]
  file_shares                  file_shares[]
  file_access_logs             file_access_logs[]
  message_conversations        message_conversations[]
  message_center_messages      message_center_messages[]
  message_center_notifications message_center_notifications[]
  ai_workflows                 ai_workflows[]
  workflow_executions          workflow_executions[]
  plans                        plans?                         @relation(fields: [plan_id], references: [id])
  tenant_users                 tenant_users[]
  workspaces                   workspaces[]
  line_users                   line_users[]
  vector_documents             vector_documents[]
  vector_chunks                vector_chunks[]
}

model system_user_roles {
  id             String   @id
  system_user_id String
  role_id        String
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt

  system_user system_users @relation(fields: [system_user_id], references: [id], onDelete: Cascade)
  role        roles        @relation("SystemUserRoles", fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([system_user_id, role_id])
}

model tenant_user_roles {
  id             String   @id
  tenant_user_id String
  role_id        String
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt

  tenant_user tenant_users @relation(fields: [tenant_user_id], references: [id], onDelete: Cascade)
  role        roles        @relation("TenantUserRoles", fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([tenant_user_id, role_id])
}

// Legacy users model removed - replaced by system_users and tenant_users

model workspace_members {
  id             String          @id @default(cuid())
  workspace_id   String
  tenant_user_id String
  role           ParticipantRole @default(MEMBER)
  joined_at      DateTime        @default(now())
  left_at        DateTime?

  // 關聯
  workspace    workspaces   @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  tenant_users tenant_users @relation(fields: [tenant_user_id], references: [id], onDelete: Cascade)

  @@unique([workspace_id, tenant_user_id])
  @@map("workspace_members")
}

model workspaces {
  id                           String                         @id
  name                         String
  description                  String?
  status                       String                         @default("active")
  settings                     Json?
  tenant_id                    String
  owner_id                     String
  created_at                   DateTime                       @default(now())
  updated_at                   DateTime                       @updatedAt
  ai_agents                    ai_agents[]
  ai_tools                     ai_tools[]
  line_bots                    line_bots[]
  line_group_verifications     line_group_verifications[]
  line_message_logs            line_message_logs[]
  workspace_members            workspace_members[]
  workspace_invitations        workspace_invitations[]
  workspace_activity_logs      workspace_activity_logs[]
  projects                     projects[]
  comments                     comments[]
  comment_reactions            comment_reactions[]
  comment_mentions             comment_mentions[]
  shared_files                 shared_files[]
  message_conversations        message_conversations[]
  message_center_notifications message_center_notifications[]
  conversations                conversations[]                @relation("ChatConversations")
  ai_workflows                 ai_workflows[]
  workflow_executions          workflow_executions[]
  tenants                      tenants                        @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  line_users                   line_users[]
  vector_documents             vector_documents[]

  @@index([owner_id])
  @@index([tenant_id])
}

model workspace_templates {
  id                  String   @id @default(cuid())
  name                String
  description         String?
  default_settings    Json?
  default_member_role String   @default("member")
  is_system           Boolean  @default(false)
  tenant_id           String?
  created_by          String
  created_at          DateTime @default(now())
  updated_at          DateTime @updatedAt
  tenants             tenants? @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@index([tenant_id])
  @@index([is_system])
}

model workspace_invitations {
  id           String     @id @default(cuid())
  workspace_id String
  email        String
  role         String     @default("member")
  invited_by   String
  token        String     @unique
  status       String     @default("pending") // pending, accepted, rejected, expired
  expires_at   DateTime
  accepted_at  DateTime?
  created_at   DateTime   @default(now())
  updated_at   DateTime   @updatedAt
  workspaces   workspaces @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@index([workspace_id])
  @@index([email])
  @@index([token])
  @@index([status])
}

model workspace_activity_logs {
  id            String      @id
  workspace_id  String
  user_id       String
  activity_type String
  details       Json?
  created_at    DateTime    @default(now())
  workspaces    workspaces? @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@index([workspace_id])
  @@index([user_id])
}

// ==========================================
// AI Service Definition System
// ==========================================
model ai_service_definitions {
  id          String        @id @default(cuid())
  key         String        @unique // Unique key for the service, e.g., "codeGeneration"
  name        String // Human-readable name, e.g., "Code Generation Service"
  description String?
  is_enabled  Boolean       @default(true)
  created_at  DateTime      @default(now())
  updated_at  DateTime      @updatedAt
  created_by  String
  updated_by  String?
  creator     system_users  @relation("CreatedAiServiceDefinitions", fields: [created_by], references: [id])
  updater     system_users? @relation("UpdatedAiServiceDefinitions", fields: [updated_by], references: [id])

  // Relations
  steps      ai_service_steps[]
  templates  ai_service_templates[]
  executions ai_service_executions[]

  @@map("ai_service_definitions")
}

model ai_service_steps {
  id          String            @id @default(cuid())
  service_id  String
  step_order  Int // Order of execution
  step_type   AiServiceStepType // e.g., PROMPT, CODE_EXECUTION, API_CALL
  name        String
  description String?
  config      Json // Configuration for the step (e.g., prompt content, API endpoint)
  is_enabled  Boolean           @default(true)
  created_at  DateTime          @default(now())
  updated_at  DateTime          @updatedAt

  // Relations
  service_definition ai_service_definitions @relation(fields: [service_id], references: [id], onDelete: Cascade)

  @@unique([service_id, step_order])
  @@map("ai_service_steps")
}

model ai_service_templates {
  id              String   @id @default(cuid())
  service_id      String
  name            String
  description     String?
  config_template Json // A template of the entire service config for easy setup
  is_public       Boolean  @default(false)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  service_definition ai_service_definitions @relation(fields: [service_id], references: [id], onDelete: Cascade)

  @@map("ai_service_templates")
}

model ai_service_executions {
  id            String            @id @default(cuid())
  service_id    String
  user_id       String
  workspace_id  String?
  input_data    Json
  output_data   Json?
  status        AiExecutionStatus
  started_at    DateTime          @default(now())
  completed_at  DateTime?
  error_message String?

  // Relations
  service_definition ai_service_definitions @relation(fields: [service_id], references: [id], onDelete: Cascade)

  @@index([service_id, user_id])
  @@map("ai_service_executions")
}

// ==========================================
// AI Service Workflow System (Creator Studio)
// ==========================================
model ai_workflows {
  id           String  @id @default(cuid())
  name         String
  description  String?
  version      String  @default("1.0.0")
  is_published Boolean @default(false)
  is_template  Boolean @default(false)

  // 工作流程配置
  config        Json? // 全域配置參數
  input_schema  Json? // 輸入資料結構定義
  output_schema Json? // 輸出資料結構定義

  // 權限與可見性
  visibility WorkflowVisibility @default(PRIVATE)

  // 作者資訊
  updated_by String?

  // 租戶隔離
  tenant_id    String?
  workspace_id String?

  // 狀態
  status WorkflowStatus @default(DRAFT)

  // 時間戳
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
  published_at DateTime?

  // 關聯關係
  nodes      workflow_nodes[]
  executions workflow_executions[]
  tenants    tenants?              @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces workspaces?           @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@index([tenant_id])
  @@index([workspace_id])
  @@index([status])
  @@index([is_published])
  @@map("ai_workflows")
}

model workflow_nodes {
  id          String @id @default(cuid())
  workflow_id String

  // 節點基本資訊
  name        String
  description String?
  node_type   WorkflowNodeType

  // 節點位置 (用於前端 Canvas 顯示)
  position_x Float @default(0)
  position_y Float @default(0)

  // 節點配置
  config Json // 節點特定配置 (如 AI Bot ID、提示範本等)

  // 執行順序與依賴
  execution_order Int? // 執行順序 (可選，支援平行執行)

  // 狀態
  is_enabled Boolean @default(true)

  // 時間戳
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // 關聯關係
  workflow           ai_workflows       @relation(fields: [workflow_id], references: [id], onDelete: Cascade)
  input_connections  node_connections[] @relation("NodeInputs")
  output_connections node_connections[] @relation("NodeOutputs")

  @@index([workflow_id])
  @@index([node_type])
  @@index([execution_order])
  @@map("workflow_nodes")
}

model node_connections {
  id String @id @default(cuid())

  // 連接的節點
  source_node_id String
  target_node_id String

  // 連接端口資訊
  source_port String @default("output") // 輸出端口名稱
  target_port String @default("input") // 輸入端口名稱

  // 連接配置
  config Json? // 連接特定配置 (如資料轉換規則)

  // 狀態
  is_enabled Boolean @default(true)

  // 時間戳
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // 關聯關係
  source_node workflow_nodes @relation("NodeOutputs", fields: [source_node_id], references: [id], onDelete: Cascade)
  target_node workflow_nodes @relation("NodeInputs", fields: [target_node_id], references: [id], onDelete: Cascade)

  @@unique([source_node_id, target_node_id, source_port, target_port])
  @@index([source_node_id])
  @@index([target_node_id])
  @@map("node_connections")
}

model workflow_executions {
  id          String @id @default(cuid())
  workflow_id String

  // 執行資訊
  input_data    Json // 執行輸入
  output_data   Json? // 執行輸出
  execution_log Json? // 執行日誌

  // 執行狀態
  status        WorkflowExecutionStatus @default(PENDING)
  error_message String?

  // 執行者資訊
  executed_by   String
  executor_type String // "system" | "tenant"

  // 租戶隔離
  tenant_id    String?
  workspace_id String?

  // 時間戳
  started_at   DateTime  @default(now())
  completed_at DateTime?

  // 關聯關係
  workflow   ai_workflows @relation(fields: [workflow_id], references: [id], onDelete: Cascade)
  tenants    tenants?     @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces workspaces?  @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@index([workflow_id])
  @@index([tenant_id])
  @@index([workspace_id])
  @@index([executed_by, executor_type])
  @@index([status])
  @@index([started_at])
  @@map("workflow_executions")
}

// Workflow 相關枚舉
enum WorkflowVisibility {
  PRIVATE // 僅作者可見
  WORKSPACE // 工作區成員可見
  TENANT // 租戶內可見
  PUBLIC // 公開 (模板)
}

enum WorkflowStatus {
  DRAFT // 草稿
  PUBLISHED // 已發布
  ARCHIVED // 已歸檔
  DEPRECATED // 已棄用
}

enum WorkflowNodeType {
  // 輸入/輸出節點
  INPUT // 輸入節點
  OUTPUT // 輸出節點

  // AI 相關節點
  AI_BOT // AI Bot 執行節點
  AI_ANALYSIS // AI 分析節點 (專案分析、照片分析等)
  PROMPT_TEMPLATE // 提示範本節點

  // 資料處理節點
  DATA_TRANSFORM // 資料轉換節點
  DATA_FILTER // 資料篩選節點
  DATA_MERGE // 資料合併節點

  // 控制流程節點
  CONDITION // 條件判斷節點
  LOOP // 迴圈節點
  PARALLEL // 平行執行節點

  // 外部整合節點
  API_CALL // API 呼叫節點
  DATABASE // 資料庫操作節點
  FILE_OPERATION // 檔案操作節點

  // 通知節點
  NOTIFICATION // 通知節點
  EMAIL // 郵件節點
  WEBHOOK // Webhook 節點
}

enum WorkflowExecutionStatus {
  PENDING // 等待執行
  RUNNING // 執行中
  COMPLETED // 執行完成
  FAILED // 執行失敗
  CANCELLED // 已取消
  TIMEOUT // 執行逾時
}

enum AiAgentProviderType {
  OPENAI
  AZURE_OPENAI
  ANTHROPIC
  GOOGLE_GEMINI
  CLAUDE
  GEMINI
  OPENAI_COMPATIBLE
  CUSTOM
}

enum AiAgentResponseFormat {
  TEXT
  JSON
}

enum AiAgentScope {
  SYSTEM
  TENANT
  WORKSPACE
}

enum AiAgentExecutionType {
  SINGLE_CALL
  AGENT
  WORKFLOW
}

enum AiToolScope {
  SYSTEM    // 系統級工具，所有租戶都可使用
  TENANT    // 租戶級工具，僅特定租戶可使用
  WORKSPACE // 工作空間級工具，僅特定工作空間可使用
}

enum LineBotScope {
  SYSTEM
  TENANT
}

enum LineMessageDirection {
  INCOMING
  OUTGOING
}

enum PermissionScope {
  SYSTEM
  TENANT
  WORKSPACE
  GLOBAL
}

enum PhotoCategory {
  SITE
  PROFILE
  PRODUCT
  OTHER
}

enum RoleScope {
  SYSTEM
  TENANT
  WORKSPACE
}

// Legacy UserRole enum removed - replaced by SystemUserRole and TenantUserRole

enum SystemUserRole {
  SUPER_ADMIN
  SYSTEM_ADMIN
  SYSTEM_MODERATOR
}

enum TenantUserStatus {
  ACTIVE
  INACTIVE
  PENDING
  LEFT_COMPANY
  SUSPENDED
}

enum TenantUserRole {
  TENANT_ADMIN
  TENANT_MANAGER
  TENANT_USER
  TENANT_VIEWER
}

enum ProgressType {
  TASK_UPDATE // 任務更新
  MILESTONE // 里程碑
  PHOTO_EVIDENCE // 照片證據
  STATUS_CHANGE // 狀態變更
  QUALITY_CHECK // 質量檢查
  RESOURCE_UPDATE // 資源更新
  RISK_IDENTIFIED // 風險識別
  ISSUE_REPORTED // 問題報告
}

enum MilestoneStatus {
  PENDING // 待處理
  IN_PROGRESS // 進行中
  COMPLETED // 已完成
  DELAYED // 延遲
  CANCELLED // 已取消
}

enum ReportType {
  DAILY // 日報
  WEEKLY // 週報
  MONTHLY // 月報
  QUARTERLY // 季報
  PROJECT // 項目報告
  CUSTOM // 自定義
}

enum AiServiceStepType {
  PROMPT_TEMPLATE // Uses a template to generate a prompt for an AI model
  BOT_EXECUTION // Executes a pre-configured AiBot
  CODE_EXECUTION // Executes a snippet of code (e.g., JavaScript)
  API_CALL // Makes an external API call
  DATA_TRANSFORMATION // Transforms data from one step to be used in another
}

enum AiExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

model system_users {
  id                       String         @id @default(cuid())
  email                    String         @unique
  password                 String
  name                     String?
  role                     SystemUserRole @default(SYSTEM_ADMIN)
  status                   String         @default("active")
  avatar                   String?
  phone                    String?
  mfa_enabled              Boolean        @default(false)
  mfa_secret               String?
  last_login_at            DateTime?
  last_login_ip            String?
  last_logout_at           DateTime?
  password_last_changed_at DateTime?
  created_at               DateTime       @default(now())
  updated_at               DateTime       @updatedAt

  // Relations
  refresh_tokens                 refresh_tokens[]
  password_reset_tokens          password_reset_tokens[]  @relation("SystemUserPasswordResets")
  system_user_roles              system_user_roles[]
  oauth_accounts                 oauth_accounts[]         @relation("SystemUserOauthAccounts")
  created_ai_service_definitions ai_service_definitions[] @relation("CreatedAiServiceDefinitions")
  updated_ai_service_definitions ai_service_definitions[] @relation("UpdatedAiServiceDefinitions")
  line_users                     line_users[]

  @@index([role])
  @@map("system_users")
}

model tenant_users {
  id                       String           @id @default(cuid())
  email                    String           @unique
  password                 String
  name                     String?
  tenant_id                String
  role                     TenantUserRole   @default(TENANT_USER)
  status                   TenantUserStatus @default(ACTIVE)
  avatar                   String?
  phone                    String?
  title                    String?
  department               String?
  last_login_at            DateTime?
  last_login_ip            String?
  last_logout_at           DateTime?
  password_last_changed_at DateTime?
  mfa_enabled              Boolean          @default(false)
  mfa_secret               String?
  invited_by               String?
  left_company_at          DateTime?
  left_company_reason      String?
  data_transfer_status     String?          @default("pending")
  data_transfer_note       String?
  created_at               DateTime         @default(now())
  updated_at               DateTime         @updatedAt

  // Relations
  tenant                tenants                 @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  tenant_user_roles     tenant_user_roles[]
  refresh_tokens        refresh_tokens[]
  password_reset_tokens password_reset_tokens[] @relation("TenantUserPasswordResets")
  workspace_members     workspace_members[]
  oauth_accounts        oauth_accounts[]        @relation("TenantUserOauthAccounts")
  line_users            line_users[]

  @@index([role])
  @@index([tenant_id])
  @@map("tenant_users")
}

model tenant_lifecycle_events {
  id           String   @id @default(cuid())
  tenant_id    String
  event_type   String // created, suspended, reactivated, deleted, etc.
  reason       String?
  metadata     Json?
  triggered_by String?
  created_at   DateTime @default(now())
  tenants      tenants  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@index([tenant_id])
  @@index([event_type])
  @@index([created_at])
}

model comments {
  id           String             @id @default(cuid())
  content      String
  content_type CommentContentType @default(TEXT)

  // 關聯的實體 (可以是專案、任務、進度記錄等)
  entity_type CommentEntityType
  entity_id   String

  // 回覆功能
  parent_id String? // 父評論 ID，支援巢狀回覆
  thread_id String? // 討論串 ID，用於快速查詢整個討論串

  // 作者資訊
  author_id   String
  author_type String // "system" | "tenant"

  // 狀態
  is_edited  Boolean   @default(false)
  is_deleted Boolean   @default(false)
  deleted_at DateTime?

  // 租戶隔離
  tenant_id    String
  workspace_id String?

  // 時間戳
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // 關聯關係
  parent     comments?           @relation("CommentReplies", fields: [parent_id], references: [id], onDelete: Cascade)
  replies    comments[]          @relation("CommentReplies")
  tenants    tenants             @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces workspaces?         @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  reactions  comment_reactions[]
  mentions   comment_mentions[]

  @@index([entity_type, entity_id])
  @@index([tenant_id])
  @@index([workspace_id])
  @@index([author_id])
  @@index([parent_id])
  @@index([thread_id])
  @@index([created_at])
}

model comment_reactions {
  id           String              @id @default(cuid())
  comment_id   String
  user_id      String
  user_type    String // "system" | "tenant"
  reaction     CommentReactionType
  tenant_id    String
  workspace_id String?
  created_at   DateTime            @default(now())

  comment    comments    @relation(fields: [comment_id], references: [id], onDelete: Cascade)
  tenants    tenants     @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces workspaces? @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@unique([comment_id, user_id, user_type])
  @@index([comment_id])
  @@index([user_id])
  @@index([tenant_id])
  @@index([workspace_id])
}

model comment_mentions {
  id           String   @id @default(cuid())
  comment_id   String
  user_id      String
  user_type    String // "system" | "tenant"
  tenant_id    String
  workspace_id String?
  created_at   DateTime @default(now())

  comment    comments    @relation(fields: [comment_id], references: [id], onDelete: Cascade)
  tenants    tenants     @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces workspaces? @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@unique([comment_id, user_id, user_type])
  @@index([comment_id])
  @@index([user_id])
  @@index([tenant_id])
  @@index([workspace_id])
}

enum CommentContentType {
  TEXT
  MARKDOWN
  HTML
}

enum CommentEntityType {
  PROJECT
  TASK
  PROGRESS_ENTRY
  MILESTONE
  PHOTO
  DOCUMENT
  SHARED_FILE
}

enum CommentReactionType {
  LIKE
  LOVE
  LAUGH
  ANGRY
  SAD
  THUMBS_UP
  THUMBS_DOWN
}

// 檔案分享系統相關模型
model shared_files {
  id             String  @id @default(cuid())
  name           String
  original_name  String
  description    String?
  file_type      String // MIME type
  file_extension String
  file_size      Int // bytes
  file_path      String // 儲存路徑
  file_url       String? // 公開存取 URL（如果適用）

  // 檔案分類
  category FileCategory @default(DOCUMENT)

  // 關聯實體
  entity_type FileEntityType?
  entity_id   String?

  // 版本控制
  version           Int     @default(1)
  parent_file_id    String? // 指向原始檔案
  is_latest_version Boolean @default(true)

  // 預覽相關
  thumbnail_path String? // 縮圖路徑
  preview_path   String? // 預覽檔案路徑
  metadata       Json? // 檔案元數據（尺寸、時長等）

  // 權限設定
  visibility     FileVisibility @default(PRIVATE)
  allow_download Boolean        @default(true)
  allow_comment  Boolean        @default(true)
  expires_at     DateTime? // 分享過期時間

  // 上傳者資訊
  uploader_id   String
  uploader_type String // "system" | "tenant"

  // 租戶隔離
  tenant_id    String
  workspace_id String?

  // 狀態
  status     FileStatus @default(ACTIVE)
  is_deleted Boolean    @default(false)
  deleted_at DateTime?

  // 時間戳
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // 關聯關係
  parent_file  shared_files?      @relation("FileVersions", fields: [parent_file_id], references: [id], onDelete: Cascade)
  versions    shared_files[]     @relation("FileVersions")
  tenants     tenants            @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces  workspaces?        @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  permissions file_permissions[]
  shares      file_shares[]
  access_logs file_access_logs[]
  vector_documents vector_documents[]

  @@index([tenant_id])
  @@index([workspace_id])
  @@index([uploader_id, uploader_type])
  @@index([entity_type, entity_id])
  @@index([parent_file_id])
  @@index([status])
  @@index([created_at])
}

model file_permissions {
  id         String         @id @default(cuid())
  file_id    String
  user_id    String
  user_type  String // "system" | "tenant"
  permission FilePermission
  granted_by String
  granted_at DateTime       @default(now())
  expires_at DateTime?
  tenant_id  String

  file    shared_files @relation(fields: [file_id], references: [id], onDelete: Cascade)
  tenants tenants      @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@unique([file_id, user_id, user_type])
  @@index([file_id])
  @@index([user_id, user_type])
  @@index([tenant_id])
}

model file_shares {
  id          String    @id @default(cuid())
  file_id     String
  share_token String    @unique
  share_type  ShareType @default(LINK)

  // 分享設定
  allow_download Boolean @default(true)
  allow_comment  Boolean @default(false)
  require_auth   Boolean @default(false)
  password       String? // 分享密碼

  // 限制設定
  max_downloads     Int? // 最大下載次數
  current_downloads Int       @default(0)
  expires_at        DateTime?

  // 分享者資訊
  shared_by      String
  shared_by_type String // "system" | "tenant"

  // 租戶隔離
  tenant_id String

  // 狀態
  is_active Boolean @default(true)

  // 時間戳
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // 關聯關係
  file        shared_files       @relation(fields: [file_id], references: [id], onDelete: Cascade)
  tenants     tenants            @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  access_logs file_access_logs[]

  @@index([file_id])
  @@index([share_token])
  @@index([tenant_id])
  @@index([expires_at])
}

model file_access_logs {
  id       String  @id @default(cuid())
  file_id  String
  share_id String? // 如果是通過分享連結存取

  // 存取者資訊
  user_id    String?
  user_type  String? // "system" | "tenant"
  ip_address String
  user_agent String?

  // 存取類型
  access_type AccessType

  // 租戶隔離
  tenant_id String

  // 時間戳
  accessed_at DateTime @default(now())

  // 關聯關係
  file    shared_files @relation(fields: [file_id], references: [id], onDelete: Cascade)
  share   file_shares? @relation(fields: [share_id], references: [id], onDelete: Cascade)
  tenants tenants      @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@index([file_id])
  @@index([share_id])
  @@index([user_id, user_type])
  @@index([tenant_id])
  @@index([accessed_at])
  @@index([access_type])
}

// 檔案系統相關枚舉
enum FileCategory {
  DOCUMENT // 文件
  IMAGE // 圖片
  VIDEO // 影片
  AUDIO // 音訊
  ARCHIVE // 壓縮檔
  SPREADSHEET // 試算表
  PRESENTATION // 簡報
  CODE // 程式碼
  OTHER // 其他
}

enum FileEntityType {
  PROJECT
  TASK
  PROGRESS_ENTRY
  MILESTONE
  COMMENT
  WORKSPACE
  USER_PROFILE
}

enum FileVisibility {
  PRIVATE // 私人（僅上傳者可見）
  WORKSPACE // 工作區成員可見
  PROJECT // 專案成員可見
  TENANT // 租戶內可見
  PUBLIC // 公開（有連結即可存取）
}

enum FileStatus {
  ACTIVE // 正常
  ARCHIVED // 已歸檔
  QUARANTINED // 隔離（安全掃描中）
  BLOCKED // 已封鎖
}

enum FilePermission {
  VIEW // 檢視
  DOWNLOAD // 下載
  COMMENT // 評論
  EDIT // 編輯（重新上傳版本）
  DELETE // 刪除
  SHARE // 分享
  MANAGE // 管理（所有權限）
}

enum ShareType {
  LINK // 連結分享
  EMAIL // 郵件分享
  EMBED // 嵌入分享
}

enum AccessType {
  VIEW // 檢視
  DOWNLOAD // 下載
  PREVIEW // 預覽
  COMMENT // 評論
  SHARE // 分享
}

// Message Center 相關模型
model message_conversations {
  id    String           @id @default(cuid())
  title String
  type  ConversationType @default(DIRECT)

  // 對話參與者
  participant_ids Json // 存儲參與者 ID 陣列

  // 最後訊息資訊
  last_message_id String?
  last_message_at DateTime?

  // 租戶隔離
  tenant_id    String
  workspace_id String?

  // 狀態
  is_active   Boolean @default(true)
  is_archived Boolean @default(false)

  // 時間戳
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  created_by String // ID of the user who created the conversation

  // 關聯關係  
  tenants    tenants                   @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces workspaces?               @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  messages   message_center_messages[]

  @@index([tenant_id])
  @@index([workspace_id])
  @@index([last_message_at])
}

model message_center_messages {
  id              String      @id @default(cuid())
  conversation_id String
  content         String
  content_type    MessageType @default(TEXT)

  // 發送者資訊
  sender_id   String
  sender_type String // "system" | "tenant"
  sender_name String

  // 回覆訊息
  reply_to_message_id String?

  // 附件
  attachments Json? // 存儲附件資訊陣列

  // 狀態
  is_read    Boolean   @default(false)
  is_edited  Boolean   @default(false)
  is_deleted Boolean   @default(false)
  deleted_at DateTime?

  // 租戶隔離
  tenant_id String

  // 時間戳
  sent_at   DateTime  @default(now())
  edited_at DateTime?
  read_at   DateTime?

  // 關聯關係
  conversation   message_conversations     @relation(fields: [conversation_id], references: [id], onDelete: Cascade)
  reply_to_message message_center_messages?  @relation("MessageReplies", fields: [reply_to_message_id], references: [id])
  replies        message_center_messages[] @relation("MessageReplies")
  tenants        tenants                   @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@index([conversation_id])
  @@index([sender_id, sender_type])
  @@index([tenant_id])
  @@index([sent_at])
  @@index([reply_to_message_id])
}

model message_center_notifications {
  id       String               @id @default(cuid())
  title    String
  message  String
  type     NotificationType     @default(INFO)
  priority NotificationPriority @default(NORMAL)

  // 接收者資訊
  recipient_id   String
  recipient_type String // "system" | "tenant"

  // 關聯資源
  entity_type String? // "project", "task", "message", etc.
  entity_id   String?
  action_url  String? // 點擊通知的跳轉連結

  // 狀態
  is_read     Boolean @default(false)
  is_archived Boolean @default(false)

  // 租戶隔離
  tenant_id    String
  workspace_id String?

  // 時間戳
  created_at  DateTime  @default(now())
  read_at     DateTime?
  archived_at DateTime?
  expires_at  DateTime? // 通知過期時間

  // 關聯關係
  tenants    tenants     @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces workspaces? @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@index([recipient_id, recipient_type])
  @@index([tenant_id])
  @@index([workspace_id])
  @@index([created_at])
  @@index([is_read])
  @@index([entity_type, entity_id])
}

// Message Center 相關枚舉
enum ConversationType {
  DIRECT // 私人對話
  GROUP // 群組對話
  ANNOUNCEMENT // 公告
  SYSTEM // 系統訊息
}

enum MessageType {
  TEXT
  RICH_TEXT
  IMAGE
  FILE
  AUDIO
  VIDEO
  LINK
  SYSTEM
}

enum NotificationType {
  INFO // 資訊
  SUCCESS // 成功
  WARNING // 警告
  ERROR // 錯誤
  REMINDER // 提醒
  ANNOUNCEMENT // 公告
}

enum NotificationPriority {
  LOW // 低
  NORMAL // 一般
  HIGH // 高
}

// Chat 相關模型
model conversations {
  id          String           @id @default(cuid())
  type        ConversationType @default(DIRECT)
  name        String?
  description String?
  avatar      String?
  is_private  Boolean          @default(false)
  is_archived Boolean          @default(false)

  // 工作區關聯
  workspace_id String
  workspaces   workspaces @relation("ChatConversations", fields: [workspace_id], references: [id], onDelete: Cascade)

  // 建立者
  created_by String

  // 最後活動
  last_message_id  String?
  last_activity_at DateTime?

  // 對話設定
  allow_file_sharing               Boolean @default(true)
  allow_image_sharing              Boolean @default(true)
  allow_video_sharing              Boolean @default(true)
  allow_audio_sharing              Boolean @default(true)
  max_file_size                    Int     @default(10485760) // 10MB
  retention_days                   Int?
  is_encrypted                     Boolean @default(false)
  require_approval_for_new_members Boolean @default(false)

  // 時間戳
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // 關聯
  participants conversation_participants[]
  messages     messages[]
  last_message  messages?                   @relation("ConversationLastMessage", fields: [last_message_id], references: [id])

  @@index([workspace_id])
  @@index([type])
  @@index([created_by])
  @@index([last_activity_at])
}

model conversation_participants {
  id              String            @id @default(cuid())
  conversation_id String
  user_id         String
  role            ParticipantRole   @default(MEMBER)
  status          ParticipantStatus @default(ACTIVE)

  // 讀取狀態
  last_seen_at         DateTime?
  last_read_message_id String?
  last_read_at         DateTime?

  // 個人設定
  is_muted            Boolean   @default(false)
  muted_until         DateTime?
  is_pinned           Boolean   @default(false)
  allow_notifications Boolean   @default(true)

  // 時間戳
  joined_at  DateTime  @default(now())
  left_at    DateTime?
  updated_at DateTime  @updatedAt

  // 關聯
  conversation conversations @relation(fields: [conversation_id], references: [id], onDelete: Cascade)

  @@unique([conversation_id, user_id])
  @@index([conversation_id])
  @@index([user_id])
  @@index([status])
}

model messages {
  id              String      @id @default(cuid())
  conversation_id String
  sender_id       String
  type            MessageType @default(TEXT)
  content         String

  // 回覆功能
  reply_to_id    String?
  reply_to_message messages?  @relation("MessageReplies", fields: [reply_to_id], references: [id])
  replies        messages[] @relation("MessageReplies")

  // 狀態
  status     MessageStatus @default(SENT)
  is_edited  Boolean       @default(false)
  edited_at  DateTime?
  is_deleted Boolean       @default(false)
  deleted_at DateTime?

  // 提及
  mentions Json? // 提及的用戶 ID 列表

  // 元數據
  metadata Json?

  // 時間戳
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // 關聯
  conversation              conversations         @relation(fields: [conversation_id], references: [id], onDelete: Cascade)
  reactions                 message_reactions[]
  attachments               message_attachments[]
  conversation_as_last_message conversations[]       @relation("ConversationLastMessage")

  @@index([conversation_id])
  @@index([sender_id])
  @@index([type])
  @@index([created_at])
}

model message_reactions {
  id         String   @id @default(cuid())
  message_id String
  user_id    String
  emoji      String
  created_at DateTime @default(now())

  // 關聯
  message messages @relation(fields: [message_id], references: [id], onDelete: Cascade)

  @@unique([message_id, user_id, emoji])
  @@index([message_id])
  @@index([user_id])
}

model message_attachments {
  id            String  @id @default(cuid())
  message_id    String
  name          String
  type          String // MIME type
  size          BigInt
  url           String
  thumbnail_url String?
  preview_url   String?

  // 檔案元數據
  metadata         Json?
  is_processed     Boolean @default(false)
  processing_error String?

  created_at DateTime @default(now())

  // 關聯
  message messages @relation(fields: [message_id], references: [id], onDelete: Cascade)

  @@index([message_id])
  @@index([type])
}

// Chat 相關枚舉
enum ParticipantRole {
  OWNER
  ADMIN
  MEMBER
  GUEST
}

enum ParticipantStatus {
  ACTIVE
  INACTIVE
  BANNED
  LEFT
}

enum MessageStatus {
  SENDING
  SENT
  DELIVERED
  READ
  FAILED
}

// OAuth 帳號模型 - 用於存儲外部認證提供商的用戶帳號
model oauth_accounts {
  id             String        @id @default(cuid())
  provider       String // 'google', 'facebook', 'github', 'line', etc.
  provider_id    String // 外部提供商的用戶 ID
  system_user_id String?
  tenant_user_id String?
  user_type      String // 'system' 或 'tenant'
  email          String? // 提供商提供的電子郵件
  profile        Json? // 存儲提供商提供的完整個人資料
  access_token   String? // 存儲加密的訪問令牌
  refresh_token  String? // 存儲加密的刷新令牌
  token_expires  DateTime? // 令牌過期時間
  last_login     DateTime      @default(now()) // 最後登入時間
  created_at     DateTime      @default(now())
  updated_at     DateTime      @updatedAt
  system_user    system_users? @relation("SystemUserOauthAccounts", fields: [system_user_id], references: [id], onDelete: Cascade)
  tenant_user    tenant_users? @relation("TenantUserOauthAccounts", fields: [tenant_user_id], references: [id], onDelete: Cascade)

  @@unique([provider, provider_id])
  @@index([system_user_id])
  @@index([tenant_user_id])
  @@index([email])
  @@map("oauth_accounts")
}

model line_users {
  id                  String   @id @default(cuid())
  line_user_id        String   @unique // The "U..." string from LINE
  display_name        String
  picture_url         String?
  status_message      String?
  last_interaction_at DateTime @default(now())
  is_following        Boolean  @default(true)
  created_at          DateTime @default(now())
  updated_at          DateTime @updatedAt
  tenant_id           String?
  workspace_id        String?
  tenant_user_id      String?
  system_user_id      String?

  tenant_user  tenant_users?       @relation(fields: [tenant_user_id], references: [id], onDelete: SetNull)
  system_user  system_users?       @relation(fields: [system_user_id], references: [id], onDelete: SetNull)
  tenant       tenants?            @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspace    workspaces?         @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  message_logs line_message_logs[]

  @@index([tenant_id])
  @@index([workspace_id])
  @@index([tenant_user_id])
  @@index([system_user_id])
}

model vector_documents {
  id            String   @id
  tenant_id     String
  file_id       String?
  workspace_id  String?
  content       String
  metadata      Json?
  embedding     Json?
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt
  tenants       tenants  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces    workspaces? @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  shared_files  shared_files? @relation(fields: [file_id], references: [id], onDelete: Cascade)
  vector_chunks vector_chunks[]

  @@index([tenant_id])
  @@index([file_id])
  @@index([workspace_id])
}

model vector_chunks {
  id            String   @id
  document_id   String
  tenant_id     String
  chunk_index   Int
  content       String
  metadata      Json?
  embedding     Json?
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt
  vector_documents vector_documents @relation(fields: [document_id], references: [id], onDelete: Cascade)
  tenants       tenants  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@index([document_id])
  @@index([tenant_id])
  @@unique([document_id, chunk_index])
}
