-- CreateEnum
CREATE TYPE "LineBotScope" AS ENUM ('SYSTEM', 'TENANT');

-- CreateTable
CREATE TABLE "line_bot_settings" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "scope" "LineBotScope" NOT NULL,
    "tenant_id" TEXT,
    "bot_secret" TEXT NOT NULL,
    "bot_token" TEXT NOT NULL,
    "webhook_url" TEXT NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "line_bot_settings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "line_bot_settings_scope_tenant_id_idx" ON "line_bot_settings"("scope", "tenant_id");

-- Add<PERSON><PERSON><PERSON><PERSON>ey
ALTER TABLE "line_bot_settings" ADD CONSTRAINT "line_bot_settings_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
