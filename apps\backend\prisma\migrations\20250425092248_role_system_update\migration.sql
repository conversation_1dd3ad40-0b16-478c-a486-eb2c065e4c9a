/*
  Warnings:

  - The `role` column on the `users` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- AlterTable
ALTER TABLE "users" DROP COLUMN "role",
ADD COLUMN     "role" TEXT NOT NULL DEFAULT 'TENANT_USER';

-- 步驟1: 建立 Role 類型如果不存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'role') THEN
        CREATE TYPE "public"."Role" AS ENUM ('SUPER_ADMIN', 'SYSTEM_ADMIN', 'TENANT_ADMIN', 'TENANT_USER');
    END IF;
END
$$;

-- 步驟2: 在 role 欄位上建立索引提升查詢效能
CREATE INDEX IF NOT EXISTS "users_role_idx" ON "users"("role");
