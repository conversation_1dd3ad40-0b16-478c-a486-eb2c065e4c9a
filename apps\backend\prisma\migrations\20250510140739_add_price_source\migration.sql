/*
  Warnings:

  - You are about to drop the `ai_bots_backup` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `ai_features` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[provider,model_name]` on the table `ai_models` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "ai_features" DROP CONSTRAINT "ai_features_keyId_fkey";

-- DropForeignKey
ALTER TABLE "ai_features" DROP CONSTRAINT "ai_features_tenantId_fkey";

-- DropIndex
DROP INDEX "ai_bots_scope_idx";

-- DropIndex
DROP INDEX "ai_models_provider_idx";

-- AlterTable
ALTER TABLE "ai_bots" ALTER COLUMN "temperature" DROP NOT NULL,
ALTER COLUMN "scope" DROP DEFAULT;

-- DropTable
DROP TABLE "ai_bots_backup";

-- DropTable
DROP TABLE "ai_features";

-- DropEnum
DROP TYPE "ResponseFormat";

-- CreateTable
CREATE TABLE "ai_price_sources" (
    "id" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_price_sources_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ai_models_provider_model_name_key" ON "ai_models"("provider", "model_name");
