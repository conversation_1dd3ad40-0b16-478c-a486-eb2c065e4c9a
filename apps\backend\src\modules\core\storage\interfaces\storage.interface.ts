/**
 * 儲存配置介面
 */
export interface StorageConfig {
  provider: 'local' | 's3' | 'azure' | 'gcs';
  region?: string;
  bucket?: string;
  accessKey?: string;
  secretKey?: string;
  endpoint?: string;
  basePath?: string;
}

/**
 * 檔案上傳選項
 */
export interface UploadOptions {
  filename?: string;
  path?: string;
  contentType?: string;
  metadata?: Record<string, any>;
  public?: boolean;
  compress?: boolean;
}

/**
 * 檔案資訊介面
 */
export interface FileInfo {
  path: string;
  size: number;
  contentType?: string;
  lastModified?: Date;
  metadata?: Record<string, any>;
  url?: string;
}

/**
 * 抽象儲存服務介面
 * 所有儲存提供商都必須實作這個介面
 */
export interface IStorageService {
  /**
   * 上傳檔案
   * @param file 檔案緩衝區或檔案物件
   * @param options 上傳選項
   * @returns 檔案路徑或識別符
   */
  uploadFile(file: Express.Multer.File | Buffer, options?: UploadOptions): Promise<string>;

  /**
   * 上傳文字內容
   * @param content 文字內容
   * @param options 上傳選項
   * @returns 檔案路徑或識別符
   */
  uploadText(content: string, options: UploadOptions): Promise<string>;

  /**
   * 下載檔案
   * @param path 檔案路徑
   * @returns 檔案緩衝區
   */
  downloadFile(path: string): Promise<Buffer>;

  /**
   * 刪除檔案
   * @param path 檔案路徑
   */
  deleteFile(path: string): Promise<void>;

  /**
   * 檢查檔案是否存在
   * @param path 檔案路徑
   * @returns 是否存在
   */
  fileExists(path: string): Promise<boolean>;

  /**
   * 獲取檔案資訊
   * @param path 檔案路徑
   * @returns 檔案資訊
   */
  getFileInfo(path: string): Promise<FileInfo>;

  /**
   * 列出目錄下的檔案
   * @param path 目錄路徑
   * @param recursive 是否遞迴
   * @returns 檔案清單
   */
  listFiles(path: string, recursive?: boolean): Promise<string[]>;

  /**
   * 獲取檔案的公開 URL（如果支援）
   * @param path 檔案路徑
   * @param expiresIn 過期時間（秒）
   * @returns 公開 URL
   */
  getPublicUrl(path: string, expiresIn?: number): Promise<string>;

  /**
   * 複製檔案
   * @param sourcePath 來源路徑
   * @param destinationPath 目標路徑
   */
  copyFile(sourcePath: string, destinationPath: string): Promise<void>;

  /**
   * 移動檔案
   * @param sourcePath 來源路徑
   * @param destinationPath 目標路徑
   */
  moveFile(sourcePath: string, destinationPath: string): Promise<void>;

  /**
   * 壓縮檔案（如果支援）
   * @param paths 檔案路徑陣列
   * @param archivePath 壓縮檔路徑
   * @param format 壓縮格式
   */
  compressFiles(paths: string[], archivePath: string, format?: 'zip' | 'gzip'): Promise<string>;

  /**
   * 測試連接
   * @returns 是否連接成功
   */
  testConnection(): Promise<boolean>;
}
