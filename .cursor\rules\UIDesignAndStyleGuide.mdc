---
description: 
globs: 
alwaysApply: true
---
# HorizAI SaaS - UI 設計與風格統一指南 (UI Design & Style Unified Guide)

**版本**: 1.0
**設計理念**: 融合 Apple Inc. 的精緻細節與 HorizAI 的創新高效精神。
**基於**: `design-system.mdc`, `color-system.mdc`, `typography.mdc`, `code-style.mdc` (部分), `global-notification.mdc`

## 1. 引言

### 1.1 文件目的

本文件旨在統一並詳細定義 HorizAI SaaS 平台的使用者介面 (UI) 設計原則、視覺風格、互動規範、排版標準、色彩系統、編碼風格以及通知系統使用指南。目標是建立一個一致、直觀、美觀且高效的使用者體驗，體現 HorizAI 的品牌價值與對卓越細節的追求。

本文檔將取代並整合以下舊有規則文件：
*   `design-system.mdc`
*   `color-system.mdc`
*   `typography.mdc`
*   `global-notification.mdc`
*   `code-style.mdc` (其中與前端 UI/UX、Vue/TypeScript 編碼風格、命名約定相關的部分)

### 1.2 核心設計哲學 (HorizAI x Apple Inc.)

1.  **清晰至上 (Clarity First)**: 介面應一目了然，操作應毫不費力。移除所有不必要的元素，聚焦核心功能與內容，讓用戶能快速理解並達成目標。
2.  **直覺互動 (Intuitive Interaction)**: 互動設計應自然且符合用戶預期。遵循用戶既有的心智模型，謹慎創新，確保新的互動方式易於學習和使用。
3.  **美學完整性 (Aesthetic Integrity)**: 追求視覺上的精緻、和諧與現代感。在排版、色彩、間距、動效和組件使用上保持高度一致性。充分運用 Shadcn-Vue 組件的優雅設計，並融入 HorizAI 的品牌識別。
4.  **使用者為中心 (User-Focused)**: 每個設計決策都應以使用者為中心。確保在各種設備和螢幕尺寸上提供流暢、愉悅的體驗 (採用 Mobile-First 響應式設計)。
5.  **無障礙設計 (Accessibility - a11y)**: 從設計之初即融入無障礙考量。使用語意化 HTML，提供必要的 ARIA 屬性，確保完整的鍵盤導航支持。一個易於使用的產品對每個人都更好。
6.  **高效與智能 (Efficiency & Intelligence - HorizAI Spirit)**: UI 設計應輔助用戶高效完成任務，並在適當時機體現產品的智能特性，提供恰到好處的引導與反饋。

## 2. 核心工具與技術

*   **UI 組件庫 (UI Components)**: 設計**完全**基於 [Shadcn-Vue](mdc:https:/www.shadcn-vue.com) 組件庫 (`apps/frontend/src/components/ui`)。當需要客製化時，優先考慮擴展或包裝現有的 Shadcn 組件，以保持設計語言的一致性。
*   **樣式 (Styling)**: **嚴格**使用 [Tailwind CSS](mdc:https:/tailwindcss.com) 進行樣式設計，遵循 `apps/frontend/tailwind.config.js` 中的設定。**禁止**使用自訂 CSS 檔案或行內樣式 (除非在極少數 Shadcn 主題定制等必要情況下，且需團隊審核)，以確保可維護性和一致性。
*   **圖示 (Icons)**: 優先使用 [Lucide Icons](mdc:https:/lucide.dev) (通常與 Shadcn-Vue 整合良好) 或專案指定的 SVG 圖示庫，確保風格統一。

## 3. 色彩系統 (Color System)

(本節內容整合自原 `color-system.mdc`)

色彩定義於 `apps/frontend/tailwind.config.js` 中，使用 CSS 變數 (例如 `hsl(var(--primary))`) 以支援主題化 (淺色/深色模式)。

### 3.1 核心色彩 (Core Colors)
*   `background`: `hsl(var(--background))` - 預設背景色。
*   `foreground`: `hsl(var(--foreground))` - 預設文字/圖示顏色。
*   `border`: `hsl(var(--border))` - 邊框顏色 (用於輸入框、卡片等)。
*   `input`: `hsl(var(--input))` - 輸入框背景/邊框顏色。
*   `ring`: `hsl(var(--ring))` - 焦點環顏色。

### 3.2 主要色彩 (Primary Colors)
*   `primary`: `hsl(var(--primary))` - 主要互動元素顏色 (按鈕、連結)。
*   `primary-foreground`: `hsl(var(--primary-foreground))` - 用於 `primary` 背景上的文字/圖示顏色。

### 3.3 次要色彩 (Secondary Colors)
*   `secondary`: `hsl(var(--secondary))` - 次要互動元素顏色。
*   `secondary-foreground`: `hsl(var(--secondary-foreground))` - 用於 `secondary` 背景上的文字/圖示顏色。

### 3.4 強調/破壞性色彩 (Accent/Destructive Colors)
*   `accent`: `hsl(var(--accent))` - 強調色，用於背景 (如懸停狀態)。
*   `accent-foreground`: `hsl(var(--accent-foreground))` - 用於 `accent` 背景上的文字/圖示顏色。
*   `destructive`: `hsl(var(--destructive))` - 破壞性操作顏色 (如刪除按鈕)。
*   `destructive-foreground`: `hsl(var(--destructive-foreground))` - 用於 `destructive` 背景上的文字/圖示顏色。

### 3.5 其他語意化色彩
*   `muted`: `hsl(var(--muted))` - 柔和背景色。
*   `muted-foreground`: `hsl(var(--muted-foreground))` - 柔和文字/圖示顏色。
*   `popover`: `hsl(var(--popover))` - 彈出框背景色。
*   `popover-foreground`: `hsl(var(--popover-foreground))` - 彈出框內文字/圖示顏色。
*   `card`: `hsl(var(--card))` - 卡片背景色。
*   `card-foreground`: `hsl(var(--card-foreground))` - 卡片內文字/圖示顏色。

### 3.6 圖表色彩 (Chart Colors)
*   `chart-1`: `hsl(var(--chart-1))`
*   `chart-2`: `hsl(var(--chart-2))`
*   `chart-3`: `hsl(var(--chart-3))`
*   `chart-4`: `hsl(var(--chart-4))`
*   `chart-5`: `hsl(var(--chart-5))`

### 3.7 色彩使用指南
*   在 Tailwind class 中使用這些語意化色彩名稱 (例如 `bg-primary`, `text-destructive-foreground`)。
*   避免直接使用任意色值。
*   參考 `apps/frontend/src/assets/styles/globals.css` (或類似主題設定檔) 中的 CSS 變數定義，了解淺色和深色模式下的具體 HSL 值。
*   確保所有色彩實現完整，無 TODO 或未完成的色彩定義。

## 4. 排版標準 (Typography)

(本節內容整合自原 `typography.mdc`)

所有排版應透過 Tailwind CSS 工具類別實現。

### 4.1 字體家族 (Font Families)
*   **主要字體 (Sans Serif)**: 優先使用系統預設無襯線字體 (`font-sans`)。
*   **等寬字體 (Monospace)**: 用於程式碼等內容 (`font-mono`)。
*   **特定字體**: 若有品牌需求，在 `tailwind.config.js` 中配置並建立工具類。

### 4.2 字體大小與行高 (Font Size & Line Height)
*   **基礎大小**: `text-base` (通常 16px)。
*   **層級結構**: 使用 Tailwind 標準字體大小工具類 (如 `text-xs` 至 `text-2xl` 等)。
*   **行高**: 搭配使用 Tailwind 行高工具類 (如 `leading-tight`, `leading-normal`)。
*   **響應式**: 使用 `md:text-lg` 等修飾符調整不同斷點的字體大小。

### 4.3 字體粗細 (Font Weight)
*   使用 Tailwind 字體粗細工具類 (如 `font-normal`, `font-medium`, `font-semibold`, `font-bold`)。
*   避免過多不同粗細，保持簡潔。

### 4.4 排版使用原則
*   **一致性**: 整個應用程式中保持排版樣式一致。
*   **可讀性**: 優先考慮文字可讀性 (對比度、大小、行高)。
*   **語意化**: 結合 HTML 語意化標籤使用排版樣式。
*   **Tailwind 優先**: 嚴格透過 Tailwind CSS 工具類控制排版。

## 5. 組件特定指南 (Component-Specific Guidelines)

*   **模態框 (Modals)**: 所有模態對話框 (如 Shadcn-Vue 的 `AlertDialog` 或 `Dialog`) **必須**呈現為**水平居中** (例如，在螢幕中心水平展開，而非垂直覆蓋)。這有助於在不同設備上保持視覺焦點和互動一致性。
*   **可重用通用組件**: 當特定功能或 UI 模式需要在多個視圖中重用時，應基於 Shadcn-Vue 基礎組件，在 `apps/frontend/src/components/common` 目錄下建立可重用的通用組件。
*   **表單 (Forms)**: 
    *   輸入框、選擇器等表單元素應有清晰的 `label`，並在活躍、錯誤、禁用狀態下有明確的視覺反饋。
    *   驗證提示應清晰、即時且不具侵略性。
*   **按鈕 (Buttons)**: 
    *   主要操作按鈕 (`primary`)、次要操作按鈕 (`secondary`)、破壞性操作按鈕 (`destructive`) 應有明確區分。
    *   按鈕大小、圖示使用應保持一致性。
*   **資料表格 (Data Tables)**: 
    *   應提供清晰的排序、篩選、分頁功能。
    *   在小螢幕上應有良好的響應式表現 (例如轉為卡片列表或允許水平滾動)。
*   **空白狀態 (Empty States)**: 當列表、圖表或內容區域無數據時，應提供友善的空白狀態提示，並引導使用者進行下一步操作。

## 6. 全局通知系統 (Global Notification System)

(本節內容整合自原 `global-notification.mdc`)

### 6.1 通知類型
*   **Toast**: 輕量級、短暫通知 (右下角)，用於快速反饋、確認。
*   **Flash**: 頁面級、高重要性通知 (頁面頂部橫幅)，用於關鍵訊息、錯誤。

### 6.2 放置位置
*   `<Toaster />` (Toast) 和 `<FlashMessage />` (Flash) 組件**僅能**放置在根組件 `App.vue` 中一次。

### 6.3 使用方式
*   **Toast**: 使用 Shadcn-Vue 的 `useToast()` API。
*   **Flash**: 使用專案提供的 `useFlashMessage()` composable。
*   可選用統一的 `useNotification()` composable 進行調用。

### 6.4 使用場景與決策流程
*   **Toast**: 非阻塞性、操作成功後的短暫反饋。
*   **Flash**: 重要的、阻塞性的、或影響主流程的錯誤/警告。
*   **禁止**: 正常頁面載入成功**不應**顯示任何通知。
*   **持續性異常狀態** (如系統健康度) 應直接在 UI 區塊顯示，而非依賴通知。

### 6.5 最佳實踐
*   單一通知狀態來源 (Store/Composable)。
*   類型安全 (使用 `notification.model.ts`)。
*   無冗餘放置，樣式一致，無障礙。
*   所有通知透過 `useNotification()` (或其子方法) 觸發。
*   內容簡潔清晰，不重複觸發。

## 7. 前端編碼風格與慣例 (Frontend Coding Style & Conventions)

(本節內容主要提煉整合自原 `code-style.mdc` 中與前端 UI/UX 開發直接相關的部分，並與 `ApplicationArchitectureGuide.mdc` 中的命名約定協調)

### 7.1 TypeScript 使用指南 (Frontend Focus)
*   所有前端程式碼使用 TypeScript。
*   明確定義函式參數和返回值的類型。
*   物件定義優先使用 `interface` (用於定義公開 API 或組件 Props)，複雜內部類型或聯合/交集類型可使用 `type`。
*   避免使用 `any` 類型，盡可能使用更精確的類型或 `unknown`。
*   正確使用類型導入/匯出 (`import type`, `export type`)。

### 7.2 Vue 3 使用指南
*   **Composition API**: 全面使用 Composition API (`<script setup>`)。
*   **Props 驗證**: 組件 Props 需有明確的類型定義和必要的運行時驗證 (如 `required`, `default`, `validator`)。
*   **生命週期鉤子**: 遵循 Vue 3 標準命名。
*   **錯誤邊界 (Error Boundaries)**: 對於可能出錯的組件區域，考慮使用 Vue 的錯誤邊界機制來優雅處理錯誤，防止整個應用崩潰。
*   **響應式資料**: 合理使用 `ref`, `reactive`, `computed`, `watchEffect`。避免不必要的深度響應性對象，適時使用 `shallowRef`。

### 7.3 命名約定 (Frontend Focus)
*   **組件 (Components)**: `PascalCase` (例如 `UserProfile.vue`, `BaseModal.vue`)。
    *   頁面級組件命名參考 `ApplicationArchitectureGuide.mdc`。
*   **Composables**: `use` 前綴 + `camelCase` (例如 `useAuth`, `useFormValidation`)。
*   **變數與函式 (Variables & Functions)**: `camelCase` (例如 `userName`, `fetchUserData`)。
    *   事件處理函式建議以 `handle` 開頭 (例如 `handleSubmit`, `handleInputChange`)。
*   **Pinia Stores**: `use` 前綴 + Store 名稱 + `Store` 後綴，`camelCase` (例如 `useCartStore`, `useSettingsStore`)。
*   **常數 (Constants)**: `UPPER_SNAKE_CASE` (例如 `MAX_USERS`, `API_TIMEOUT`)。
*   **CSS Class (Tailwind)**: 使用 Tailwind 預設的 utility classes。若需自訂 class (應極少)，遵循 `kebab-case`。
*   **路由名稱 (Route Names)**: `kebab-case` 或 `PascalCase` (取決於團隊約定，需一致)。
*   **檔案與目錄**: 參考 `ApplicationArchitectureGuide.mdc` 中的檔案與目錄命名規範。

### 7.4 程式碼註釋 (Frontend Focus)
*   **Vue 組件**: 對於複雜組件的 Props, Emits, Slots 應有 JSDoc 註釋。
*   **Composables**: 導出的函式和狀態應有 JSDoc 註釋，說明其用途、參數和返回值。
*   **複雜邏輯**: 對於非顯而易見的業務邏輯或演算法，應添加簡潔註釋說明「為什麼」這麼做，而不是「做了什麼」。
*   **TODOs**: 開發過程中允許使用 `// TODO:` 或 `// FIXME:` 標記，但應註明原因和預期完成時間，並在提交前盡可能解決。

### 7.5 樣式與佈局 (Styling & Layout)
*   **Tailwind CSS 優先**: 所有樣式和佈局應優先且主要透過 Tailwind CSS 工具類實現。
*   **Mobile-First**: 遵循移動優先的響應式設計方法。
*   **語意化 HTML**: 使用正確的 HTML 標籤 (例如 `nav`, `aside`, `main`, `article`, `button`) 以提升可讀性和無障礙性。
*   **無障礙性 (a11y)**: 
    *   互動元素 (按鈕、連結、輸入框) 應可通過鍵盤訪問和操作。
    *   為非文字內容提供替代文字 (例如 `<img>` 的 `alt` 屬性)。
    *   使用 ARIA 屬性增強非標準 UI 元素的可訪問性 (但優先使用原生 HTML 元素)。
    *   確保足夠的色彩對比度。

### 7.6 (新增) 權限管理界面 UI/UX 原則 (Admin Console Focus)
*   **清晰性與直觀性**: 權限管理界面（尤其是在管理後台為角色分配權限的部分）的設計應以清晰易懂和操作直觀為首要目標。
*   **高效操作**: 應提供高效的方式讓管理員查看、篩選、搜尋和指派權限。考慮使用視覺化工具（如權限樹）和便捷操作（如拖拽指派、批量處理）。
*   **信息反饋**: 對於所有管理操作，系統應提供即時且明確的成功或失敗反饋。
*   **錯誤預防**: 對於可能產生重大影響的敏感操作（如刪除角色、移除關鍵權限），應設計警告提示或二次確認流程。
*   **一致性**: 權限管理界面的視覺風格和互動模式應與 HorizAI SaaS 平台的整體設計語言保持一致。
*   **易學性**: 即使是初次接觸系統的管理員，也應能快速理解並上手權限管理功能。
*   (詳細的權限管理 UI/UX 設計考量，可進一步參考 `AuthAndAccessControlGuide.mdc` 中關於此部分的描述。)

## 8. 實施與維護

*   **一致性檢查**: 定期進行設計和程式碼審查，確保本指南的原則得到遵循。
*   **組件庫更新**: 關注 Shadcn-Vue 的更新，並評估升級帶來的影響。
*   **文件同步**: 本指南應隨著設計系統和技術棧的演進而更新。
*   **完整性**: 確保所有 UI/UX 設計、組件功能和風格實現都完整，無 TODO 或未完成的部分 (指最終交付狀態)。

---
本文檔是 HorizAI SaaS 平台 UI/UX 設計與前端風格的**核心指導原則**，旨在打造卓越的使用者體驗。

