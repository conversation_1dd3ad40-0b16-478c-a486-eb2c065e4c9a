import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsISO8601,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  Min,
  IsEmail,
} from 'class-validator';
import { OrderStatus } from '../interfaces/order.interface';

export class UpdateOrderDto {
  @ApiProperty({
    description: '租戶名稱',
    example: '範例企業',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '租戶名稱必須是字串' })
  tenantName?: string;

  @ApiProperty({
    description: '方案名稱',
    example: '企業方案',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '方案名稱必須是字串' })
  planName?: string;

  @ApiProperty({
    description: '訂單金額',
    example: 30000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '訂單金額必須是數字' })
  @IsPositive({ message: '訂單金額必須大於零' })
  amount?: number;

  @ApiProperty({
    description: '訂閱期間（月）',
    example: 12,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '訂閱期間必須是數字' })
  @Min(1, { message: '訂閱期間至少為 1 個月' })
  period?: number;

  @ApiProperty({
    description: '訂閱人數',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '訂閱人數必須是數字' })
  @Min(1, { message: '訂閱人數至少為 1 人' })
  numberOfSubscribers?: number;

  @ApiProperty({
    description: '訂閱開始日期',
    example: '2023-06-01',
    required: false,
  })
  @IsOptional()
  @IsISO8601({}, { message: '訂閱開始日期格式不正確' })
  startDate?: string;

  @ApiProperty({
    description: '訂閱結束日期',
    example: '2024-05-31',
    required: false,
  })
  @IsOptional()
  @IsISO8601({}, { message: '訂閱結束日期格式不正確' })
  endDate?: string;

  @ApiProperty({
    description: '帳單週期',
    example: 'monthly',
    enum: ['monthly', 'yearly'],
    default: 'monthly',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '帳單週期必須是字串' })
  @IsEnum(['monthly', 'yearly'], { message: '帳單週期必須是 monthly 或 yearly' })
  billingCycle?: string;

  @ApiProperty({
    description: '訂單狀態',
    example: 'PENDING',
    enum: Object.values(OrderStatus),
    required: false,
  })
  @IsOptional()
  @IsEnum(OrderStatus, { message: '訂單狀態無效' })
  status?: OrderStatus;

  @ApiProperty({
    description: '訂單備註',
    example: '此訂單包含優惠折扣',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '訂單備註必須是字串' })
  remarks?: string;

  @ApiProperty({
    description: '操作者',
    example: 'admin',
    required: false,
  })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  // 付款相關欄位
  @ApiProperty({
    description: '付款方式',
    example: '銀行轉帳',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '付款方式必須是字串' })
  paymentMethod?: string;

  @ApiProperty({
    description: '付款狀態',
    example: 'pending',
    enum: ['pending', 'paid', 'failed'],
    required: false,
  })
  @IsOptional()
  @IsString({ message: '付款狀態必須是字串' })
  @IsEnum(['pending', 'paid', 'failed'], { message: '付款狀態必須是 pending、paid 或 failed' })
  paymentStatus?: string;

  // 聯絡人相關欄位
  @ApiProperty({
    description: '聯絡人',
    example: '張三',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '聯絡人必須是字串' })
  contactName?: string;

  @ApiProperty({
    description: '聯絡信箱',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: '聯絡信箱格式不正確' })
  contactEmail?: string;
}
