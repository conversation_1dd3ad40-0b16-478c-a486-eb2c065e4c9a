/*
  Warnings:

  - Added the required column `createdBy` to the `ai_bots` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "AiBotScope" AS ENUM ('ADMIN', 'WORKSPACE', 'BACKEND');

-- AlterTable
ALTER TABLE "ai_bots" ADD COLUMN     "createdBy" TEXT NOT NULL,
ADD COLUMN     "isEnabled" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "isTemplate" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "scope" "AiBotScope" NOT NULL DEFAULT 'WORKSPACE',
ADD COLUMN     "tenantId" TEXT,
ADD COLUMN     "updatedBy" TEXT,
ALTER COLUMN "scene" DROP NOT NULL;

-- CreateIndex
CREATE INDEX "ai_bots_scope_idx" ON "ai_bots"("scope");

-- CreateIndex
CREATE INDEX "ai_bots_tenantId_idx" ON "ai_bots"("tenantId");

-- CreateIndex
CREATE INDEX "ai_bots_scope_tenantId_idx" ON "ai_bots"("scope", "tenantId");

-- AddForeignKey
ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_bots" ADD CONSTRAINT "ai_bots_updatedBy_fkey" FOREIGN KEY ("updatedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
