export enum OrderStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
}

export interface Order {
  id: string;
  tenant_name: string;
  plan_name: string;
  amount: number;
  period: number;
  number_of_subscribers: number;
  start_date: string;
  end_date: string;
  status: OrderStatus;
  billing_cycle: string;
  created_at: string;
  updated_at: string;
  remarks?: string;
}

export interface OrderDetail extends Order {
  payment_method: string;
  payment_status: string;
  contact_name: string;
  contact_email: string;
  tenant: {
    id: string;
    name: string;
    contact_name: string;
    contact_email: string;
  };
  plan: {
    name: string;
    description: string;
    price: number;
    billing_cycle: string;
    users_limit: number;
    workspaces_limit: number;
  };
  order_history: OrderHistoryItem[];
}

export interface OrderHistoryItem {
  type: 'info' | 'success' | 'warning' | 'danger';
  date: string;
  status: string;
  description: string;
  by: string | null;
}
