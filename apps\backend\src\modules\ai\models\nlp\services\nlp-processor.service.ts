import { Injectable, Logger } from '@nestjs/common';
import nlp from 'compromise';
import sentences from 'compromise-sentences';
import numbers from 'compromise-numbers';
import dates from 'compromise-dates';
import Sentiment from 'sentiment';
import {
  NLPProcessingResult,
  Token,
  Sentence,
  RequirementEntity,
  DependencyRelation,
  SentimentAnalysis,
  Keyword,
  Topic,
  EntityLabel,
  RequirementParsingOptions
} from '../types/requirement-parsing.types';

// 擴展 compromise
nlp.extend(sentences);
nlp.extend(numbers);
nlp.extend(dates);

/**
 * 簡單的詞幹提取器（替代 Natural.js 的 Porter Stemmer）
 */
class SimpleStemmer {
  static stem(word: string): string {
    word = word.toLowerCase();
    
    // 簡單的英文詞幹規則
    if (word.endsWith('ing')) {
      return word.slice(0, -3);
    }
    if (word.endsWith('ed')) {
      return word.slice(0, -2);
    }
    if (word.endsWith('er')) {
      return word.slice(0, -2);
    }
    if (word.endsWith('est')) {
      return word.slice(0, -3);
    }
    if (word.endsWith('ly')) {
      return word.slice(0, -2);
    }
    if (word.endsWith('s') && word.length > 3) {
      return word.slice(0, -1);
    }
    
    return word;
  }
}

/**
 * 簡單的分詞器（替代 Natural.js 的 WordTokenizer）
 */
class SimpleTokenizer {
  tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 0);
  }
}

/**
 * 英文停用詞列表（替代 Natural.js 的 stopwords）
 */
const STOP_WORDS = new Set([
  'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from', 'has', 'he',
  'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the', 'to', 'was', 'will', 'with',
  'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', 'your', 'yours',
  'yourself', 'yourselves', 'him', 'his', 'himself', 'she', 'her', 'hers', 'herself',
  'they', 'them', 'their', 'theirs', 'themselves', 'what', 'which', 'who', 'whom',
  'this', 'that', 'these', 'those', 'am', 'is', 'are', 'was', 'were', 'being',
  'been', 'have', 'has', 'had', 'having', 'do', 'does', 'did', 'doing', 'would',
  'should', 'could', 'ought', 'i\'m', 'you\'re', 'he\'s', 'she\'s', 'it\'s',
  'we\'re', 'they\'re', 'i\'ve', 'you\'ve', 'we\'ve', 'they\'ve', 'i\'d',
  'you\'d', 'he\'d', 'she\'d', 'we\'d', 'they\'d', 'i\'ll', 'you\'ll',
  'he\'ll', 'she\'ll', 'we\'ll', 'they\'ll'
]);

/**
 * NLP 處理服務
 * 負責基礎的自然語言處理任務，包括：
 * - 文本預處理
 * - 分詞和句子分割
 * - 詞性標註
 * - 命名實體識別
 * - 依存關係分析
 * - 情感分析
 * - 關鍵字提取
 */
@Injectable()
export class NLPProcessorService {
  private readonly logger = new Logger(NLPProcessorService.name);
  private readonly sentimentAnalyzer: any;
  private readonly stemmer = SimpleStemmer;
  private readonly tokenizer = new SimpleTokenizer();

  constructor() {
    this.sentimentAnalyzer = new Sentiment();
    this.logger.log('NLP Processor Service initialized with compromise');
  }

  /**
   * 處理文本並返回完整的 NLP 分析結果
   */
  async processText(
    text: string,
    options: RequirementParsingOptions = {}
  ): Promise<NLPProcessingResult> {
    const startTime = Date.now();
    
    try {
      this.logger.debug(`Processing text: ${text.substring(0, 100)}...`);

      // 使用 compromise 進行基礎 NLP 處理
      const doc = nlp(text);
      
      // 提取句子
      const sentences = this.extractSentences(text, doc);
      
      // 提取詞彙
      const tokens = this.extractTokens(text, doc);
      
      // 命名實體識別
      const entities = options.includeEntities !== false 
        ? this.extractEntities(doc, text) 
        : [];
      
      // 依存關係分析（簡化版本）
      const dependencies = options.includeDependencies !== false
        ? this.extractDependencies(doc)
        : [];
      
      // 情感分析
      const sentimentResult = options.includeSentiment !== false
        ? this.analyzeSentiment(text)
        : { polarity: 0, subjectivity: 0, label: 'neutral' as const, confidence: 0 };
      
      // 關鍵字提取
      const keywords = options.includeKeywords !== false
        ? this.extractKeywords(text, tokens)
        : [];
      
      // 主題分析（簡化版本）
      const topics = options.includeTopics === true
        ? this.extractTopics(text, keywords)
        : [];

      const processingTime = Date.now() - startTime;
      this.logger.debug(`Text processing completed in ${processingTime}ms`);

      return {
        tokens,
        sentences,
        entities,
        dependencies,
        sentiment: sentimentResult,
        keywords,
        topics
      };

    } catch (error) {
      this.logger.error('Error processing text:', error);
      throw new Error(`NLP processing failed: ${error.message}`);
    }
  }

  /**
   * 提取句子
   */
  private extractSentences(text: string, doc: any): Sentence[] {
    const sentences: Sentence[] = [];
    const sentenceTexts = doc.sentences().out('array');
    
    let currentIndex = 0;
    
    sentenceTexts.forEach((sentenceText: string, index: number) => {
      const start = text.indexOf(sentenceText, currentIndex);
      const end = start + sentenceText.length;
      
      // 為每個句子提取詞彙
      const sentenceDoc = nlp(sentenceText);
      const sentenceTokens = this.extractTokensFromDoc(sentenceDoc, start);
      
      // 句子級別的情感分析
      const sentimentScore = this.sentimentAnalyzer.analyze(sentenceText).score;
      
      sentences.push({
        text: sentenceText,
        start,
        end,
        tokens: sentenceTokens,
        sentiment: sentimentScore
      });
      
      currentIndex = end;
    });
    
    return sentences;
  }

  /**
   * 提取詞彙
   */
  private extractTokens(text: string, doc: any): Token[] {
    return this.extractTokensFromDoc(doc, 0);
  }

  /**
   * 從 compromise 文檔提取詞彙
   */
  private extractTokensFromDoc(doc: any, offset: number = 0): Token[] {
    const tokens: Token[] = [];
    const terms = doc.terms().out('array');
    
    terms.forEach((term: any, index: number) => {
      const termText = typeof term === 'string' ? term : term.text || term;
      const termObj = doc.match(termText);
      
      // 獲取詞性標註
      const pos = this.getPOSTag(termObj);
      
      // 獲取詞根
      const lemma = this.stemmer.stem(termText.toLowerCase());
      
      tokens.push({
        text: termText,
        pos,
        lemma,
        index: index + offset,
        isStop: STOP_WORDS.has(termText.toLowerCase()),
        isAlpha: /^[a-zA-Z\u4e00-\u9fff]+$/.test(termText),
        isDigit: /^\d+$/.test(termText)
      });
    });
    
    return tokens;
  }

  /**
   * 獲取詞性標註
   */
  private getPOSTag(term: any): string {
    if (!term || !term.tags) return 'UNKNOWN';
    
    // compromise 的標籤映射
    const tags = term.tags();
    if (tags.includes('Noun')) return 'NOUN';
    if (tags.includes('Verb')) return 'VERB';
    if (tags.includes('Adjective')) return 'ADJ';
    if (tags.includes('Adverb')) return 'ADV';
    if (tags.includes('Pronoun')) return 'PRON';
    if (tags.includes('Preposition')) return 'ADP';
    if (tags.includes('Determiner')) return 'DET';
    if (tags.includes('Conjunction')) return 'CONJ';
    if (tags.includes('Number')) return 'NUM';
    if (tags.includes('Punctuation')) return 'PUNCT';
    
    return 'UNKNOWN';
  }

  /**
   * 命名實體識別
   */
  private extractEntities(doc: any, text: string): RequirementEntity[] {
    const entities: RequirementEntity[] = [];
    
    try {
      // 使用 compromise 進行基礎實體識別
      const people = doc.people().out('array');
      const places = doc.places().out('array');
      const organizations = doc.organizations().out('array');
      const dates = doc.dates().out('array');
      const money = doc.money().out('array');
      
      // 處理人名
      people.forEach((person: string) => {
        const matches = this.findEntityPositions(text, person);
        matches.forEach(match => {
          entities.push({
            text: person,
            label: EntityLabel.ACTOR,
            start: match.start,
            end: match.end,
            confidence: 0.8
          });
        });
      });
      
      // 處理地點
      places.forEach((place: string) => {
        const matches = this.findEntityPositions(text, place);
        matches.forEach(match => {
          entities.push({
            text: place,
            label: EntityLabel.LOCATION,
            start: match.start,
            end: match.end,
            confidence: 0.7
          });
        });
      });
      
      // 處理組織
      organizations.forEach((org: string) => {
        const matches = this.findEntityPositions(text, org);
        matches.forEach(match => {
          entities.push({
            text: org,
            label: EntityLabel.ACTOR,
            start: match.start,
            end: match.end,
            confidence: 0.7
          });
        });
      });
      
      // 處理時間
      dates.forEach((date: string) => {
        const matches = this.findEntityPositions(text, date);
        matches.forEach(match => {
          entities.push({
            text: date,
            label: EntityLabel.TIME,
            start: match.start,
            end: match.end,
            confidence: 0.9
          });
        });
      });
      
      // 處理金額
      money.forEach((amount: string) => {
        const matches = this.findEntityPositions(text, amount);
        matches.forEach(match => {
          entities.push({
            text: amount,
            label: EntityLabel.METRIC,
            start: match.start,
            end: match.end,
            confidence: 0.9
          });
        });
      });
      
      // 使用規則識別更多實體類型
      entities.push(...this.extractCustomEntities(text));
      
    } catch (error) {
      this.logger.warn('Error in entity extraction:', error);
    }
    
    return entities;
  }

  /**
   * 查找實體在文本中的位置
   */
  private findEntityPositions(text: string, entity: string): Array<{start: number, end: number}> {
    const positions: Array<{start: number, end: number}> = [];
    let index = 0;
    
    while (index < text.length) {
      const found = text.indexOf(entity, index);
      if (found === -1) break;
      
      positions.push({
        start: found,
        end: found + entity.length
      });
      
      index = found + entity.length;
    }
    
    return positions;
  }

  /**
   * 使用自定義規則提取實體
   */
  private extractCustomEntities(text: string): RequirementEntity[] {
    const entities: RequirementEntity[] = [];
    
    // 動作詞模式
    const actionPatterns = [
      /\b(需要|必須|應該|能夠|可以|執行|實現|提供|支持|處理|管理|創建|刪除|更新|查詢|顯示|輸入|輸出|驗證|確認|通知|發送|接收)\b/g,
      /\b(create|update|delete|read|insert|select|display|show|input|output|validate|confirm|notify|send|receive)\b/gi
    ];
    
    actionPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        entities.push({
          text: match[0],
          label: EntityLabel.ACTION,
          start: match.index,
          end: match.index + match[0].length,
          confidence: 0.8
        });
      }
    });
    
    // 對象模式
    const objectPatterns = [
      /\b(用戶|使用者|客戶|管理員|系統|數據|資料|文件|報告|界面|頁面|功能|模組|服務|API|數據庫|表格|欄位)\b/g,
      /\b(user|customer|admin|system|data|file|report|interface|page|function|module|service|database|table|field)\b/gi
    ];
    
    objectPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        entities.push({
          text: match[0],
          label: EntityLabel.OBJECT,
          start: match.index,
          end: match.index + match[0].length,
          confidence: 0.7
        });
      }
    });
    
    // 條件模式
    const conditionPatterns = [
      /\b(如果|當|在|條件|情況下|狀態|模式)\b/g,
      /\b(if|when|while|condition|case|state|mode)\b/gi
    ];
    
    conditionPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        entities.push({
          text: match[0],
          label: EntityLabel.CONDITION,
          start: match.index,
          end: match.index + match[0].length,
          confidence: 0.6
        });
      }
    });
    
    return entities;
  }

  /**
   * 依存關係分析（簡化版本）
   */
  private extractDependencies(doc: any): DependencyRelation[] {
    const dependencies: DependencyRelation[] = [];
    
    try {
      // 使用 compromise 的基礎語法分析
      const sentences = doc.sentences();
      
      sentences.forEach((sentence: any, sentIndex: number) => {
        const terms = sentence.terms();
        
        // 簡單的主謂賓關係識別
        const nouns = sentence.nouns().out('array');
        const verbs = sentence.verbs().out('array');
        
        if (nouns.length > 0 && verbs.length > 0) {
          // 假設第一個名詞是主語，第一個動詞是謂語
          const subject = nouns[0];
          const predicate = verbs[0];
          
          if (nouns.length > 1) {
            // 如果有多個名詞，假設後面的是賓語
            const object = nouns[1];
            
            dependencies.push({
              head: predicate,
              dependent: subject,
              relation: 'nsubj',
              headIndex: 0,
              dependentIndex: 0
            });
            
            dependencies.push({
              head: predicate,
              dependent: object,
              relation: 'dobj',
              headIndex: 0,
              dependentIndex: 0
            });
          }
        }
      });
      
    } catch (error) {
      this.logger.warn('Error in dependency parsing:', error);
    }
    
    return dependencies;
  }

  /**
   * 情感分析
   */
  private analyzeSentiment(text: string): SentimentAnalysis {
    try {
      const result = this.sentimentAnalyzer.analyze(text);
      
      // 正規化分數到 -1 到 1 的範圍
      const normalizedScore = Math.max(-1, Math.min(1, result.score / 10));
      
      let label: 'positive' | 'negative' | 'neutral';
      if (normalizedScore > 0.1) {
        label = 'positive';
      } else if (normalizedScore < -0.1) {
        label = 'negative';
      } else {
        label = 'neutral';
      }
      
      return {
        polarity: normalizedScore,
        subjectivity: Math.abs(normalizedScore), // 簡化的主觀性計算
        label,
        confidence: Math.abs(normalizedScore)
      };
      
    } catch (error) {
      this.logger.warn('Error in sentiment analysis:', error);
      return {
        polarity: 0,
        subjectivity: 0,
        label: 'neutral',
        confidence: 0
      };
    }
  }

  /**
   * 關鍵字提取
   */
  private extractKeywords(text: string, tokens: Token[]): Keyword[] {
    try {
      // 過濾出有意義的詞彙（名詞、動詞、形容詞）
      const meaningfulTokens = tokens.filter(token => 
        !token.isStop && 
        token.isAlpha && 
        token.text.length > 2 &&
        ['NOUN', 'VERB', 'ADJ'].includes(token.pos)
      );
      
      // 計算詞頻
      const termFreq = new Map<string, number>();
      meaningfulTokens.forEach(token => {
        const term = token.lemma.toLowerCase();
        termFreq.set(term, (termFreq.get(term) || 0) + 1);
      });
      
      // 計算簡化的 TF-IDF 分數（這裡只用 TF，因為我們只有一個文檔）
      const keywords: Keyword[] = [];
      const totalTokens = meaningfulTokens.length;
      
      termFreq.forEach((frequency, term) => {
        const tf = frequency / totalTokens;
        // 簡化的分數計算：TF * 詞長度權重
        const score = tf * Math.log(term.length + 1);
        
        keywords.push({
          text: term,
          score,
          frequency
        });
      });
      
      return keywords
        .sort((a, b) => b.score - a.score)
        .slice(0, 20); // 取前 20 個關鍵字
      
    } catch (error) {
      this.logger.warn('Error in keyword extraction:', error);
      return [];
    }
  }

  /**
   * 主題提取（簡化版本）
   */
  private extractTopics(text: string, keywords: Keyword[]): Topic[] {
    try {
      // 基於關鍵字聚類的簡化主題提取
      const topics: Topic[] = [];
      
      // 技術相關主題
      const techKeywords = keywords.filter(k => 
        /\b(系統|技術|開發|程式|軟體|硬體|網路|數據庫|API|界面|功能|模組)\b/i.test(k.text) ||
        /\b(system|technology|development|program|software|hardware|network|database|interface|function|module)\b/i.test(k.text)
      );
      
      if (techKeywords.length > 0) {
        topics.push({
          label: '技術需求',
          keywords: techKeywords.slice(0, 5).map(k => k.text),
          score: techKeywords.reduce((sum, k) => sum + k.score, 0) / techKeywords.length
        });
      }
      
      // 用戶相關主題
      const userKeywords = keywords.filter(k => 
        /\b(用戶|使用者|客戶|操作|體驗|界面|交互)\b/i.test(k.text) ||
        /\b(user|customer|operation|experience|interface|interaction)\b/i.test(k.text)
      );
      
      if (userKeywords.length > 0) {
        topics.push({
          label: '用戶需求',
          keywords: userKeywords.slice(0, 5).map(k => k.text),
          score: userKeywords.reduce((sum, k) => sum + k.score, 0) / userKeywords.length
        });
      }
      
      // 業務相關主題
      const businessKeywords = keywords.filter(k => 
        /\b(業務|商業|流程|管理|報告|分析|決策)\b/i.test(k.text) ||
        /\b(business|commercial|process|management|report|analysis|decision)\b/i.test(k.text)
      );
      
      if (businessKeywords.length > 0) {
        topics.push({
          label: '業務需求',
          keywords: businessKeywords.slice(0, 5).map(k => k.text),
          score: businessKeywords.reduce((sum, k) => sum + k.score, 0) / businessKeywords.length
        });
      }
      
      return topics.sort((a, b) => b.score - a.score);
      
    } catch (error) {
      this.logger.warn('Error in topic extraction:', error);
      return [];
    }
  }
} 