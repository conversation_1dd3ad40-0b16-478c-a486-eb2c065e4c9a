import { Logger } from '@nestjs/common';
import { PrismaClient, AiAgentResponseFormat } from '@prisma/client';
import {
  BaseAiException,
  AiCircuitBreakerOpenException,
} from '../../exceptions/ai-service.exceptions';

export interface AiMessage {
  role: 'system' | 'user' | 'assistant';
  content:
    | string
    | Array<{
        type: 'text' | 'image_url';
        text?: string;
        image_url?: {
          url: string;
          detail?: 'low' | 'high' | 'auto';
        };
      }>;
}

export interface AiExecuteOptions {
  model: string;
  temperature?: number;
  maxTokens?: number;
  responseFormat?: AiAgentResponseFormat;
}

export interface AiUsage {
  inputTokens: number;
  outputTokens: number;
}

export interface AiResponse {
  content: string;
  usage: AiUsage;
}

export interface VisionAnalysisOptions extends AiExecuteOptions {
  imageUrl: string;
  prompt: string;
  detail?: 'low' | 'high' | 'auto';
}

export abstract class BaseAiProvider {
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY_MS = 1000;

  // Circuit Breaker State
  private circuitState: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failureCount = 0;
  private successCount = 0;
  private lastFailureTime: number | null = null;
  private readonly FAILURE_THRESHOLD = 5;
  private readonly SUCCESS_THRESHOLD = 3; // Successes needed to close the circuit
  private readonly RESET_TIMEOUT_MS = 30000; // 30 seconds

  constructor(
    protected readonly apiKey: string,
    protected readonly logger: Logger,
    protected readonly apiUrl?: string,
  ) {}

  abstract _execute(messages: AiMessage[], options: AiExecuteOptions): Promise<AiResponse>;

  async execute(messages: AiMessage[], options: AiExecuteOptions): Promise<AiResponse> {
    if (this.circuitState === 'OPEN') {
      if (this.lastFailureTime && Date.now() - this.lastFailureTime > this.RESET_TIMEOUT_MS) {
        this.circuitState = 'HALF_OPEN';
        this.successCount = 0;
        this.logger.warn('Circuit breaker is now HALF_OPEN.');
      } else {
        throw new AiCircuitBreakerOpenException('Circuit is open. Request blocked.');
      }
    }

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        const response = await this._execute(messages, options);
        this.handleSuccess();
        return response;
      } catch (error) {
        this.handleFailure(error);

        if (error instanceof BaseAiException && error.retryable && attempt < this.MAX_RETRIES) {
          this.logger.warn(
            `AI provider call failed (attempt ${attempt}/${this.MAX_RETRIES}). Retrying in ${
              this.RETRY_DELAY_MS * attempt
            }ms...`,
            error.stack,
          );
          await new Promise((res) => setTimeout(res, this.RETRY_DELAY_MS * attempt)); // Exponential backoff
        } else {
          this.logger.error(
            `AI provider call failed definitively after ${attempt} attempt(s).`,
            error.stack,
          );
          throw error; // Rethrow on non-retryable error or last attempt
        }
      }
    }
    // This line should be unreachable due to the throw in the catch block.
    // Added to satisfy TypeScript's need for a return path.
    const finalError = new Error('AI provider call failed after all retries.');
    this.logger.error(finalError.message, finalError.stack);
    throw finalError;
  }

  private handleSuccess() {
    if (this.circuitState === 'HALF_OPEN') {
      this.successCount++;
      if (this.successCount >= this.SUCCESS_THRESHOLD) {
        this.circuitState = 'CLOSED';
        this.failureCount = 0;
        this.logger.log('Circuit breaker is now CLOSED after recovery.');
      }
    } else {
      // Reset failure count on any success when closed
      this.failureCount = 0;
    }
  }

  private handleFailure(error: any) {
    if (error instanceof AiCircuitBreakerOpenException) {
      return; // Do not count circuit breaker exceptions as failures
    }
    this.logger.warn(
      `Handling failure: ${error.constructor.name}. Failure count is now ${this.failureCount + 1}`,
    );

    if (this.circuitState === 'HALF_OPEN') {
      this.tripCircuit(error);
    } else {
      this.failureCount++;
      if (this.failureCount >= this.FAILURE_THRESHOLD) {
        this.tripCircuit(error);
      }
    }
  }

  private tripCircuit(error: any) {
    this.logger.error(
      `Circuit breaker tripping due to: ${error.constructor.name}. Threshold (${this.FAILURE_THRESHOLD}) reached. Circuit is now OPEN.`,
      error.stack,
    );
    this.circuitState = 'OPEN';
    this.lastFailureTime = Date.now();
    this.failureCount = 0; // Reset failure count after tripping
  }

  abstract getAvailableModels(): Promise<string[]>;

  // 可選的視覺分析方法，不是所有提供者都支援
  async executeVisionAnalysis?(options: VisionAnalysisOptions): Promise<AiResponse> {
    throw new Error('Vision analysis not supported by this provider');
  }
}
