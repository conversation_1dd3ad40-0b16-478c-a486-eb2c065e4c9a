/**
 * 歸檔設定介面
 */
export interface ArchivingSettings {
  // 基本設定
  enabled: boolean;
  schedule: string; // cron 表達式
  retentionDays: number; // 日誌保留天數

  // 儲存設定
  storageProvider: 'local' | 's3' | 'azure' | 'gcs';
  storagePath: string; // 儲存路徑或容器名稱

  // 歸檔選項
  compressionEnabled: boolean;
  archiveFormat: 'json' | 'csv';
  batchSize: number; // 批次處理大小
  deleteAfterArchive: boolean; // 是否在歸檔後刪除原始記錄

  // 歸檔檔案管理
  archiveRetentionDays: number; // 歸檔檔案保留天數 (0 = 永久保留)

  // 元數據
  lastArchiveDate?: Date; // 最後歸檔日期
  totalArchivedRecords?: number; // 總歸檔記錄數
  lastArchiveStatus?: 'success' | 'failed' | 'in-progress'; // 最後歸檔狀態
  lastArchiveError?: string; // 最後歸檔錯誤訊息
}

/**
 * 歸檔設定預設值
 */
export const DEFAULT_ARCHIVING_SETTINGS: ArchivingSettings = {
  enabled: false,
  schedule: '0 2 * * *', // 每天凌晨 2 點
  retentionDays: 90, // 90 天
  storageProvider: 'local',
  storagePath: 'archived-logs',
  compressionEnabled: true,
  archiveFormat: 'json',
  batchSize: 1000,
  deleteAfterArchive: false,
  archiveRetentionDays: 365, // 歸檔檔案保留 1 年
  lastArchiveStatus: undefined,
};
