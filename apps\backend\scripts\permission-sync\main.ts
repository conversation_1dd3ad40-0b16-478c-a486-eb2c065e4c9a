#!/usr/bin/env ts-node

/**
 * 權限同步主執行檔案
 * 提供簡化的 CLI 介面用於權限同步
 */

import { PermissionSync } from './index';
import { program } from 'commander';

async function main() {
  program
    .name('permission-sync')
    .description('HorizAI 權限同步工具')
    .version('1.0.0');

  program
    .option('--dry-run', '預覽模式，不實際修改資料庫')
    .option('--force', '強制覆蓋現有權限定義')
    .option('--no-cache', '不使用快取')
    .option('-v, --verbose', '詳細輸出')
    .action(async (options) => {
      const sync = new PermissionSync();
      
      try {
        await sync.run({
          dryRun: options.dryRun,
          force: options.force,
          useCache: options.cache,
          verbose: options.verbose
        });
        
        process.exit(0);
      } catch (error) {
        console.error('❌ 同步失敗:', error.message);
        process.exit(1);
      } finally {
        await sync.cleanup();
      }
    });

  await program.parseAsync(process.argv);
}

// 執行主函數
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 執行失敗:', error);
    process.exit(1);
  });
}
