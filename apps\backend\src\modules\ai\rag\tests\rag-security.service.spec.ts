import { Test, TestingModule } from '@nestjs/testing';
import { RAGSecurityService } from '../rag-security.service';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { ForbiddenException, BadRequestException } from '@nestjs/common';

describe('RAGSecurityService', () => {
  let service: RAGSecurityService;

  const mockPrismaService = {
    vector_documents: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
    },
    tenants: {
      findUnique: jest.fn(),
    },
    workspaces: {
      findUnique: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RAGSecurityService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<RAGSecurityService>(RAGSecurityService);
    
    // 重置所有 mocks
    jest.clearAllMocks();
  });

  describe('validateDocumentAccess', () => {
    it('should return true when tenant_id matches', async () => {
      const tenantId = 'tenant-123';
      const documentId = 'doc-456';
      const workspaceId = 'workspace-789';

      mockPrismaService.vector_documents.findUnique.mockResolvedValueOnce({
        tenant_id: tenantId,
        workspace_id: workspaceId,
      });

      const result = await service.validateDocumentAccess(documentId, tenantId, workspaceId);
      expect(result).toBe(true);
    });

    it('should return false when tenant_id does not match', async () => {
      const tenantId = 'tenant-123';
      const documentId = 'doc-456';
      const workspaceId = 'workspace-789';

      mockPrismaService.vector_documents.findUnique.mockResolvedValueOnce({
        tenant_id: 'different-tenant',
        workspace_id: workspaceId,
      });

      const result = await service.validateDocumentAccess(documentId, tenantId, workspaceId);
      expect(result).toBe(false);
    });

    it('should return false when document does not exist', async () => {
      const tenantId = 'tenant-123';
      const documentId = 'doc-456';
      const workspaceId = 'workspace-789';

      mockPrismaService.vector_documents.findUnique.mockResolvedValueOnce(null);

      const result = await service.validateDocumentAccess(documentId, tenantId, workspaceId);
      expect(result).toBe(false);
    });

    it('should return false when workspace_id does not match', async () => {
      const tenantId = 'tenant-123';
      const documentId = 'doc-456';
      const workspaceId = 'workspace-789';

      mockPrismaService.vector_documents.findUnique.mockResolvedValueOnce({
        tenant_id: tenantId,
        workspace_id: 'different-workspace',
      });

      const result = await service.validateDocumentAccess(documentId, tenantId, workspaceId);
      expect(result).toBe(false);
    });
  });

  describe('validateFileUpload', () => {
    beforeEach(() => {
      // Reset mocks
      mockPrismaService.tenants.findUnique.mockReset();
      mockPrismaService.workspaces.findUnique.mockReset();
      mockPrismaService.vector_documents.findFirst.mockReset();
    });

    it('should not throw for valid file uploads', async () => {
      const filePath = '/documents/valid-file.pdf';
      const tenantId = 'tenant-456';
      const fileId = 'file-123';
      const workspaceId = 'workspace-789';

      // Mock tenant exists and is active
      mockPrismaService.tenants.findUnique.mockResolvedValueOnce({
        id: tenantId,
        status: 'ACTIVE',
      });

      // Mock workspace belongs to tenant
      mockPrismaService.workspaces.findUnique.mockResolvedValueOnce({
        tenant_id: tenantId,
      });

      // Mock file doesn't exist yet
      mockPrismaService.vector_documents.findFirst.mockResolvedValueOnce(null);

      await expect(
        service.validateFileUpload(filePath, tenantId, fileId, workspaceId),
      ).resolves.not.toThrow();
    });

    it('should throw BadRequestException for path traversal attempts', async () => {
      const maliciousPath = '../../../etc/passwd';
      const tenantId = 'tenant-456';
      const fileId = 'file-123';

      await expect(service.validateFileUpload(maliciousPath, tenantId, fileId)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw ForbiddenException when tenant does not exist', async () => {
      const filePath = '/documents/valid-file.pdf';
      const tenantId = 'non-existent-tenant';
      const fileId = 'file-123';

      mockPrismaService.tenants.findUnique.mockResolvedValueOnce(null);

      await expect(service.validateFileUpload(filePath, tenantId, fileId)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw BadRequestException when file already exists', async () => {
      const filePath = '/documents/duplicate-file.pdf';
      const tenantId = 'tenant-456';
      const fileId = 'file-123';

      mockPrismaService.tenants.findUnique.mockResolvedValueOnce({
        id: tenantId,
        status: 'ACTIVE',
      });

      mockPrismaService.vector_documents.findFirst.mockResolvedValueOnce({
        id: 'existing-doc',
        file_id: fileId,
        tenant_id: tenantId,
      });

      await expect(service.validateFileUpload(filePath, tenantId, fileId)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('validateSearchQuery', () => {
    it('should not throw for normal search queries', () => {
      const query = 'What is machine learning?';
      const tenantId = 'tenant-123';

      expect(() => service.validateSearchQuery(query, tenantId)).not.toThrow();
    });

    it('should throw BadRequestException for empty queries', () => {
      const query = '';
      const tenantId = 'tenant-123';

      expect(() => service.validateSearchQuery(query, tenantId)).toThrow(BadRequestException);
    });

    it('should throw BadRequestException for queries without tenant ID', () => {
      const query = 'Valid query content';
      const tenantId = '';

      expect(() => service.validateSearchQuery(query, tenantId)).toThrow(BadRequestException);
    });

    it('should throw BadRequestException for excessively long queries', () => {
      const query = 'a'.repeat(1001); // Exceeds 1000 character limit
      const tenantId = 'tenant-123';

      expect(() => service.validateSearchQuery(query, tenantId)).toThrow(BadRequestException);
    });

    it('should throw BadRequestException for queries with suspicious patterns', () => {
      const suspiciousQueries = [
        '<script>alert("xss")</script>',
        'javascript:alert(1)',
        'UNION SELECT * FROM users',
        'data:text/html,<script>alert(1)</script>',
      ];

      suspiciousQueries.forEach((query) => {
        expect(() => service.validateSearchQuery(query, 'tenant-123')).toThrow(BadRequestException);
      });
    });
  });

  describe('validateBulkDocumentAccess', () => {
    it('should return true when all documents belong to the tenant', async () => {
      const tenantId = 'tenant-123';
      const workspaceId = 'workspace-456';
      const documentIds = ['doc-1', 'doc-2', 'doc-3'];

      mockPrismaService.vector_documents.findMany.mockResolvedValueOnce([
        { id: 'doc-1', tenant_id: tenantId, workspace_id: workspaceId },
        { id: 'doc-2', tenant_id: tenantId, workspace_id: workspaceId },
        { id: 'doc-3', tenant_id: tenantId, workspace_id: workspaceId },
      ]);

      const result = await service.validateBulkDocumentAccess(documentIds, tenantId, workspaceId);
      expect(result).toBe(true);
    });

    it('should return false if any document has different tenant_id', async () => {
      const tenantId = 'tenant-123';
      const workspaceId = 'workspace-456';
      const documentIds = ['doc-1', 'doc-2', 'doc-3'];

      mockPrismaService.vector_documents.findMany.mockResolvedValueOnce([
        { id: 'doc-1', tenant_id: tenantId, workspace_id: workspaceId },
        { id: 'doc-2', tenant_id: 'different-tenant', workspace_id: workspaceId },
        { id: 'doc-3', tenant_id: tenantId, workspace_id: workspaceId },
      ]);

      const result = await service.validateBulkDocumentAccess(documentIds, tenantId, workspaceId);
      expect(result).toBe(false);
    });

    it('should return false if documents are missing', async () => {
      const tenantId = 'tenant-123';
      const workspaceId = 'workspace-456';
      const documentIds = ['doc-1', 'doc-2', 'doc-3'];

      // Only return 2 documents when 3 were requested
      mockPrismaService.vector_documents.findMany.mockResolvedValueOnce([
        { id: 'doc-1', tenant_id: tenantId, workspace_id: workspaceId },
        { id: 'doc-2', tenant_id: tenantId, workspace_id: workspaceId },
      ]);

      const result = await service.validateBulkDocumentAccess(documentIds, tenantId, workspaceId);
      expect(result).toBe(false);
    });
  });

  describe('logSecurityEvent', () => {
    it('should log security events without throwing', async () => {
      await expect(
        service.logSecurityEvent('unauthorized_access', 'tenant-123', {
          documentId: 'doc-456',
          attemptedBy: 'user-789',
        }),
      ).resolves.not.toThrow();
    });

    it('should handle logging errors gracefully', async () => {
      // The method should not throw even if logging fails
      await expect(
        service.logSecurityEvent('access_violation', 'tenant-123', {}),
      ).resolves.not.toThrow();
    });
  });
});
