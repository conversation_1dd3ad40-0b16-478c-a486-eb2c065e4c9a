import { Controller, Get, Put, Post, Body, Req, UseGuards } from '@nestjs/common';
import { ApiT<PERSON><PERSON>, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/core/auth/guards/auth.guard';
import { RolesGuard } from '@/modules/core/auth/guards/roles.guard';
import { Roles } from '@/modules/core/auth/decorators/roles.decorator';
import { Role } from '@/common/enums/role.enum';
import { LineLoginConfigService } from '../services/line-login-config.service';
import { UpdateLineLoginSettingsDto } from '../dto/update-line-login-settings.dto';
import { UpdateChannelIdDto } from '../dto/update-channel-id.dto';
import { UpdateChannelSecretDto } from '../dto/update-channel-secret.dto';
import { UpdateEnableDto } from '../dto/update-enable.dto';
import { Request } from 'express';

interface RequestWithUser extends Request {
  user: { id: string };
}

@ApiTags('admin/line/login-config')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('admin/line/login-config')
export class LineLoginConfigController {
  constructor(private readonly configService: LineLoginConfigService) {}

  @Get()
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '取得完整的 LINE Login 設定' })
  async getSettings() {
    return this.configService.getSettings();
  }

  @Put()
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新完整的 LINE Login 設定' })
  async updateSettings(@Req() req: RequestWithUser, @Body() dto: UpdateLineLoginSettingsDto) {
    return this.configService.updateSettings(dto, req.user.id);
  }

  @Get('status')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '檢查 LINE Login 設定狀態' })
  async getStatus() {
    return this.configService.getStatus();
  }

  @Put('channel-id')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新 LINE Channel ID' })
  async updateChannelId(@Req() req: RequestWithUser, @Body() dto: UpdateChannelIdDto) {
    return this.configService.updateChannelId(dto, req.user.id);
  }

  @Put('channel-secret')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '更新 LINE Channel Secret' })
  async updateChannelSecret(@Req() req: RequestWithUser, @Body() dto: UpdateChannelSecretDto) {
    return this.configService.updateChannelSecret(dto, req.user.id);
  }

  @Put('enable')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '啟用或停用 LINE Login 功能' })
  async updateEnable(@Req() req: RequestWithUser, @Body() dto: UpdateEnableDto) {
    return this.configService.updateEnable(dto, req.user.id);
  }

  @Get('auth-url')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '取得 LINE OAuth 認證網址' })
  async getAuthUrl(@Req() req: RequestWithUser) {
    return this.configService.getAuthUrl(req.user.id);
  }

  @Post('disconnect')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '解除 LINE 綁定' })
  async disconnectLine(@Req() req: RequestWithUser) {
    return this.configService.disconnectLine(req.user.id);
  }
}
