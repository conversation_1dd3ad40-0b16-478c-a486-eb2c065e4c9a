import { Test, TestingModule } from '@nestjs/testing';
import { NotificationTool } from './notification.tool';
import { NotificationService } from '@/modules/notification/notification.service';
import { MessageCenterService } from '@/modules/workspace/message-center/services/message-center.service';
import { ToolExecutionContext } from '../core/tool-registry.interface';

describe('NotificationTool', () => {
  let tool: NotificationTool;
  let notificationService: jest.Mocked<NotificationService>;
  let messageCenterService: jest.Mocked<MessageCenterService>;

  const mockContext: ToolExecutionContext = {
    userId: 'test-user-id',
    workspaceId: 'test-workspace-id',
    tenantId: 'test-tenant-id',
    ability: {} as any,
    user: {} as any,
  };

  beforeEach(async () => {
    const mockNotificationService = {
      sendMessage: jest.fn(),
      createNotification: jest.fn(),
      markAsRead: jest.fn(),
      deleteNotification: jest.fn(),
      getUserNotifications: jest.fn(),
    };

    const mockMessageCenterService = {
      sendMessage: jest.fn(),
      getMessages: jest.fn(),
      markAsRead: jest.fn(),
      deleteMessage: jest.fn(),
      createNotification: jest.fn(),
      getNotifications: jest.fn(),
      markNotificationAsRead: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationTool,
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
        {
          provide: MessageCenterService,
          useValue: mockMessageCenterService,
        },
      ],
    }).compile();

    tool = module.get<NotificationTool>(NotificationTool);
    notificationService = module.get(NotificationService);
    messageCenterService = module.get(MessageCenterService);
  });

  it('should be defined', () => {
    expect(tool).toBeDefined();
  });

  it('should have correct tool configuration', () => {
    const config = tool.getConfig();
    expect(config.name).toBe('notification_tool');
    expect(config.displayName).toBe('Notification Tool');
    expect(config.description).toContain('Comprehensive notification management tool');
  });

  describe('send_message operation', () => {
    it('should send internal message successfully', async () => {
      const contextWithInput = {
        ...mockContext,
        input: {
          operation: 'send_message' as const,
          channel: 'internal' as const,
          recipient_type: 'user' as const,
          recipient_id: 'user-123',
          message: 'Test message',
        },
      };

      notificationService.sendMessage.mockResolvedValue(undefined);

      const result = await tool.execute(contextWithInput);

      expect(result.success).toBe(true);
      expect(result.data?.success).toBe(true);
      expect(notificationService.sendMessage).toHaveBeenCalledWith(
        {
          channel: 'internal',
          recipient_type: 'user',
          recipient_id: 'user-123',
          message: 'Test message',
          message_type: undefined,
        },
        'test-tenant-id',
        'test-user-id',
      );
    });

    it('should handle send message failure', async () => {
      const contextWithInput = {
        ...mockContext,
        input: {
          operation: 'send_message' as const,
          channel: 'internal' as const,
          recipient_type: 'user' as const,
          recipient_id: 'user-123',
          message: 'Test message',
        },
      };

      notificationService.sendMessage.mockRejectedValue(new Error('Send failed'));

      const result = await tool.execute(contextWithInput);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Send failed');
    });
  });

  describe('create_notification operation', () => {
    it('should create notification successfully', async () => {
      const contextWithInput = {
        ...mockContext,
        input: {
          operation: 'create_notification' as const,
          recipient_id: 'user-123',
          title: 'Test Notification',
          message: 'Test message',
          type: 'INFO' as const,
          priority: 'NORMAL' as const,
        },
      };

      messageCenterService.createNotification.mockResolvedValue({
        id: 'notif-123',
        title: 'Test Notification',
        createdAt: new Date(),
      } as any);

      const result = await tool.execute(contextWithInput);

      expect(result.success).toBe(true);
      expect(result.data?.data?.id).toBe('notif-123');
      expect(messageCenterService.createNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Notification',
          message: 'Test message',
          recipient_id: 'user-123',
        }),
        'test-tenant-id',
      );
    });
  });

  describe('get_notifications operation', () => {
    it('should get user notifications successfully', async () => {
      const contextWithInput = {
        ...mockContext,
        input: {
          operation: 'get_notifications' as const,
          recipient_id: 'user-123',
          limit: 10,
        },
      };

      const mockNotifications = [
        { id: 'notif-1', title: 'Notification 1', isRead: false },
        { id: 'notif-2', title: 'Notification 2', isRead: false },
      ];

      messageCenterService.getNotifications.mockResolvedValue(mockNotifications as any);

      const result = await tool.execute(contextWithInput);

      expect(result.success).toBe(true);
      expect(result.data?.data?.notifications).toHaveLength(2);
      expect(result.data?.data?.count).toBe(2);
    });
  });

  describe('input validation', () => {
    it('should reject invalid operation', async () => {
      const contextWithInput = {
        ...mockContext,
        input: {
          operation: 'invalid_operation',
        },
      };

      const result = await tool.execute(contextWithInput);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should reject missing required fields', async () => {
      const contextWithInput = {
        ...mockContext,
        input: {
          operation: 'send_message' as const,
          // missing required fields
        },
      };

      const result = await tool.execute(contextWithInput);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });
});
