import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { CreatePermissionDto, UpdatePermissionDto } from './dto/permission.dto';
import {
  SyncPermissionsDto,
  ScanPermissionsDto,
  SyncResultDto,
  ScanResultDto,
  SyncStatusDto,
} from './dto/sync.dto';
import { permissions } from '@prisma/client';
import { PermissionSyncService } from './permission-sync.service';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class PermissionsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly permissionSyncService: PermissionSyncService,
  ) {}

  async findAll(): Promise<permissions[]> {
    return this.prisma.permissions.findMany({ orderBy: { updated_at: 'asc' } });
  }

  async findOne(id: string): Promise<permissions> {
    const permission = await this.prisma.permissions.findUnique({
      where: { id },
    });
    if (!permission) {
      throw new NotFoundException(`找不到 ID 為 ${id} 的權限定義`);
    }
    return permission;
  }

  async create(dto: CreatePermissionDto): Promise<permissions> {
    const { action, subject, conditions, fields, description } = dto;
    return this.prisma.permissions.create({
      data: {
        id: `${action}_${subject}`,
        action,
        subject,
        conditions,
        fields,
        description,
        updated_at: new Date(),
      },
    });
  }

  async update(id: string, dto: UpdatePermissionDto): Promise<permissions> {
    await this.findOne(id);
    return this.prisma.permissions.update({ where: { id }, data: { ...dto } });
  }

  async remove(id: string): Promise<void> {
    await this.findOne(id);
    await this.prisma.permissions.delete({ where: { id } });
  }

  /**
   * 執行權限同步（委託給PermissionSyncService）
   */
  async syncPermissions(dto: SyncPermissionsDto): Promise<SyncResultDto> {
    try {
      // 統一使用CLI命令處理所有同步操作
      return this.executeSyncCommand(dto);
    } catch (error) {
      throw new Error(`權限同步失敗: ${error.message}`);
    }
  }

  /**
   * 執行權限掃描（委託給PermissionSyncService）
   */
  async scanPermissions(dto: ScanPermissionsDto): Promise<ScanResultDto> {
    try {
      return await this.permissionSyncService.scanPermissions(dto);
    } catch (error) {
      throw new Error(`權限掃描失敗: ${error.message}`);
    }
  }

  /**
   * 獲取同步報告
   */
  async getSyncReport(format: 'json' | 'markdown' = 'json'): Promise<any> {
    try {
      const backendDir = process.cwd().includes('/apps/backend')
        ? process.cwd()
        : path.join(process.cwd(), 'apps', 'backend');
      const reportPath = path.join(backendDir, 'reports', 'permission-sync-report.json');

      if (!fs.existsSync(reportPath)) {
        throw new Error('同步報告不存在，請先執行權限同步');
      }

      if (format === 'json') {
        const reportContent = fs.readFileSync(reportPath, 'utf8');
        return JSON.parse(reportContent);
      } else {
        // 生成Markdown報告
        const { execSync } = require('child_process');
        const command = 'pnpm db:report-perms --format markdown';
        const output = execSync(command, {
          cwd: backendDir,
          encoding: 'utf8',
          timeout: 10000,
        });
        return { content: output };
      }
    } catch (error) {
      throw new Error(`獲取同步報告失敗: ${error.message}`);
    }
  }

  /**
   * 獲取同步狀態（委託給PermissionSyncService）
   */
  async getSyncStatus(): Promise<SyncStatusDto> {
    try {
      return await this.permissionSyncService.getSyncStatus();
    } catch (error) {
      throw new Error(`獲取同步狀態失敗: ${error.message}`);
    }
  }

  /**
   * 執行同步命令（用於所有同步操作）
   */
  private async executeSyncCommand(dto: SyncPermissionsDto): Promise<SyncResultDto> {
    const { execSync } = require('child_process');

    // 構建CLI命令
    let command = 'pnpm db:sync-perms';
    if (dto.dry_run) command += ' --dry-run';
    if (dto.force) command += ' --force';
    if (dto.no_cache) command += ' --no-cache';

    // 確保使用正確的工作目錄
    const currentDir = process.cwd();
    const backendDir = currentDir.includes('/apps/backend')
      ? currentDir
      : path.join(currentDir, 'apps', 'backend');

    try {
      const output = execSync(command, {
        cwd: backendDir,
        encoding: 'utf8',
        timeout: 60000,
        stdio: 'pipe',
      });
      const result = this.parseCliOutput(output);
      return {
        success: true,
        total: result.total || 0,
        created: result.created || 0,
        updated: result.updated || 0,
        deprecated: result.deprecated || 0,
        errors: 0,
        timestamp: new Date().toISOString(),
        changes: result.changes || [],
        error_details: [],
      };
    } catch (error) {
      if (dto.dry_run && error.status === 1) {
        const output = error.stdout || error.stderr || '';
        const result = this.parseCliOutput(output);
        // Dry-run 模式下，這表示有變更，不是錯誤
        return {
          success: true,
          total: result.total || 0,
          created: result.created || 0,
          updated: result.updated || 0,
          deprecated: result.deprecated || 0,
          errors: 0,
          timestamp: new Date().toISOString(),
          changes: result.changes || [],
          error_details: [],
        };
      }
      // 其他錯誤才是真正的錯誤
      throw new Error(`執行同步命令失敗: ${error.message}`);
    }
  }

  /**
   * 解析CLI輸出結果
   */
  private parseCliOutput(output: string): any {
    try {
      console.log('開始解析 CLI 輸出...');

      // 首先嘗試讀取 JSON 報告檔案
      const backendDir = process.cwd().includes('/apps/backend')
        ? process.cwd()
        : path.join(process.cwd(), 'apps', 'backend');
      const reportPath = path.join(backendDir, 'reports', 'permission-sync-report.json');
      if (fs.existsSync(reportPath)) {
        console.log('找到同步報告檔案，讀取內容...');
        const reportContent = fs.readFileSync(reportPath, 'utf8');
        const report = JSON.parse(reportContent);

        // 從報告中提取結果
        if (report.syncResult) {
          return {
            total:
              report.syncResult.created + report.syncResult.updated + report.syncResult.deprecated,
            created: report.syncResult.created || 0,
            updated: report.syncResult.updated || 0,
            deprecated: report.syncResult.deprecated || 0,
            errors: report.syncResult.errors || 0,
            changes: report.syncResult.details || [],
            rawOutput: output,
          };
        }
      }

      // 如果沒有報告檔案，嘗試從輸出中提取JSON結果
      const lines = output.split('\n');
      let jsonLine = '';

      for (const line of lines) {
        if (line.trim().startsWith('{') && line.trim().endsWith('}')) {
          jsonLine = line.trim();
          break;
        }
      }

      if (jsonLine) {
        console.log('從輸出中找到 JSON 結果');
        return JSON.parse(jsonLine);
      }

      // 如果沒有找到JSON，解析文字輸出
      console.log('解析文字輸出...');
      return this.parseTextOutput(output);
    } catch (error) {
      console.error('解析 CLI 輸出失敗:', error.message);
      // 如果解析失敗，返回基本信息
      return {
        total: 0,
        created: 0,
        updated: 0,
        deprecated: 0,
        errors: 0,
        filesScanned: 0,
        permissionsFound: 0,
        hardcodedWarnings: 0,
        rawOutput: output,
      };
    }
  }

  /**
   * 解析文字輸出
   */
  private parseTextOutput(output: string): any {
    const result: any = {
      total: 0,
      created: 0,
      updated: 0,
      deprecated: 0,
      errors: 0,
      filesScanned: 0,
      permissionsFound: 0,
      hardcodedWarnings: 0,
    };

    const lines = output.split('\n');

    for (const line of lines) {
      // 掃描統計
      if (line.includes('總檔案數:') || line.includes('掃描檔案:')) {
        const match = line.match(/\d+/);
        if (match) result.filesScanned = parseInt(match[0]);
      } else if (line.includes('發現權限:') || line.includes('權限總數:')) {
        const match = line.match(/\d+/);
        if (match) result.permissionsFound = parseInt(match[0]);
      } else if (line.includes('硬編碼權限警告:')) {
        const match = line.match(/\d+/);
        if (match) result.hardcodedWarnings = parseInt(match[0]);
      }

      // 同步統計 - 支援多種格式
      else if (line.includes('新增') && line.includes('個')) {
        const match = line.match(/新增\s*(\d+)\s*個/);
        if (match) result.created = parseInt(match[1]);
      } else if (line.includes('更新') && line.includes('個')) {
        const match = line.match(/更新\s*(\d+)\s*個/);
        if (match) result.updated = parseInt(match[1]);
      } else if (line.includes('廢棄') && line.includes('個')) {
        const match = line.match(/廢棄\s*(\d+)\s*個/);
        if (match) result.deprecated = parseInt(match[1]);
      } else if (line.includes('錯誤') && line.includes('個')) {
        const match = line.match(/錯誤\s*(\d+)\s*個/);
        if (match) result.errors = parseInt(match[1]);
      }

      // CLI 格式的統計資訊
      else if (line.includes('統計:')) {
        // 解析格式如: "統計: 新增 2 個, 更新 1 個, 標記廢棄 0 個, 錯誤 0 個"
        const createdMatch = line.match(/新增\s*(\d+)/);
        const updatedMatch = line.match(/更新\s*(\d+)/);
        const deprecatedMatch = line.match(/標記廢棄\s*(\d+)/);
        const errorsMatch = line.match(/錯誤\s*(\d+)/);

        if (createdMatch) result.created = parseInt(createdMatch[1]);
        if (updatedMatch) result.updated = parseInt(updatedMatch[1]);
        if (deprecatedMatch) result.deprecated = parseInt(deprecatedMatch[1]);
        if (errorsMatch) result.errors = parseInt(errorsMatch[1]);
      }
    }

    result.total = result.created + result.updated + result.deprecated;

    console.log('解析文字輸出結果:', result);
    return result;
  }

  private parseSyncOutput(output: string, hasChanges: boolean): SyncResultDto {
    // Implementation of parseSyncOutput method
    // This method should return a SyncResultDto based on the parsed output
    // and the boolean indicating whether there were changes
    throw new Error('Method not implemented');
  }
}
