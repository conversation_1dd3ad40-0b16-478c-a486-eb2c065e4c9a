
/**
 * 權限自動化同步機制主入口
 */

export { PermissionScanner } from "./scanner";
export { PermissionSyncer } from "./syncer";
export { PermissionReporter } from "./reporter";
export { PermissionSyncCLI } from "./cli";

export {
  PermissionDefinition,
  ScanResult,
  SyncResult,
  PermissionSyncReport,
  CLIOptions,
  ScanConfig
} from "./types";

// 提供簡化的 API
import { PrismaClient } from "@prisma/client";
import { PermissionScanner } from "./scanner";
import { PermissionSyncer } from "./syncer";
import { PermissionReporter } from "./reporter";

/**
 * 簡化的權限同步 API
 */
export class PermissionSync {
  private prisma: PrismaClient;
  private scanner: PermissionScanner;
  private syncer: PermissionSyncer;
  private reporter: PermissionReporter;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
    this.scanner = new PermissionScanner();
    this.syncer = new PermissionSyncer(this.prisma);
    this.reporter = new PermissionReporter();
  }

  /**
   * 執行完整的權限同步流程
   */
  async run(options: {
    dryRun?: boolean;
    force?: boolean;
    useCache?: boolean;
    verbose?: boolean;
  } = {}): Promise<void> {
    const { dryRun = false, force = false, useCache = true, verbose = false } = options;

    try {
      console.log(`🚀 開始${dryRun ? '預覽' : '執行'}權限同步...\n`);

      // 1. 掃描權限
      if (verbose) console.log("🔍 掃描權限定義...");
      const scanResult = await this.scanner.scan(useCache);

      // 2. 同步權限
      if (verbose) console.log("🔄 同步權限到資料庫...");
      const syncResult = await this.syncer.sync(scanResult.permissions, {
        dryRun,
        force
      });

      // 3. 產生報告
      if (verbose) console.log("📊 產生同步報告...");
      const report = await this.reporter.generateReport(
        scanResult,
        syncResult,
        dryRun ? 'dry-run' : 'sync'
      );

      // 4. 產生 Markdown 報告
      if (!dryRun) {
        await this.reporter.generateMarkdownReport(report);
      }

      console.log(`✅ ${dryRun ? '預覽' : '同步'}完成！`);

    } catch (error) {
      console.error("❌ 權限同步失敗:", error.message);
      throw error;
    }
  }

  /**
   * 只執行掃描
   */
  async scanOnly(useCache = true): Promise<void> {
    try {
      console.log("🔍 掃描權限定義...\n");
      const scanResult = await this.scanner.scan(useCache);
      await this.reporter.generateScanReport(scanResult);
      console.log("✅ 掃描完成！");
    } catch (error) {
      console.error("❌ 權限掃描失敗:", error.message);
      throw error;
    }
  }

  /**
   * 清理資源
   */
  async cleanup(): Promise<void> {
    await this.syncer.disconnect();
  }
}
