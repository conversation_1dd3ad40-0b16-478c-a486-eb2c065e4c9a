-- 這是修正後的 migration.sql
-- 使用最簡單可靠的方式處理 ENUM 型別

-- 建立 ENUM 型別（使用 PL/pgSQL 安全檢查）
DO
$$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'responseformat') THEN
        CREATE TYPE "ResponseFormat" AS ENUM ('text', 'json_object', 'json_schema');
    END IF;
END
$$;

-- 使用臨時表格方式重建欄位
-- 步驟 1: 建立臨時表格儲存原始資料
CREATE TABLE "ai_bots_temp" AS SELECT * FROM "ai_bots";

-- 步驟 2: 移除原始欄位
ALTER TABLE "ai_bots" DROP COLUMN IF EXISTS "responseFormat";

-- 步驟 3: 新增正確型別的欄位
ALTER TABLE "ai_bots" ADD COLUMN "responseFormat" "ResponseFormat" DEFAULT 'text';

-- 步驟 4: 更新資料（根據原始值設定）
UPDATE "ai_bots" a
SET "responseFormat" = 
    CASE 
        WHEN b."responseFormat" = 'text' THEN 'text'::"ResponseFormat"
        WHEN b."responseFormat" = 'json_object' THEN 'json_object'::"ResponseFormat"
        WHEN b."responseFormat" = 'json_schema' THEN 'json_schema'::"ResponseFormat"
        ELSE 'text'::"ResponseFormat"
    END
FROM "ai_bots_temp" b
WHERE a.id = b.id;

-- 步驟 5: 移除臨時表格
DROP TABLE "ai_bots_temp";
