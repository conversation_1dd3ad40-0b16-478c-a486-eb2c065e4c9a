import { Injectable, ExecutionContext, Logger, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CaslAbilityFactory } from '../ability/casl-ability.factory';
import { AppAbility } from '../../types/models/casl.model';
import {
  CHECK_POLICIES_KEY,
  PolicyHandler,
  REQUIRE_PERMISSIONS_KEY,
  PermissionRequirement,
} from '../decorators/check-policies.decorator';
import { JwtUser } from '../../types/jwt-user.type';
import { Request } from 'express';
import { PermissionCheckerService } from '../services/permission-checker.service';
import { performPermissionCheck, isSuperAdmin } from '../utils/permission.utils';
import { processConditionsForGuard } from '../utils/condition.utils';

interface ExtendedRequest extends Request {
  ability?: AppAbility;
}

/**
 * 統一的權限守衛
 * 提供強健的 CASL 權限檢查，支援多種權限驗證方式
 * 整合了原有的 PoliciesGuard、EnhancedPermissionGuard 和 UnifiedPermissionGuard 的功能
 */
@Injectable()
export class PoliciesGuard {
  private readonly logger = new Logger(PoliciesGuard.name);

  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory,
    private permissionChecker?: PermissionCheckerService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const policyHandlers =
      this.reflector.get<PolicyHandler[]>(CHECK_POLICIES_KEY, context.getHandler()) || [];

    const requirements =
      this.reflector.get<PermissionRequirement[]>(REQUIRE_PERMISSIONS_KEY, context.getHandler()) ||
      [];

    const requirementsAsHandlers: PolicyHandler[] = requirements.map(
      (requirement) => (ability: AppAbility) => {
        // Here we would ideally have a more complex check for conditions and fields
        // For now, we'll stick to a basic action/subject check.
        return ability.can(requirement.action, requirement.subject);
      },
    );

    const allPolicies = [...policyHandlers, ...requirementsAsHandlers];

    if (allPolicies.length === 0) {
      return true;
    }

    const req = context.switchToHttp().getRequest<ExtendedRequest>();
    const user = req.user as JwtUser;

    if (!user) {
      this.logger.warn('PermissionGuard: No user object found in request.');
      return false;
    }

    this.logger.debug(`PermissionGuard: Checking permissions for user ${user.sub} (${user.email})`);

    const ability = await this.createUserAbility(user);
    req.ability = ability;

    this.logger.debug(`PermissionGuard: Created ability with ${ability.rules.length} rules`);

    const results = allPolicies.map((handler) => {
      const result = this.execPolicyHandler(handler, ability);
      this.logger.debug(`PermissionGuard: Policy check result: ${result}`);
      return result;
    });

    const finalResult = results.every((result) => result);
    this.logger.debug(`PermissionGuard: Final permission check result: ${finalResult}`);

    return finalResult;
  }

  private execPolicyHandler(handler: PolicyHandler, ability: AppAbility) {
    if (typeof handler === 'function') {
      return handler(ability);
    }
    return handler.handle(ability);
  }

  private async createUserAbility(user: JwtUser): Promise<AppAbility> {
    try {
      const ability = await this.caslAbilityFactory.createForUser({
        user_id: user.sub,
        tenant_id: user.tenant_id,
        user_type: user.user_type,
      });
      this.logger.debug(`Ability ready for user ${user.sub} with ${ability.rules.length} rules`);
      return ability;
    } catch (error) {
      this.logger.error(`Failed to create ability for user ${user.sub}:`, error.stack);
      throw new ForbiddenException('Failed to create permissions for user.');
    }
  }
}
