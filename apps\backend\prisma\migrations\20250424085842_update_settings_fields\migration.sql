/*
  Warnings:

  - You are about to drop the column `value` on the `settings` table. All the data in the column will be lost.

*/
-- DropIndex
DROP INDEX "settings_type_key";

-- AlterTable
ALTER TABLE "settings" DROP COLUMN "value",
ADD COLUMN     "accentColor" TEXT,
ADD COLUMN     "auditLogRetentionDays" INTEGER DEFAULT 90,
ADD COLUMN     "defaultLanguage" TEXT DEFAULT 'zh-TW',
ADD COLUMN     "emailProvider" TEXT DEFAULT 'smtp',
ADD COLUMN     "faviconUrl" TEXT,
ADD COLUMN     "fromEmailAddress" TEXT,
ADD COLUMN     "fromName" TEXT,
ADD COLUMN     "ipWhitelist" TEXT,
ADD COLUMN     "logoUrl" TEXT,
ADD COLUMN     "maintenanceAllowedIps" TEXT,
ADD COLUMN     "maintenanceEndTime" TIMESTAMP(3),
ADD COLUMN     "maintenanceMessage" TEXT,
ADD COLUMN     "maintenanceMode" BOOLEAN DEFAULT false,
ADD COLUMN     "maintenanceNotification" BOOLEAN DEFAULT false,
ADD COLUMN     "maintenanceStartTime" TIMESTAMP(3),
ADD COLUMN     "passwordMinLength" INTEGER DEFAULT 8,
ADD COLUMN     "primaryColor" TEXT,
ADD COLUMN     "requireMFA" BOOLEAN DEFAULT false,
ADD COLUMN     "sessionTimeout" INTEGER DEFAULT 30,
ADD COLUMN     "siteDescription" TEXT,
ADD COLUMN     "siteName" TEXT DEFAULT 'HorizAI',
ADD COLUMN     "smtpHost" TEXT,
ADD COLUMN     "smtpPassword" TEXT,
ADD COLUMN     "smtpPort" INTEGER DEFAULT 587,
ADD COLUMN     "smtpSecure" BOOLEAN DEFAULT true,
ADD COLUMN     "smtpUser" TEXT,
ADD COLUMN     "storageAccessKey" TEXT,
ADD COLUMN     "storageBucket" TEXT,
ADD COLUMN     "storageEndpoint" TEXT,
ADD COLUMN     "storageProvider" TEXT DEFAULT 'local',
ADD COLUMN     "storageRegion" TEXT,
ADD COLUMN     "storageSecretKey" TEXT,
ADD COLUMN     "supportEmail" TEXT,
ADD COLUMN     "theme" TEXT DEFAULT 'light',
ADD COLUMN     "tillwoApiKey" TEXT,
ADD COLUMN     "timezone" TEXT DEFAULT 'Asia/Taipei',
ALTER COLUMN "type" SET DEFAULT 'general';
