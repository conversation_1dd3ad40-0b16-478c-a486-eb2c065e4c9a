/**
 * Agent Tools 統一導出
 *
 * 此檔案負責統一導出所有 Agent 工具，已全面轉換為新架構：
 * - Modern: 新架構工具（Factory Functions）
 * - Core: 核心工具和服務
 * - Types: 型別定義和介面
 */

import { RAGIngestionService } from '@/modules/ai/rag/rag-ingestion.service';
import { RAGSecurityService } from '@/modules/ai/rag/rag-security.service';
import { TasksService } from '@/modules/workspace/tasks/tasks.service';
import { ProgressService } from '@/modules/workspace/progress/progress.service';
import { ProjectsService } from '@/modules/workspace/projects/projects.service';
import { ToolExecutionContext } from './core/tool-registry.interface';

// Core Tools & Services
export { ToolRegistryService } from './core/tool-registry.service';
export { ToolRegistrationService } from './tool-registration.service';

// Modern Tools (新架構 - Factory Functions)
export * from './implementations';

// Error Types
export * from './core/tool.errors';

// Interfaces
export * from './core/tool-registry.interface';

/**
 * 工具類別列舉（現代架構）
 */
export enum ModernToolCategory {
  FILE_OPERATIONS = 'file_operations',
  WEB_SEARCH = 'web_search',
  NOTIFICATIONS = 'notifications',
  PROJECT_MANAGEMENT = 'project_management',
  TASK_MANAGEMENT = 'task_management',
  PROGRESS_TRACKING = 'progress_tracking',
  KNOWLEDGE_BASE = 'knowledge_base',
}

/**
 * 現代工具註冊表（新架構 - 完整版）
 */
export const MODERN_TOOL_REGISTRY = {
  // File Operations
  file_reader: 'FileReaderToolsFactory',
  
  // Web Search
  web_search: 'WebSearchToolsFactory',
  
  // Notifications
  send_message: 'NotificationToolsFactory',
  create_notification: 'NotificationToolsFactory',
  get_notifications: 'NotificationToolsFactory',
  mark_notification_read: 'NotificationToolsFactory',
  
  // Project Management
  project_info: 'ProjectInfoToolsFactory',
  
  // Task Management
  create_task: 'CreateTaskToolsFactory',
  
  // Progress Tracking
  progress_update: 'ProgressUpdateToolsFactory',
  
  // Knowledge Base
  knowledge_base_search: 'KnowledgeBaseToolsFactory',
} as const;

/**
 * 現代工具創建助手函數
 * 這些函數提供更便捷的工具創建方式，隱藏了複雜的依賴注入
 */
export class ModernToolsHelper {
  /**
   * 創建檔案讀取工具
   */
  static createFileReaderTool(context: ToolExecutionContext, config?: any) {
    const { createFileReaderTool } = require('./implementations');
    return createFileReaderTool(context, config);
  }

  /**
   * 創建網頁搜尋工具
   */
  static createWebSearchTool(context: ToolExecutionContext, config?: any) {
    const { createWebSearchTool } = require('./implementations');
    return createWebSearchTool(context, config);
  }

  /**
   * 創建專案資訊工具
   */
  static createProjectInfoTool(
    projectsService: ProjectsService,
    context: ToolExecutionContext,
    config?: any,
  ) {
    const { createProjectInfoTool } = require('./implementations');
    return createProjectInfoTool(projectsService, context, config);
  }

  /**
   * 創建任務建立工具
   */
  static createCreateTaskTool(
    tasksService: TasksService,
    context: ToolExecutionContext,
    config?: any,
  ) {
    const { createCreateTaskTool } = require('./implementations');
    return createCreateTaskTool(tasksService, context, config);
  }

  /**
   * 創建進度更新工具
   */
  static createProgressUpdateTool(
    progressService: ProgressService,
    context: ToolExecutionContext,
    config?: any,
  ) {
    const { createProgressUpdateTool } = require('./implementations');
    return createProgressUpdateTool(progressService, context, config);
  }

  /**
   * 創建知識庫搜尋工具
   */
  static createKnowledgeBaseSearchTool(
    ragIngestionService: RAGIngestionService,
    ragSecurityService: RAGSecurityService,
    context: ToolExecutionContext,
    config?: any,
  ) {
    const { createKnowledgeBaseSearchTool } = require('./implementations');
    return createKnowledgeBaseSearchTool(ragIngestionService, ragSecurityService, context, config);
  }

  /**
   * 創建通知工具集合
   */
  static createNotificationTools(context: ToolExecutionContext, config?: any) {
    const {
      createSendMessageTool,
      createNotificationTool,
      createGetNotificationsTool,
      createMarkNotificationReadTool,
    } = require('./implementations');
    
    return {
      sendMessage: createSendMessageTool(context, config),
      createNotification: createNotificationTool(context, config),
      getNotifications: createGetNotificationsTool(context, config),
      markNotificationRead: createMarkNotificationReadTool(context, config),
    };
  }

  /**
   * 創建所有可用的現代工具
   */
  static createAllTools(
    services: {
      projectsService: ProjectsService;
      tasksService: TasksService;
      progressService: ProgressService;
      ragIngestionService: RAGIngestionService;
      ragSecurityService: RAGSecurityService;
    },
    context: ToolExecutionContext,
    configs?: {
      fileReader?: any;
      webSearch?: any;
      projectInfo?: any;
      createTask?: any;
      progressUpdate?: any;
      knowledgeBase?: any;
      notification?: any;
    },
  ) {
    return {
      // File Operations
      fileReader: this.createFileReaderTool(context, configs?.fileReader),
      
      // Web Search
      webSearch: this.createWebSearchTool(context, configs?.webSearch),
      
      // Project Management
      projectInfo: this.createProjectInfoTool(services.projectsService, context, configs?.projectInfo),
      
      // Task Management
      createTask: this.createCreateTaskTool(services.tasksService, context, configs?.createTask),
      
      // Progress Tracking
      progressUpdate: this.createProgressUpdateTool(services.progressService, context, configs?.progressUpdate),
      
      // Knowledge Base
      knowledgeBaseSearch: this.createKnowledgeBaseSearchTool(
        services.ragIngestionService,
        services.ragSecurityService,
        context,
        configs?.knowledgeBase,
      ),
      
      // Notifications
      ...this.createNotificationTools(context, configs?.notification),
    };
  }
}
