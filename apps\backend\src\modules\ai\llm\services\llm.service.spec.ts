import { Test, TestingModule } from '@nestjs/testing';
import { LlmService } from './llm.service';
import { AiKeysService } from '../../../ai/models/configuration/keys/ai-keys.service';
import { ConfigService } from '@nestjs/config';
import { BadRequestException } from '@nestjs/common';
import { AiBotProviderType } from '@prisma/client';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Mock the external SDKs
jest.mock('openai');
jest.mock('@anthropic-ai/sdk');
jest.mock('@google/generative-ai');

describe('LlmService', () => {
  let service: LlmService;
  let aiKeysService: jest.Mocked<AiKeysService>;
  let configService: jest.Mocked<ConfigService>;

  // Mock implementations for the SDKs
  const mockOpenAIChatCompletionsCreate = jest.fn();
  const mockAnthropicMessagesCreate = jest.fn();
  const mockGoogleGenerativeAIGetGenerativeModel = jest.fn();
  const mockGoogleGenerateContent = jest.fn();

  beforeEach(async () => {
    // Reset mocks before each test
    mockOpenAIChatCompletionsCreate.mockReset();
    mockAnthropicMessagesCreate.mockReset();
    mockGoogleGenerativeAIGetGenerativeModel.mockReset();
    mockGoogleGenerateContent.mockReset();

    // Setup the mock implementations
    (OpenAI as unknown as jest.Mock).mockImplementation(() => ({
      chat: {
        completions: {
          create: mockOpenAIChatCompletionsCreate,
        },
      },
    }));
    (Anthropic as unknown as jest.Mock).mockImplementation(() => ({
      messages: {
        create: mockAnthropicMessagesCreate,
      },
    }));
    mockGoogleGenerativeAIGetGenerativeModel.mockReturnValue({
      generateContent: mockGoogleGenerateContent,
    });
    (GoogleGenerativeAI as unknown as jest.Mock).mockImplementation(() => ({
      getGenerativeModel: mockGoogleGenerativeAIGetGenerativeModel,
    }));

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LlmService,
        {
          provide: AiKeysService,
          useValue: {
            getDecryptedKey: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<LlmService>(LlmService);
    aiKeysService = module.get(AiKeysService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('execute', () => {
    it('should throw NotFoundException if keyId is not found', async () => {
      aiKeysService.getDecryptedKey.mockResolvedValue(null as any);
      await expect(
        service.execute([], {
          providerType: AiBotProviderType.OPENAI,
          keyId: 'unknown-key-id',
          model: 'gpt-4',
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should call OpenAI SDK for OPENAI provider', async () => {
      aiKeysService.getDecryptedKey.mockResolvedValue('decrypted-openai-key');
      mockOpenAIChatCompletionsCreate.mockResolvedValue({
        choices: [{ message: { content: 'OpenAI response' } }],
        usage: { prompt_tokens: 5, completion_tokens: 5 },
      });

      const result = await service.execute([{ role: 'user', content: 'test' }], {
        providerType: AiBotProviderType.OPENAI,
        keyId: 'openai-key-id',
        model: 'gpt-4',
      });

      expect(OpenAI).toHaveBeenCalledWith({ apiKey: 'decrypted-openai-key' });
      expect(mockOpenAIChatCompletionsCreate).toHaveBeenCalled();
      expect(result.content).toEqual('OpenAI response');
    });

    it('should call Anthropic SDK for CLAUDE provider', async () => {
      aiKeysService.getDecryptedKey.mockResolvedValue('decrypted-claude-key');
      mockAnthropicMessagesCreate.mockResolvedValue({
        content: [{ type: 'text', text: 'Claude response' }],
        usage: { input_tokens: 5, output_tokens: 5 },
      });

      const result = await service.execute([{ role: 'user', content: 'test' }], {
        providerType: AiBotProviderType.CLAUDE,
        keyId: 'claude-key-id',
        model: 'claude-3-opus',
      });

      expect(Anthropic).toHaveBeenCalledWith({ apiKey: 'decrypted-claude-key' });
      expect(mockAnthropicMessagesCreate).toHaveBeenCalled();
      expect(result.content).toEqual('Claude response');
    });

    it('should call Google SDK for GEMINI provider', async () => {
      aiKeysService.getDecryptedKey.mockResolvedValue('decrypted-gemini-key');
      mockGoogleGenerateContent.mockResolvedValue({
        response: {
          text: () => 'Gemini response',
          candidates: [{ finishReason: 'STOP' }],
        },
      });

      const result = await service.execute([{ role: 'user', content: 'test' }], {
        providerType: AiBotProviderType.GEMINI,
        keyId: 'gemini-key-id',
        model: 'gemini-pro',
      });

      expect(GoogleGenerativeAI).toHaveBeenCalledWith('decrypted-gemini-key');
      expect(mockGoogleGenerateContent).toHaveBeenCalled();
      expect(result.content).toEqual('Gemini response');
    });

    it('should call OpenAI SDK for OPENAI_COMPATIBLE provider', async () => {
      aiKeysService.getDecryptedKey.mockResolvedValue('decrypted-compatible-key');
      mockOpenAIChatCompletionsCreate.mockResolvedValue({
        choices: [{ message: { content: 'Compatible response' } }],
        usage: { prompt_tokens: 5, completion_tokens: 5 },
      });

      const result = await service.execute([{ role: 'user', content: 'test' }], {
        providerType: AiBotProviderType.OPENAI_COMPATIBLE,
        keyId: 'compatible-key-id',
        model: 'compatible-model',
        apiUrl: 'https://api.example.com/v1',
      });

      expect(OpenAI).toHaveBeenCalledWith({
        apiKey: 'decrypted-compatible-key',
        baseURL: 'https://api.example.com/v1',
      });
      expect(mockOpenAIChatCompletionsCreate).toHaveBeenCalled();
      expect(result.content).toEqual('Compatible response');
    });

    it('should throw BadRequestException for unsupported provider', async () => {
      aiKeysService.getDecryptedKey.mockResolvedValue('some-key');
      await expect(
        service.execute([], {
          providerType: 'unsupported' as AiBotProviderType,
          keyId: 'some-key-id',
          model: 'any-model',
        }),
      ).rejects.toThrow(BadRequestException);
    });
  });
});
