import { IsBoolean, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateNotificationSettingsDto {
  @ApiProperty({ description: '是否啟用 Email 通知' })
  @IsBoolean()
  emailNotifications: boolean;

  @ApiProperty({ description: '是否啟用系統通知' })
  @IsBoolean()
  systemNotifications: boolean;

  @ApiProperty({ description: '是否啟用通知音效' })
  @IsBoolean()
  notificationSound: boolean;

  @ApiProperty({ description: '特定通知開關' })
  @IsObject()
  specificNotificationToggles: {
    newUser: boolean;
    taskAssigned: boolean;
  };
}
