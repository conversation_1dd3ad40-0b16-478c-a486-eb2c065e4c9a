/*
  Warnings:

  - You are about to drop the column `lineAuthState` on the `User` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "User" DROP COLUMN "lineAuthState",
ADD COLUMN     "lineConnected" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "LineAuthState" (
    "id" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "LineAuthState_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "LineAuthState_state_key" ON "LineAuthState"("state");

-- CreateIndex
CREATE INDEX "LineAuthState_state_idx" ON "LineAuthState"("state");

-- CreateIndex
CREATE INDEX "LineAuthState_userId_idx" ON "LineAuthState"("userId");

-- AddF<PERSON>ignKey
ALTER TABLE "LineAuthState" ADD CONSTRAINT "LineAuthState_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
