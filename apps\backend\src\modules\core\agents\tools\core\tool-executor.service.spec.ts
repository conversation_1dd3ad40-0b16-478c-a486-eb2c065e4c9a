import { Test, TestingModule } from '@nestjs/testing';
import { ToolExecutorService } from './tool-executor.service';
import { ToolRegistryService } from './tool-registry.service';
import { AiToolsService } from '@/modules/ai/models/configuration/tools/ai-tools.service';
import { AiBotsService } from '@/modules/ai/models/bots/ai-bots.service';
import { ToolImplementation, ToolExecutionContext, ToolConfig } from './tool-registry.interface';
import { Tool } from 'langchain/tools';
import { DynamicStructuredTool } from 'langchain/tools';
import { z } from 'zod';
import { NotFoundException, ForbiddenException } from '@nestjs/common';

// Mock 工具實作
class MockToolImplementation implements ToolImplementation {
  constructor(
    public readonly key: string,
    public readonly name: string,
    public readonly description: string,
    private shouldThrowPermissionError = false,
  ) {}

  async createTool(context: ToolExecutionContext, config: ToolConfig): Promise<Tool> {
    return new DynamicStructuredTool({
      name: this.key,
      description: this.description,
      schema: z.object({
        input: z.string().describe('Test input'),
      }),
      func: async ({ input }) => `Mock response for: ${input}`,
    });
  }

  async validateConfig(config: ToolConfig): Promise<boolean> {
    return config.key === this.key;
  }

  async checkPermissions(context: ToolExecutionContext): Promise<boolean> {
    if (this.shouldThrowPermissionError) {
      return false;
    }
    return true;
  }
}

describe('ToolExecutorService', () => {
  let service: ToolExecutorService;
  let toolRegistry: jest.Mocked<ToolRegistryService>;
  let aiToolsService: jest.Mocked<AiToolsService>;
  let aiBotsService: jest.Mocked<AiBotsService>;

  const mockContext: ToolExecutionContext = {
    tenantId: 'test-tenant-id',
    userId: 'test-user-id',
    workspaceId: 'test-workspace-id',
    ability: {} as any,
    user: {
      id: 'test-user-id',
      tenant_id: 'test-tenant-id',
      user_type: 'tenant',
    } as any,
  };

  const mockToolConfig: ToolConfig = {
    id: 'tool-config-id',
    key: 'test_tool',
    name: 'Test Tool',
    description: 'A test tool',
    inputSchema: {},
    isEnabled: true,
    config: {},
  };

  beforeEach(async () => {
    const mockToolRegistry = {
      getImplementation: jest.fn(),
      hasImplementation: jest.fn(),
      register: jest.fn(),
      unregister: jest.fn(),
      listTools: jest.fn(),
      getAllImplementations: jest.fn(),
      clear: jest.fn(),
    };

    const mockAiToolsService = {
      findOne: jest.fn(),
      findAll: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    };

    const mockAiBotsService = {
      getBotToolConfigs: jest.fn(),
      findOne: jest.fn(),
      assignToolsToBot: jest.fn(),
      removeToolFromBot: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ToolExecutorService,
        { provide: ToolRegistryService, useValue: mockToolRegistry },
        { provide: AiToolsService, useValue: mockAiToolsService },
        { provide: AiBotsService, useValue: mockAiBotsService },
      ],
    }).compile();

    service = module.get<ToolExecutorService>(ToolExecutorService);
    toolRegistry = module.get(ToolRegistryService);
    aiToolsService = module.get(AiToolsService);
    aiBotsService = module.get(AiBotsService);
  });

  describe('loadTool', () => {
    it('should successfully load and create a tool', async () => {
      const mockImplementation = new MockToolImplementation(
        'test_tool',
        'Test Tool',
        'A test tool',
      );
      toolRegistry.getImplementation.mockReturnValue(mockImplementation);

      const tool = await service.loadTool(mockToolConfig, mockContext);

      expect(tool).toBeDefined();
      expect(tool.name).toBe('test_tool');
      expect(toolRegistry.getImplementation).toHaveBeenCalledWith('test_tool');
    });

    it('should throw NotFoundException when tool implementation not found', async () => {
      toolRegistry.getImplementation.mockReturnValue(undefined);

      await expect(service.loadTool(mockToolConfig, mockContext)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ForbiddenException when permission check fails', async () => {
      const mockImplementation = new MockToolImplementation(
        'test_tool',
        'Test Tool',
        'A test tool',
        true,
      );
      toolRegistry.getImplementation.mockReturnValue(mockImplementation);

      await expect(service.loadTool(mockToolConfig, mockContext)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw error when tool is disabled', async () => {
      const disabledConfig = { ...mockToolConfig, isEnabled: false };
      const mockImplementation = new MockToolImplementation(
        'test_tool',
        'Test Tool',
        'A test tool',
      );
      toolRegistry.getImplementation.mockReturnValue(mockImplementation);

      await expect(service.loadTool(disabledConfig, mockContext)).rejects.toThrow('工具已被停用');
    });
  });

  describe('loadBotTools', () => {
    it('should successfully load bot tools', async () => {
      const botToolConfigs = [
        { ...mockToolConfig, key: 'tool1' },
        { ...mockToolConfig, key: 'tool2' },
      ];

      aiBotsService.getBotToolConfigs.mockResolvedValue(botToolConfigs as any);

      const mockImplementation1 = new MockToolImplementation('tool1', 'Tool 1', 'First tool');
      const mockImplementation2 = new MockToolImplementation('tool2', 'Tool 2', 'Second tool');

      toolRegistry.getImplementation
        .mockReturnValueOnce(mockImplementation1)
        .mockReturnValueOnce(mockImplementation2);

      const tools = await service.loadBotTools('bot-id', mockContext);

      expect(tools).toHaveLength(2);
      expect(aiBotsService.getBotToolConfigs).toHaveBeenCalledWith('bot-id', 'test-tenant-id');
    });

    it('should filter out tools that fail to load', async () => {
      const botToolConfigs = [
        { ...mockToolConfig, key: 'valid_tool' },
        { ...mockToolConfig, key: 'invalid_tool' },
      ];

      aiBotsService.getBotToolConfigs.mockResolvedValue(botToolConfigs as any);

      const mockImplementation = new MockToolImplementation(
        'valid_tool',
        'Valid Tool',
        'A valid tool',
      );

      toolRegistry.getImplementation
        .mockReturnValueOnce(mockImplementation)
        .mockReturnValueOnce(undefined); // 第二個工具找不到實作

      const tools = await service.loadBotTools('bot-id', mockContext);

      expect(tools).toHaveLength(1);
      expect(tools[0].name).toBe('valid_tool');
    });

    it('should return empty array when bot has no tools', async () => {
      aiBotsService.getBotToolConfigs.mockResolvedValue([]);

      const tools = await service.loadBotTools('bot-id', mockContext);

      expect(tools).toHaveLength(0);
    });
  });

  describe('validateToolAccess', () => {
    it('should return true for valid tool access', async () => {
      const mockImplementation = new MockToolImplementation(
        'test_tool',
        'Test Tool',
        'A test tool',
      );
      toolRegistry.getImplementation.mockReturnValue(mockImplementation);

      const isValid = await service.validateToolAccess('test_tool', mockContext);

      expect(isValid).toBe(true);
    });

    it('should return false when tool implementation not found', async () => {
      toolRegistry.getImplementation.mockReturnValue(undefined);

      const isValid = await service.validateToolAccess('test_tool', mockContext);

      expect(isValid).toBe(false);
    });

    it('should return false when permission check fails', async () => {
      const mockImplementation = new MockToolImplementation(
        'test_tool',
        'Test Tool',
        'A test tool',
        true,
      );
      toolRegistry.getImplementation.mockReturnValue(mockImplementation);

      const isValid = await service.validateToolAccess('test_tool', mockContext);

      expect(isValid).toBe(false);
    });
  });

  describe('getAvailableTools', () => {
    it('should return list of available tools for context', async () => {
      const mockImplementation1 = new MockToolImplementation('tool1', 'Tool 1', 'First tool');
      const mockImplementation2 = new MockToolImplementation('tool2', 'Tool 2', 'Second tool');

      const implementations = new Map([
        ['tool1', mockImplementation1],
        ['tool2', mockImplementation2],
      ]);

      toolRegistry.getAllImplementations.mockReturnValue(implementations);

      const availableTools = await service.getAvailableTools(mockContext);

      expect(availableTools).toHaveLength(2);
      expect(availableTools).toEqual([
        { key: 'tool1', name: 'Tool 1', description: 'First tool' },
        { key: 'tool2', name: 'Tool 2', description: 'Second tool' },
      ]);
    });

    it('should filter out tools with failed permission checks', async () => {
      const mockImplementation1 = new MockToolImplementation('tool1', 'Tool 1', 'First tool');
      const mockImplementation2 = new MockToolImplementation(
        'tool2',
        'Tool 2',
        'Second tool',
        true,
      );

      const implementations = new Map([
        ['tool1', mockImplementation1],
        ['tool2', mockImplementation2],
      ]);

      toolRegistry.getAllImplementations.mockReturnValue(implementations);

      const availableTools = await service.getAvailableTools(mockContext);

      expect(availableTools).toHaveLength(1);
      expect(availableTools[0].key).toBe('tool1');
    });
  });
});
