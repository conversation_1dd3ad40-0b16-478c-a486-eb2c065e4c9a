import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  IsNumber,
  IsEnum,
  IsDateString,
  IsArray,
} from 'class-validator';
import { PrismaClient } from '@prisma/client';

export class CreateTenantDto {
  @ApiProperty({ description: '租戶名稱' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '租戶網域' })
  @IsString()
  @IsNotEmpty()
  domain: string;

  @ApiProperty({ description: '國家/地區代碼', default: 'TW' })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({
    description: '租戶方案',
    enum: ['basic', 'professional', 'enterprise'],
    default: 'basic',
  })
  @IsString()
  @IsOptional()
  @IsEnum(['basic', 'professional', 'enterprise'])
  plan?: string;

  @ApiProperty({ description: '方案 ID', required: false })
  @IsString()
  @IsOptional()
  planId?: string;

  @ApiProperty({
    description: '租戶狀態',
    enum: ['active', 'inactive', 'pending'],
    default: 'active',
  })
  @IsString()
  @IsOptional()
  @IsEnum(['active', 'inactive', 'pending'])
  status?: string;

  @ApiProperty({ description: '管理員名稱' })
  @IsString()
  @IsOptional()
  adminName?: string;

  @ApiProperty({ description: '管理員信箱' })
  @IsEmail()
  @IsOptional()
  adminEmail?: string;

  @ApiProperty({
    description: '公司規模',
    enum: ['1-10', '11-50', '51-200', '201-500', '500+'],
  })
  @IsString()
  @IsOptional()
  @IsEnum(['1-10', '11-50', '51-200', '201-500', '500+'])
  companySize?: string;

  @ApiProperty({ description: '產業類型' })
  @IsString()
  @IsOptional()
  industry?: string;

  @ApiProperty({ description: '最大使用者數', minimum: 1, maximum: 1000 })
  @IsNumber()
  @IsOptional()
  maxUsers?: number;

  @ApiProperty({ description: '最大專案數', minimum: 1, maximum: 1000 })
  @IsNumber()
  @IsOptional()
  maxProjects?: number;

  @ApiProperty({ description: '最大儲存空間 (GB)', minimum: 1, maximum: 10000 })
  @IsNumber()
  @IsOptional()
  maxStorage?: number;

  @ApiProperty({
    description: '帳單週期',
    enum: ['monthly', 'yearly'],
    default: 'monthly',
  })
  @IsString()
  @IsOptional()
  @IsEnum(['monthly', 'yearly'])
  billingCycle?: string;

  @ApiProperty({ description: '下次帳單日期', required: false })
  @IsOptional()
  @IsDateString()
  nextBillingDate?: string;

  @ApiProperty({ description: '部門列表', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  departments?: string[];
}
