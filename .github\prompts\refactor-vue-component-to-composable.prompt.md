---
mode: "agent"
tools: ["codebase"]
description: "協助將 Vue 3 元件的邏輯重構到 Composables 中"
---

# Vue 3 元件與 Composable 重構助手

我需要協助重構一個 Vue 3 元件，將其部分的 UI 邏輯或狀態管理分離到一個或多個 Composable 函式中。

**目標元件路徑**: `${input:componentPath:請輸入 Vue 元件的完整路徑 (例如：apps/frontend/src/views/MyView.vue)}`

請分析指定的 Vue 元件 (`${componentPath}`)，並提供以下建議：

1.  **可抽取的邏輯**:

    - 列出該元件中哪些響應式狀態 (ref, reactive)、計算屬性 (computed) 和函式 (methods/functions) 適合被移動到一個新的 Composable 函式中。
    - 說明抽取這些邏輯的理由 (例如：提高可複用性、簡化元件邏輯、關注點分離)。

2.  **Composable 函式結構建議**:

    - 為每個建議抽取的邏輯集合，提供一個 Composable 函式的基本結構 (TypeScript 格式)。
    - 包含必要的參數傳遞和回傳值。
    - 建議 Composable 函式的命名 (例如 `useMyFeature` 或 `useSpecificLogic`)。
    - 建議新 Composable 檔案的存放路徑 (例如 `apps/frontend/src/composables/`)。

3.  **元件重構指引**:
    - 展示如何在原始 Vue 元件中引入並使用新建立的 Composable 函式。
    - 指出原始元件中哪些程式碼可以被移除或替換。

請確保 Composable 函式遵循 Vue 3 Composition API 的最佳實踐，並使用 TypeScript。
