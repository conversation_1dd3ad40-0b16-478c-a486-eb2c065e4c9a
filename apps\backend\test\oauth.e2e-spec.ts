import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';
import { AppModule } from '../src/app.module';

describe('OAuth (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Google OAuth', () => {
    it('/auth/google (GET) should redirect to Google OAuth', async () => {
      const response = await request(app.getHttpServer())
        .get('/auth/google')
        .expect(302);

      // 應該重定向到 Google OAuth 授權 URL
      expect(response.headers.location).toContain('accounts.google.com');
    });

    it('/auth/google/callback (GET) should handle callback', async () => {
      // 這個測試需要模擬 Google OAuth 回調
      // 在實際測試中，這需要設置測試用的 OAuth 應用
      const response = await request(app.getHttpServer())
        .get('/auth/google/callback')
        .expect(302);

      // 沒有有效的 OAuth 回調參數時，應該重定向到錯誤頁面
      expect(response.headers.location).toContain('error');
    });
  });

  describe('LINE OAuth', () => {
    it('/auth/line (GET) should redirect to LINE Login', async () => {
      const response = await request(app.getHttpServer())
        .get('/auth/line')
        .expect(302);

      // 應該重定向到 LINE Login 授權 URL
      expect(response.headers.location).toContain('access.line.me');
    });

    it('/auth/line/callback (GET) should handle callback', async () => {
      // 這個測試需要模擬 LINE OAuth 回調
      const response = await request(app.getHttpServer())
        .get('/auth/line/callback')
        .expect(302);

      // 沒有有效的 OAuth 回調參數時，應該重定向到錯誤頁面
      expect(response.headers.location).toContain('error');
    });
  });

  describe('OAuth Routes Availability', () => {
    it('should have all OAuth routes available', async () => {
      // 測試路由是否正確註冊
      const googleResponse = await request(app.getHttpServer())
        .get('/auth/google')
        .expect(302);

      const lineResponse = await request(app.getHttpServer())
        .get('/auth/line')
        .expect(302);

      // 確保路由存在且有重定向
      expect(googleResponse.status).toBe(302);
      expect(lineResponse.status).toBe(302);
    });
  });
}); 