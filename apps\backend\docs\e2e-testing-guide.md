# E2E 測試指南 (End-to-End Testing Guide)

## 概述

本文檔說明 HorizAI SaaS 專案的端到端 (E2E) 測試架構、配置和執行方式。E2E 測試確保整個應用程式的核心功能在真實環境中正常運作，特別是身份驗證和權限控制系統。

## 測試架構

### 技術棧

- **測試框架**: Jest
- **HTTP 測試**: Supertest
- **資料庫**: PostgreSQL (測試環境)
- **應用框架**: NestJS

### 目錄結構

```
apps/backend/test/
├── jest-e2e.json          # Jest E2E 配置
├── setup-e2e.ts           # 測試環境設置
└── app.e2e-spec.ts        # 主要 E2E 測試案例
```

## 配置說明

### Jest 配置 (`jest-e2e.json`)

```json
{
  "moduleFileExtensions": ["js", "json", "ts"],
  "rootDir": ".",
  "testRegex": ".e2e-spec.ts$",
  "transform": {
    "^.+\\.(t|j)s$": "ts-jest"
  },
  "moduleNameMapper": {
    "^@horizai/permissions$": "<rootDir>/../../../packages/permissions/src",
    "^@horizai/permissions/(.*)$": "<rootDir>/../../../packages/permissions/src/$1",
    "^@/(.*)$": "<rootDir>/../src/$1",
    "^@modules/(.*)$": "<rootDir>/../src/modules/$1"
  },
  "testEnvironment": "node",
  "setupFilesAfterEnv": ["<rootDir>/setup-e2e.ts"]
}
```

### 環境設置 (`setup-e2e.ts`)

- 設定測試環境變數
- 配置測試資料庫連接
- 設定 JWT 密鑰
- 配置測試超時時間

## 測試案例說明

### 1. 身份驗證測試 (Authentication Tests)

- **未授權訪問測試**: 驗證未登入用戶無法存取受保護的端點
- **註冊登入流程測試**: 完整測試用戶註冊、租戶建立、登入和個人資料獲取
- **無效憑證測試**: 驗證錯誤的登入憑證會被正確拒絕
- **資料驗證測試**: 驗證註冊時的資料驗證規則
- **登出功能測試**: 驗證登出功能正常運作

### 2. 權限控制測試 (Permission Tests)

- **管理員權限測試**: 驗證管理員可以存取管理端點
- **一般用戶權限測試**: 驗證一般用戶無法存取管理端點
- **角色管理測試**: 驗證角色讀取和管理功能
- **權限隔離測試**: 確保不同權限級別的正確隔離

### 3. 角色和權限管理測試 (Role and Permission Management Tests)

- **角色建立測試**: 驗證管理員可以建立新角色
- **權限列表測試**: 驗證權限列表的正確獲取
- **日誌匯出測試**: 驗證系統日誌匯出功能

### 4. 數據安全和隔離測試 (Data Security and Isolation Tests)

- **租戶隔離測試**: 確保不同租戶的數據完全隔離
- **用戶數據安全測試**: 驗證用戶只能存取自己租戶的數據

## 執行測試

### 本地執行

```bash
# 在後端目錄執行
cd apps/backend

# 運行所有 E2E 測試
pnpm test:e2e

# 運行特定測試檔案
pnpm test:e2e app.e2e-spec.ts
```

### CI/CD 整合

E2E 測試可以整合到 CI/CD 流程中：

```yaml
# 示例 GitHub Actions 配置
- name: Run E2E Tests
  run: |
    cd apps/backend
    pnpm test:e2e
```

## 測試數據管理

### 數據清理策略

測試開始前會按照外鍵依賴順序清理所有測試數據：

```typescript
// 清理順序 (最深層的表先清理)
await prisma.aiUsageLog.deleteMany({});
await prisma.aiBot.deleteMany({});
await prisma.aiKey.deleteMany({});
await prisma.refreshToken.deleteMany({});
await prisma.systemLog.deleteMany({});
await prisma.loginLog.deleteMany({});
await prisma.userRoleMapping.deleteMany({});
await prisma.rolePermissionMapping.deleteMany({});
await prisma.orderHistory.deleteMany({});
await prisma.payment.deleteMany({});
await prisma.user.deleteMany({});
await prisma.order.deleteMany({});
await prisma.role.deleteMany({});
await prisma.permission.deleteMany({});
await prisma.tenant.deleteMany({});
```

### 測試數據建立

- 動態建立權限和角色
- 使用時間戳確保數據唯一性
- 自動啟用租戶狀態
- 建立完整的用戶角色權限關聯

## 最佳實踐

### 1. 測試隔離

- 每個測試案例都應該是獨立的
- 使用 `beforeAll` 和 `afterAll` 進行設置和清理
- 避免測試間的數據依賴

### 2. 資料庫管理

- 使用專用的測試資料庫
- 每次測試前清理數據
- 按正確順序處理外鍵約束

### 3. 權限測試

- 測試正面案例（有權限時的成功操作）
- 測試負面案例（無權限時的拒絕）
- 驗證返回的數據結構

### 4. 錯誤處理

- 測試各種錯誤情況
- 驗證正確的 HTTP 狀態碼
- 檢查錯誤訊息的適當性

## 故障排除

### 常見問題

1. **資料庫連接失敗**

   - 檢查 PostgreSQL 容器是否運行
   - 驗證環境變數中的資料庫連接字串
   - 確認資料庫憑證正確

2. **權限測試失敗**

   - 檢查權限是否正確建立和分配
   - 驗證角色權限映射關係
   - 確認用戶角色分配正確

3. **測試超時**
   - 增加 Jest 超時時間
   - 檢查測試邏輯是否有死迴圈
   - 優化資料庫操作

### 調試技巧

- 使用 `console.log` 輸出關鍵信息
- 檢查測試資料庫中的實際數據
- 使用 Jest 的 `--verbose` 選項獲取詳細輸出

## 未來改進

- [ ] 增加更多業務邏輯測試
- [ ] 實作測試數據工廠模式
- [ ] 增加性能測試案例
- [ ] 整合測試覆蓋率報告
- [ ] 自動化測試報告生成

---

這個 E2E 測試套件為 HorizAI SaaS 專案提供了全面的端到端測試覆蓋，確保核心功能的可靠性和安全性。
