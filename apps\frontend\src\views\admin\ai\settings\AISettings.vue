<script setup lang="ts">
import { onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { 
  Settings, RefreshCw, ArrowLeft, Plus,
  Globe, Puzzle, Key, Brain, Bot
} from 'lucide-vue-next';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

// 導入 Tab 元件
import FeatureConfigTab from "@/components/admin/ai/settings/features/FeatureConfigTab.vue";
import AgentsManagementTab from "@/components/admin/ai/settings/agents/AgentsManagementTab.vue";
import ModelsManagementTab from "@/components/admin/ai/settings/models/ModelsManagementTab.vue";
import KeysManagementTab from "@/components/admin/ai/settings/keys/KeysManagementTab.vue";
import BaseSettingsTab from "@/components/admin/ai/settings/core/BaseSettingsTab.vue";

// 導入編輯器元件
import AiKeyEditor from "@/components/admin/ai/settings/keys/AiKeyEditor.vue";
import AiModelEditor from "@/components/admin/ai/settings/models/AiModelEditor.vue";
import AIBotEditor from "@/components/admin/ai/agents/AIBotEditor.vue";

// 導入統一的 composable
import { useAISettings } from "@/composables/admin/ai/settings/useAISettings";
import { AiBotScope } from "@/types/models/ai.model";

// 使用 Vue Router
const router = useRouter();

// 設定標籤配置 - 按照建議流程順序排列
const settingTabs = [
  {
    value: 'keys',
    label: 'API 金鑰',
    description: '憑證管理',
    fullDescription: '管理各種 AI 服務提供商的 API 金鑰和憑證',
    icon: Key,
    activeColor: 'bg-gradient-to-br from-emerald-500 to-green-600'
  },
  {
    value: 'models',
    label: 'AI 模型',
    description: '模型配置',
    fullDescription: '配置和管理各種 AI 模型的參數和設定',
    icon: Brain,
    activeColor: 'bg-gradient-to-br from-orange-500 to-red-600'
  },
  {
    value: 'agents',
    label: 'AI 助理',
    description: '助理管理',
    fullDescription: '創建和管理智能對話助理和聊天機器人',
    icon: Bot,
    activeColor: 'bg-gradient-to-br from-cyan-500 to-blue-600'
  },
  {
    value: 'feature-configs',
    label: '功能組態',
    description: '功能配置',
    fullDescription: '配置各項 AI 功能的參數和行為設定',
    icon: Puzzle,
    activeColor: 'bg-gradient-to-br from-purple-500 to-pink-600'
  },
  {
    value: 'global-settings',
    label: '基礎設定',
    description: '系統配置',
    fullDescription: '管理 AI 系統的基礎配置和核心設定',
    icon: Globe,
    activeColor: 'bg-gradient-to-br from-green-500 to-emerald-600'
  }
];

// 使用統一的 composable
const {
  // UI 狀態
  isPageLoading,
  showBotSheet,
  isCreatingNewBot,
  isKeySheetOpen,
  isModelSheetOpen,
  isEditingModel,
  isEditingKeyForEditor,

  // Tab 管理
  activeTab,
  updateActiveTabFromQuery,

  // 編輯器資料
  currentModelForEditor,
  currentKeyForEditor,
  currentBotFromManager,
  enabledModels,
  mappedEnabledModels,
  availableKeys,

  // 資料列表
  aiBots,
  aiModelsForTabs,
  aiKeysForTabs,
  keyList,
  systemFeatureDefinitions,
  aiGlobalSettings,

  // 載入狀態
  loading,
  isUpdating,
  keysLoading,
  modelManager,

  // 常數
  KEY_PROVIDERS_LIST,
  RESPONSE_FORMATS_LIST,

  // 方法
  updateFeatureConfig,
  fetchSystemFeatureDefinitions,
  getEffectiveBotForFeatureProp,
  getBotById,
  updateAiBotForFeature,
  fetchAiBots,
  openNewBotSheetHandler,
  openEditBotSheetHandler,
  saveBotHandler,
  deleteBotHandlerFromPage,
  updateEditData,
  updateSystemFeature,
  getBotScenesByScope,
  openNewModelSheet,
  openEditModelSheetHandler,
  closeModelSheet,
  handleSaveModelComposable,
  deleteModelHandler,
  openNewKeySheetHandler,
  openEditKeySheetHandler,
  saveKeyHandler,
  deleteKeyHandler,
  actualNavigateToBotTester,
  duplicateBot,
  currentBotSystemFeatureInfo,
  currentBotScenes,
  responseFormats,
  initialLoad,
  syncFeatureDefinitions,
} = useAISettings();

// 計算當前標籤資訊
const currentTabInfo = computed(() => {
  return settingTabs.find(tab => tab.value === activeTab.value) || settingTabs[0];
});

onMounted(() => {
  updateActiveTabFromQuery();
  initialLoad();
});
</script>

<template>
  <div class="min-h-screen bg-zinc-50 dark:bg-zinc-900">
    <div class="container mx-auto p-4 space-y-6">
      <!-- 頁面標題區域 - 簡潔設計 -->
      <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <!-- 標題與描述區域 -->
        <div class="flex items-center space-x-3">
          <!-- 圖示區域 -->
          <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md border border-blue-200 dark:border-blue-800">
            <Settings class="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          
          <!-- 文字內容 -->
          <div>
            <h1 class="text-xl font-semibold text-gray-900 dark:text-zinc-100">
              AI 系統管理
            </h1>
            <p class="text-sm text-gray-600 dark:text-zinc-400 mt-0.5">
              管理 AI 金鑰、模型、功能組態與基礎設定
            </p>
          </div>
        </div>
        
        <!-- 操作按鈕區域 -->
        <div class="flex items-center gap-2">
          <!-- 重新載入按鈕 -->
          <Button 
            @click="initialLoad" 
            variant="outline" 
            size="sm" 
            class="border-gray-200 dark:border-zinc-700"
            :disabled="loading"
          >
            <RefreshCw 
              class="w-4 h-4 mr-2" 
              :class="{ 'animate-spin': loading }" 
            />
            重新載入
          </Button>
          
          <!-- 返回儀表板按鈕 -->
          <Button 
            @click="router.push('/admin/ai-dashboard')" 
            size="sm"
          >
            <ArrowLeft class="w-4 h-4 mr-2" />
            返回儀表板
          </Button>
        </div>
      </div>

      <!-- 設定導航卡片 - 簡化設計 -->
      <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
        <!-- 側邊導航 -->
        <div class="xl:col-span-1">
          <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
            <CardContent class="p-4">
              <!-- 導航標題 -->
              <div class="flex items-center space-x-2 mb-4">
                <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md border border-blue-200 dark:border-blue-800">
                  <Settings class="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 class="text-base font-semibold text-gray-900 dark:text-zinc-100">設定分類</h3>
              </div>
              
              <!-- 導航選單 -->
              <nav class="space-y-2">
                <button
                  v-for="tab in settingTabs"
                  :key="tab.value"
                  @click="activeTab = tab.value"
                  :class="[
                    'w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left',
                    activeTab === tab.value
                      ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700'
                      : 'text-gray-600 dark:text-zinc-400 hover:bg-gray-50 dark:hover:bg-zinc-700 hover:text-gray-900 dark:hover:text-zinc-100'
                  ]"
                >
                  <!-- 圖示容器 -->
                  <div :class="[
                    'p-2 rounded-md',
                    activeTab === tab.value
                      ? 'bg-blue-100 dark:bg-blue-800'
                      : 'bg-gray-100 dark:bg-zinc-700'
                  ]">
                    <component 
                      :is="tab.icon" 
                      class="w-4 h-4" 
                      :class="activeTab === tab.value ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-zinc-400'" 
                    />
                  </div>
                  
                  <!-- 文字內容 -->
                  <div class="flex-1">
                    <div class="font-medium text-sm">{{ tab.label }}</div>
                    <div class="text-xs opacity-75 mt-0.5">{{ tab.description }}</div>
                  </div>
                  
                  <!-- 狀態指示器 -->
                  <div 
                    v-if="activeTab === tab.value" 
                    class="w-2 h-2 bg-blue-500 rounded-full"
                  ></div>
                </button>
              </nav>
              
              <!-- 底部描述 -->
              <div class="mt-4 pt-3 border-t border-gray-200 dark:border-zinc-700">
                <div class="text-xs text-gray-500 dark:text-zinc-400 leading-relaxed">
                  {{ settingTabs.find(t => t.value === activeTab)?.fullDescription }}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- 主要內容區域 -->
        <div class="xl:col-span-3">
          <div class="space-y-4">
            <!-- 當前標籤指示器 -->
            <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
              <CardContent class="p-4">
                <!-- 標籤資訊 -->
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md border border-blue-200 dark:border-blue-800">
                    <component :is="currentTabInfo.icon" class="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-zinc-100">{{ currentTabInfo.label }}</h2>
                    <p class="text-xs text-gray-600 dark:text-zinc-400 mt-0.5">{{ currentTabInfo.fullDescription }}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- 內容區域 -->
            <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
              <CardContent class="p-4">
                <!-- 基礎設定 -->
                <BaseSettingsTab v-if="activeTab === 'global-settings'" />

                <!-- 功能組態 -->
                <FeatureConfigTab
                  v-if="activeTab === 'feature-configs'"
                  :system-feature-definitions="systemFeatureDefinitions"
                  :ai-bots="aiBots"
                  :update-feature-config="updateFeatureConfig"
                  :fetch-system-feature-definitions="fetchSystemFeatureDefinitions"
                  :get-bot-by-id="getBotById"
                  :get-effective-bot-for-feature-prop="getEffectiveBotForFeatureProp"
                  :update-ai-bot="updateAiBotForFeature"
                  :fetch-ai-bots="fetchAiBots"
                  :active-tab="activeTab"
                  :sync-feature-definitions="syncFeatureDefinitions"
                  @request-new-bot="() => router.push('/ai-studio/new')"
                  @update:active-tab="activeTab = $event"
                />

                <!-- API 金鑰 -->
                <KeysManagementTab
                  v-if="activeTab === 'keys'"
                  :ai-keys="aiKeysForTabs"
                  :is-loading="keysLoading"
                  :key-providers="KEY_PROVIDERS_LIST"
                  @edit-key="openEditKeySheetHandler"
                  @delete-key="deleteKeyHandler"
                  @create-key="openNewKeySheetHandler"
                />

                <!-- AI 模型 -->
                <ModelsManagementTab 
                  v-if="activeTab === 'models'"
                  @open-new-model="openNewModelSheet" 
                />

                <!-- AI 助理 -->
                <AgentsManagementTab
                  v-if="activeTab === 'agents'"
                  :ai-bots="aiBots"
                  :ai-models="aiModelsForTabs"
                  :ai-keys="keyList"
                  @delete-agent="deleteBotHandlerFromPage"
                  @test-agent="actualNavigateToBotTester"
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>

    <!-- AI Key Sheet for Add/Edit -->
    <Sheet v-model:open="isKeySheetOpen">
      <SheetContent class="w-full sm:max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl overflow-y-auto">
        <SheetHeader class="pb-4 border-b border-gray-200 dark:border-zinc-700">
          <SheetTitle class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
            {{ isEditingKeyForEditor ? "編輯 API 金鑰" : "新增 API 金鑰" }}
          </SheetTitle>
          <SheetDescription class="text-sm text-gray-600 dark:text-zinc-400">
            {{ isEditingKeyForEditor
              ? "修改您的 API 金鑰詳細資訊。"
              : "新增一個 API 金鑰以供 AI Bot 使用。" }}
          </SheetDescription>
        </SheetHeader>
        <div class="py-4">
          <AiKeyEditor
            :key-data="currentKeyForEditor"
            :is-editing="isEditingKeyForEditor"
            @save="saveKeyHandler"
            @cancel="() => { isKeySheetOpen = false; }"
          />
        </div>
      </SheetContent>
    </Sheet>

    <!-- AI Model Sheet for Add/Edit -->
    <Sheet v-model:open="isModelSheetOpen">
      <SheetContent class="w-full sm:max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl overflow-y-auto">
        <SheetHeader class="pb-4 border-b border-gray-200 dark:border-zinc-700">
          <SheetTitle class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
            {{ isEditingModel ? "編輯 AI 模型" : "新增 AI 模型" }}
          </SheetTitle>
          <SheetDescription class="text-sm text-gray-600 dark:text-zinc-400">
            {{ isEditingModel
              ? "修改您的 AI 模型詳細資訊。"
              : "新增一個 AI 模型及其設定。" }}
          </SheetDescription>
        </SheetHeader>
        <div class="py-4">
          <AiModelEditor
            v-if="currentModelForEditor && Object.keys(currentModelForEditor).length > 0"
            :model-data="currentModelForEditor"
            :is-editing="isEditingModel"
            :available-providers="KEY_PROVIDERS_LIST"
            @save="handleSaveModelComposable"
            @close="closeModelSheet"
          />
        </div>
      </SheetContent>
    </Sheet>

    <!-- AI Bot Sheet for Add/Edit -->
    <Sheet v-model:open="showBotSheet">
      <SheetContent class="w-full sm:max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl flex flex-col">
        <SheetHeader class="pb-4 border-b border-gray-200 dark:border-zinc-700">
          <SheetTitle class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
            {{ isCreatingNewBot ? "新增 AI 助理" : "編輯 AI 助理" }}
          </SheetTitle>
          <SheetDescription class="text-sm text-gray-600 dark:text-zinc-400">
            {{ isCreatingNewBot
              ? "新增一個 AI 助理以供您的應用程式使用。"
              : "修改您的 AI 助理詳細資訊。" }}
          </SheetDescription>
        </SheetHeader>
        <div class="flex-1 overflow-y-auto py-4">
          <AIBotEditor
            :bot="currentBotFromManager"
            :edit-data="currentBotFromManager"
            :is-editing="!isCreatingNewBot"
            :available-models="mappedEnabledModels"
            :bot-scenes="currentBotScenes"
            :response-formats="responseFormats"
            :can-edit-core-props="true"
            :can-delete="true"
            :scope-description="
              currentBotFromManager.scope === AiBotScope.SYSTEM
                ? '系統級機器人'
                : '工作區級機器人'
            "
            :is-system-bot="currentBotFromManager.scope === AiBotScope.SYSTEM"
            :system-feature-info="currentBotSystemFeatureInfo"
            :system-features="systemFeatureDefinitions"
            :available-keys="availableKeys"
            :update-system-feature="updateSystemFeature"
            :update-edit-data="updateEditData"
            @confirm-delete="
              async () => {
                await deleteBotHandlerFromPage(currentBotFromManager.id);
                showBotSheet = false;
              }
            "
            @duplicate-bot="duplicateBot"
            @update:isDirty="(isDirty) => {}"
          />
        </div>
        <SheetFooter class="sticky bottom-0 bg-white dark:bg-zinc-900 border-t border-gray-200 dark:border-zinc-700 pt-4 mt-4 flex justify-end gap-2">
          <Button variant="outline" @click="showBotSheet = false" size="sm">
            取消
          </Button>
          <Button @click="saveBotHandler" :disabled="isUpdating" size="sm">
            {{ isCreatingNewBot ? "新增" : "儲存" }}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  </div>
</template>
