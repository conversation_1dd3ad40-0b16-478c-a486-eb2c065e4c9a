import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PrismaService } from '../../modules/core/prisma/prisma.service';
import { EncryptionService } from '../../modules/core/encryption/encryption.service';
import { JwtUser } from '../../types/jwt-user.type';
import * as crypto from 'crypto';

export interface AuditLogData {
  action: string;
  message?: string;
  user_id?: string;
  tenant_id?: string | null;
  ip?: string;
  target_resource?: string;
  target_resource_id?: string;
  status?: 'SUCCESS' | 'FAILURE' | 'WARNING';
  details?: Record<string, any>;
  path?: string;
  method?: string;
  error_message?: string;
}

export interface AuditContextData {
  user?: JwtUser;
  ip?: string;
  userAgent?: string;
  path?: string;
  method?: string;
}

export interface AuditLogFilter {
  user_id?: string;
  tenant_id?: string;
  action?: string;
  target_resource?: string;
  status?: string;
  start_date?: Date;
  end_date?: Date;
  search_query?: string;
  limit?: number;
  offset?: number;
}

@Injectable()
export class AuditLogService {
  private readonly logger = new Logger(AuditLogService.name);
  private readonly sensitiveFields = [
    'password',
    'secret',
    'token',
    'key',
    'auth',
    'credential',
    'authorization',
    'x-api-key',
    'refresh_token',
    'access_token',
    'private_key',
    'secret_key',
    'api_secret',
    'webhook_secret',
  ];

  constructor(
    private readonly prisma: PrismaService,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * 記錄系統日誌
   */
  async log(data: AuditLogData, context?: AuditContextData): Promise<void> {
    try {
      const sanitizedDetails = this.sanitizeDetails({
        ...data.details,
        userAgent: context?.userAgent,
        timestamp: new Date().toISOString(),
      });

      const auditData = {
        id: crypto.randomUUID(),
        level: 'AUDIT',
        action: data.action,
        message: data.message || `${data.action} operation`,
        user_id: data.user_id || context?.user?.id,
        tenant_id: data.tenant_id !== undefined ? data.tenant_id : context?.user?.tenant_id,
        ip: this.encryptSensitiveField(data.ip || context?.ip), // 加密 IP 地址
        target_resource: data.target_resource,
        target_resource_id: data.target_resource_id,
        status: data.status || 'SUCCESS',
        path: data.path || context?.path,
        method: data.method || context?.method,
        error_message: data.error_message,
        details: sanitizedDetails ? this.encryptDetailsForStorage(sanitizedDetails) : null, // 加密詳細信息
        created_at: new Date(),
      };

      await this.prisma.system_logs.create({
        data: auditData,
      });

      this.logger.debug(`稽核日誌已建立 [${data.action}]`, {
        summary: '日誌統計',
        userId: auditData.user_id || '系統',
        tenantId: auditData.tenant_id || '無',
        targetResource: auditData.target_resource || '無',
        targetResourceId: auditData.target_resource_id || '無',
        status: auditData.status,
        path: auditData.path || '無',
        method: auditData.method || '無',
      });
    } catch (error) {
      this.logger.error('Failed to create audit log', {
        error: error.message,
        action: data.action,
        stack: error.stack,
      });
      // 不拋出錯誤，避免影響主要業務流程
    }
  }

  /**
   * 記錄用戶操作
   */
  async logUserAction(
    action: string,
    user: JwtUser,
    details?: Record<string, any>,
    context?: Partial<AuditContextData>,
  ): Promise<void> {
    await this.log(
      {
        action: `USER_${action.toUpperCase()}`,
        user_id: user.id,
        tenant_id: user.tenant_id,
        details,
      },
      {
        user,
        ...context,
      },
    );
  }

  /**
   * 記錄資源操作
   */
  async logResourceAction(
    action: string,
    resourceType: string,
    resourceId: string,
    user?: JwtUser,
    details?: Record<string, any>,
    context?: Partial<AuditContextData>,
  ): Promise<void> {
    await this.log(
      {
        action: `${resourceType.toUpperCase()}_${action.toUpperCase()}`,
        target_resource: resourceType,
        target_resource_id: resourceId,
        user_id: user?.id,
        tenant_id: user?.tenant_id,
        details,
      },
      {
        user,
        ...context,
      },
    );
  }

  /**
   * 記錄安全事件
   */
  async logSecurityEvent(
    event: string,
    user?: JwtUser,
    details?: Record<string, any>,
    context?: Partial<AuditContextData>,
    status: 'SUCCESS' | 'FAILURE' | 'WARNING' = 'WARNING',
  ): Promise<void> {
    await this.log(
      {
        action: `SECURITY_${event.toUpperCase()}`,
        status,
        user_id: user?.id,
        tenant_id: user?.tenant_id,
        details,
      },
      {
        user,
        ...context,
      },
    );
  }

  /**
   * 記錄權限相關事件
   */
  async logPermissionEvent(
    event: string,
    resource: string,
    user?: JwtUser,
    details?: Record<string, any>,
    context?: Partial<AuditContextData>,
    status: 'SUCCESS' | 'FAILURE' = 'SUCCESS',
  ): Promise<void> {
    await this.log(
      {
        action: `PERMISSION_${event.toUpperCase()}`,
        target_resource: resource,
        status,
        user_id: user?.id,
        tenant_id: user?.tenant_id,
        details,
      },
      {
        user,
        ...context,
      },
    );
  }

  /**
   * 查詢系統日誌
   */
  async findLogs(filters: AuditLogFilter): Promise<{
    logs: any[];
    total: number;
    page: number;
    limit: number;
  }> {
    const where: any = {
      level: 'AUDIT',
    };

    // 構建查詢條件
    if (filters.user_id) {
      where.user_id = filters.user_id;
    }

    if (filters.tenant_id) {
      where.tenant_id = filters.tenant_id;
    }

    if (filters.action) {
      where.action = {
        contains: filters.action,
        mode: 'insensitive',
      };
    }

    if (filters.target_resource) {
      where.target_resource = {
        contains: filters.target_resource,
        mode: 'insensitive',
      };
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.search_query) {
      where.OR = [
        {
          message: {
            contains: filters.search_query,
            mode: 'insensitive',
          },
        },
        {
          action: {
            contains: filters.search_query,
            mode: 'insensitive',
          },
        },
      ];
    }

    // 日期範圍過濾
    if (filters.start_date || filters.end_date) {
      where.created_at = {};
      if (filters.start_date) {
        where.created_at.gte = filters.start_date;
      }
      if (filters.end_date) {
        where.created_at.lte = filters.end_date;
      }
    }

    const limit = filters.limit || 50;
    const offset = filters.offset || 0;
    const page = Math.floor(offset / limit) + 1;

    const [logs, total] = await Promise.all([
      this.prisma.system_logs.findMany({
        where,
        orderBy: { created_at: 'desc' },
        take: limit,
        skip: offset,
      }),
      this.prisma.system_logs.count({ where }),
    ]);

    // 解密敏感字段用於顯示
    const decryptedLogs = logs.map((log) => ({
      ...log,
      ip: this.decryptSensitiveField(log.ip),
      details: log.details ? this.parseDecryptedDetails(log.details) : null,
    }));

    return {
      logs: decryptedLogs,
      total,
      page,
      limit,
    };
  }

  /**
   * 清理敏感資料
   */
  private sanitizeDetails(details: Record<string, any>): Record<string, any> {
    if (!details || typeof details !== 'object') {
      return details;
    }

    const sanitized = { ...details };

    for (const key in sanitized) {
      if (this.isSensitiveField(key)) {
        sanitized[key] = '***REDACTED***';
      } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        if (Array.isArray(sanitized[key])) {
          sanitized[key] = sanitized[key].map((item: any) =>
            typeof item === 'object' && item !== null ? this.sanitizeDetails(item) : item,
          );
        } else {
          sanitized[key] = this.sanitizeDetails(sanitized[key]);
        }
      }
    }

    return sanitized;
  }

  /**
   * 檢查是否為敏感字段
   */
  private isSensitiveField(fieldName: string): boolean {
    const lowerField = fieldName.toLowerCase();
    return this.sensitiveFields.some((sensitive) => lowerField.includes(sensitive));
  }

  /**
   * 加密敏感字段
   */
  private encryptSensitiveField(value: string | null | undefined): string | null {
    if (!value) return null;
    try {
      return this.encryptionService.encrypt(value);
    } catch (error) {
      this.logger.error('Failed to encrypt sensitive field', { error: error.message });
      return value; // 如果加密失敗，返回原值但記錄錯誤
    }
  }

  /**
   * 解密敏感字段
   */
  private decryptSensitiveField(encryptedValue: string | null | undefined): string | null {
    if (!encryptedValue) return null;
    try {
      return this.encryptionService.decrypt(encryptedValue);
    } catch (error) {
      this.logger.error('Failed to decrypt sensitive field', { error: error.message });
      return encryptedValue; // 如果解密失敗，返回原值但記錄錯誤
    }
  }

  /**
   * 加密詳細信息以供存儲
   */
  private encryptDetailsForStorage(details: Record<string, any>): any {
    try {
      const jsonString = JSON.stringify(details);
      const encrypted = this.encryptSensitiveField(jsonString);

      // 使用特殊標記來識別加密的數據
      return {
        __encrypted: true,
        data: encrypted,
      };
    } catch (error) {
      this.logger.error('Failed to encrypt details for storage', { error: error.message });
      return details; // 返回原始數據
    }
  }

  /**
   * 解析並解密詳細信息
   */
  private parseDecryptedDetails(detailsData: any): Record<string, any> | null {
    try {
      // 檢查是否為新的加密格式
      if (
        detailsData &&
        typeof detailsData === 'object' &&
        detailsData.__encrypted &&
        detailsData.data
      ) {
        const decryptedJson = this.decryptSensitiveField(detailsData.data);
        return decryptedJson ? JSON.parse(decryptedJson) : null;
      }

      // 處理舊格式數據
      if (detailsData && typeof detailsData === 'object' && detailsData.encrypted) {
        const decryptedJson = this.decryptSensitiveField(detailsData.encrypted);
        return decryptedJson ? JSON.parse(decryptedJson) : null;
      }

      // 處理字符串格式
      if (typeof detailsData === 'string') {
        try {
          return JSON.parse(detailsData);
        } catch {
          const decryptedJson = this.decryptSensitiveField(detailsData);
          return decryptedJson ? JSON.parse(decryptedJson) : null;
        }
      }

      // 直接返回 JSON 對象
      return detailsData;
    } catch (error) {
      this.logger.error('Failed to parse decrypted details', { error: error.message });
      return detailsData; // 返回原始數據
    }
  }

  /**
   * 取得稽核統計資訊
   */
  async getAuditStats(
    tenant_id?: string,
    start_date?: Date,
    end_date?: Date,
  ): Promise<{
    totalLogs: number;
    logsByAction: Record<string, number>;
    logsByStatus: Record<string, number>;
    logsByResource: Record<string, number>;
  }> {
    const where: any = {
      level: 'AUDIT',
    };

    if (tenant_id) {
      where.tenant_id = tenant_id;
    }

    if (start_date || end_date) {
      where.created_at = {};
      if (start_date) {
        where.created_at.gte = start_date;
      }
      if (end_date) {
        where.created_at.lte = end_date;
      }
    }

    const [totalLogs, actionStats, statusStats, resourceStats] = await Promise.all([
      this.prisma.system_logs.count({ where }),
      this.prisma.system_logs.groupBy({
        by: ['action'],
        where,
        _count: true,
      }),
      this.prisma.system_logs.groupBy({
        by: ['status'],
        where,
        _count: true,
      }),
      this.prisma.system_logs.groupBy({
        by: ['target_resource'],
        where: {
          ...where,
          target_resource: { not: null },
        },
        _count: true,
      }),
    ]);

    return {
      totalLogs,
      logsByAction: actionStats.reduce(
        (acc, item) => {
          acc[item.action || 'UNKNOWN'] = item._count;
          return acc;
        },
        {} as Record<string, number>,
      ),
      logsByStatus: statusStats.reduce(
        (acc, item) => {
          acc[item.status || 'UNKNOWN'] = item._count;
          return acc;
        },
        {} as Record<string, number>,
      ),
      logsByResource: resourceStats.reduce(
        (acc, item) => {
          acc[item.target_resource || 'UNKNOWN'] = item._count;
          return acc;
        },
        {} as Record<string, number>,
      ),
    };
  }

  /**
   * 事件監聽器：處理來自 LoggingInterceptor 的詳細系統日誌事件
   */
  @OnEvent('audit.detailed.log')
  async handleDetailedLogEvent(payload: { logData: AuditLogData; context: AuditContextData }) {
    try {
      await this.log(payload.logData, payload.context);
    } catch (error) {
      this.logger.error('Failed to process detailed audit log event', {
        error: error.message,
        action: payload.logData.action,
        user_id: payload.logData.user_id || payload.context.user?.id,
      });
    }
  }

  private static sanitizeMetadata(metadata: Record<string, any>): Record<string, any> {
    if (!metadata || typeof metadata !== 'object') {
      return {};
    }

    const sensitiveFieldPrefixes = [
      'password',
      'secret',
      'token',
      'key',
      'auth',
      'credential',
      'x-api-key',
      'refresh_token',
      'secret_key',
      'api_secret',
      'webhook_secret',
    ];

    const sanitized = { ...metadata };

    Object.keys(sanitized).forEach((key) => {
      const lowerKey = key.toLowerCase();
      if (sensitiveFieldPrefixes.some((prefix) => lowerKey.includes(prefix))) {
        sanitized[key] = '[REDACTED]';
      }
    });

    return sanitized;
  }
}
